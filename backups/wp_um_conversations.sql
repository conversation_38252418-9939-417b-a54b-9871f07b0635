-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jul 20, 2025 at 08:38 PM
-- Server version: 10.6.22-MariaDB
-- PHP Version: 7.2.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `mytour7_tour`
--

-- --------------------------------------------------------

--
-- Table structure for table `wp_um_conversations`
--

CREATE TABLE `wp_um_conversations` (
  `conversation_id` bigint(20) UNSIGNED NOT NULL,
  `user_a` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `user_b` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `last_updated` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `wp_um_conversations`
--

INSERT INTO `wp_um_conversations` (`conversation_id`, `user_a`, `user_b`, `last_updated`) VALUES
(1, 5, 1, '2023-12-12 18:15:55'),
(2, 6, 1, '2024-02-21 21:51:19'),
(6, 1, 3, '2024-04-29 15:28:21'),
(7, 50, 6, '2024-05-21 14:37:07'),
(8, 51, 6, '2024-05-21 14:37:36'),
(9, 47, 6, '2024-05-22 21:30:31'),
(10, 53, 6, '2024-05-21 14:36:59'),
(11, 56, 6, '2024-05-21 14:36:46'),
(12, 3, 6, '2024-05-21 18:56:39'),
(13, 47, 58, '2024-05-22 23:10:16'),
(14, 65, 6, '2024-05-21 14:35:46'),
(15, 6, 67, '2024-05-20 20:56:17'),
(16, 60, 62, '2024-05-02 12:49:29'),
(17, 70, 6, '2024-05-20 20:54:57'),
(18, 60, 6, '2024-05-20 20:56:25'),
(19, 66, 6, '2024-05-21 14:35:35'),
(20, 64, 6, '2024-05-21 14:35:58'),
(21, 63, 6, '2024-07-18 13:56:52'),
(22, 62, 6, '2025-07-18 21:53:40'),
(23, 61, 6, '2024-05-21 14:36:20'),
(24, 59, 6, '2024-05-21 14:36:26'),
(25, 55, 6, '2024-05-21 14:36:33'),
(26, 58, 6, '2024-07-19 17:22:36'),
(27, 54, 6, '2024-05-21 14:36:51'),
(28, 52, 6, '2024-05-23 02:30:46'),
(29, 45, 6, '2024-05-21 14:37:49'),
(30, 44, 6, '2024-05-21 14:38:58'),
(31, 72, 6, '2024-07-19 17:21:21'),
(32, 59, 64, '2024-05-24 17:47:04'),
(33, 44, 81, '2024-07-12 21:12:04'),
(34, 65, 81, '2024-07-12 21:14:04'),
(35, 51, 81, '2024-07-12 21:15:36'),
(36, 130, 6, '2024-07-18 18:03:22'),
(37, 6, 177, '2024-07-19 17:13:13'),
(38, 81, 71, '2024-07-19 21:45:37'),
(39, 149, 6, '2024-07-19 17:12:58'),
(40, 154, 6, '2024-07-19 17:13:22'),
(41, 192, 6, '2024-07-19 17:13:33'),
(42, 173, 6, '2024-07-19 17:13:59'),
(43, 190, 6, '2024-07-19 17:14:09'),
(44, 144, 6, '2024-07-19 17:14:21'),
(45, 191, 6, '2024-07-19 17:15:16'),
(46, 163, 6, '2024-07-19 17:15:29'),
(47, 184, 6, '2024-07-19 17:15:39'),
(48, 170, 6, '2024-07-19 19:15:51'),
(49, 179, 6, '2024-07-19 17:16:22'),
(50, 187, 6, '2024-07-19 17:16:35'),
(51, 151, 6, '2024-07-19 19:41:40'),
(52, 137, 6, '2024-07-19 17:17:09'),
(53, 139, 6, '2024-07-19 17:17:18'),
(54, 136, 6, '2024-07-19 17:17:28'),
(55, 105, 6, '2024-07-19 18:34:46'),
(57, 128, 6, '2024-07-19 17:18:23'),
(58, 119, 6, '2024-07-22 14:53:24'),
(59, 120, 6, '2024-07-19 17:18:59'),
(60, 123, 6, '2024-07-19 17:19:07'),
(61, 118, 6, '2024-07-19 17:19:15'),
(62, 101, 6, '2024-07-19 17:19:31'),
(63, 90, 6, '2024-07-19 17:19:39'),
(64, 94, 6, '2024-07-19 17:19:46'),
(65, 92, 6, '2024-07-19 17:19:56'),
(66, 95, 6, '2024-07-19 17:20:05'),
(67, 96, 6, '2024-07-19 17:20:15'),
(68, 85, 6, '2024-07-19 17:20:24'),
(69, 82, 6, '2024-07-19 17:20:41'),
(70, 79, 6, '2024-07-19 17:21:01'),
(71, 113, 96, '2024-07-22 16:14:11'),
(72, 113, 54, '2024-07-23 08:29:18'),
(73, 45, 1, '2024-07-25 16:20:49'),
(74, 45, 236, '2024-07-26 21:08:48'),
(75, 47, 81, '2024-08-06 20:27:23'),
(76, 75, 187, '2024-08-08 18:10:46'),
(77, 166, 44, '2024-09-06 19:40:21'),
(78, 6, 389, '2024-10-07 17:45:50'),
(79, 389, 44, '2024-10-07 18:29:18'),
(80, 262, 236, '2024-10-15 20:54:31'),
(81, 235, 166, '2024-11-20 02:23:05'),
(82, 235, 1, '2024-11-20 22:51:46'),
(83, 384, 444, '2024-12-11 22:36:59'),
(84, 6, 73, '2025-01-10 19:28:05'),
(85, 227, 62, '2025-01-25 00:32:28'),
(86, 154, 44, '2025-02-11 15:59:18'),
(87, 235, 420, '2025-02-11 19:54:17'),
(88, 420, 3, '2025-02-11 21:17:18'),
(89, 420, 3, '2025-02-11 21:14:48'),
(90, 3, 235, '2025-02-13 21:32:30'),
(91, 6, 504, '2025-03-05 22:16:47'),
(92, 6, 444, '2025-03-06 17:59:45'),
(93, 235, 548, '2025-04-09 00:41:22'),
(94, 573, 235, '2025-05-18 22:52:23'),
(95, 578, 6, '2025-06-17 15:46:16'),
(96, 608, 6, '2025-06-26 21:40:21'),
(97, 235, 554, '2025-06-28 03:52:01'),
(98, 609, 6, '2025-07-18 17:34:31');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `wp_um_conversations`
--
ALTER TABLE `wp_um_conversations`
  ADD PRIMARY KEY (`conversation_id`),
  ADD KEY `user_a_user_b` (`user_a`,`user_b`),
  ADD KEY `user_a` (`user_a`),
  ADD KEY `user_b` (`user_b`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `wp_um_conversations`
--
ALTER TABLE `wp_um_conversations`
  MODIFY `conversation_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=99;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
