-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jul 20, 2025 at 08:37 PM
-- Server version: 10.6.22-MariaDB
-- PHP Version: 7.2.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `mytour7_tour`
--

-- --------------------------------------------------------

--
-- Table structure for table `wp_upvote_counter`
--

CREATE TABLE `wp_upvote_counter` (
  `ID` int(11) NOT NULL,
  `post_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `upvote_count` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `wp_upvote_counter`
--

INSERT INTO `wp_upvote_counter` (`ID`, `post_id`, `user_id`, `upvote_count`) VALUES
(1, 683, 0, 6),
(2, 836, 0, 1),
(3, 880, 0, 1),
(4, 877, 0, 1),
(5, 644, 0, 1),
(6, 643, 0, 1),
(7, 942, 0, 1),
(8, 891, 0, 1),
(9, 4735, 0, 2),
(10, 4732, 0, 3),
(11, 4738, 0, 1),
(12, 4668, 0, 3),
(13, 4746, 0, 1),
(14, 4742, 0, 1),
(15, 4712, 0, 1),
(16, 4757, 0, 1),
(17, 4763, 0, 2),
(18, 4761, 0, 1),
(19, 4753, 0, 1),
(20, 4838, 0, 4),
(21, 4836, 0, 1),
(22, 4837, 0, 3),
(23, 4834, 0, 1),
(24, 4826, 0, 5),
(25, 4822, 0, 5),
(26, 4819, 0, 4),
(27, 4818, 0, 1),
(28, 4835, 0, 1),
(29, 4840, 0, 1),
(30, 4766, 0, 1),
(31, 4764, 0, 1),
(32, 4843, 0, 3),
(33, 4867, 0, 3),
(34, 4841, 0, 3),
(35, 4915, 0, 7),
(36, 4894, 0, 4),
(37, 4868, 0, 3),
(38, 4924, 0, 4),
(39, 4922, 0, 4),
(40, 4920, 0, 4),
(41, 4918, 0, 4),
(42, 4965, 0, 5),
(43, 4952, 0, 4),
(44, 4935, 0, 2),
(45, 4937, 0, 3),
(46, 4968, 0, 3),
(47, 4966, 0, 5),
(48, 4869, 0, 3),
(49, 4970, 0, 4),
(50, 4959, 0, 2),
(51, 4985, 0, 3),
(52, 4980, 0, 3),
(53, 4983, 0, 4),
(54, 4988, 0, 7),
(55, 4728, 0, 1),
(56, 2706, 0, 1),
(57, 5005, 0, 3),
(58, 5000, 0, 4),
(59, 5006, 0, 4),
(60, 5016, 0, 5),
(61, 5015, 0, 4),
(62, 4999, 0, 3),
(63, 5025, 0, 1),
(64, 5026, 0, 1),
(65, 5024, 0, 1),
(66, 5023, 0, 1),
(67, 5021, 0, 2),
(68, 5019, 0, 2),
(69, 5001, 0, 2),
(70, 5007, 0, 2),
(71, 5009, 0, 2),
(72, 4975, 0, 1),
(73, 4974, 0, 2),
(74, 4931, 0, 2),
(75, 4871, 0, 1),
(76, 4895, 0, 2),
(77, 4844, 0, 2),
(78, 4896, 0, 2),
(79, 5027, 0, 2),
(80, 5047, 0, 5),
(81, 5041, 0, 1),
(82, 5028, 0, 1),
(83, 4839, 0, 1),
(84, 5060, 0, 1),
(85, 5072, 0, 9),
(86, 5069, 0, 3),
(87, 5063, 0, 1),
(88, 5080, 0, 3),
(89, 5036, 0, 1),
(90, 5094, 0, 3),
(91, 5091, 0, 3),
(92, 5086, 0, 4),
(93, 5083, 0, 2),
(94, 5089, 0, 2),
(95, 5088, 0, 2),
(96, 5109, 0, 8),
(97, 5129, 0, 7),
(98, 5146, 0, 4),
(99, 5148, 0, 4),
(100, 5150, 0, 3),
(101, 5120, 0, 2),
(102, 5127, 0, 2),
(103, 5124, 0, 3),
(104, 5125, 0, 2),
(105, 5106, 0, 3),
(106, 5153, 0, 1),
(107, 5158, 0, 3),
(108, 5161, 0, 4),
(109, 5152, 0, 3),
(110, 5156, 0, 2),
(111, 5130, 0, 1),
(112, 5110, 0, 1),
(113, 5111, 0, 1),
(114, 5164, 0, 1),
(115, 5165, 0, 1),
(116, 5186, 0, 2),
(117, 5279, 0, 5),
(118, 5285, 0, 6),
(119, 5270, 0, 3),
(120, 5342, 0, 2),
(121, 5316, 0, 1),
(122, 5298, 0, 1),
(123, 5330, 0, 1),
(124, 5352, 0, 4),
(125, 5383, 0, 3),
(126, 5419, 0, 4),
(127, 5417, 0, 1),
(128, 5418, 0, 4),
(129, 5374, 0, 3),
(130, 5306, 0, 1),
(131, 5075, 0, 1),
(132, 5448, 0, 1),
(133, 5276, 0, 3),
(134, 5426, 0, 1),
(135, 5476, 0, 3),
(136, 5480, 0, 1),
(137, 5482, 0, 3),
(138, 5501, 0, 4),
(139, 5497, 0, 1),
(140, 5634, 0, 2),
(141, 5952, 0, 5),
(142, 4824, 0, 1),
(143, 4648, 0, 1),
(144, 1033, 0, 1),
(145, 1030, 0, 1),
(146, 1027, 0, 1),
(147, 1022, 0, 1),
(148, 1019, 0, 1),
(149, 1014, 0, 1),
(150, 994, 0, 1),
(151, 991, 0, 1),
(152, 987, 0, 1),
(153, 985, 0, 1),
(154, 982, 0, 1),
(155, 978, 0, 1),
(156, 975, 0, 1),
(157, 972, 0, 1),
(158, 970, 0, 3),
(159, 968, 0, 2),
(160, 966, 0, 2),
(161, 841, 0, 2),
(162, 5916, 0, 1),
(163, 5632, 0, 2),
(164, 5484, 0, 2),
(165, 5453, 0, 2),
(166, 5432, 0, 2),
(167, 5499, 0, 2),
(168, 5956, 0, 1),
(169, 5958, 0, 4),
(170, 5949, 0, 1),
(171, 5967, 0, 3),
(172, 5969, 0, 2),
(173, 5964, 0, 1),
(174, 5994, 0, 4),
(175, 6001, 0, 3),
(176, 5988, 0, 1),
(177, 6005, 0, 2),
(178, 5993, 0, 4),
(179, 5974, 0, 1),
(180, 5961, 0, 2),
(181, 5980, 0, 2),
(182, 5960, 0, 1),
(183, 6011, 0, 1),
(184, 6007, 0, 1),
(185, 6009, 0, 2),
(186, 6033, 0, 4),
(187, 6039, 0, 1),
(188, 6054, 0, 7),
(189, 6057, 0, 3),
(190, 6070, 0, 4),
(191, 6087, 0, 3),
(192, 6081, 0, 1),
(193, 6092, 0, 3),
(194, 6085, 0, 2),
(195, 6038, 0, 6),
(196, 6066, 0, 1),
(197, 6067, 0, 1),
(198, 6047, 0, 3),
(199, 6110, 0, 1),
(200, 6140, 0, 2),
(201, 6117, 0, 2),
(202, 6200, 0, 1),
(203, 6191, 0, 2),
(204, 6126, 0, 1),
(205, 6118, 0, 1),
(206, 6022, 0, 1),
(207, 6189, 0, 2),
(208, 6187, 0, 3),
(209, 6123, 0, 1),
(210, 6226, 0, 3),
(211, 6230, 0, 1),
(212, 6234, 0, 2),
(213, 6248, 0, 3),
(214, 6243, 0, 2),
(215, 6277, 0, 1),
(216, 6232, 0, 1),
(217, 6233, 0, 1),
(218, 6291, 0, 3),
(219, 6292, 0, 1),
(220, 6300, 0, 1),
(221, 6299, 0, 2),
(222, 6302, 0, 5),
(223, 6293, 0, 1),
(224, 6241, 0, 2),
(225, 6316, 0, 2),
(226, 6312, 0, 1),
(227, 6326, 0, 4),
(228, 6336, 0, 1),
(229, 6341, 0, 2),
(230, 6356, 0, 2),
(231, 6359, 0, 2),
(232, 6377, 0, 1),
(233, 6384, 0, 2),
(234, 6392, 0, 1),
(235, 6417, 0, 2),
(236, 6406, 0, 1),
(237, 6418, 0, 1),
(238, 6438, 0, 2),
(239, 6432, 0, 1),
(240, 6451, 0, 1),
(241, 6468, 0, 1),
(242, 6474, 0, 1),
(243, 6492, 0, 1),
(244, 6500, 0, 2),
(245, 6498, 0, 1),
(246, 6517, 0, 1),
(247, 6523, 0, 2),
(248, 6519, 0, 1),
(249, 6543, 0, 2),
(250, 6481, 0, 3),
(251, 6578, 0, 5),
(252, 6583, 0, 1),
(253, 6574, 0, 1),
(254, 6571, 0, 2),
(255, 6588, 0, 1),
(256, 6589, 0, 1),
(257, 6565, 0, 1),
(258, 6550, 0, 1),
(259, 6549, 0, 1),
(260, 6526, 0, 2),
(261, 6530, 0, 1),
(262, 6533, 0, 1),
(263, 6612, 0, 1),
(264, 6507, 0, 1),
(265, 6653, 0, 3),
(266, 6683, 0, 1),
(267, 6676, 0, 1),
(268, 6714, 0, 2),
(269, 6605, 0, 1),
(270, 6728, 0, 2),
(271, 6690, 0, 3),
(272, 6732, 0, 1),
(273, 6813, 0, 3),
(274, 6836, 0, 1),
(275, 6825, 0, 3),
(276, 6864, 0, 3),
(277, 6882, 0, 4),
(278, 6870, 0, 1),
(279, 6908, 0, 5),
(280, 6851, 0, 3),
(281, 6912, 0, 1),
(282, 6988, 0, 2),
(283, 6993, 0, 3),
(284, 6991, 0, 5),
(285, 6962, 0, 2),
(286, 6959, 0, 2),
(287, 6951, 0, 1),
(288, 6905, 0, 1),
(289, 6849, 0, 1),
(290, 6842, 0, 1),
(291, 7000, 0, 2),
(292, 7005, 0, 4),
(293, 7002, 0, 4),
(294, 7049, 0, 1),
(295, 7042, 0, 1),
(296, 7054, 0, 1),
(297, 7067, 0, 1),
(298, 7082, 0, 1),
(299, 7115, 0, 2),
(300, 7131, 0, 4),
(301, 7081, 0, 1),
(302, 7117, 0, 1),
(303, 7153, 0, 1),
(304, 7178, 0, 2),
(305, 7181, 0, 2),
(306, 7226, 0, 3),
(307, 7237, 0, 3),
(308, 7273, 0, 2),
(309, 7255, 0, 1),
(310, 7278, 0, 2),
(311, 7247, 0, 1),
(312, 7325, 0, 2),
(313, 7283, 0, 1),
(314, 7290, 0, 1),
(315, 7276, 0, 3),
(316, 7274, 0, 1),
(317, 7359, 0, 1),
(318, 7385, 0, 1),
(319, 7411, 0, 2),
(320, 7416, 0, 1),
(321, 7413, 0, 1),
(322, 7438, 0, 3),
(323, 7363, 0, 1),
(324, 7520, 0, 2),
(325, 7524, 0, 1),
(326, 7518, 0, 1),
(327, 7602, 0, 5),
(328, 7594, 0, 1),
(329, 7593, 0, 4),
(330, 7590, 0, 1),
(331, 7609, 0, 2),
(332, 7628, 0, 2),
(333, 7621, 0, 2),
(334, 7626, 0, 1),
(335, 7655, 0, 4),
(336, 7635, 0, 1),
(337, 7682, 0, 1),
(338, 7697, 0, 2),
(339, 7693, 0, 2),
(340, 7724, 0, 5),
(341, 7755, 0, 2),
(342, 7749, 0, 1),
(343, 7760, 0, 1),
(344, 7777, 0, 2),
(345, 7791, 0, 1),
(346, 7802, 0, 1),
(347, 7814, 0, 1),
(348, 7843, 0, 2),
(349, 7834, 0, 1),
(350, 7857, 0, 3),
(351, 7862, 0, 3),
(352, 7845, 0, 1),
(353, 7868, 0, 1),
(354, 7891, 0, 3),
(355, 7901, 0, 2),
(356, 7906, 0, 2),
(357, 7960, 0, 2),
(358, 8012, 0, 3),
(359, 7981, 0, 1),
(360, 7980, 0, 2),
(361, 8042, 0, 2),
(362, 8066, 0, 2),
(363, 8063, 0, 1),
(364, 8082, 0, 3),
(365, 8106, 0, 1),
(366, 8142, 0, 3),
(367, 8176, 0, 2),
(368, 8177, 0, 3),
(369, 8183, 0, 1),
(370, 8073, 0, 1),
(371, 8064, 0, 1),
(372, 8072, 0, 1),
(373, 7972, 0, 1),
(374, 8196, 0, 3),
(375, 8236, 0, 6),
(376, 8198, 0, 2),
(377, 8271, 0, 2),
(378, 8272, 0, 2),
(379, 8287, 0, 4),
(380, 8336, 0, 4),
(381, 8349, 0, 2),
(382, 8353, 0, 4),
(383, 8361, 0, 2),
(384, 8323, 0, 1),
(385, 8362, 0, 4),
(386, 8378, 0, 2),
(387, 8412, 0, 4),
(388, 8400, 0, 2),
(389, 8368, 0, 3),
(390, 8432, 0, 2),
(391, 8370, 0, 1),
(392, 8403, 0, 2),
(393, 8435, 0, 1),
(394, 8473, 0, 3),
(395, 8493, 0, 2),
(396, 8506, 0, 2),
(397, 8492, 0, 2),
(398, 8472, 0, 1),
(399, 8547, 0, 3),
(400, 8544, 0, 1),
(401, 8510, 0, 1),
(402, 8552, 0, 1),
(403, 8577, 0, 5),
(404, 8556, 0, 2),
(405, 8613, 0, 3),
(406, 8643, 0, 1),
(407, 8633, 0, 1),
(408, 8641, 0, 1),
(409, 8662, 0, 2),
(410, 8636, 0, 1),
(411, 8651, 0, 1),
(412, 8714, 0, 4),
(413, 8700, 0, 2),
(414, 8722, 0, 2),
(415, 8622, 0, 2),
(416, 8760, 0, 1),
(417, 8761, 0, 3),
(418, 8504, 0, 2),
(419, 8798, 0, 1),
(420, 8763, 0, 1),
(421, 8803, 0, 1),
(422, 8821, 0, 2),
(423, 8555, 0, 2),
(424, 8861, 0, 1),
(425, 8876, 0, 2),
(426, 8904, 0, 4),
(427, 8907, 0, 1),
(428, 8825, 0, 1),
(429, 8815, 0, 1),
(430, 8909, 0, 3),
(431, 8919, 0, 3),
(432, 8880, 0, 1),
(433, 8800, 0, 1),
(434, 8929, 0, 3),
(435, 8934, 0, 3),
(436, 8969, 0, 5),
(437, 8961, 0, 1),
(438, 8962, 0, 1),
(439, 8917, 0, 1),
(440, 8989, 0, 2),
(441, 9002, 0, 3),
(442, 9020, 0, 2),
(443, 9058, 0, 3),
(444, 9087, 0, 1),
(445, 9127, 0, 2),
(446, 9136, 0, 2),
(447, 9140, 0, 1),
(448, 9102, 0, 1),
(449, 9030, 0, 2),
(450, 9165, 0, 1),
(451, 9192, 0, 1),
(452, 9199, 0, 3),
(453, 9207, 0, 1);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `wp_upvote_counter`
--
ALTER TABLE `wp_upvote_counter`
  ADD PRIMARY KEY (`ID`),
  ADD UNIQUE KEY `post_id` (`post_id`,`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `wp_upvote_counter`
--
ALTER TABLE `wp_upvote_counter`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=454;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
