<?php
/**
 * TourismIQ Headless Theme functions and definitions
 *
 * @package TourismIQ_Headless
 */


// Define theme version
define('TOURISMIQ_HEADLESS_VERSION', '1.0.0');

// Define theme constants
define('TOURISMIQ_THEME_DIR', get_template_directory());
define('TOURISMIQ_THEME_URI', get_template_directory_uri());

// Stripe Configuration - Use environment variable
if (!defined('STRIPE_PAYMENT_LINK')) {
    $stripe_link = $_ENV['NEXT_PUBLIC_STRIPE_PAYMENT_LINK'] ?? $_SERVER['NEXT_PUBLIC_STRIPE_PAYMENT_LINK'] ?? '';
    if ($stripe_link) {
        define('STRIPE_PAYMENT_LINK', $stripe_link);
    } else {
        // Fallback if env var not set
        define('STRIPE_PAYMENT_LINK', 'https://buy.stripe.com/test_dRm5kFgeB9SU6z72gr2cg00');
    }
}

// Load all files from the inc directory
function touraqi_load_inc_files() {
    // Load security utilities first (critical for all other files)
    require_once TOURISMIQ_THEME_DIR . '/inc/security-utils.php';
    
    // Load CORS handler early for REST API
    require_once TOURISMIQ_THEME_DIR . '/inc/cors-handler.php';
    
    // Load performance optimizations
    require_once TOURISMIQ_THEME_DIR . '/inc/performance-cache.php';
    
    // Load cache invalidation handler
    require_once TOURISMIQ_THEME_DIR . '/inc/cache-invalidation.php';
    
    // Load REST user fields first (before other files that might depend on it)
    require_once TOURISMIQ_THEME_DIR . '/inc/rest-user-fields.php';
    
    // Load REST post fields to expose ACF fields
    require_once TOURISMIQ_THEME_DIR . '/inc/rest-post-fields.php';
    
    // Load featured media embedded fix
    // require_once TOURISMIQ_THEME_DIR . '/inc/fix-featured-media-embedded.php';
    
    // Load attachment permissions fix
    // require_once TOURISMIQ_THEME_DIR . '/inc/fix-attachment-permissions.php';
    
    // Load sponsored posts functionality
    require_once TOURISMIQ_THEME_DIR . '/inc/sponsored-posts.php';
    
    // Load Yoast SEO integration for headless setup
    require_once TOURISMIQ_THEME_DIR . '/inc/yoast-integration.php';

    // Load notifications before IQ score system (dependency order)
    require_once TOURISMIQ_THEME_DIR . '/inc/notifications.php';
    
    // Load email handler
    require_once TOURISMIQ_THEME_DIR . '/inc/email-handler.php';
    
    // Load password reset REST endpoint
    require_once TOURISMIQ_THEME_DIR . '/inc/rest-password-reset.php';
    
    // Load custom login page styling
    require_once TOURISMIQ_THEME_DIR . '/inc/custom-login-page.php';
    
    // Load REST endpoints for support and feedback
    require_once TOURISMIQ_THEME_DIR . '/inc/rest-support-feedback.php';
    
    // Include REST API endpoints for posts, members, and other custom functionality
    require_once TOURISMIQ_THEME_DIR . '/inc/rest-api.php';
    
    // Include legacy messages API
    require_once TOURISMIQ_THEME_DIR . '/inc/api/legacy-messages.php';
    
    // Include legacy admin page
    require_once TOURISMIQ_THEME_DIR . '/inc/legacy-admin-page.php';
    
    // Load ACF field groups
    $acf_files = [
        'acf/acf-blog-post.php',
        'acf/acf-book.php',
        'acf/acf-case-study.php',
        'acf/acf-course.php',
        'acf/acf-event.php',
        'acf/acf-job.php',
        'acf/acf-leadership.php',
        'acf/acf-news.php',
        'acf/acf-people.php',
        'acf/acf-podcast.php',
        'acf/acf-post-vendor-relationship.php',
        'acf/acf-presentation.php',
        'acf/acf-press-release.php',
        'acf/acf-rfp.php',
        'acf/acf-template.php',
        'acf/acf-user-profile.php',
        'acf/acf-vendor.php',
        'acf/acf-video.php',
        'acf/acf-webinar.php',
        'acf/acf-whitepaper.php',
        'acf/acf-sponsorship.php'
    ];
    
    foreach ($acf_files as $acf_file) {
        $file_path = TOURISMIQ_THEME_DIR . '/inc/' . $acf_file;
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }
    
    // Load other inc files in specific order for dependencies
    $priority_files = [
        'stripe-integration.php',
        'vendor-post-type.php',
        'people-post-type.php',
        'job-post-type.php',
        'rfp-post-type.php',
        'forum-post-type.php',
        'connections-api.php',
        'iq-score-system.php',
    ];
    
    foreach ($priority_files as $priority_file) {
        $file_path = TOURISMIQ_THEME_DIR . '/inc/' . $priority_file;
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }
    
    // Load admin-only functionality
    if (is_admin()) {
        require_once TOURISMIQ_THEME_DIR . '/inc/admin-email-tester.php';
        // require_once TOURISMIQ_THEME_DIR . '/inc/admin-migration-tools.php'; // Disabled after successful migration
    }
    
    // Load remaining inc files
    $files = scandir(TOURISMIQ_THEME_DIR . '/inc');
    $loaded_files = array_merge(['security-utils.php', 'cors-handler.php', 'performance-cache.php', 'rest-user-fields.php', 'notifications.php', 'admin-email-tester.php', 'admin-migration-tools.php'], $acf_files, $priority_files);
    
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && !is_dir(TOURISMIQ_THEME_DIR . '/inc/' . $file) && 
            $file !== 'acf' && !in_array($file, $loaded_files)) {
            require_once TOURISMIQ_THEME_DIR . '/inc/' . $file;
        }
    }
}
add_action('after_setup_theme', 'touraqi_load_inc_files', 1);

// Increase memory limit to fix exhaustion errors
if (function_exists('wp_raise_memory_limit')) {
    wp_raise_memory_limit('admin');
} else {
    ini_set('memory_limit', '256M');
}

// Theme setup
if (!function_exists('tourismiq_headless_setup')) {
    /**
     * Sets up theme defaults and registers support for various WordPress features.
     */
    function tourismiq_headless_setup() {
        // Add default posts and comments RSS feed links to head.
        add_theme_support('automatic-feed-links');

        // Let WordPress manage the document title.
        add_theme_support('title-tag');

        // Enable support for Post Thumbnails on posts and pages.
        add_theme_support('post-thumbnails');

        // Add support for responsive embeds
        add_theme_support('responsive-embeds');

        // Add support for editor styles
        add_theme_support('editor-styles');

        // Add support for wide alignment
        add_theme_support('align-wide');

        // Add editor color palette support
        add_theme_support('editor-color-palette');

        // Add support for custom line height
        add_theme_support('custom-line-height');

        // Add support for experimental features
        add_theme_support('custom-spacing');

        // Register navigation menus.
        register_nav_menus(
            array(
                'primary' => esc_html__('Primary Menu', 'tourismiq-headless'),
            )
        );

        // Add theme support for selective refresh for widgets.
        add_theme_support('customize-selective-refresh-widgets');

        // Add support for core custom logo.
        add_theme_support(
            'custom-logo',
            array(
                'height'      => 250,
                'width'       => 250,
                'flex-width'  => true,
                'flex-height' => true,
            )
        );

        // Enable Gutenberg block editor features
        add_theme_support('wp-block-styles');
        add_theme_support('editor-gradient-presets');
        
        // Ensure post formats support (required for some editor features)
        add_theme_support('post-formats', array(
            'aside', 'image', 'video', 'quote', 'link', 'gallery', 'status', 'audio', 'chat'
        ));
    }
}
add_action('after_setup_theme', 'tourismiq_headless_setup');



// Enqueue scripts and styles
function tourismiq_headless_scripts() {
    // Enqueue styles
    wp_enqueue_style('tourismiq-headless-style', get_stylesheet_uri(), array(), TOURISMIQ_HEADLESS_VERSION);
    wp_style_add_data('tourismiq-headless-style', 'rtl', 'replace');

    // Enqueue WordPress REST API script
    wp_enqueue_script('wp-api');

    // Localize the script with the REST API URL and nonce
    wp_localize_script('wp-api', 'wpApiSettings', [
        'root' => esc_url_raw(rest_url()),
        'nonce' => wp_create_nonce('wp_rest'),
        'user' => [
            'id' => get_current_user_id(),
            'isLoggedIn' => is_user_logged_in(),
        ],
    ]);

    // Make sure jQuery is loaded (required by wp-api)
    wp_enqueue_script('jquery');

    // Enqueue our custom script that depends on wp-api and jQuery
    wp_enqueue_script(
        'tourismiq-auth',
        get_template_directory_uri() . '/frontend/src/lib/auth.js',
        array('jquery', 'wp-api'),
        TOURISMIQ_HEADLESS_VERSION,
        true
    );
}
add_action('wp_enqueue_scripts', 'tourismiq_headless_scripts');

// Enqueue block editor scripts and styles
function tourismiq_headless_admin_scripts($hook) {
    // Only enqueue on post edit screens
    if (!in_array($hook, ['post.php', 'post-new.php'])) {
        return;
    }

    // Enqueue block editor assets
    wp_enqueue_script('wp-blocks');
    wp_enqueue_script('wp-editor');
    wp_enqueue_script('wp-edit-post');
    wp_enqueue_script('wp-element');
    wp_enqueue_script('wp-components');
    wp_enqueue_script('wp-data');
    wp_enqueue_script('wp-core-data');


    // Ensure REST API settings are available in admin
    wp_localize_script('wp-blocks', 'wpApiSettings', [
        'root' => esc_url_raw(rest_url()),
        'nonce' => wp_create_nonce('wp_rest'),
        'user' => [
            'id' => get_current_user_id(),
            'isLoggedIn' => is_user_logged_in(),
        ],
    ]);
}
add_action('admin_enqueue_scripts', 'tourismiq_headless_admin_scripts');








// Ensure proper REST API authentication for logged-in users
add_filter('rest_authentication_errors', function($result) {
    // Always allow if user is logged in
    if (is_user_logged_in()) {
        return true;
    }
    
    // Always allow if using Basic Auth with application password
    if (isset($_SERVER['HTTP_AUTHORIZATION']) && 
        strpos($_SERVER['HTTP_AUTHORIZATION'], 'Basic ') === 0) {
        return true;
    }
    
    // Allow public access to standard WordPress endpoints (posts, categories, etc.)
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    
    // Parse the URI to get the path without query parameters
    $parsed_uri = parse_url($request_uri);
    $path = $parsed_uri['path'] ?? '';
    
    // Allow ALL WordPress v2 API endpoints to be public (posts, categories, media, etc.)
    if (strpos($path, '/wp-json/wp/v2/') !== false) {
        return true;
    }
    
    // Allow specific custom endpoints to be public
    $public_endpoints = [
        '/wp-json/tourismiq/v1/members',
        '/wp-json/tourismiq/v1/auth/status'
    ];
    
    foreach ($public_endpoints as $endpoint) {
        if (strpos($path, $endpoint) !== false) {
            return true; // Allow public access
        }
    }
    
    return $result;
});

// Create custom taxonomy endpoint that bypasses permission issues
add_action('rest_api_init', function() {
    // Custom taxonomies endpoint that definitely works
    register_rest_route('tourismiq/v1', '/taxonomies-for-editor', array(
        'methods' => 'GET',
        'callback' => function($request) {
            // Get taxonomies for posts with all the data the editor needs
            $post_taxonomies = get_object_taxonomies('post', 'objects');
            $data = array();
            
            foreach ($post_taxonomies as $tax_name => $taxonomy) {
                if ($taxonomy->show_in_rest) {
                    $data[] = array(
                        'slug' => $tax_name,
                        'name' => $taxonomy->labels->name,
                        'description' => $taxonomy->description,
                        'types' => array('post'),
                        'hierarchical' => $taxonomy->hierarchical,
                        'rest_base' => $taxonomy->rest_base,
                        'rest_namespace' => 'wp/v2',
                        'capabilities' => array(
                            'manage_terms' => 'manage_categories',
                            'edit_terms' => 'manage_categories', 
                            'delete_terms' => 'manage_categories',
                            'assign_terms' => 'edit_posts'
                        ),
                        'labels' => $taxonomy->labels,
                        'visibility' => array(
                            'public' => $taxonomy->public,
                            'show_ui' => $taxonomy->show_ui,
                            'show_admin_column' => $taxonomy->show_admin_column,
                        )
                    );
                }
            }
            
            return rest_ensure_response($data);
        },
        'permission_callback' => '__return_true' // Allow everyone
    ));
    
    register_rest_route('wp/v2', '/taxonomies-bypass', array(
        'methods' => 'GET',
        'callback' => function($request) {
            $taxonomies = get_taxonomies(array('public' => true), 'objects');
            $data = array();
            
            foreach ($taxonomies as $tax_name => $taxonomy) {
                $data[$tax_name] = array(
                    'name' => $tax_name,
                    'slug' => $tax_name,
                    'labels' => $taxonomy->labels,
                    'description' => $taxonomy->description,
                    'types' => $taxonomy->object_type,
                    'hierarchical' => $taxonomy->hierarchical,
                    'rest_base' => $taxonomy->rest_base,
                    'visibility' => array(
                        'public' => $taxonomy->public,
                        'show_ui' => $taxonomy->show_ui,
                    )
                );
            }
            
            return rest_ensure_response($data);
        },
        'permission_callback' => function() {
            return is_user_logged_in(); // Simple login check
        }
    ));
    
    // Override the taxonomies controller permission callback completely
    add_filter('rest_prepare_taxonomy', function($response, $taxonomy, $request) {
        // Always allow if user is logged in
        return $response;
    }, 10, 3);
    
    // Replace the taxonomies endpoint entirely to bypass permission issues
    register_rest_route('wp/v2', '/taxonomies', array(
        'methods' => 'GET',
        'callback' => function($request) {
            $context = $request->get_param('context') ?: 'view';
            
            // Get taxonomies that are available for posts
            $post_taxonomies = get_object_taxonomies('post', 'objects');
            $data = array();
            
            foreach ($post_taxonomies as $tax_name => $taxonomy) {
                if ($taxonomy->show_in_rest) {
                    $tax_data = array(
                        'name' => $taxonomy->labels->name,
                        'slug' => $tax_name,
                        'description' => $taxonomy->description,
                        'types' => array('post'),
                        'hierarchical' => $taxonomy->hierarchical,
                        'rest_base' => $taxonomy->rest_base,
                        'rest_namespace' => 'wp/v2'
                    );
                    
                    // Add edit context specific data
                    if ($context === 'edit') {
                        $tax_data['capabilities'] = array(
                            'manage_terms' => 'manage_categories',
                            'edit_terms' => 'manage_categories',
                            'delete_terms' => 'manage_categories',
                            'assign_terms' => 'edit_posts'
                        );
                        $tax_data['labels'] = $taxonomy->labels;
                        $tax_data['show_cloud'] = $taxonomy->show_tagcloud;
                        $tax_data['show_in_quick_edit'] = $taxonomy->show_in_quick_edit;
                        $tax_data['show_admin_column'] = $taxonomy->show_admin_column;
                    }
                    
                    $data[$tax_name] = $tax_data;
                }
            }
            
            return rest_ensure_response($data);
        },
        'permission_callback' => function() {
            return is_user_logged_in(); // Simple login check
        },
        'args' => array(
            'context' => array(
                'default' => 'view',
                'enum' => array('view', 'edit'),
            ),
        )
    ), true); // Override existing route
});

// Make sure ACF fields are available in REST API
if (function_exists('acf_add_options_page')) {
    // Enable ACF REST API - member endpoints are now handled in inc/rest-api.php
    function tourismiq_acf_rest_api_init() {
        // Member endpoints moved to inc/rest-api.php to avoid duplication
    }

    // Single member endpoint callback
    function tourismiq_get_single_member_endpoint($request) {
        $user_id = $request->get_param('id');
        $user = get_user_by('id', $user_id);

        if (!$user) {
            return new WP_Error('user_not_found', 'User not found', array('status' => 404));
        }

        // Reuse the members endpoint logic but for a single user
        $users = array($user);
        $members = array_map('tourismiq_prepare_member_data', $users);

        if (empty($members)) {
            return new WP_Error('user_not_found', 'User not found', array('status' => 404));
        }

        return new WP_REST_Response($members[0], 200);
    }

    // Helper function to prepare member data
    function tourismiq_prepare_member_data($user) {
        $acf_fields = get_fields('user_' . $user->ID) ?: [];
        $avatar_url = null; // Let frontend handle the fallback to placeholder

        // Get ACF profile picture if available
        $profile_picture = $acf_fields['profile_picture'] ?? '';

        // Clean up the profile picture URL if it's an array
        if (is_array($profile_picture) && isset($profile_picture['url'])) {
            $profile_picture = $profile_picture['url'];
        }

        // Prepare the response to match frontend interface
        $response = array(
            'id' => $user->ID,
            'username' => $user->user_login,
            'name' => $user->display_name,
            'email' => $user->user_email,
            'firstName' => $user->first_name,
            'lastName' => $user->last_name,
            'bio' => $acf_fields['bio'] ?? null,
            'avatarUrl' => $profile_picture ?: $avatar_url,
            'location' => $acf_fields['location'] ?? null,
            'website' => $user->user_url,
            'jobTitle' => $acf_fields['job_title'] ?? null,
            'categories' => [], // Will be populated if you have member categories
            'featured' => false,
            'company' => $acf_fields['company'] ?? null,
            'contactInfo' => array(
                'email' => $user->user_email,
                'phone' => $acf_fields['phone'] ?? null,
                'website' => $user->user_url,
            ),
            'socialLinks' => array(
                'website' => $user->user_url,
                'twitter' => $acf_fields['twitter'] ?? null,
                'facebook' => $acf_fields['facebook'] ?? null,
                'linkedin' => $acf_fields['linkedin'] ?? null,
                'instagram' => $acf_fields['instagram'] ?? null,
            ),
            'user_registered' => $user->user_registered,
        );

        // Add any additional ACF fields that aren't already included
        foreach ($acf_fields as $key => $value) {
            if (!isset($response[$key]) && !in_array($key, ['contactInfo', 'socialLinks'])) {
                $response[$key] = $value;
            }
        }

        return $response;
    }

    // Custom members endpoint callback
    function tourismiq_get_members_endpoint($request) {
        $users = get_users(array(
            'number' => -1, // Get all users including administrators
            'orderby' => 'registered',
            'order' => 'DESC', // Newest users first
        ));

        $members = array_map('tourismiq_prepare_member_data', $users);
        return new WP_REST_Response($members, 200);
    }

    // Member by username endpoint callback
    function tourismiq_get_member_by_username_endpoint($request) {
        $username = $request->get_param('username');

        // First try to find by user_login (username)
        $user = get_user_by('login', $username);

        // If not found, try by user_nicename (slug)
        if (!$user) {
            $user = get_user_by('slug', $username);
        }

        // If still not found, try by email
        if (!$user && is_email($username)) {
            $user = get_user_by('email', $username);
        }

        if (!$user) {
            return new WP_Error('user_not_found', 'User not found', array('status' => 404));
        }

        // Reuse the members endpoint logic but for a single user
        $users = array($user);
        $members = array_map('tourismiq_prepare_member_data', $users);

        if (empty($members)) {
            return new WP_Error('user_not_found', 'User not found', array('status' => 404));
        }

        return new WP_REST_Response($members[0], 200);
    }

    add_action('rest_api_init', 'tourismiq_acf_rest_api_init');
}

// Include notifications system
require_once get_template_directory() . '/inc/notifications.php';

// Include post interactions system
require_once get_template_directory() . '/inc/post-interactions.php';

/**
 * Register a custom REST API endpoint for comments and debug endpoints
 */
function tourismiq_register_comments_endpoints() {
    // Debug endpoint to check if user is logged in
    register_rest_route('tourismiq/v1', '/auth-check', array(
        'methods'  => 'GET',
        'callback' => function() {
            return array(
                'logged_in' => is_user_logged_in(),
                'user_id' => get_current_user_id(),
                'user_login' => wp_get_current_user()->user_login,
                'cookies_present' => !empty($_COOKIE),
                'cookies' => array_keys($_COOKIE),
                'wp_cookies' => preg_grep('/^wordpress_|^wp-|^comment_/', array_keys($_COOKIE))
            );
        },
        'permission_callback' => '__return_true',
    ));

    // Comments endpoint without using permission_callback
    register_rest_route('tourismiq/v1', '/comments/create', array(
        'methods'  => 'POST',
        'callback' => 'tourismiq_handle_create_comment',
        'permission_callback' => '__return_true', // Allow anyone to access this endpoint
    ));

    // Custom endpoint to get comments with author_info
    register_rest_route('tourismiq/v1', '/posts/(?P<post_id>\d+)/comments', array(
        'methods'  => 'GET',
        'callback' => 'tourismiq_get_post_comments_with_author_info',
        'permission_callback' => '__return_true',
        'args' => array(
            'post_id' => array(
                'validate_callback' => function($param, $request, $key) {
                    return is_numeric($param);
                }
            ),
        ),
    ));
}
add_action('rest_api_init', 'tourismiq_register_comments_endpoints');

/**
 * Handle creating a comment
 */
function tourismiq_handle_create_comment($request) {
    $params = $request->get_params();

    if (!isset($params['post_id']) || !isset($params['content'])) {
        return new WP_Error('missing_params', 'Missing required parameters.', array('status' => 400));
    }

    $post_id = intval($params['post_id']);
    $content = sanitize_textarea_field($params['content']);

    // Get current user
    $user = wp_get_current_user();

    // Check if the user is actually logged in
    if (!$user || $user->ID === 0) {
        return new WP_Error('not_logged_in', 'You must be logged in to comment.', array('status' => 401));
    }

    // Comment data
    $comment_data = array(
        'comment_post_ID'      => $post_id,
        'comment_content'      => $content,
        'comment_author'       => $user->display_name,
        'comment_author_email' => $user->user_email,
        'comment_author_url'   => $user->user_url,
        'user_id'              => $user->ID,
        'comment_approved'     => 1, // Auto approve for registered users
        'comment_type'         => 'comment',
    );

    // Insert the comment
    $comment_id = wp_insert_comment($comment_data);

    if (!$comment_id) {
        return new WP_Error('comment_failed', 'Failed to create comment.', array('status' => 500));
    }

    
    // CRITICAL: Clear all WordPress caches after comment creation
    wp_cache_flush();
    
    // Clear object cache groups related to comments
    if (function_exists('wp_cache_delete_group')) {
        wp_cache_delete_group('comment');
        wp_cache_delete_group('counts');
    }
    
    // Clear transients that might cache comment data
    delete_transient('comment_count_' . $post_id);
    
    // Clear any WP Engine or hosting-level caches for this post
    if (function_exists('wp_cache_flush_group')) {
        wp_cache_flush_group('comments');
    }

    // Check if this is a comment on a forum question and award points
    $post = get_post($post_id);
    if ($post && $post->post_type === 'forum_question') {
        // Award IQ points for forum response (1 point)
        if (function_exists('award_iq_points')) {
            award_iq_points($user->ID, 1, 'forum_response', $comment_id);
        }
    }

    // Get the created comment
    $comment = get_comment($comment_id);
    $current_comment_author = wp_get_current_user();

    // Get ACF profile picture for the current user
    $acf_fields = get_fields('user_' . $current_comment_author->ID) ?: [];
    $profile_picture_url = '';
    
    if (isset($acf_fields['profile_picture'])) {
        if (is_array($acf_fields['profile_picture']) && isset($acf_fields['profile_picture']['url'])) {
            $profile_picture_url = $acf_fields['profile_picture']['url'];
        } else if (is_string($acf_fields['profile_picture']) && !empty($acf_fields['profile_picture'])) {
            $profile_picture_url = $acf_fields['profile_picture'];
        }
    }
    
    // Let frontend handle the fallback to placeholder if no ACF profile picture
    if (empty($profile_picture_url)) {
        $profile_picture_url = null; // Let frontend handle the fallback to placeholder
    }

    $author_data_for_embed = null;
    if ($current_comment_author->ID) {
        $author_data_for_embed = array(
            array( // WP embeds author as an array
                'id' => $current_comment_author->ID,
                'name' => $current_comment_author->display_name,
                'url' => get_author_posts_url($current_comment_author->ID),
                'description' => get_the_author_meta('description', $current_comment_author->ID),
                'link' => get_author_posts_url($current_comment_author->ID),
                'slug' => $current_comment_author->user_nicename,
                'avatar_urls' => array(
                    '24' => $profile_picture_url,
                    '48' => $profile_picture_url,
                    '96' => $profile_picture_url,
                ),
                'acf' => $acf_fields, // Include ACF fields for the author
                'profile_picture' => $profile_picture_url, // Direct access to profile picture
            )
        );
    }

    // Create author_info in the same format as forum comments
    $author_info = array(
        'id' => $current_comment_author->ID,
        'name' => $current_comment_author->display_name,
        'username' => $current_comment_author->user_login,
        'profile_picture' => $profile_picture_url,
        'avatar_urls' => array(
            '24' => $profile_picture_url,
            '48' => $profile_picture_url,
            '96' => $profile_picture_url,
        ),
        'url' => get_author_posts_url($current_comment_author->ID),
    );

    $response = array(
        'id'           => $comment->comment_ID,
        'author'       => $current_comment_author->ID ? $current_comment_author->ID : null, // REST API often uses 'author' for author ID
        'author_name'  => $current_comment_author->ID ? $current_comment_author->display_name : $comment->comment_author,
        'author_id'    => $current_comment_author->ID ? $current_comment_author->ID : null, // Explicit author_id for clarity
        'author_info'  => $author_info, // Add author_info for consistency with forum comments
        'content'      => array('rendered' => apply_filters('comment_text', $comment->comment_content)),
        'date'         => $comment->comment_date,
        'parent'       => $comment->comment_parent,
        'status'       => 'approved', // Assuming auto-approval
        'type'         => 'comment',
        'author_avatar_urls' => $author_data_for_embed ? $author_data_for_embed[0]['avatar_urls'] : null, // For direct access if needed
        '_embedded'    => $author_data_for_embed ? array('author' => $author_data_for_embed) : null,
    );

    return rest_ensure_response(array(
        'success' => true,
        'comment' => $response
    ));
}

/**
 * Get comments for a post with author_info included
 */
function tourismiq_get_post_comments_with_author_info($request) {
    $post_id = $request->get_param('post_id');
    
    if (!$post_id) {
        return new WP_Error('missing_post_id', 'Post ID is required', array('status' => 400));
    }

    // Check if post exists
    $post = get_post($post_id);
    if (!$post) {
        return new WP_Error('post_not_found', 'Post not found', array('status' => 404));
    }

    // BYPASS ALL WORDPRESS CACHING - Direct database query
    global $wpdb;
    $comments = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$wpdb->comments} 
         WHERE comment_post_ID = %d 
         AND comment_approved = '1' 
         AND comment_type = 'comment'
         ORDER BY comment_date ASC",
        $post_id
    ));

    $formatted_comments = array();
    
    foreach ($comments as $comment) {
        // Get the comment author directly from user ID
        $current_comment_author = null;
        if ($comment->user_id > 0) {
            $current_comment_author = get_user_by('ID', $comment->user_id);
        }
        
        // Get ACF fields for the author
        $acf_fields = array();
        $profile_picture_url = '';
        
        if ($current_comment_author && $current_comment_author->ID) {
            $acf_fields = get_fields('user_' . $current_comment_author->ID) ?: array();
            
            // Get profile picture URL
            if (isset($acf_fields['profile_picture'])) {
                if (is_array($acf_fields['profile_picture']) && isset($acf_fields['profile_picture']['url'])) {
                    $profile_picture_url = $acf_fields['profile_picture']['url'];
                } else if (is_string($acf_fields['profile_picture']) && !empty($acf_fields['profile_picture'])) {
                    $profile_picture_url = $acf_fields['profile_picture'];
                }
            }
            
            // Let frontend handle the fallback to placeholder if no ACF profile picture
            if (empty($profile_picture_url)) {
                $profile_picture_url = null;
            }
        } else {
            // Guest comment - let frontend handle the fallback to placeholder
            $profile_picture_url = null;
        }

        // Create author_data_for_embed in the same format as WordPress REST API
        $author_data_for_embed = null;
        if ($current_comment_author && $current_comment_author->ID) {
            $author_data_for_embed = array(
                array( // WP embeds author as an array
                    'id' => $current_comment_author->ID,
                    'name' => $current_comment_author->display_name,
                    'url' => get_author_posts_url($current_comment_author->ID),
                    'description' => get_the_author_meta('description', $current_comment_author->ID),
                    'link' => get_author_posts_url($current_comment_author->ID),
                    'slug' => $current_comment_author->user_nicename,
                    'avatar_urls' => array(
                        '24' => $profile_picture_url,
                        '48' => $profile_picture_url,
                        '96' => $profile_picture_url,
                    ),
                    'acf' => $acf_fields,
                    'profile_picture' => $profile_picture_url,
                )
            );
        }

        // Create author_info in the same format as forum comments
        $author_info = array(
            'id' => $current_comment_author ? $current_comment_author->ID : 0,
            'name' => $current_comment_author ? $current_comment_author->display_name : $comment->comment_author,
            'username' => $current_comment_author ? $current_comment_author->user_login : '',
            'profile_picture' => $profile_picture_url,
            'avatar_urls' => array(
                '24' => $profile_picture_url,
                '48' => $profile_picture_url,
                '96' => $profile_picture_url,
            ),
            'url' => $current_comment_author ? get_author_posts_url($current_comment_author->ID) : '',
        );

        $formatted_comment = array(
            'id' => (int) $comment->comment_ID,
            'author' => $current_comment_author ? $current_comment_author->ID : null,
            'author_name' => $current_comment_author ? $current_comment_author->display_name : $comment->comment_author,
            'author_id' => $current_comment_author ? $current_comment_author->ID : null,
            'author_info' => $author_info,
            'content' => array('rendered' => apply_filters('comment_text', $comment->comment_content)),
            'date' => $comment->comment_date,
            'parent' => (int) $comment->comment_parent,
            'status' => 'approved',
            'type' => 'comment',
            'author_avatar_urls' => $author_data_for_embed ? $author_data_for_embed[0]['avatar_urls'] : array(
                '24' => $profile_picture_url,
                '48' => $profile_picture_url,
                '96' => $profile_picture_url,
            ),
            '_embedded' => $author_data_for_embed ? array('author' => $author_data_for_embed) : null,
        );
        
        $formatted_comments[] = $formatted_comment;
    }

    // Get direct database count for debugging
    $db_count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->comments} 
         WHERE comment_post_ID = %d 
         AND comment_approved = '1' 
         AND comment_type = 'comment'",
        $post_id
    ));

    return rest_ensure_response(array(
        'success' => true,
        'comments' => $formatted_comments,
        'debug' => array(
            'post_id' => $post_id,
            'direct_db_count' => (int) $db_count,
            'returned_comments' => count($formatted_comments),
            'cache_bypassed' => true,
            'timestamp' => current_time('mysql')
        )
    ));
}

// Function to check comments directly in database (bypass cache)
function tourismiq_direct_database_comment_check($post_id) {
    global $wpdb;
    
    // Direct database query to bypass all caching
    $total_comments = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_post_ID = %d AND comment_type = 'comment'",
        $post_id
    ));
    
    $approved_comments = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_post_ID = %d AND comment_approved = '1' AND comment_type = 'comment'",
        $post_id
    ));
    
    // Get sample of actual comments from database
    $sample_comments = $wpdb->get_results($wpdb->prepare(
        "SELECT comment_ID, comment_approved, comment_author, comment_content FROM {$wpdb->comments} 
         WHERE comment_post_ID = %d AND comment_type = 'comment' 
         ORDER BY comment_date DESC LIMIT 5",
        $post_id
    ));
    
    return array(
        'db_total' => (int) $total_comments,
        'db_approved' => (int) $approved_comments,
        'sample_comments' => array_map(function($c) {
            return array(
                'id' => $c->comment_ID,
                'approved' => $c->comment_approved,
                'author' => $c->comment_author,
                'content_preview' => substr($c->comment_content, 0, 50) . '...'
            );
        }, $sample_comments)
    );
}

// Function to get the comment count for a post
function get_comment_count_for_post( $object, $field_name, $request ) {
    // $object is the post object. The ID is in $object['id'] for default post types.
    // If you're using this for CPTs, ensure the ID key is correct.
    $post_id = isset($object['id']) ? $object['id'] : 0;
    if ( $post_id > 0 ) {
        $comments_count = wp_count_comments( $post_id );
        return $comments_count->approved;
    }
    return 0;
}

// Function to add the comment count to the REST API 'post' type
function add_comment_count_to_rest_api() {
    register_rest_field(
        'post', // Target post type. Use an array for multiple, e.g., ['post', 'page']
        'total_comments', // Name of the new field in the API response
        array(
            'get_callback'    => 'get_comment_count_for_post',
            'update_callback' => null, // No update callback needed for a read-only field
            'schema'          => array(
                'description' => __( 'Total number of approved comments for the post.' ),
                'type'        => 'integer',
                'context'     => array( 'view', 'edit', 'embed' ), // Contexts where field should be available
            ),
        )
    );
}
add_action( 'rest_api_init', 'add_comment_count_to_rest_api' );

// Custom endpoint for cookie-based login
function tourismiq_register_cookie_login_endpoint() {
    register_rest_route('tourismiq/v1', '/auth/login', array(
        'methods'  => 'POST',
        'callback' => 'tourismiq_handle_cookie_login',
        'permission_callback' => '__return_true', // Publicly accessible
    ));
}
add_action('rest_api_init', 'tourismiq_register_cookie_login_endpoint');

function tourismiq_handle_cookie_login($request) {
    $creds = array();
    $params = $request->get_params();

    $creds['user_login']    = sanitize_user($params['username'] ?? '');
    $creds['user_password'] = $params['password'] ?? ''; // Password should not be sanitized beyond what wp_signon expects
    $creds['remember']      = true; // Or based on a param if you want a "remember me" option

    if (empty($creds['user_login']) || empty($creds['user_password'])) {
        return new WP_Error(
            'missing_credentials',
            __('Missing username or password.'),
            array('status' => 400)
        );
    }

    $user = wp_signon($creds, is_ssl()); // is_ssl() helps set secure cookies if applicable

    if (is_wp_error($user)) {
        return new WP_Error(
            'login_failed',
            __('Invalid username or password.'), // Generic error message
            array('status' => 403) // Forbidden or Unauthorized
        );
    }

    // We just need to return a success response.
    wp_set_current_user($user->ID); // Ensure the current user is set for this request context

    // Prepare user data to return (similar to your JWT endpoint)
    $user_data_response = array(
        'id' => $user->ID,
        'username' => $user->user_login,
        'email' => $user->user_email,
        'display_name' => $user->display_name,
        'nicename' => $user->user_nicename,
        // You can add more user fields or ACF fields if needed here
    );

    return new WP_REST_Response(array(
        'success' => true,
        'message' => __('Login successful.'),
        'user'    => $user_data_response
    ), 200);
}

// Custom endpoint for cookie-based logout
function tourismiq_register_cookie_logout_endpoint() {
    register_rest_route('tourismiq/v1', '/auth/logout', array(
        'methods'  => 'POST',
        'callback' => 'tourismiq_handle_cookie_logout',
        'permission_callback' => '__return_true', // Publicly accessible, but wp_logout requires a valid nonce if not called from admin
    ));
}
add_action('rest_api_init', 'tourismiq_register_cookie_logout_endpoint');

// Custom endpoint for registration
function tourismiq_register_registration_endpoint() {
    register_rest_route('tourismiq/v1', '/auth/register', array(
        'methods'  => 'POST',
        'callback' => 'tourismiq_handle_registration',
        'permission_callback' => '__return_true', // Publicly accessible
    ));
}
add_action('rest_api_init', 'tourismiq_register_registration_endpoint');

// Custom endpoint for generating auth tokens
function tourismiq_register_auth_token_endpoint() {
    register_rest_route('tourismiq/v1', '/auth/generate-token', array(
        'methods'  => 'POST',
        'callback' => 'tourismiq_generate_auth_token',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
    ));
    
    register_rest_route('tourismiq/v1', '/auth/validate-token', array(
        'methods'  => 'POST',
        'callback' => 'tourismiq_validate_auth_token',
        'permission_callback' => '__return_true',
    ));
    
    register_rest_route('tourismiq/v1', '/auth/forgot-password', array(
        'methods'  => 'POST',
        'callback' => 'tourismiq_handle_forgot_password',
        'permission_callback' => '__return_true',
    ));
    
    register_rest_route('tourismiq/v1', '/auth/reset-password', array(
        'methods'  => 'POST',
        'callback' => 'tourismiq_handle_reset_password',
        'permission_callback' => '__return_true',
    ));
}
add_action('rest_api_init', 'tourismiq_register_auth_token_endpoint');

function tourismiq_handle_cookie_logout(WP_REST_Request $request) {
    // It's good practice to check a nonce for logout, especially if it can be triggered by GET.
    // However, for a POST request initiated by our own frontend, direct wp_logout() is often acceptable.
    // If you had a nonce sent from the client, you would check it here:
    // check_ajax_referer('wp_rest', '_wpnonce'); // Example if using _wpnonce from wpApiSettings

    wp_logout(); // This function handles clearing the auth cookies by sending expired cookie headers.

    // wp_logout() doesn't return a value we need to check, it just performs actions (clears cookies, hooks).
    // We just need to confirm the action was called.
    return new WP_REST_Response(array(
        'success' => true,
        'message' => __('Logged out successfully.')
    ), 200);
}

function tourismiq_handle_registration($request) {
    $params = $request->get_params();
    
    // Validate required fields
    $required_fields = ['firstName', 'lastName', 'username', 'email', 'password'];
    foreach ($required_fields as $field) {
        if (empty($params[$field])) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => "Missing required field: {$field}",
            ), 400);
        }
    }

    // Sanitize input
    $username = sanitize_user($params['username']);
    $email = sanitize_email($params['email']);
    $first_name = sanitize_text_field($params['firstName']);
    $last_name = sanitize_text_field($params['lastName']);
    $password = $params['password']; // Don't sanitize password
    $newsletter_optin = isset($params['newsletterOptIn']) ? (bool) $params['newsletterOptIn'] : false;

    // Validate email format
    if (!is_email($email)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Please enter a valid email address.',
        ), 400);
    }

    // Check if username already exists
    if (username_exists($username)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Sorry, that username already exists!',
        ), 400);
    }

    // Check if email already exists
    if (email_exists($email)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Sorry, that email address is already used!',
        ), 400);
    }

    // Validate password strength (basic check)
    if (strlen($password) < 6) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Password must be at least 6 characters long.',
        ), 400);
    }

    // Create the user
    $user_data = array(
        'user_login' => $username,
        'user_email' => $email,
        'user_pass'  => $password,
        'first_name' => $first_name,
        'last_name'  => $last_name,
        'display_name' => $first_name . ' ' . $last_name,
        'role' => 'member' // Use custom member role
    );

    $user_id = wp_insert_user($user_data);

    if (is_wp_error($user_id)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => $user_id->get_error_message(),
        ), 400);
    }

    // Set initial IQ score
    update_user_meta($user_id, 'iq_score', 0);
    
    // Set newsletter opt-in preference using ACF field
    if (function_exists('update_field')) {
        update_field('field_newsletter_optin', $newsletter_optin, 'user_' . $user_id);
    } else {
        // Fallback to user meta if ACF is not available
        update_user_meta($user_id, 'newsletter_optin', $newsletter_optin);
    }
    
    // Subscribe to Mailchimp if user opted in
    if ($newsletter_optin && function_exists('tourismiq_subscribe_to_mailchimp')) {
        error_log('Attempting Mailchimp subscription for user ' . $user_id . ' with email: ' . $email);
        $mailchimp_result = tourismiq_subscribe_to_mailchimp($email, $first_name, $last_name, array('Newsletter Subs', 'Current Users'));
        if (is_wp_error($mailchimp_result)) {
            // Log error but don't fail registration
            error_log('Mailchimp subscription failed for user ' . $user_id . ': ' . $mailchimp_result->get_error_message());
        } else {
            error_log('Mailchimp subscription successful for user ' . $user_id);
        }
    } else {
        error_log('Mailchimp subscription skipped. Opt-in: ' . ($newsletter_optin ? 'true' : 'false') . ', Function exists: ' . (function_exists('tourismiq_subscribe_to_mailchimp') ? 'true' : 'false'));
    }

    // Send welcome email (optional)
    wp_new_user_notification($user_id, null, 'user');

    return new WP_REST_Response(array(
        'success' => true,
        'message' => 'Registration successful! You can now log in.',
        'user_id' => $user_id
    ), 201);
}

function tourismiq_generate_auth_token() {
    $current_user = wp_get_current_user();
    if (!$current_user->exists()) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'User not authenticated'
        ), 401);
    }

    // Generate a secure token
    $token = wp_generate_password(32, false);
    $expiry = time() + 300; // Token expires in 5 minutes

    // Store token in user meta
    update_user_meta($current_user->ID, 'auth_transfer_token', array(
        'token' => $token,
        'expires' => $expiry,
        'used' => false
    ));

    return new WP_REST_Response(array(
        'success' => true,
        'token' => $token,
        'user_id' => $current_user->ID
    ), 200);
}

function tourismiq_validate_auth_token($request) {
    $params = $request->get_params();
    $token = sanitize_text_field($params['token'] ?? '');
    $user_id = intval($params['user_id'] ?? 0);

    if (empty($token) || empty($user_id)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Token and user_id required'
        ), 400);
    }

    // Get stored token data
    $stored_token_data = get_user_meta($user_id, 'auth_transfer_token', true);

    if (!$stored_token_data || !is_array($stored_token_data)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Invalid or expired token'
        ), 400);
    }

    // Check token validity
    if ($stored_token_data['token'] !== $token || 
        $stored_token_data['expires'] < time() || 
        $stored_token_data['used']) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Invalid or expired token'
        ), 400);
    }

    // Mark token as used
    $stored_token_data['used'] = true;
    update_user_meta($user_id, 'auth_transfer_token', $stored_token_data);

    // Get user data
    $user = get_user_by('ID', $user_id);
    if (!$user) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'User not found'
        ), 400);
    }

    // Return user data for frontend login
    $user_data = array(
        'id' => $user->ID,
        'username' => $user->user_login,
        'name' => $user->display_name,
        'first_name' => $user->first_name,
        'last_name' => $user->last_name,
        'email' => $user->user_email,
        'roles' => $user->roles
    );

    return new WP_REST_Response(array(
        'success' => true,
        'user' => $user_data,
        'message' => 'Token validated successfully'
    ), 200);
}

function tourismiq_handle_forgot_password($request) {
    $params = $request->get_params();
    $email = sanitize_email($params['email'] ?? '');

    if (empty($email)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Email is required'
        ), 400);
    }

    if (!is_email($email)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Please enter a valid email address'
        ), 400);
    }

    // Check if user exists (but don't reveal this in response for security)
    $user = get_user_by('email', $email);
    
    if ($user) {
        // Generate reset key
        $reset_key = get_password_reset_key($user);
        
        if (is_wp_error($reset_key)) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => 'Unable to generate reset key. Please try again.'
            ), 500);
        }

        // Create reset URL pointing to our frontend
        $frontend_url = 'https://mytourismiq.com';
        if (strpos(get_site_url(), 'tourismiq.local') !== false || strpos(get_site_url(), 'localhost') !== false) {
            $frontend_url = 'http://localhost:3000';
        }
        
        $reset_url = $frontend_url . '/reset-password?key=' . $reset_key . '&login=' . rawurlencode($user->user_login);

        // Send custom email
        $subject = 'Reset Your MyTourismIQ Password';
        $message = "Hi {$user->display_name},\n\n";
        $message .= "You recently requested to reset your password for your MyTourismIQ account. ";
        $message .= "Click the link below to reset it:\n\n";
        $message .= $reset_url . "\n\n";
        $message .= "If you did not request a password reset, please ignore this email or contact support if you have questions.\n\n";
        $message .= "This link will expire in 24 hours for security reasons.\n\n";
        $message .= "Thanks,\n";
        $message .= "The MyTourismIQ Team";

        $sent = wp_mail($email, $subject, $message);
        
        if (!$sent) {
            error_log('Failed to send password reset email to: ' . $email);
        }
    }

    // Always return success message regardless of whether user exists (security)
    return new WP_REST_Response(array(
        'success' => true,
        'message' => 'If an account with that email exists, we have sent you a password reset link.'
    ), 200);
}

function tourismiq_handle_reset_password($request) {
    $params = $request->get_params();
    $reset_key = sanitize_text_field($params['key'] ?? '');
    $user_login = sanitize_text_field($params['login'] ?? '');
    $new_password = $params['password'] ?? '';

    if (empty($reset_key) || empty($user_login) || empty($new_password)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Reset key, login, and new password are required'
        ), 400);
    }

    // Validate password strength
    if (strlen($new_password) < 6) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Password must be at least 6 characters long'
        ), 400);
    }

    // Get user by login
    $user = get_user_by('login', $user_login);
    
    if (!$user) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Invalid reset link'
        ), 400);
    }

    // Check if reset key is valid
    $check = check_password_reset_key($reset_key, $user_login);
    
    if (is_wp_error($check)) {
        return new WP_REST_Response(array(
            'success' => false,
            'message' => 'Invalid or expired reset link'
        ), 400);
    }

    // Reset the password
    reset_password($user, $new_password);

    return new WP_REST_Response(array(
        'success' => true,
        'message' => 'Password reset successfully'
    ), 200);
}

// Debug endpoint to check WordPress settings
function tourismiq_register_debug_endpoint() {
    register_rest_route('tourismiq/v1', '/debug/settings', array(
        'methods'  => 'GET',
        'callback' => 'tourismiq_handle_debug_settings',
        'permission_callback' => '__return_true',
    ));
}
add_action('rest_api_init', 'tourismiq_register_debug_endpoint');

function tourismiq_handle_debug_settings($request) {
    $default_category_id = get_option('default_category', 1);
    $default_category = get_category($default_category_id);
    
    return new WP_REST_Response(array(
        'default_category_id' => $default_category_id,
        'default_category_name' => $default_category ? $default_category->name : 'Unknown',
        'default_category_slug' => $default_category ? $default_category->slug : 'unknown',
        'uncategorized_id' => get_cat_ID('Uncategorized'),
        'all_categories' => get_categories(array('hide_empty' => false))
    ), 200);
}

// Fix WordPress default category setting
function tourismiq_fix_default_category() {
    // Only run this once
    if (get_option('tourismiq_default_category_fixed', false)) {
        return;
    }
    
    // Get the Thought Leadership category ID
    $thought_leadership_cat = get_category_by_slug('thought-leadership');
    
    if ($thought_leadership_cat) {
        // Change default category from Uncategorized to Thought Leadership
        update_option('default_category', $thought_leadership_cat->term_id);
        
        // Mark that we've fixed this
        update_option('tourismiq_default_category_fixed', true);
        
    }
}
add_action('init', 'tourismiq_fix_default_category');

/**
 * Register user stats endpoints
 */
function tourismiq_register_user_stats_endpoints() {
    // User comments count endpoint
    register_rest_route('tourismiq/v1', '/users/(?P<user_id>\d+)/comments-count', array(
        'methods'  => 'GET',
        'callback' => 'tourismiq_get_user_comments_count',
        'permission_callback' => '__return_true',
        'args' => array(
            'user_id' => array(
                'validate_callback' => function($param) {
                    return is_numeric($param);
                }
            ),
        ),
    ));
}
add_action('rest_api_init', 'tourismiq_register_user_stats_endpoints');

/**
 * Get user comments count
 */
function tourismiq_get_user_comments_count($request) {
    $user_id = $request['user_id'];
    
    // Check if user exists
    $user = get_user_by('ID', $user_id);
    if (!$user) {
        return new WP_Error('user_not_found', 'User not found', array('status' => 404));
    }
    
    global $wpdb;
    
    // Count approved comments by this user
    $comments_count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->comments} 
         WHERE user_id = %d 
         AND comment_approved = '1' 
         AND comment_type = 'comment'",
        $user_id
    ));
    
    return array(
        'user_id' => $user_id,
        'comments_count' => (int) $comments_count,
    );
}

// Register post meta fields for REST API
add_action('rest_api_init', function() {
    // Register vendor ID meta field for posts
    register_post_meta('post', 'post_vendor_id', array(
        'show_in_rest' => true,
        'single' => true,
        'type' => 'integer',
        'description' => 'ID of the vendor this post is associated with',
        'sanitize_callback' => 'absint',
        'auth_callback' => function() {
            return current_user_can('edit_posts');
        }
    ));
    
    // Add vendor data to embedded response for posts
    register_rest_field('post', 'vendor_data', array(
        'get_callback' => 'get_post_vendor_data_for_embed',
        'schema' => array(
            'description' => 'Vendor data for vendor posts',
            'type' => 'object',
        )
    ));
    
    // Ensure categories are properly exposed in REST API for block editor
    register_rest_field('post', 'category_names', array(
        'get_callback' => function($post) {
            $categories = get_the_category($post['id']);
            return array_map(function($cat) {
                return array(
                    'id' => $cat->term_id,
                    'name' => $cat->name,
                    'slug' => $cat->slug
                );
            }, $categories);
        },
        'schema' => array(
            'description' => 'Category names for the post',
            'type' => 'array',
        )
    ));
    
    // Ensure featured media URL is exposed
    register_rest_field('post', 'featured_media_url', array(
        'get_callback' => function($post) {
            if ($post['featured_media']) {
                return wp_get_attachment_url($post['featured_media']);
            }
            return null;
        },
        'schema' => array(
            'description' => 'Featured media URL',
            'type' => 'string',
        )
    ));
});

// Function to get vendor data for embedding in post responses
function get_post_vendor_data_for_embed($object) {
    $post_id = $object['id'];
    $vendor_id = get_post_meta($post_id, 'post_vendor_id', true);
    
    if (!$vendor_id || $vendor_id == 0) {
        return null;
    }
    
    // Get vendor post
    $vendor = get_post($vendor_id);
    if (!$vendor || $vendor->post_type !== 'vendor') {
        return null;
    }
    
    // Get vendor meta
    $vendor_meta = array();
    $vendor_meta['website'] = get_post_meta($vendor_id, 'vendor_website', true);
    $vendor_meta['is_paid'] = (bool) get_post_meta($vendor_id, 'vendor_is_paid', true);
    $vendor_meta['logo_url'] = wp_get_attachment_url(get_post_thumbnail_id($vendor_id));
    
    return array(
        'id' => $vendor->ID,
        'name' => $vendor->post_title,
        'slug' => $vendor->post_name,
        'title' => array(
            'rendered' => $vendor->post_title
        ),
        'vendor_meta' => $vendor_meta
    );
}

// Function to retroactively tag existing vendor posts
function tag_existing_vendor_posts() {
    // Get all vendors and their assigned members
    $vendors = get_posts(array(
        'post_type' => 'vendor',
        'post_status' => 'publish',
        'numberposts' => -1
    ));
    
    $updated_count = 0;
    
    foreach ($vendors as $vendor) {
        $vendor_id = $vendor->ID;
        $assigned_members = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
        
        if (empty($assigned_members)) {
            continue;
        }
        
        // Get all posts by assigned members that don't already have a valid post_vendor_id set
        $posts = get_posts(array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'numberposts' => -1,
            'author__in' => $assigned_members,
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => 'post_vendor_id',
                    'compare' => 'NOT EXISTS'
                ),
                array(
                    'key' => 'post_vendor_id',
                    'value' => '0',
                    'compare' => '='
                ),
                array(
                    'key' => 'post_vendor_id',
                    'value' => '',
                    'compare' => '='
                )
            )
        ));
        
        // Tag these posts with the vendor ID
        foreach ($posts as $post) {
            update_post_meta($post->ID, 'post_vendor_id', $vendor_id);
            $updated_count++;
        }
    }
    
    return $updated_count;
}

// Add admin action to manually trigger the tagging process
add_action('wp_ajax_tag_vendor_posts', function() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    $updated_count = tag_existing_vendor_posts();
    
    wp_send_json_success(array(
        'message' => "Tagged {$updated_count} existing vendor posts with post_vendor_id meta field.",
        'updated_count' => $updated_count
    ));
});

// Hook to clear caches when comments are deleted/trashed/spammed
function tourismiq_clear_comment_cache_on_status_change($comment_id, $comment_approved = null) {
    // Get the comment to find the post ID
    $comment = get_comment($comment_id);
    if ($comment && $comment->comment_post_ID) {
        $post_id = $comment->comment_post_ID;
        
        // Clear all WordPress caches
        wp_cache_flush();
        
        // Clear object cache groups related to comments
        if (function_exists('wp_cache_delete_group')) {
            wp_cache_delete_group('comment');
            wp_cache_delete_group('counts');
        }
        
        // Clear transients that might cache comment data
        delete_transient('comment_count_' . $post_id);
        
        // Clear any WP Engine or hosting-level caches
        if (function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group('comments');
        }
    }
}

// Add hooks for comment status changes
add_action('delete_comment', 'tourismiq_clear_comment_cache_on_status_change');
add_action('trash_comment', 'tourismiq_clear_comment_cache_on_status_change');
add_action('spam_comment', 'tourismiq_clear_comment_cache_on_status_change');
add_action('wp_set_comment_status', 'tourismiq_clear_comment_cache_on_status_change');


