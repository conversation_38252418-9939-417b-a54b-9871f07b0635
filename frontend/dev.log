
> frontend@0.1.0 dev /Users/<USER>/Local Sites/tourismiq/app/public/wp-content/themes/tourismq_wp/frontend
> node server.js

 ⨯ uncaughtException: Error: listen EADDRINUSE: address already in use :::3000
    at <unknown> (Error: listen EADDRINUSE: address already in use :::3000)
    at <unknown> (server.js:41:10) {
  code: 'EADDRINUSE',
  errno: -48,
  syscall: 'listen',
  address: '::',
  port: 3000
}
 ⨯ uncaughtException:  Error: listen EADDRINUSE: address already in use :::3000
    at <unknown> (Error: listen EADDRINUSE: address already in use :::3000)
    at <unknown> (server.js:41:10) {
  code: 'EADDRINUSE',
  errno: -48,
  syscall: 'listen',
  address: '::',
  port: 3000
}
 ELIFECYCLE  Command failed.
