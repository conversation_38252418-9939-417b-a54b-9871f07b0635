{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "dev:socket": "cd socket-server && npm run dev", "dev:all": "concurrently \"npm run dev\" \"npm run dev:socket\"", "build": "next build", "start": "NODE_ENV=production node server.js", "start:socket": "cd socket-server && npm start", "start:all": "concurrently \"npm run start\" \"npm run start:socket\"", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "setup:socket": "cd socket-server && npm install"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.80.7", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^8.2.2", "cookie": "^1.0.2", "critters": "^0.0.25", "date-fns": "^4.1.0", "framer-motion": "^12.22.0", "google-auth-library": "^10.1.0", "lucide-react": "^0.511.0", "next": "15.3.2", "node-fetch": "2", "quill": "^2.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-quill": "^2.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "stripe": "^18.3.0", "swiper": "^11.1.15", "tailwind-merge": "^3.3.0", "tailwindcss-line-clamp": "^1.0.5", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tanstack/react-query-devtools": "^5.80.7", "@types/cookie": "^0.6.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/parser": "^8.38.0", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.4.31", "prettier": "^3.6.2", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}