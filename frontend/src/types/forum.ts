import { Member } from '../lib/types';

export interface Author {
  id: string;
  name: string;
  avatar: string;
  roles?: string[];
}

export interface Question {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  user?: Member;
  author?: Author;
  commentCount: number;
  isResolved: boolean;
}

export interface Comment {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  user?: Member;
  author?: Author;
  replies?: Comment[];
}

export interface QuestionDetail extends Question {
  comments: Comment[];
}

export interface QuestionFilters {
  page?: number;
  sort?: 'new' | 'trending';
  search?: string;
}

export interface QuestionsResponse {
  questions: Question[];
  totalPages: number;
  currentPage: number;
}
