import 'next';

declare global {
  interface Window {
    wpApiSettings?: {
      root: string;
      nonce: string;
    };
  }
}

export interface UserACFFields {
  location?: string;
  job_title?: string;
  social_twitter?: string;
  social_linkedin?: string;
  social_facebook?: string;
  social_instagram?: string;
  profile_picture?: {
    ID: number;
    id: number;
    title: string;
    filename: string;
    filesize: number;
    url: string;
    link: string;
    alt: string;
    author: string;
    description: string;
    caption: string;
    name: string;
    status: string;
    uploaded_to: number;
    date: string;
    modified: string;
    menu_order: number;
    mime_type: string;
    type: string;
    subtype: string;
    icon: string;
    width: number;
    height: number;
    sizes: Record<string, unknown>;
  };
  [key: string]: unknown; // For any additional ACF fields
}

export interface WPUser {
  id: number;
  name: string;
  slug: string;
  email: string;
  first_name: string;
  last_name: string;
  url: string;
  description: string;
  link: string;
  avatar_urls: {
    24: string;
    48: string;
    96: string;
    [key: string]: string | undefined;
    custom?: string;
    thumbnail?: string;
  };
  _links: Record<string, unknown>;
  acf?: UserACFFields;
  // User roles and authentication
  roles?: string[];
  _user_roles?: string[] | string;
  capabilities?: Record<string, boolean> | string;
  username?: string;
  user_login?: string;
  user_email?: string;
  display_name?: string;
}

export interface WPMetaData {
  [key: string]: unknown;
  first_name?: string;
  last_name?: string;
  description?: string;
  location?: string;
  job_title?: string;
  twitter?: string;
  linkedin?: string;
  facebook?: string;
  instagram?: string;
}
