// Extend the Window interface to include our custom properties
declare global {
  interface Window {
    // WordPress REST API settings
    wpApiSettings?: {
      root: string;
      nonce: string;
      user: {
        id: number;
        isLoggedIn: boolean;
      };
    };

    // Our custom API settings
    tourismiq?: {
      api: {
        root: string;
        nonce: string;
        user: {
          id: number;
          isLoggedIn: boolean;
        };
      };
    };
  }
}

export {}; // This file needs to be a module
