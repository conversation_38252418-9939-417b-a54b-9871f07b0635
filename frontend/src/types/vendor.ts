export interface VendorCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  count?: number;
}

export interface VendorSocialLinks {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
  youtube?: string;
}

export interface VendorResource {
  title: string;
  description?: string;
  file: {
    id: number;
    url: string;
    filename: string;
    filesize: number;
    mime_type: string;
  };
}

export interface VendorTeamMember {
  id: number;
  name: string;
  first_name: string;
  last_name: string;
  username: string;
  slug: string;
  email: string;
  avatar_url: string;
  profile_picture?: {
    ID?: number;
    id?: number;
    url?: string;
    sizes?: {
      [key: string]: string;
    };
  };
  job_title?: string;
  company?: string;
  bio?: string;
  roles: string[];
}

export interface VendorMeta {
  website?: string;
  is_paid: boolean;
  subscription_status?: 'active' | 'pending' | 'cancelled' | 'expired';
  cover_photo?: string;
  logo_url?: string;
  social_links?: VendorSocialLinks;
  assigned_members?: VendorTeamMember[];
  lead_form_enabled?: boolean;
  resources?: VendorResource[];
  // Stripe integration fields
  stripe_customer_id?: string;
  stripe_subscription_id?: string;
  stripe_session_id?: string;
  subscription_start?: string;
  last_payment_amount?: number;
  currency?: string;
  upgraded_at?: string;
}

export interface Vendor {
  id: number;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  excerpt: {
    rendered: string;
  };
  slug: string;
  status: 'publish' | 'draft' | 'private';
  date: string;
  modified: string;
  featured_media: number;
  vendor_meta: VendorMeta;
  vendor_categories: VendorCategory[];
  _links?: unknown;
}

export interface VendorListResponse {
  vendors: Vendor[];
  pagination: {
    page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}

export interface VendorCategoriesResponse {
  categories: VendorCategory[];
  pagination: {
    page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}

export interface VendorFilters {
  search?: string;
  category?: string;
  location?: string;
  page?: number;
  per_page?: number;
  orderby?: 'date' | 'title' | 'modified';
  order?: 'asc' | 'desc';
}

// For the simplified vendor card component
export interface VendorCardData {
  id: number;
  name: string;
  slug: string;
  logo?: string;
  coverPhoto?: string;
  categories: string[];
  description: string;
  isPaid: boolean;
  website?: string;
}

import { decodeHtmlEntities } from '@/lib/text-utils';

// Transform function to convert API response to card data
export function transformVendorToCardData(vendor: Vendor): VendorCardData {
  const cardData: VendorCardData = {
    id: vendor.id,
    name: decodeHtmlEntities(vendor.title.rendered),
    slug: vendor.slug,
    logo: vendor.vendor_meta.logo_url || '',
    coverPhoto: vendor.vendor_meta.cover_photo || '',
    categories:
      vendor.vendor_categories.length > 0
        ? vendor.vendor_categories.map((cat) => decodeHtmlEntities(cat.name))
        : ['Uncategorized'],
    description: decodeHtmlEntities(vendor.excerpt.rendered.replace(/<[^>]*>/g, '')), // Strip HTML and decode entities
    isPaid: vendor.vendor_meta.is_paid,
    website: vendor.vendor_meta.website || '',
  };
  return cardData;
}
