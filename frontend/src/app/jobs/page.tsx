import { Metadata } from 'next';
import { searchJobs, Job } from '@/lib/api/jobs';
import { JobsHub } from '@/components/jobs/JobsHub';

export const metadata: Metadata = {
  title: 'Jobs Hub | TourismIQ',
  description:
    'Discover tourism and travel industry job opportunities. Find your next career move in hospitality, travel, and tourism.',
  keywords: [
    'tourism jobs',
    'travel jobs',
    'hospitality careers',
    'travel industry',
    'job opportunities',
  ],
};

export default async function JobsPage() {
  // Fetch initial jobs server-side for better SEO and initial load performance
  let initialJobs: Job[] = [];
  let initialTotal = 0;

  try {
    const response = await searchJobs({
      per_page: 20,
      page: 1,
      orderby: 'date',
      order: 'desc',
    });

    initialJobs = response.jobs;
    initialTotal = response.total;
  } catch (error) {
    console.error('Failed to fetch initial jobs:', error);
    // Continue with empty state - JobsHub will handle loading
  }

  return (
    <div className="min-h-screen bg-white">
      <JobsHub initialJobs={initialJobs} initialTotal={initialTotal} />
    </div>
  );
}
