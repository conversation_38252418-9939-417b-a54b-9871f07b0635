import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { MapPin, Building2, Calendar, ExternalLink, Clock } from 'lucide-react';
import { getJobBySlug, formatJobDeadline, getCategoryLabel, getStatusLabel } from '@/lib/api/jobs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BackToSectionHeader } from '@/components/ui/BackToSectionHeader';
import { decodeHtmlEntities } from '@/lib/utils';

interface JobPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: JobPageProps): Promise<Metadata> {
  try {
    const { slug } = await params;
    const job = await getJobBySlug(slug);

    return {
      title: `${decodeHtmlEntities(job.title.rendered)} at ${job.job_meta?.job_organization || 'Unknown'} | TourismIQ Jobs`,
      description: decodeHtmlEntities(job.excerpt.rendered.replace(/<[^>]*>/g, '')).substring(0, 160),
      keywords: [
        'tourism job',
        'travel job',
        ...(job.job_categories || []),
        job.job_meta?.job_organization || '',
        ...(job.job_countries || []),
      ].filter(Boolean),
    };
  } catch {
    return {
      title: 'Job Not Found | TourismIQ Jobs',
      description: 'The requested job posting could not be found.',
    };
  }
}

export default async function JobPage({ params }: JobPageProps) {
  let job;

  try {
    const { slug } = await params;
    job = await getJobBySlug(slug);
  } catch {
    notFound();
  }

  const { title, content, excerpt, date, job_meta } = job;

  const {
    job_status = 'active',
    job_organization = '',
    job_source_url = '',
    job_deadline = '',
  } = job_meta || {};

  const hasDescription = content?.rendered?.trim() || excerpt?.rendered?.trim();
  const hasSourceUrl = job_source_url?.trim();

  // Create location string
  const state = job.job_states?.[0] || '';
  const country = job.job_countries?.[0] || '';
  const category = job.job_categories?.[0] || '';
  const location = [state, country].filter(Boolean).join(', ') || 'Remote';

  // Format dates
  const postingDate = new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // Get deadline info
  const deadlineInfo = formatJobDeadline(job_deadline);
  const isExpired = job_status === 'expired' || deadlineInfo === 'Expired';
  const isUrgent = deadlineInfo.includes('day') && !isExpired;

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'NEW':
        return 'bg-[#22c55e] text-white border-[#22c55e]';
      case 'ACTIVE':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'FILLED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-[#eff1f4]">
      {/* Sticky Back to Jobs Header */}
      <BackToSectionHeader href="/jobs" text="Back to Jobs" />

      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-white to-blue-50 border-b">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-5 leading-tight">
              {job_status && job_status.toUpperCase() === 'NEW' && (
                <Badge className="bg-[#22c55e] text-white border-[#22c55e] text-sm px-2 py-1 mr-3 inline-block align-middle">
                  New
                </Badge>
              )}
              {decodeHtmlEntities(title.rendered)}
            </h1>

            {job_organization && (
              <div className="flex items-center justify-center gap-2 mb-5">
                <Building2 className="h-5 w-5 text-blue-600" />
                <span className="text-lg font-semibold text-gray-700">{job_organization}</span>
              </div>
            )}

            <div className="flex flex-wrap items-center justify-center gap-6 text-gray-600 mb-6">
              {(state || country) && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>{location}</span>
                </div>
              )}

              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>Posted {postingDate}</span>
              </div>

              {job_deadline && (
                <div
                  className={`flex items-center gap-2 ${isUrgent ? 'text-orange-600 font-medium' : ''}`}
                >
                  <Clock className="h-4 w-4" />
                  <span>Deadline: {deadlineInfo}</span>
                </div>
              )}
            </div>

            <div className="flex flex-wrap items-center justify-center gap-3 mb-6">
              {job_status && job_status.trim() && job_status.toUpperCase() !== 'NEW' && (
                <Badge className={`${getStatusColor(job_status)} text-sm px-3 py-1`}>
                  {getStatusLabel(job_status)}
                </Badge>
              )}
              {category && category.trim() && (
                <Badge variant="outline" className="text-sm px-3 py-1">
                  {getCategoryLabel(category)}
                </Badge>
              )}
            </div>

            {(hasSourceUrl || isExpired) && (
              <div className="flex justify-center">
                {hasSourceUrl && !isExpired && (
                  <Button size="lg" className="px-8 py-3 text-lg" asChild>
                    <a
                      href={job_source_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2"
                    >
                      Apply Now
                      <ExternalLink className="h-5 w-5" />
                    </a>
                  </Button>
                )}

                {isExpired && (
                  <Button size="lg" className="px-8 py-3 text-lg" disabled>
                    Application Closed
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      {hasDescription && (
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-sm border p-6 md:p-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Job Description</h2>
            <div
              className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
              dangerouslySetInnerHTML={{
                __html: decodeHtmlEntities(content?.rendered || excerpt?.rendered || ''),
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
