'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/auth-context';

const loginSchema = z.object({
  username: z.string().min(1, { message: 'Username is required' }),
  password: z.string().min(1, { message: 'Password is required' }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

function LoginForm() {
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  // const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/';
  const resetConfirmation = searchParams.get('reset');
  const { login, isLoading } = useAuth();

  // Show password reset confirmation message
  useEffect(() => {
    if (resetConfirmation === 'true') {
      setSuccessMessage('Password reset email sent! Check your email for the reset link.');

      // Clean up URL parameter
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('reset');
      router.replace(newUrl.pathname + newUrl.search, { scroll: false });
    }
  }, [resetConfirmation, router]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormValues) => {
    try {
      setError(null);
      const result = await login(data.username, data.password);

      if (result?.success) {
        // Redirect to callback URL if login is successful
        router.push(callbackUrl);
      } else if (result?.error) {
        setError(result.error);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed. Please try again.');
    }
  };

  // const handleGoogleLogin = async () => {
  //   setIsGoogleLoading(true);
  //   setError(null);

  //   try {
  //     const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;

  //     if (!clientId) {
  //       throw new Error('Google Client ID not configured');
  //     }

  //     // Use the OAuth 2.0 flow
  //     const redirectUri = `${window.location.origin}/api/google-callback`;
  //     const scope = 'openid email profile';
  //     const responseType = 'code';
  //     const state = Math.random().toString(36).substring(2, 15);

  //     // Store state for verification
  //     sessionStorage.setItem('google_oauth_state', state);

  //     const googleAuthUrl = `https://accounts.google.com/oauth/v2/auth?` +
  //       `client_id=${encodeURIComponent(clientId)}` +
  //       `&redirect_uri=${encodeURIComponent(redirectUri)}` +
  //       `&scope=${encodeURIComponent(scope)}` +
  //       `&response_type=${encodeURIComponent(responseType)}` +
  //       `&state=${encodeURIComponent(state)}`;

  //     // Redirect to Google OAuth
  //     window.location.href = googleAuthUrl;

  //   } catch (err) {
  //     console.error('Google login error:', err);
  //     setError('Failed to initialize Google login. Please try again.');
  //     setIsGoogleLoading(false);
  //   }
  // };

  // Display the callback URL destination if it exists and is not the homepage
  const callbackDestination =
    callbackUrl && callbackUrl !== '/'
      ? `After login, you will be redirected to ${callbackUrl}`
      : null;

  return (
    <div className="container mx-auto pt-12 pb-8">
      <div className="w-full max-w-md mx-auto space-y-6 p-8 border bg-white rounded-lg shadow-sm">
        <div className="space-y-2 text-center">
          <h1 className="text-3xl font-bold">Welcome back</h1>
          <p className="text-gray-500">Enter your credentials to access your account</p>
          {callbackDestination && (
            <p className="text-sm text-blue-500 mt-2">{callbackDestination}</p>
          )}
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 text-red-700 rounded-md text-sm">
            {error}
          </div>
        )}

        {successMessage && (
          <div className="p-3 bg-green-50 border border-green-200 text-green-700 rounded-md text-sm">
            {successMessage}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="username" className="text-sm font-medium">
              Username
            </label>
            <Input
              id="username"
              placeholder="Username"
              {...register('username')}
              className={errors.username ? 'border-red-500' : ''}
            />
            {errors.username && <p className="text-sm text-red-500">{errors.username.message}</p>}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label htmlFor="password" className="text-sm font-medium">
                Password
              </label>
              <Link href="/forgot-password" className="text-sm text-primary">
                Forgot password?
              </Link>
            </div>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              {...register('password')}
              className={errors.password ? 'border-red-500' : ''}
            />
            {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}
          </div>

          <Button
            type="submit"
            className="w-full bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-extrabold border-2 border-[#5cc8ff]"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Logging in...
              </>
            ) : (
              'Log in'
            )}
          </Button>
        </form>

        {/* Google auth temporarily disabled */}
        {/* <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-muted-foreground">Or continue with</span>
          </div>
        </div>

        <Button
          type="button"
          variant="outline"
          className="w-full"
          onClick={handleGoogleLogin}
          disabled={isGoogleLoading || isLoading}
        >
          {isGoogleLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Connecting to Google...
            </>
          ) : (
            <>
              <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                <path
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  fill="#4285F4"
                />
                <path
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  fill="#34A853"
                />
                <path
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  fill="#FBBC05"
                />
                <path
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  fill="#EA4335"
                />
              </svg>
              Continue with Google
            </>
          )}
        </Button> */}

        <div className="mt-4 text-center text-sm">
          Don&apos;t have an account?{' '}
          <Link href="/register" className="text-primary">
            Sign up
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense
      fallback={
        <div className="container mx-auto pt-12 pb-8">
          <div className="w-full max-w-md mx-auto space-y-6 p-8 border bg-white rounded-lg shadow-sm">
            <div className="space-y-2 text-center">
              <h1 className="text-3xl font-bold">Welcome back</h1>
              <p className="text-gray-500">Loading...</p>
            </div>
          </div>
        </div>
      }
    >
      <LoginForm />
    </Suspense>
  );
}
