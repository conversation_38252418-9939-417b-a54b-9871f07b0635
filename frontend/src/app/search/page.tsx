'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Search } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

interface SearchResult {
  id: number;
  title?: string;
  name?: string;
  question?: string;
  excerpt?: string;
  content?: string;
  description?: string;
  link: string;
  type: 'post' | 'user' | 'vendor' | 'forum' | 'rfp' | 'job';
  author?: string;
  avatar_url?: string;
  featured_media?: string;
  categories?: string[];
  job_title?: string;
  company?: string;
  votes?: number;
  comments_count?: number;
  location?: string;
  budget?: string;
  is_paid?: boolean;
  date?: string;
  job_type?: string;
  salary_range?: string;
  deadline?: string;
  status?: string;
}

interface SearchResults {
  posts: SearchResult[];
  users: SearchResult[];
  vendors: SearchResult[];
  forum: SearchResult[];
  rfps: SearchResult[];
  jobs: SearchResult[];
  total: number;
  totalPosts?: number;
}

const RESULT_TYPE_ICONS = {
  post: '📄',
  user: '👤',
  vendor: '🏢',
  forum: '💬',
  rfp: '📋',
  job: '💼',
};

function SearchPageContent() {
  const searchParams = useSearchParams();
  const initialQuery = searchParams.get('q') || '';

  const [query] = useState(initialQuery);
  const [results, setResults] = useState<SearchResults>({
    posts: [],
    users: [],
    vendors: [],
    forum: [],
    rfps: [],
    jobs: [],
    total: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [searchStatus, setSearchStatus] = useState('');
  const [allResults, setAllResults] = useState<SearchResult[]>([]);

  // Perform search (no pagination)
  const performSearch = useCallback(async (searchQuery: string) => {
    console.log(`[SEARCH FRONTEND] performSearch called - Query: "${searchQuery}"`);

    if (!searchQuery.trim()) {
      setResults({ posts: [], users: [], vendors: [], forum: [], rfps: [], jobs: [], total: 0 });
      setAllResults([]);
      return;
    }

    setIsLoading(true);
    setSearchStatus('Starting search...');

    try {
      // Cycle through search messages
      const searchMessages = [
        'Searching posts and articles...',
        'Looking through community members...',
        'Finding industry vendors...',
        'Checking forum discussions...',
        'Scanning job opportunities...',
        'Reviewing project RFPs...',
        'Exploring case studies...',
        'Searching news and updates...',
        'Finding expert insights...',
        'Checking event listings...',
        'Looking through resources...',
        'Scanning marketplace items...',
      ];

      let messageIndex = 0;
      setSearchStatus(searchMessages[0]);

      const messageTimer = setInterval(() => {
        messageIndex = (messageIndex + 1) % searchMessages.length;
        setSearchStatus(searchMessages[messageIndex]);
      }, 3000);

      const limit = 100; // WordPress max limit
      const response = await fetch(
        `/api/search?q=${encodeURIComponent(searchQuery)}&limit=${limit}`,
        {
          credentials: 'include',
        }
      );

      clearInterval(messageTimer);
      setSearchStatus('Processing results...');

      if (response.ok) {
        const data = await response.json();
        console.log(`[SEARCH FRONTEND] API Response:`, {
          postsCount: data.posts?.length || 0,
          usersCount: data.users?.length || 0,
          vendorsCount: data.vendors?.length || 0,
          forumCount: data.forum?.length || 0,
          rfpsCount: data.rfps?.length || 0,
          jobsCount: data.jobs?.length || 0,
          total: data.total,
        });

        // Set all results (no pagination)
        setResults(data);
        const allResultTypes = [
          ...data.posts,
          ...data.users,
          ...data.vendors,
          ...data.forum,
          ...data.rfps,
          ...data.jobs,
        ];

        setSearchStatus('Organizing results...');
        setAllResults(allResultTypes);

        setSearchStatus('Complete!');

        // Hide after brief completion message
        setTimeout(() => {
          setIsLoading(false);
        }, 400);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchStatus('Search failed');
      setTimeout(() => {
        setIsLoading(false);
      }, 1500);
    }
  }, []);

  // Search on initial load
  useEffect(() => {
    if (initialQuery) {
      performSearch(initialQuery);
    }
  }, [initialQuery, performSearch]);

  // Use allResults for display
  const currentResults = allResults;

  return (
    <div className="min-h-screen bg-[#eff1f4]">
      {/* Header */}
      <div className="border-b border-gray-200">
        <div className="max-w-[1360px] mx-auto py-6 px-4">
          <div>
            <h1 className="text-3xl font-bold">Search Results</h1>
            <p className="text-gray-600 mt-1">
              {results.total > 0 ? `${results.total} results found` : 'Search across all content'}
            </p>
          </div>

          {/* Query Display */}
          {initialQuery && (
            <div className="mt-6">
              <p className="text-lg text-gray-700">
                Search results for:{' '}
                <span className="font-semibold">&ldquo;{initialQuery}&rdquo;</span>
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Results */}
      <div className="max-w-[1360px] mx-auto py-6 px-4">
        {/* Controls */}
        <div className="flex items-center justify-between mb-6">
          <span className="text-sm text-gray-500">Showing {currentResults.length} results</span>
        </div>

        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
            <style jsx>{`
              @keyframes fade-up {
                0% {
                  opacity: 0;
                  transform: translateY(10px);
                }
                100% {
                  opacity: 1;
                  transform: translateY(0);
                }
              }
              .animate-fade-up {
                animation: fade-up 0.6s ease-out;
              }
            `}</style>
            <div className="bg-white rounded-lg p-8 max-w-sm w-full mx-4 text-center shadow-2xl">
              <div className="mb-6">
                <div className="w-16 h-16 mx-auto mb-4 relative">
                  <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                  <div className="absolute inset-0 rounded-full border-4 border-[#5cc8ff] border-t-transparent animate-spin"></div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Searching All Content</h3>
                <div className="min-h-[20px] overflow-hidden">
                  <p key={searchStatus} className="text-sm text-gray-600 animate-fade-up">
                    {searchStatus || 'Starting search...'}
                  </p>
                </div>
              </div>

              <div className="flex justify-center mt-4">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-[#5cc8ff] rounded-full animate-bounce"></div>
                  <div
                    className="w-2 h-2 bg-[#5cc8ff] rounded-full animate-bounce"
                    style={{ animationDelay: '0.1s' }}
                  ></div>
                  <div
                    className="w-2 h-2 bg-[#5cc8ff] rounded-full animate-bounce"
                    style={{ animationDelay: '0.2s' }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Results List */}
        {!isLoading ? (
          currentResults.length === 0 && query ? (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-gray-300 mx-auto mb-6" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No results found</h3>
              <p className="text-gray-500 text-sm">
                Try adjusting your search terms or check a different category.
              </p>
            </div>
          ) : currentResults.length === 0 ? (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-gray-300 mx-auto mb-6" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Start searching</h3>
              <p className="text-gray-500 text-sm">
                Enter a search term to find posts, people, vendors, and more.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {currentResults.map((result) => (
                <Card
                  key={`${result.type}-${result.id}`}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Icon/Avatar - centered at top */}
                      <div className="flex justify-center">
                        {result.type === 'user' && result.avatar_url ? (
                          <img
                            src={result.avatar_url}
                            alt={result.name || ''}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        ) : result.featured_media ? (
                          <img
                            src={result.featured_media}
                            alt={result.title || ''}
                            className="w-16 h-16 rounded-lg object-cover"
                          />
                        ) : (
                          <div className="w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center text-3xl">
                            {RESULT_TYPE_ICONS[result.type as keyof typeof RESULT_TYPE_ICONS] ||
                              '📄'}
                          </div>
                        )}
                      </div>

                      {/* Content - single column */}
                      <div className="text-center space-y-3">
                        <div className="flex items-center justify-center gap-2 flex-wrap">
                          <h3 className="font-semibold text-gray-900 line-clamp-2">
                            <a href={result.link} className="hover:text-blue-600 transition-colors">
                              {result.title || result.name || result.question}
                            </a>
                          </h3>
                          {result.is_paid && (
                            <Badge className="bg-yellow-100 text-yellow-800">Paid</Badge>
                          )}
                          {result.status === 'NEW' && (
                            <Badge className="bg-[#22c55e] text-white">New</Badge>
                          )}
                        </div>

                        {(result.excerpt || result.content || result.description) && (
                          <p className="text-sm text-gray-600 line-clamp-3">
                            {result.excerpt || result.content || result.description}
                          </p>
                        )}

                        <div className="flex flex-wrap items-center justify-center gap-2 text-xs text-gray-500">
                          <span className="capitalize">{result.type}</span>
                          {result.author && <span>by {result.author}</span>}
                          {result.company && <span>{result.company}</span>}
                          {result.location && <span>{result.location}</span>}
                          {result.date && (
                            <span>
                              {new Date(result.date).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                              })}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Results summary */}
              {currentResults.length > 0 && (
                <div className="col-span-full text-center py-8 text-gray-500">
                  Showing all {currentResults.length} search results
                </div>
              )}
            </div>
          )
        ) : null}
      </div>
    </div>
  );
}

export default function SearchPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-[#eff1f4] flex items-center justify-center">
          <div className="flex items-center gap-2 text-gray-500">
            <div className="animate-spin h-5 w-5 border-2 border-gray-300 border-t-blue-500 rounded-full"></div>
            <span>Loading search results...</span>
          </div>
        </div>
      }
    >
      <SearchPageContent />
    </Suspense>
  );
}
