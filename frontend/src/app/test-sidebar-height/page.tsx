/*********************************************
# frontend/src/app/test-sidebar-height/page.tsx
# 02/07/2025 4:30pm Created test page for sidebar height detection
**********************************************/

'use client';

import { useWindowSize } from '@/hooks/useWindowSize';
import { RightSidebar } from '@/components/layout/RightSidebar';

export default function TestSidebarHeight() {
  const { width, height, shouldUseAccordion } = useWindowSize();

  return (
    <div className="min-h-screen p-8 flex">
      <div className="flex-1 max-w-md">
        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <h1 className="text-2xl font-bold mb-6">Sidebar Height Test</h1>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Browser Width:</label>
              <p className="text-lg font-mono">{width}px</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-600">Browser Height:</label>
              <p className="text-lg font-mono">{height}px</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-600">Sidebar Mode:</label>
              <p
                className={`text-lg font-semibold ${shouldUseAccordion ? 'text-orange-600' : 'text-green-600'}`}
              >
                {shouldUseAccordion ? 'Accordion Mode' : 'All Open Mode'}
              </p>
            </div>

            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium mb-2">Instructions:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Resize your browser window vertically</li>
                <li>• Watch the sidebar mode change</li>
                <li>• Height threshold: ~1338px</li>
                <li>• Above threshold: All boxes open</li>
                <li>• Below threshold: Accordion mode (priority-based)</li>
                <li>
                  • <strong>No scrollbars will appear during transitions</strong>
                </li>
              </ul>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium mb-2">Priority Order:</h3>
              <ol className="text-sm text-gray-600 space-y-1">
                <li>1. My Connections (highest priority)</li>
                <li>2. Suggested Connections</li>
                <li>3. Upcoming Events (lowest priority)</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      <div className="w-[350px] ml-8">
        <div className="sticky top-4 h-[calc(100vh-32px)]">
          <RightSidebar />
        </div>
      </div>
    </div>
  );
}
