/*********************************************
# frontend/src/app/not-found.tsx  
# 02/06/2025 11:20am Created not-found page to resolve build error
**********************************************/

import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-[#eff1f4] flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
        <p className="text-gray-600 mb-8">
          Sorry, we couldn&apos;t find the page you&apos;re looking for.
        </p>
        <Link
          href="/"
          className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
        >
          Return Home
        </Link>
      </div>
    </div>
  );
}
