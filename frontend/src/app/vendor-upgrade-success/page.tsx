'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { CheckCircle, Loader2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

function VendorUpgradeSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    if (!sessionId) {
      setError('No session ID provided');
      setLoading(false);
      return;
    }

    // Here you would typically:
    // 1. Call your backend to verify the Stripe session
    // 2. Get the vendor ID from the session metadata
    // 3. Get the vendor slug from the vendor ID
    // 4. Redirect to the vendor profile

    const handleSuccessRedirect = async () => {
      try {
        // Clean the session ID of any URL encoding issues and trailing characters
        const cleanSessionId = decodeURIComponent(sessionId).replace(/[{}]/g, '').trim();

        // Verify the Stripe session and get vendor information
        const response = await fetch(`/api/stripe/verify-session/${cleanSessionId}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Session verification failed:', response.status, errorText);
          throw new Error(`Failed to verify session: ${response.status} ${errorText}`);
        }

        const result = await response.json();

        if (result.success) {
          setLoading(false);

          // If we have a vendor slug, redirect to the specific vendor profile
          if (result.vendorSlug) {
            setTimeout(() => {
              router.push(`/vendors/${result.vendorSlug}?upgraded=true`);
            }, 3000);
          } else {
            // Otherwise, redirect to vendors directory with success message
            setTimeout(() => {
              router.push('/vendors?upgraded=true');
            }, 3000);
          }
        } else {
          throw new Error(result.message || 'Session verification failed');
        }
      } catch (err) {
        console.error('Error handling success redirect:', err);
        setError('Failed to process upgrade');
        setLoading(false);
      }
    };

    handleSuccessRedirect();
  }, [sessionId, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#eff1f4] flex items-center justify-center">
        <div className="max-w-md w-full mx-auto p-6">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <Loader2 className="h-12 w-12 text-[#5cc8ff] animate-spin" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Processing Your Upgrade</h1>
            <p className="text-gray-600">
              We&apos;re setting up your premium vendor account. This will only take a moment...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#eff1f4] flex items-center justify-center">
        <div className="max-w-md w-full mx-auto p-6">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <AlertCircle className="h-12 w-12 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Something went wrong</h1>
            <p className="text-gray-600">{error}</p>
            <div className="flex gap-2 justify-center">
              <Button asChild>
                <Link href="/vendors">Go to Vendors</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/vendor-upgrade">Try Again</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#eff1f4] flex items-center justify-center">
      <div className="max-w-md w-full mx-auto p-6">
        <div className="text-center space-y-6">
          <div className="flex justify-center">
            <div className="bg-green-100 rounded-full p-3">
              <CheckCircle className="h-12 w-12 text-green-600" />
            </div>
          </div>

          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-gray-900">Upgrade Successful!</h1>
            <p className="text-lg text-gray-600">Welcome to your premium vendor account</p>
          </div>

          <div className="bg-white rounded-lg p-6 border shadow-sm">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">What&apos;s next?</h2>
            <ul className="text-left space-y-2 text-gray-600">
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                <span>Access premium vendor features</span>
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                <span>Upload cover photos and resources</span>
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                <span>Enhanced profile visibility</span>
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                <span>Priority support</span>
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <p className="text-sm text-gray-500">Redirecting you to the vendors directory...</p>
            <Button asChild className="w-full">
              <Link href="/vendors?upgraded=true">View Vendors Directory</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function VendorUpgradeSuccessPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-[#eff1f4] flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      }
    >
      <VendorUpgradeSuccessContent />
    </Suspense>
  );
}
