import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getPostsByCategory, getCategories } from '@/lib/api';
import { Feed } from '@/components/feed';
import { getCategoryDisplayName } from '@/lib/utils/categoryNames';

// Generate metadata for the page
export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;

  // Get the proper display name for the category
  const formattedName = getCategoryDisplayName(slug);
  const categoryUrl = `https://mytourismiq.com/category/${slug}`;

  return {
    title: `${formattedName} - TourismIQ`,
    description: `Explore the latest ${formattedName} posts, insights, and resources from the TourismIQ community. Stay updated with industry trends and expert knowledge.`,
    openGraph: {
      title: `${formattedName} - TourismIQ`,
      description: `Explore the latest ${formattedName} posts, insights, and resources from the TourismIQ community.`,
      url: categoryUrl,
      siteName: 'TourismIQ',
      type: 'website',
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${formattedName} - TourismIQ`,
      description: `Explore the latest ${formattedName} posts, insights, and resources from the TourismIQ community.`,
    },
    alternates: {
      canonical: categoryUrl,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

// Generate static params for categories
export async function generateStaticParams() {
  const categories = await getCategories();
  return categories.map((category) => ({
    slug: category.slug,
  }));
}

export default async function CategoryPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;

  // Get posts for this category
  const postsData = await getPostsByCategory(slug);

  // If no posts found, show 404
  if (!postsData || postsData.posts.length === 0) {
    return notFound();
  }

  // Get the proper display name for the category
  const formattedName = getCategoryDisplayName(slug);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">{formattedName}</h1>
      </div>

      <Feed initialPosts={postsData.posts} />
    </div>
  );
}
