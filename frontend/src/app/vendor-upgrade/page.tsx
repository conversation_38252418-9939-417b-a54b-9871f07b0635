'use client';

import { useAuth } from '@/contexts/auth-context';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';

interface VendorData {
  id: number;
  title?:
    | {
        rendered: string;
      }
    | string;
  name?: string;
  vendor_meta?: {
    is_paid: boolean;
  };
}

function VendorUpgradeContent() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [vendor, setVendor] = useState<VendorData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState<string | null>(null);
  const [emailSent, setEmailSent] = useState(false);

  const vendorIdFromUrl = searchParams.get('vendor');

  // Helper function to get vendor name safely
  const getVendorName = (vendor: VendorData | null): string => {
    if (!vendor) return '';

    if (typeof vendor.title === 'object' && vendor.title?.rendered) {
      return vendor.title.rendered;
    }

    if (typeof vendor.title === 'string') {
      return vendor.title;
    }

    if (vendor.name) {
      return vendor.name;
    }

    return 'Unknown Vendor';
  };

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    if (vendorIdFromUrl) {
      fetchVendor(vendorIdFromUrl);
    } else {
      setError('No vendor specified');
      setLoading(false);
    }
  }, [user, router, vendorIdFromUrl]);

  const fetchVendor = async (vendorId: string) => {
    try {
      const response = await fetch(`/api/wp-proxy/vendors/by-id/${vendorId}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const vendor = await response.json();
        setVendor(vendor);

        // Check if vendor is already paid
        if (vendor.vendor_meta?.is_paid) {
          setError('This vendor already has an active subscription');
        }
      } else if (response.status === 404) {
        setError('Vendor not found');
      } else {
        setError('Failed to load vendor information');
      }
    } catch (error) {
      console.error('Error fetching vendor:', error);
      setError('Failed to load vendor information');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeClick = () => {
    if (!vendor || !user) return;
    setShowEmailDialog(true);
    setEmail(user.email || '');
    setEmailError(null);
    setEmailSent(false);
  };

  const handleEmailSubmit = async () => {
    if (!vendor || !email) return;

    setIsProcessing(true);
    setEmailError(null);

    try {
      // Request upgrade link via email
      const response = await fetch(`/api/wp-proxy/vendors/by-id/${vendor.id}/request-upgrade-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send upgrade link');
      }

      await response.json();
      setEmailSent(true);
      
      // Auto-close dialog after 5 seconds
      setTimeout(() => {
        setShowEmailDialog(false);
        router.push('/vendors');
      }, 5000);
    } catch (err) {
      console.error('Error requesting upgrade link:', err);
      setEmailError(err instanceof Error ? err.message : 'Failed to send upgrade link');
    } finally {
      setIsProcessing(false);
    }
  };

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#5cc8ff]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#eff1f4] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        {/* Hero Header */}
        <div className="mb-12">
          <div className="max-w-2xl">
            {vendor && (
              <div className="mb-6">
                <h1 className="text-5xl font-bold text-gray-900 mb-4">{getVendorName(vendor)}</h1>
                <div className="inline-flex items-center gap-2 bg-gray-100 px-4 py-2 rounded-full text-sm text-gray-600 mb-6">
                  <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                  Current: Basic Vendor Account
                </div>
              </div>
            )}
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to unlock premium features?
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Transform your tourism business with enhanced visibility, priority placement, and
              powerful marketing tools designed for industry leaders.
            </p>
          </div>
        </div>

        {/* Price Card */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-8">
          <div className="bg-gradient-to-r from-[#5cc8ff] to-[#4bb8e8] px-8 py-6">
            <h3 className="text-2xl font-bold text-[#3d405b]">Vendor Premium</h3>
            <div className="mt-4 flex items-baseline">
              <span className="text-5xl font-extrabold text-[#3d405b]">$99</span>
              <span className="text-xl text-[#3d405b]/80 ml-2">/month</span>
            </div>
          </div>

          <div className="px-8 py-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
              Everything you need to succeed:
            </h3>

            <ul className="space-y-4">
              <li className="flex items-start">
                <svg
                  className="w-6 h-6 text-green-500 mr-3 flex-shrink-0 mt-0.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <div>
                  <p className="font-medium text-gray-900">Priority Search Placement</p>
                  <p className="text-gray-600 text-sm mt-1">Appear at the top of search results</p>
                </div>
              </li>

              <li className="flex items-start">
                <svg
                  className="w-6 h-6 text-green-500 mr-3 flex-shrink-0 mt-0.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <div>
                  <p className="font-medium text-gray-900">Social Media Integration</p>
                  <p className="text-gray-600 text-sm mt-1">
                    Display all your social links on your profile
                  </p>
                </div>
              </li>

              <li className="flex items-start">
                <svg
                  className="w-6 h-6 text-green-500 mr-3 flex-shrink-0 mt-0.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <div>
                  <p className="font-medium text-gray-900">Resource Downloads</p>
                  <p className="text-gray-600 text-sm mt-1">
                    Upload brochures, guides, and other materials
                  </p>
                </div>
              </li>

              <li className="flex items-start">
                <svg
                  className="w-6 h-6 text-green-500 mr-3 flex-shrink-0 mt-0.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <div>
                  <p className="font-medium text-gray-900">Lead Generation Forms</p>
                  <p className="text-gray-600 text-sm mt-1">Capture customer inquiries directly</p>
                </div>
              </li>

              <li className="flex items-start">
                <svg
                  className="w-6 h-6 text-green-500 mr-3 flex-shrink-0 mt-0.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <div>
                  <p className="font-medium text-gray-900">Enhanced Profile Features</p>
                  <p className="text-gray-600 text-sm mt-1">Cover photos, team members, and more</p>
                </div>
              </li>

              <li className="flex items-start">
                <svg
                  className="w-6 h-6 text-green-500 mr-3 flex-shrink-0 mt-0.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <div>
                  <p className="font-medium text-gray-900">Premium Support</p>
                  <p className="text-gray-600 text-sm mt-1">
                    Priority customer support when you need it
                  </p>
                </div>
              </li>
            </ul>

            {/* Error Messages */}
            {error && (
              <div className="mt-8 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-800">{error}</p>
                <Link href="/vendors" className="mt-2 inline-block text-red-600 underline">
                  Back to vendors
                </Link>
              </div>
            )}

            {/* CTA Button */}
            {vendor && !error && (
              <div className="mt-8">
                <button
                  onClick={handleUpgradeClick}
                  disabled={!vendor || vendor.vendor_meta?.is_paid || isProcessing}
                  className="w-full bg-[#5cc8ff] text-[#3d405b] py-3 px-6 rounded-full font-semibold text-lg hover:bg-[#5cc8ff]/80 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  {isProcessing
                    ? 'Processing...'
                    : vendor.vendor_meta?.is_paid
                      ? 'Already Subscribed'
                      : 'Upgrade Now - $99/month'}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Additional Info */}
        <div className="text-center space-y-4">
          <p className="text-gray-600">
            <svg
              className="w-5 h-5 inline mr-2 text-green-500"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            Secure payment via Stripe
          </p>

          <p className="text-gray-600">Cancel anytime from your account settings</p>

          <div className="pt-4">
            <Link href="/vendors" className="text-[#5cc8ff] hover:text-[#4bb8e8] font-medium">
              ← Back to Vendor Directory
            </Link>
          </div>
        </div>
      </div>

      {/* Email Verification Dialog */}
      {showEmailDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full p-8 shadow-2xl">
            {!emailSent ? (
              <>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Verify Your Email</h3>
                <p className="text-gray-600 mb-6">
                  We'll send a secure payment link to your email address. This link will be valid for 24 hours.
                </p>
                
                <div className="mb-6">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value);
                      setEmailError(null);
                    }}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#5cc8ff] focus:border-[#5cc8ff] transition-colors"
                    disabled={isProcessing}
                  />
                  {emailError && (
                    <p className="mt-2 text-sm text-red-600">{emailError}</p>
                  )}
                </div>

                <div className="flex gap-4">
                  <button
                    onClick={() => setShowEmailDialog(false)}
                    disabled={isProcessing}
                    className="flex-1 px-6 py-3 border border-gray-300 rounded-full font-medium text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleEmailSubmit}
                    disabled={!email || isProcessing}
                    className="flex-1 bg-[#5cc8ff] text-[#3d405b] px-6 py-3 rounded-full font-medium hover:bg-[#5cc8ff]/80 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {isProcessing ? (
                      <>
                        <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                        </svg>
                        Sending...
                      </>
                    ) : (
                      'Send Upgrade Link'
                    )}
                  </button>
                </div>

                <p className="mt-4 text-xs text-gray-500 text-center">
                  By continuing, you agree to receive an email with payment instructions.
                </p>
              </>
            ) : (
              <div className="text-center">
                <div className="mb-4">
                  <svg className="w-16 h-16 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Email Sent!</h3>
                <p className="text-gray-600 mb-6">
                  We've sent an upgrade link to <strong>{email}</strong>. Please check your inbox and click the link to complete your upgrade.
                </p>
                <p className="text-sm text-gray-500">
                  Redirecting you back to vendors page...
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default function VendorUpgradePage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-[#eff1f4] flex items-center justify-center">Loading...</div>
      }
    >
      <VendorUpgradeContent />
    </Suspense>
  );
}
