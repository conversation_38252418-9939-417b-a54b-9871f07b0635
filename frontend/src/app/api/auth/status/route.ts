import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Define interface for WordPress API status
interface WPApiStatus {
  status: number;
  ok: boolean;
  userId?: number;
  userName?: string;
  error?: string;
}

export async function GET() {
  const cookieStore = await cookies();
  const allCookies = cookieStore.getAll();

  // Create cookie header string
  const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

  const authCookies = {
    wordpress_logged_in: allCookies
      .filter((c) => c.name.startsWith('wordpress_logged_in_'))
      .map((c) => c.name),
  };

  // Check authentication status with WordPress if we have cookies
  let wpApiStatus: WPApiStatus = { status: 0, ok: false };
  if (authCookies.wordpress_logged_in.length > 0) {
    try {
      const WORDPRESS_API_URL =
        process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8000';

      // Use cookie header for authentication
      const headers: HeadersInit = {
        Cookie: cookieHeader,
      };

      const wpResponse = await fetch(`${WORDPRESS_API_URL}/wp-json/wp/v2/users/me`, {
        method: 'GET',
        headers,
        // credentials: 'include' is not needed for server-side fetch when manually setting Cookie header
      });

      wpApiStatus = {
        status: wpResponse.status,
        ok: wpResponse.ok,
      };

      if (wpResponse.ok) {
        const userData = await wpResponse.json();
        wpApiStatus.userId = userData.id;
        wpApiStatus.userName = userData.name;
      } else {
        await wpResponse.text();
      }
    } catch {
      wpApiStatus = {
        status: 500,
        ok: false,
        error: 'Failed to check WordPress authentication',
      };
    }
  }

  return NextResponse.json({
    isAuthenticated: !!(authCookies.wordpress_logged_in.length > 0),
    cookies: authCookies,
    wpApiStatus,
  });
}
