import { NextResponse, NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json();

    if (!username || !password) {
      return NextResponse.json(
        { success: false, message: 'Username and password are required' },
        { status: 400 }
      );
    }

    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
    if (!WORDPRESS_API_URL) {
      console.error('NEXT_PUBLIC_WORDPRESS_API_URL is not defined');
      return NextResponse.json(
        { success: false, message: 'Server configuration error' },
        { status: 500 }
      );
    }

    // New WordPress endpoint for cookie-based login
    const cookieLoginEndpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/login`;

    const wpResponse = await fetch(cookieLoginEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forward original cookies if any are relevant, though for login, usually not critical
        // However, some WP security plugins might expect this for CSRF or other checks.
        ...(request.headers.get('cookie') ? { Cookie: request.headers.get('cookie')! } : {}),
      },
      body: JSON.stringify({
        username,
        password,
      }),
    });

    const responseData = await wpResponse.json();

    if (!wpResponse.ok || !responseData.success) {
      console.error('WordPress cookie login failed:', responseData);
      return NextResponse.json(
        {
          success: false,
          message: responseData.message || 'Login failed',
          code: responseData.code || 'wordpress_login_error',
        },
        { status: wpResponse.status }
      );
    }

    // WordPress has set its httpOnly auth cookies.
    // We need to relay any Set-Cookie headers from WordPress to the client.
    const headers = new Headers();

    // Iterate over Set-Cookie headers from WordPress response and append them
    // For Node.js runtime, response.headers.raw()['set-cookie'] would be an array.
    // For standard Fetch API, multiple Set-Cookie headers should be accessible by iterating.
    // However, direct iteration over headers for a specific key is not straightforward with standard Headers API.
    // A common workaround is to check if the environment provides a way to get raw headers or if a library handles it.
    // For Next.js, let's try a more direct pass-through or specific handling if available.

    // The `Headers` object in the Fetch API spec does not combine multiple headers of the same name
    // when using `get()`. `getAll()` is the standards-compliant way but not universally available on Request.Headers.
    // For Response.Headers, `forEach` can be used.
    wpResponse.headers.forEach((value, key) => {
      if (key.toLowerCase() === 'set-cookie') {
        headers.append('Set-Cookie', value);
      }
    });

    if (!headers.has('Set-Cookie')) {
      console.warn(
        "WordPress login endpoint did not return Set-Cookie header or it wasn't relayed."
      );
    }

    // Do NOT set auth_token or is_logged_in cookies here anymore.

    return NextResponse.json(
      {
        success: true,
        message: 'Login successful',
        user: responseData.user, // User data from our custom WP endpoint
      },
      { status: 200, headers: headers }
    );
  } catch (error) {
    console.error('Login API route error:', error);
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { success: false, message: 'Invalid request body' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { success: false, message: 'An unexpected error occurred during login' },
      { status: 500 }
    );
  }
}
