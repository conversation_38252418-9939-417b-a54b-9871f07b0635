import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { firstName, lastName, username, email, password, newsletterOptIn } = await request.json();

    if (!firstName || !lastName || !username || !email || !password) {
      return NextResponse.json(
        { message: 'First name, last name, username, email, and password are required' },
        { status: 400 }
      );
    }

    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
    if (!WORDPRESS_API_URL) {
      return NextResponse.json({ message: 'Server configuration error' }, { status: 500 });
    }

    // Check if there's a custom TourismIQ register endpoint first
    const customRegisterEndpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/register`;

    try {
      // Try the custom endpoint first
      const customResponse = await fetch(customRegisterEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName,
          lastName,
          username,
          email,
          password,
          newsletterOptIn,
        }),
      });

      const customResponseText = await customResponse.text();
      let customResponseData;
      
      try {
        customResponseData = JSON.parse(customResponseText);
      } catch (e) {
        console.error('Failed to parse custom endpoint response:', customResponseText);
        customResponseData = { message: 'Invalid response from server' };
      }

      if (customResponse.ok) {
        return NextResponse.json(
          {
            message: customResponseData.message || 'User created successfully',
            userId: customResponseData.userId || customResponseData.user_id,
          },
          { status: 201 }
        );
      }

      // Log the actual error for debugging
      console.error('Custom registration endpoint error:', customResponse.status, customResponseData);

      // If custom endpoint doesn't exist (404), fall back to WordPress default
      if (customResponse.status !== 404) {
        return NextResponse.json(
          {
            message: customResponseData.message || 'Registration failed',
            code: customResponseData.code || 'registration_error',
          },
          { status: customResponse.status }
        );
      }
    } catch (customError) {
      console.warn('Custom registration endpoint not available, falling back to WordPress API', customError);
    }

    // Fallback to WordPress default user creation endpoint
    const WP_USER = process.env.WORDPRESS_USERNAME;
    const WP_APP_PASSWORD_VALUE = process.env.WORDPRESS_APPLICATION_PASSWORD;

    if (!WP_USER || !WP_APP_PASSWORD_VALUE) {
      console.error('WordPress credentials not configured:', { 
        hasUser: !!WP_USER, 
        hasPassword: !!WP_APP_PASSWORD_VALUE 
      });
      return NextResponse.json(
        { message: 'Server authentication configuration error. Please check environment variables.' },
        { status: 500 }
      );
    }

    const basicAuthToken = Buffer.from(`${WP_USER}:${WP_APP_PASSWORD_VALUE}`).toString('base64');

    const response = await fetch(`${WORDPRESS_API_URL}/wp-json/wp/v2/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${basicAuthToken}`,
      },
      body: JSON.stringify({
        username,
        email,
        password,
        first_name: firstName,
        last_name: lastName,
        roles: ['member'], // Use custom member role instead of subscriber
      }),
    });

    const responseData = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        {
          message: responseData.message || 'Failed to create user in WordPress',
          code: responseData.code || 'wordpress_error',
        },
        { status: response.status }
      );
    }

    return NextResponse.json(
      { message: 'User created successfully', userId: responseData.id },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration API error:', error);
    if (error instanceof SyntaxError) {
      return NextResponse.json({ message: 'Invalid request body' }, { status: 400 });
    }
    return NextResponse.json({ message: 'An unexpected error occurred' }, { status: 500 });
  }
}
