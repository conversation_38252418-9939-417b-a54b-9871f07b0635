import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { key, login, password } = await request.json();

    if (!key || !login || !password) {
      return NextResponse.json(
        { message: 'Reset key, login, and new password are required' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (password.length < 6) {
      return NextResponse.json(
        { message: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
    if (!WORDPRESS_API_URL) {
      return NextResponse.json({ message: 'Server configuration error' }, { status: 500 });
    }

    // Call our custom WordPress reset password API
    const wpResponse = await fetch(
      `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/reset-password`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          key: key,
          login: login,
          password: password,
        }),
      }
    );

    const wpData = await wpResponse.json();

    if (!wpResponse.ok) {
      return NextResponse.json(
        { message: wpData.message || 'Failed to reset password. Please try again.' },
        { status: wpResponse.status }
      );
    }

    // Return success response
    return NextResponse.json(
      {
        message: wpData.message || 'Password reset successfully',
        success: wpData.success,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Reset password error:', error);
    return NextResponse.json(
      { message: 'An unexpected error occurred. Please try again.' },
      { status: 500 }
    );
  }
}
