import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    // Handle OAuth errors
    if (error) {
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set(
        'error',
        error === 'access_denied' ? 'Google login was cancelled' : 'Google login failed'
      );
      return NextResponse.redirect(redirectUrl);
    }

    if (!code) {
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('error', 'No authorization code received');
      return NextResponse.redirect(redirectUrl);
    }

    // Exchange code for tokens
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        code,
        client_id: process.env.GOOGLE_CLIENT_ID || '',
        client_secret: process.env.GOOGLE_CLIENT_SECRET || '',
        redirect_uri: `${new URL(request.url).origin}/api/auth/google/callback`,
        grant_type: 'authorization_code',
      }),
    });

    if (!tokenResponse.ok) {
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('error', 'Failed to exchange code for tokens');
      return NextResponse.redirect(redirectUrl);
    }

    const tokens = await tokenResponse.json();

    // Get user info from Google
    const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${tokens.access_token}`,
      },
    });

    if (!userResponse.ok) {
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('error', 'Failed to get user information');
      return NextResponse.redirect(redirectUrl);
    }

    const googleUser = await userResponse.json();

    // Prepare user data for WordPress
    const userData = {
      email: googleUser.email,
      first_name: googleUser.given_name || '',
      last_name: googleUser.family_name || '',
      display_name: googleUser.name || googleUser.email.split('@')[0],
      username: googleUser.email.split('@')[0],
      avatar_url: googleUser.picture || '',
      google_id: googleUser.id,
    };

    // Determine if this is a registration based on state
    const isRegistration = state?.includes('_register');

    // Call WordPress API to handle Google authentication
    const wpEndpoint = isRegistration
      ? `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/google-register`
      : `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/google-login`;

    const wpResponse = await fetch(wpEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    const wpData = await wpResponse.json();

    if (!wpResponse.ok) {
      const redirectUrl = new URL('/login', request.url);
      redirectUrl.searchParams.set('error', wpData.message || 'Authentication failed');
      return NextResponse.redirect(redirectUrl);
    }

    // If successful, redirect to home page
    const redirectUrl = new URL('/', request.url);
    const response = NextResponse.redirect(redirectUrl);

    // Set the WordPress auth cookie if provided
    if (wpData.cookie) {
      response.cookies.set('wordpress_logged_in', wpData.cookie, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      });
    }

    return response;
  } catch (error) {
    console.error('Google OAuth callback error:', error);
    const redirectUrl = new URL('/login', request.url);
    redirectUrl.searchParams.set('error', 'Authentication failed. Please try again.');
    return NextResponse.redirect(redirectUrl);
  }
}
