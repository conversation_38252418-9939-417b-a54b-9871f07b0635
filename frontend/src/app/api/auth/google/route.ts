import { NextRequest, NextResponse } from 'next/server';
import { OAuth2Client } from 'google-auth-library';

const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

export async function POST(request: NextRequest) {
  try {
    const { credential, isRegistration } = await request.json();

    if (!credential) {
      return NextResponse.json(
        { success: false, message: 'No credential provided' },
        { status: 400 }
      );
    }

    // Verify the Google token
    const ticket = await client.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();

    if (!payload) {
      return NextResponse.json(
        { success: false, message: 'Invalid Google token' },
        { status: 400 }
      );
    }

    const { email, name, given_name, family_name, picture } = payload;

    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email not provided by Google' },
        { status: 400 }
      );
    }

    // Prepare user data for WordPress
    const userData = {
      email,
      first_name: given_name || '',
      last_name: family_name || '',
      display_name: name || email.split('@')[0],
      username: email.split('@')[0], // Use email prefix as username
      avatar_url: picture || '',
      google_id: payload.sub,
    };

    // Call WordPress API to handle Google authentication
    const wpEndpoint = isRegistration
      ? `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/google-register`
      : `${process.env.NEXT_PUBLIC_WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/google-login`;

    const wpResponse = await fetch(wpEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    const wpData = await wpResponse.json();

    if (!wpResponse.ok) {
      return NextResponse.json(
        {
          success: false,
          message: wpData.message || 'Authentication failed',
        },
        { status: wpResponse.status }
      );
    }

    // If successful, set the auth cookie and return success
    const response = NextResponse.json({
      success: true,
      message: isRegistration ? 'Account created successfully!' : 'Login successful!',
      user: wpData.user,
    });

    // Set the WordPress auth cookie if provided
    if (wpData.cookie) {
      response.cookies.set('wordpress_logged_in', wpData.cookie, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      });
    }

    return response;
  } catch (error) {
    console.error('Google auth error:', error);

    if (error instanceof Error && error.message.includes('verify')) {
      return NextResponse.json(
        { success: false, message: 'Invalid Google token' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, message: 'Authentication failed. Please try again.' },
      { status: 500 }
    );
  }
}
