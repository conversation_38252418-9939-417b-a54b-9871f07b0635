import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST() {
  try {
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const logoutUrl = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/logout`;

    // Make a POST request to the WordPress logout endpoint
    // Crucially, we must forward the client's cookies so WordPress knows which session to log out
    const wpResponse = await fetch(logoutUrl, {
      method: 'POST',
      headers: {
        Cookie: cookieHeader, // Send existing cookies to WordPress
      },
    });

    // Prepare headers to send back to the client
    const responseHeaders = new Headers();

    // Relay Set-Cookie headers from WordPress (these will have past expiry dates)
    const setCookieHeader = wpResponse.headers.get('Set-Cookie');
    if (setCookieHeader) {
      // If there are multiple Set-Cookie headers, they might be combined or need individual handling.
      // For simplicity, we assume they are correctly formatted to be passed on.
      // Browsers should handle multiple Set-Cookie headers correctly if they are sent individually.
      // fetch() combines them into a single string, so we might need to split if WP sends multiple.
      // However, `wp_logout` typically sends multiple distinct `Set-Cookie` headers.
      // To handle this robustly, it's better to get all `Set-Cookie` headers if possible
      // or ensure the proxy can split and re-set them.
      // For now, let's try with get and see. Modern fetch might handle this better via iteration.

      // The `Headers` object in `node-fetch` (used by Next.js server-side) and standard Fetch API
      // concatenates multiple headers of the same name with `, `.
      // WordPress `wp_logout()` sends multiple `Set-Cookie` headers, not a single comma-separated one.
      // So, we need to iterate over them if the `Headers` object supports it, or use a workaround.
      // The standard `Headers.forEach` or iterating over `wpResponse.headers.entries()` is the way.

      wpResponse.headers.forEach((value, name) => {
        if (name.toLowerCase() === 'set-cookie') {
          responseHeaders.append('Set-Cookie', value);
        }
      });
    }

    if (!wpResponse.ok) {
      const errorData = await wpResponse
        .json()
        .catch(() => ({ message: 'Unknown WordPress logout error' }));
      return NextResponse.json(
        { success: false, message: errorData.message || 'WordPress logout failed' },
        { status: wpResponse.status, headers: responseHeaders } // Send any cookie clearing headers even on error
      );
    }

    // WordPress has successfully logged out and sent cookie-clearing headers.
    // Our proxy relays these headers.
    return NextResponse.json(
      { success: true, message: 'Logged out successfully' },
      { status: 200, headers: responseHeaders }
    );
  } catch (error) {
    console.error('[API Logout Error]', error);
    return NextResponse.json({ success: false, message: 'Logout request failed' }, { status: 500 });
  }
}
