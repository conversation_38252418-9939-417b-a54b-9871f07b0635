import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ message: 'Email is required' }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ message: 'Please enter a valid email address' }, { status: 400 });
    }

    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
    if (!WORDPRESS_API_URL) {
      return NextResponse.json({ message: 'Server configuration error' }, { status: 500 });
    }

    // Call our custom WordPress password reset API
    const wpResponse = await fetch(
      `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/forgot-password`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
        }),
      }
    );

    const wpData = await wpResponse.json();

    if (!wpResponse.ok) {
      return NextResponse.json(
        { message: wpData.message || 'Failed to send reset email. Please try again.' },
        { status: wpResponse.status }
      );
    }

    // Return the success message from WordPress
    return NextResponse.json(
      {
        message: wpData.message,
        success: wpData.success,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Forgot password error:', error);
    return NextResponse.json(
      { message: 'An unexpected error occurred. Please try again.' },
      { status: 500 }
    );
  }
}
