import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const { firstName, lastName, email, organization, website, message } = body;

    // Validate required fields
    if (!firstName || !lastName || !email || !organization) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Forward to WordPress REST API
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const response = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/vendor-suggestion`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('cookie') || '',
      },
      body: JSON.stringify({
        firstName,
        lastName,
        email,
        organization,
        website: website || '',
        message: message || '',
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('WordPress API error:', error);
      return NextResponse.json(
        { error: 'Failed to submit vendor suggestion' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error('Error processing vendor suggestion:', error);
    return NextResponse.json({ error: 'Failed to submit vendor suggestion' }, { status: 500 });
  }
}
