import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Check for WordPress authentication cookies
    const wordpressLoggedInCookie = request.cookies
      .getAll()
      .find((cookie) => cookie.name.startsWith('wordpress_logged_in_'));

    if (!wordpressLoggedInCookie) {
      return NextResponse.json({ message: 'Authentication required' }, { status: 401 });
    }

    // Get all cookies for WordPress cookie authentication
    const allCookies = request.cookies.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get the media file from form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ message: 'No file provided' }, { status: 400 });
    }

    // Get the WordPress API URL from environment variables
    let apiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    apiUrl = apiUrl.replace(/\/+$/, ''); // Remove trailing slashes
    if (!apiUrl.endsWith('/wp-json')) {
      apiUrl += '/wp-json';
    }

    // Create new form data to send to WordPress
    const wpFormData = new FormData();
    wpFormData.append('file', file);

    // Upload the media to WordPress
    const mediaResponse = await fetch(`${apiUrl}/wp/v2/media`, {
      method: 'POST',
      headers: {
        Cookie: cookieHeader,
      },
      body: wpFormData,
    });

    if (!mediaResponse.ok) {
      const errorData = await mediaResponse.text();
      console.error('Media Upload Error:', errorData);
      return NextResponse.json(
        { message: `Failed to upload media: ${mediaResponse.status} ${mediaResponse.statusText}` },
        { status: mediaResponse.status }
      );
    }

    const media = await mediaResponse.json();

    // Return the uploaded media
    return NextResponse.json(media, { status: 201 });
  } catch (error) {
    console.error('Error in posts/upload-media API route:', error);
    return NextResponse.json(
      { message: 'An error occurred while uploading media' },
      { status: 500 }
    );
  }
}
