import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Check for WordPress authentication cookies
    const wordpressLoggedInCookie = request.cookies
      .getAll()
      .find((cookie) => cookie.name.startsWith('wordpress_logged_in_'));

    if (!wordpressLoggedInCookie) {
      return NextResponse.json({ message: 'Authentication required' }, { status: 401 });
    }

    // Get all cookies for WordPress cookie authentication
    const allCookies = request.cookies.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get the request body
    const data = await request.json();
    const { postId, ...updateData } = data;

    if (!postId) {
      return NextResponse.json({ message: 'Post ID is required' }, { status: 400 });
    }

    // Get the WordPress API URL from environment variables
    let apiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    apiUrl = apiUrl.replace(/\/+$/, ''); // Remove trailing slashes
    if (!apiUrl.endsWith('/wp-json')) {
      apiUrl += '/wp-json';
    }

    // Update the post in WordPress
    const updateResponse = await fetch(`${apiUrl}/wp/v2/posts/${postId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: cookieHeader,
      },
      body: JSON.stringify(updateData),
    });

    if (!updateResponse.ok) {
      const errorData = await updateResponse.text();
      console.error('Post Update Error:', errorData);
      return NextResponse.json(
        { message: `Failed to update post: ${updateResponse.status} ${updateResponse.statusText}` },
        { status: updateResponse.status }
      );
    }

    const updatedPost = await updateResponse.json();

    // Return the updated post
    return NextResponse.json(updatedPost, { status: 200 });
  } catch (error) {
    console.error('Error in posts/update API route:', error);
    return NextResponse.json(
      { message: 'An error occurred while updating the post' },
      { status: 500 }
    );
  }
}
