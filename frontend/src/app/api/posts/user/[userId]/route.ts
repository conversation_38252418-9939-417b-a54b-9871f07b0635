import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest, context: { params: Promise<{ userId: string }> }) {
  try {
    const { userId } = await context.params;

    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    if (!wordPressCookies) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get WordPress API URL
    const wpApiUrl =
      process.env.WORDPRESS_API_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL ||
      'http://tourismiq.local';

    // Get query parameters for pagination
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const perPage = searchParams.get('per_page') || '10';

    // Build the WordPress API URL to fetch posts by author
    const wpUrl = `${wpApiUrl}/wp-json/wp/v2/posts?author=${userId}&page=${page}&per_page=${perPage}&_embed&orderby=date&order=desc`;

    // Forward the request to WordPress
    const wpResponse = await fetch(wpUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: wordPressCookies,
      },
    });

    if (!wpResponse.ok) {
      const errorData = await wpResponse.json().catch(() => ({ message: 'Request failed' }));
      console.error('WordPress API error:', wpResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to fetch posts from WordPress' },
        { status: wpResponse.status }
      );
    }

    const posts = await wpResponse.json();

    // Get pagination info from headers
    const totalPages = wpResponse.headers.get('X-WP-TotalPages');
    const total = wpResponse.headers.get('X-WP-Total');

    return NextResponse.json({
      posts,
      pagination: {
        page: parseInt(page),
        per_page: parseInt(perPage),
        total_pages: totalPages ? parseInt(totalPages) : 1,
        total: total ? parseInt(total) : posts.length,
      },
    });
  } catch (error) {
    console.error('Error fetching user posts:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
