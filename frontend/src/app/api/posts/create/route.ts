import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Check for WordPress authentication cookies
    const wordpressLoggedInCookie = request.cookies
      .getAll()
      .find((cookie) => cookie.name.startsWith('wordpress_logged_in_'));

    if (!wordpressLoggedInCookie) {
      return NextResponse.json({ message: 'Authentication required' }, { status: 401 });
    }

    // Get all cookies for WordPress cookie authentication
    const allCookies = request.cookies.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Check content type to determine how to parse the request
    const contentType = request.headers.get('content-type') || '';
    let title = '';
    let content = '';
    let featuredImage = null;

    // Handle multipart form data (for file uploads)
    let categories = [];
    if (contentType.includes('multipart/form-data')) {
      const formData = await request.formData();
      title = formData.get('title') as string;
      content = formData.get('content') as string;
      featuredImage = formData.get('featured_media') as File | null;

      // Extract categories from form data
      // FormData can have multiple entries with the same name (for arrays)
      for (const [key, value] of formData.entries()) {
        if (key.startsWith('categories[')) {
          categories.push(parseInt(value as string, 10));
        }
      }
    }
    // Handle JSON data
    else {
      const data = await request.json();
      title = data.title;
      content = data.content;
      categories = data.categories || [];
    }

    // Get the WordPress API URL from environment variables
    let apiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    apiUrl = apiUrl.replace(/\/+$/, ''); // Remove trailing slashes
    if (!apiUrl.endsWith('/wp-json')) {
      apiUrl += '/wp-json';
    }

    // Create the post in WordPress
    const postData: {
      title: string;
      content: string;
      status: string;
      categories?: number[];
    } = {
      title,
      content,
      status: 'publish',
    };

    // Add categories if provided
    if (categories.length > 0) {
      postData.categories = categories;
    }

    const postResponse = await fetch(`${apiUrl}/wp/v2/posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: cookieHeader,
      },
      body: JSON.stringify(postData),
    });

    if (!postResponse.ok) {
      const errorData = await postResponse.text();
      console.error('Post Creation Error:', errorData);
      return NextResponse.json(
        {
          message: `Failed to create post: ${postResponse.status} ${postResponse.statusText}`,
        },
        { status: postResponse.status }
      );
    }

    const post = await postResponse.json();

    // If we have a featured image, upload it and set it as the post's featured media
    if (featuredImage) {
      try {
        // First, upload the media file to WordPress
        // We need to convert the File object to a Blob with proper content type
        const arrayBuffer = await featuredImage.arrayBuffer();
        const blob = new Blob([arrayBuffer], { type: featuredImage.type });

        // Create form data for media upload
        const mediaFormData = new FormData();
        mediaFormData.append('file', blob, featuredImage.name);

        const mediaResponse = await fetch(`${apiUrl}/wp/v2/media`, {
          method: 'POST',
          headers: {
            Cookie: cookieHeader,
            // Do NOT set Content-Type header, the browser will set it with boundary
          },
          body: mediaFormData,
        });

        if (mediaResponse.ok) {
          const media = await mediaResponse.json();

          // Set the uploaded media as the post's featured media
          await fetch(`${apiUrl}/wp/v2/posts/${post.id}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Cookie: cookieHeader,
            },
            body: JSON.stringify({
              featured_media: media.id,
            }),
          });
        }
      } catch (mediaError) {
        console.error('Error uploading featured image:', mediaError);
        // Don't fail the post creation if media upload fails
      }
    }

    // Return the created post
    return NextResponse.json(post, { status: 201 });
  } catch (error) {
    console.error('Error in posts/create API route:', error);
    return NextResponse.json(
      { message: 'An error occurred while creating the post' },
      { status: 500 }
    );
  }
}
