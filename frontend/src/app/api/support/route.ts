/*********************************************
# frontend/src/app/api/support/route.ts
# 02/07/2025 12:55pm Created API route for support form submissions
# Updated to proxy to WordPress REST API
**********************************************/

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();

    // Extract data from form
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const subject = formData.get('subject') as string;
    const priority = formData.get('priority') as string;
    const description = formData.get('description') as string;

    // Handle file attachments
    const attachments: string[] = [];
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('attachment_') && value instanceof File) {
        attachments.push(value.name);
      }
    }

    // Validate required fields
    if (!name || !email || !subject || !priority || !description) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Prepare data for WordPress API
    const wpFormData = new FormData();
    wpFormData.append('name', name);
    wpFormData.append('email', email);
    wpFormData.append('subject', subject);
    wpFormData.append('priority', priority);
    wpFormData.append('description', description);

    // Add attachments
    let attachmentIndex = 0;
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('attachment_') && value instanceof File) {
        wpFormData.append(`attachment_${attachmentIndex}`, value);
        attachmentIndex++;
      }
    }

    // Forward to WordPress REST API
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const response = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/support`, {
      method: 'POST',
      body: wpFormData,
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('WordPress API error:', error);
      return NextResponse.json(
        { error: 'Failed to submit support request' },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error('Error processing support request:', error);
    return NextResponse.json({ error: 'Failed to submit support request' }, { status: 500 });
  }
}
