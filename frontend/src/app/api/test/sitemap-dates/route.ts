/**
 * Test route to check sitemap date formats
 * This helps debug date formatting issues in the sitemap
 */

import { getPostsForSitemap } from '@/lib/api';

export async function GET() {
  try {
    const posts = await getPostsForSitemap();
    
    // Check first 5 posts for date issues
    const samplePosts = posts.slice(0, 5).map(post => {
      const dateValue = post.modified || post.date;
      let parsedDate = null;
      let isValidDate = false;
      let isoDate = '';
      
      if (dateValue) {
        try {
          parsedDate = new Date(dateValue);
          isValidDate = !isNaN(parsedDate.getTime());
          if (isValidDate) {
            isoDate = parsedDate.toISOString();
          }
        } catch (error) {
          // Date parsing error
        }
      }
      
      return {
        slug: post.slug,
        originalDate: post.date,
        originalModified: post.modified,
        dateValue,
        isValidDate,
        isoDate: isoDate || 'INVALID',
        title: post.title?.rendered || post.title || 'No title'
      };
    });
    
    return Response.json({
      totalPosts: posts.length,
      samplePosts,
      allDatesValid: samplePosts.every(p => p.isValidDate)
    });
  } catch (error) {
    return Response.json({ 
      error: 'Failed to fetch posts',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}