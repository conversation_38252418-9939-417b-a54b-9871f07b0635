import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * GET user metadata by user ID
 * This endpoint fetches a user's metadata from WordPress using the user ID
 */
export async function GET(_request: Request, { params }: { params: Promise<{ id: string }> }) {
  // Await params in Next.js 15
  const { id: userId } = await params;

  try {
    // Get all cookies for WordPress cookie authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
    if (!wpApiUrl) {
      return NextResponse.json({ error: 'WordPress API URL not configured' }, { status: 500 });
    }

    // Setup headers for WordPress API request - only using Cookie header for authentication
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: cookieHeader,
    };

    // Request user data with ACF fields
    const wpEndpoint = `${wpApiUrl}/wp-json/wp/v2/users/${userId}?context=edit&acf_format=standard`;

    const wpResponse = await fetch(wpEndpoint, {
      method: 'GET',
      headers,
    });

    // Handle authentication errors
    if (wpResponse.status === 401) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Handle other API errors
    if (!wpResponse.ok) {
      await wpResponse.text();
      return NextResponse.json(
        { error: `WordPress API error: ${wpResponse.status}` },
        { status: wpResponse.status }
      );
    }

    // Parse and return the user data
    const userData = await wpResponse.json();

    return NextResponse.json(userData);
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to fetch user metadata',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
