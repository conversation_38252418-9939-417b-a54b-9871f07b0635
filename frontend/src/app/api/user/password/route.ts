import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * PUT update user password
 * This endpoint updates the user's password in WordPress
 */
export async function PUT(request: Request) {
  try {
    // Get all cookies for WordPress cookie authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
    if (!wpApiUrl) {
      return NextResponse.json({ error: 'WordPress API URL not configured' }, { status: 500 });
    }

    // Parse the password data from the request
    const { currentPassword, newPassword } = await request.json();

    // Validate required fields
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { error: 'Current password and new password are required' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: 'New password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Headers for WordPress API requests
    const headers = {
      'Content-Type': 'application/json',
      Cookie: cookieHeader,
    };

    // First, verify the current password by trying to authenticate
    const authResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/auth/verify-password`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        password: currentPassword,
      }),
    });

    if (!authResponse.ok) {
      const errorText = await authResponse.text();
      console.error(
        'PASSWORD API: Current password verification failed:',
        authResponse.status,
        errorText
      );

      if (authResponse.status === 401 || authResponse.status === 403) {
        return NextResponse.json({ error: 'Current password is incorrect' }, { status: 401 });
      }

      return NextResponse.json(
        { error: 'Failed to verify current password' },
        { status: authResponse.status }
      );
    }

    // Now update the password
    const updateResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/users/me`, {
      method: 'POST', // WordPress uses POST with /users/me even for updates
      headers,
      body: JSON.stringify({
        password: newPassword,
      }),
    });

    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      console.error('PASSWORD API: Password update error:', updateResponse.status, errorText);
      return NextResponse.json(
        {
          error: `Failed to update password: ${updateResponse.statusText || 'WordPress API error'}`,
          details: errorText,
        },
        { status: updateResponse.status }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Password updated successfully',
    });
  } catch (error) {
    console.error('PASSWORD API: Unexpected error:', error);
    return NextResponse.json(
      {
        error: 'Failed to update password',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
