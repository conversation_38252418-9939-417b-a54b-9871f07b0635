import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { User } from '@/lib/api';

/**
 * PUT update user profile data
 * This endpoint updates user profile data in WordPress
 * It updates both standard WordPress user fields and ACF fields
 */
export async function PUT(request: Request) {
  try {
    // Get all cookies for WordPress cookie authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
    if (!wpApiUrl) {
      return NextResponse.json({ error: 'WordPress API URL not configured' }, { status: 500 });
    }

    // Parse the user data from the request
    const userData: Partial<User> = await request.json();

    // Prepare WordPress core user fields
    const wpUserFields: Record<string, string> = {};
    if (userData.firstName !== undefined) wpUserFields.first_name = userData.firstName || '';
    if (userData.lastName !== undefined) wpUserFields.last_name = userData.lastName || '';

    // Prepare ACF fields (not WordPress core fields)
    const acfFields: Record<string, string> = {};
    if (userData.bio !== undefined) acfFields.bio = userData.bio || '';
    if (userData.jobTitle !== undefined) acfFields.job_title = userData.jobTitle || '';
    if (userData.company !== undefined) acfFields.company = userData.company || '';
    if (userData.location !== undefined) acfFields.location = userData.location || '';
    if (userData.phone !== undefined) acfFields.phone = userData.phone || '';
    if (userData.website !== undefined) acfFields.website = userData.website || '';

    // Handle cover image updates
    if (userData.coverImage !== undefined) {
      if (userData.coverImage === null) {
        // Delete cover image by setting to empty string
        acfFields.cover_image = '';
      } else if (typeof userData.coverImage === 'string') {
        // Set cover image URL
        acfFields.cover_image = userData.coverImage;
      }
    }

    // Handle social links with correct field names
    if (userData.socialLinks) {
      if (userData.socialLinks.twitter !== undefined) {
        acfFields.twitter = userData.socialLinks.twitter || '';
      }
      if (userData.socialLinks.facebook !== undefined) {
        acfFields.facebook = userData.socialLinks.facebook || '';
      }
      if (userData.socialLinks.linkedin !== undefined) {
        acfFields.linkedin = userData.socialLinks.linkedin || '';
      }
      if (userData.socialLinks.instagram !== undefined) {
        acfFields.instagram = userData.socialLinks.instagram || '';
      }
    }

    // Headers for WordPress API requests - only using Cookie header for authentication
    const headers = {
      'Content-Type': 'application/json',
      Cookie: cookieHeader,
    };

    // First update WordPress core user fields if any
    if (Object.keys(wpUserFields).length > 0) {
      const wpUserResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/users/me`, {
        method: 'POST', // WordPress uses POST with /users/me even for updates
        headers,
        body: JSON.stringify(wpUserFields),
      });

      if (!wpUserResponse.ok) {
        const errorText = await wpUserResponse.text();
        console.error(
          'PROFILE API: WordPress core fields update error:',
          wpUserResponse.status,
          errorText
        );
        return NextResponse.json(
          {
            success: false,
            error: `Failed to update profile: ${
              wpUserResponse.statusText || 'WordPress core API error'
            }`,
            details: errorText,
          },
          { status: wpUserResponse.status }
        );
      }
    }

    // Then update ACF fields if any
    if (Object.keys(acfFields).length > 0) {
      try {
        const wpAcfResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/users/me/acf`, {
          method: 'POST',
          headers,
          body: JSON.stringify({
            fields: acfFields,
          }),
        });

        if (!wpAcfResponse.ok) {
          const errorText = await wpAcfResponse.text();
          console.error('PROFILE API: ACF update error:', wpAcfResponse.status, errorText);
          return NextResponse.json(
            {
              success: false,
              error: `Failed to update profile: ${wpAcfResponse.statusText || 'ACF API error'}`,
              details: errorText,
            },
            { status: wpAcfResponse.status }
          );
        }

        // ACF update was successful
        return NextResponse.json({
          success: true,
          message: 'Profile updated successfully',
        });
      } catch (error) {
        console.error('PROFILE API: Error updating ACF fields:', error);
        return NextResponse.json(
          {
            success: false,
            error: 'Failed to update profile',
          },
          { status: 500 }
        );
      }
    } else {
      // No fields to update
      return NextResponse.json({
        success: true,
        message: 'No fields to update',
      });
    }
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update profile',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
