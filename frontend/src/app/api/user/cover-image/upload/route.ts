/*********************************************
# frontend/src/app/api/user/cover-image/upload/route.ts
# 02/07/2025 11:50pm Created file - API endpoint for cover image uploads
**********************************************/

import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * POST cover image upload
 * This endpoint handles user cover image uploads
 * It performs the following steps:
 * 1. Upload the image to WordPress media library
 * 2. Associate the uploaded media with the user's cover_image ACF field
 */
export async function POST(request: Request) {
  try {
    // Get all cookies for WordPress cookie authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
    if (!wpApiUrl) {
      return NextResponse.json(
        { success: false, error: 'WordPress API URL not configured' },
        { status: 500 }
      );
    }

    // Parse the form data with the file
    const formData = await request.formData();
    const file = formData.get('coverImage') as File;

    // Validate file presence
    if (!file) {
      return NextResponse.json({ success: false, error: 'No file provided' }, { status: 400 });
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: 'File size exceeds 5MB limit' },
        { status: 400 }
      );
    }

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid file type. Please upload a JPG, PNG, GIF, or WebP image.',
        },
        { status: 400 }
      );
    }

    // Prepare headers for WordPress API requests - only using Cookie header for authentication
    const headers = {
      Cookie: cookieHeader,
    };

    // Create a new FormData to send to WordPress
    const wpFormData = new FormData();
    wpFormData.append('file', file);

    // Step 1: Upload the file to the WordPress media library
    const uploadResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/media`, {
      method: 'POST',
      headers,
      body: wpFormData,
    });

    // Handle upload errors
    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();

      // Extract error message
      let errorMessage = 'Failed to upload image to WordPress';
      try {
        const errorJson = JSON.parse(errorText);
        if (errorJson.message) {
          errorMessage = errorJson.message;
        }
      } catch {
        errorMessage = uploadResponse.statusText || errorMessage;
      }

      return NextResponse.json(
        { success: false, error: errorMessage },
        { status: uploadResponse.status }
      );
    }

    // Parse the media response
    const media = await uploadResponse.json();

    // Step 2: Update the user's ACF cover_image field with the media ID
    const updateResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/users/me/acf`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fields: {
          cover_image: media.id,
        },
      }),
    });

    // Handle update errors
    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();

      // Extract error message
      let errorMessage = 'Failed to update profile with new cover image';
      try {
        const errorJson = JSON.parse(errorText);
        if (errorJson.message) {
          errorMessage = errorJson.message;
        }
      } catch {
        errorMessage = updateResponse.statusText || errorMessage;
      }

      // We still return success=true because the media upload worked, but include warning
      return NextResponse.json({
        success: true,
        warning: errorMessage,
        url: media.source_url,
        mediaId: media.id,
      });
    }

    // Success - both upload and profile update worked
    await updateResponse.json();

    // Return the media URL for the frontend
    return NextResponse.json({
      success: true,
      url: media.source_url,
      mediaId: media.id,
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process cover image upload',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
