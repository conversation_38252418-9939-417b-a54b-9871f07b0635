import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * POST cover image upload
 * This endpoint handles user cover image uploads
 * It performs the following steps:
 * 1. Upload the image to WordPress media library
 * 2. Associate the uploaded media with the user's cover_image ACF field
 */
export async function POST(request: Request) {
  try {
    // Get all cookies for WordPress cookie authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
    if (!wpApiUrl) {
      return NextResponse.json(
        { success: false, error: 'WordPress API URL not configured' },
        { status: 500 }
      );
    }

    // Parse the form data with the file
    const formData = await request.formData();
    const file = formData.get('cover_image') as File;

    console.log('Cover upload - FormData keys:', Array.from(formData.keys()));
    console.log('Cover upload - File:', file);
    console.log('Cover upload - File type:', file?.type);
    console.log('Cover upload - File size:', file?.size);

    // Validate file presence
    if (!file) {
      console.log('Cover upload - No file found in FormData');
      return NextResponse.json({ success: false, error: 'No file provided' }, { status: 400 });
    }

    // Validate file size (5MB limit to match avatar upload)
    if (file.size > 5 * 1024 * 1024) {
      console.log('Cover upload - File too large:', file.size, 'bytes');
      return NextResponse.json(
        { success: false, error: 'File size exceeds 5MB limit' },
        { status: 400 }
      );
    }

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      console.log('Cover upload - Invalid file type:', file.type, 'Valid types:', validTypes);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid file type. Please upload a JPG, PNG, GIF, or WebP image.',
        },
        { status: 400 }
      );
    }

    console.log('Cover upload - File validation passed');

    // Prepare headers for WordPress API requests - only using Cookie header for authentication
    const headers = {
      Cookie: cookieHeader,
    };

    // Create a new FormData to send to WordPress
    const wpFormData = new FormData();
    wpFormData.append('file', file);

    // Step 1: Upload the file to the WordPress media library
    const uploadResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/media`, {
      method: 'POST',
      headers,
      body: wpFormData,
    });

    // Handle upload errors
    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();

      // Extract error message
      let errorMessage = 'Failed to upload image to WordPress';
      try {
        const errorJson = JSON.parse(errorText);
        if (errorJson.message) {
          errorMessage = errorJson.message;
        }
      } catch {
        errorMessage = uploadResponse.statusText || errorMessage;
      }

      return NextResponse.json(
        { success: false, error: errorMessage },
        { status: uploadResponse.status }
      );
    }

    // Parse the media response
    const media = await uploadResponse.json();
    console.log('Cover upload - Media uploaded successfully:', {
      id: media.id,
      url: media.source_url,
      title: media.title?.rendered || 'No title',
    });

    // First, let's check which user we're updating
    const userCheckResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/users/me`, {
      method: 'GET',
      headers,
    });

    if (userCheckResponse.ok) {
      const currentUser = await userCheckResponse.json();
      console.log('Cover upload - Current user ID:', currentUser.id, 'Username:', currentUser.slug);
    }

    // Step 2: Update the user's ACF cover_image field with the media ID
    console.log('Cover upload - Updating ACF field with media ID:', media.id);
    const updateResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/users/me/acf`, {
      method: 'POST',
      headers: {
        ...headers,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fields: {
          cover_image: media.id,
        },
      }),
    });

    console.log('Cover upload - ACF update response status:', updateResponse.status);

    // Handle update errors
    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      console.log('Cover upload - ACF update failed:', {
        status: updateResponse.status,
        statusText: updateResponse.statusText,
        errorText: errorText,
      });

      // Extract error message
      let errorMessage = 'Failed to update profile with new cover image';
      try {
        const errorJson = JSON.parse(errorText);
        if (errorJson.message) {
          errorMessage = errorJson.message;
        }
      } catch {
        errorMessage = updateResponse.statusText || errorMessage;
      }

      // We still return success=true because the media upload worked, but include warning
      return NextResponse.json({
        success: true,
        warning: errorMessage,
        url: media.source_url,
        mediaId: media.id,
      });
    }

    // Success - both upload and profile update worked
    const acfUpdateResult = await updateResponse.json();
    console.log('Cover upload - ACF update successful:', acfUpdateResult);

    // Return the media URL for the frontend
    return NextResponse.json({
      success: true,
      url: media.source_url,
      mediaId: media.id,
    });
  } catch (error) {
    console.error('Cover upload error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process cover image upload',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
