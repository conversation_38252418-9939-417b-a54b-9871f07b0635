import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { WPUser } from '@/lib/api';

/**
 * GET current user data
 * This endpoint is a proxy to the WordPress /wp-json/wp/v2/users/me endpoint
 * Returns user data in a standardized format
 */
export async function GET() {
  try {
    // Get authentication cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Check for WordPress authentication cookie
    const hasWordpressCookie = allCookies.some((c) => c.name.startsWith('wordpress_logged_in_'));

    if (!hasWordpressCookie) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Setup request headers - only using Cookie header for authentication
    const headers: HeadersInit = {
      Cookie: cookieHeader,
    };

    // Get WordPress URL from env or use default
    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8000';

    // Forward request to WordPress REST API
    const response = await fetch(`${WORDPRESS_API_URL}/wp-json/wp/v2/users/me`, {
      headers,
      // credentials: 'include' is not needed for server-side fetch when manually setting Cookie header
    });

    // Handle authentication errors
    if (response.status === 401) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Handle other WordPress API errors
    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        {
          error: `WordPress API returned ${response.status}`,
          details: errorText,
        },
        { status: response.status }
      );
    }

    // Parse the WordPress response
    const wpUser: WPUser = await response.json();

    // Debug log for ACF data availability
    if (process.env.NODE_ENV !== 'production') {
      if (wpUser.acf?.profile_picture) {
      } else if (wpUser.avatar_urls) {
      }
    }

    // Return the raw WordPress user data
    // The transformation will happen client-side in the transformWPUserToUser function
    return NextResponse.json(wpUser);
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to fetch user data',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
