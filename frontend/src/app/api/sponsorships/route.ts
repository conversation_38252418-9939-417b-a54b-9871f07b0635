/*********************************************
# frontend/src/app/api/sponsorships/route.ts
# 02/06/2025 5:45pm Created sponsorship API route
**********************************************/

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('Sponsorship API route called');
    const body = await request.json();
    console.log('Request body:', body);

    const { companyName, contactName, email, phone, budget, message } = body;

    // Validate required fields
    if (!companyName || !contactName || !email || !budget || !message) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Forward to WordPress REST API
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    console.log('WordPress API URL:', wpApiUrl);

    const response = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/sponsorship`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        companyName,
        contactName,
        email,
        phone: phone || '',
        budget,
        message,
      }),
    });

    console.log('WordPress response status:', response.status);

    if (!response.ok) {
      const error = await response.text();
      console.error('WordPress API error:', error);
      return NextResponse.json(
        { error: 'Failed to submit sponsorship inquiry' },
        { status: response.status }
      );
    }

    const result = await response.json();
    console.log('WordPress response:', result);
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error('Error processing sponsorship inquiry:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Add a GET method for testing
export async function GET() {
  return NextResponse.json({ message: 'Sponsorship API route is working' }, { status: 200 });
}
