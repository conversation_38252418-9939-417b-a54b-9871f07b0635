import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { decodeHtmlEntities } from '@/lib/utils';

// Simple in-memory cache for search results
const searchCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Rate limiting storage
const rateLimit = new Map<string, { count: number; resetTime: number; lastRequest: number }>();
const RATE_LIMIT_MAX = 30; // 30 requests per minute (reduced from 50)
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const MIN_REQUEST_INTERVAL = 200; // Minimum 200ms between requests (reduced)

interface SearchResults {
  posts: any[];
  users: any[];
  vendors: any[];
  forum: any[];
  rfps: any[];
  jobs: any[];
  total: number;
  totalPosts?: number;
  hasMore?: boolean;
}

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
const WP_API_URL = `${WP_BASE_URL}/wp-json`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q')?.trim();
    const typesParam = searchParams.get('types');
    const limitParam = searchParams.get('limit');

    // Validate query
    if (!query || query.length < 2) {
      return NextResponse.json({
        posts: [],
        users: [],
        vendors: [],
        forum: [],
        rfps: [],
        jobs: [],
        total: 0,
      });
    }

    // Rate limiting with burst protection
    const clientIP =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    const now = Date.now();
    const userRateLimit = rateLimit.get(clientIP);

    if (userRateLimit) {
      // Check minimum interval between requests
      if (now - userRateLimit.lastRequest < MIN_REQUEST_INTERVAL) {
        return NextResponse.json(
          { error: 'Too many requests too quickly. Please wait.' },
          {
            status: 429,
            headers: { 'Retry-After': Math.ceil(MIN_REQUEST_INTERVAL / 1000).toString() },
          }
        );
      }

      if (now < userRateLimit.resetTime) {
        if (userRateLimit.count >= RATE_LIMIT_MAX) {
          return NextResponse.json(
            { error: 'Rate limit exceeded' },
            {
              status: 429,
              headers: {
                'Retry-After': Math.ceil((userRateLimit.resetTime - now) / 1000).toString(),
              },
            }
          );
        }
        userRateLimit.count++;
        userRateLimit.lastRequest = now;
      } else {
        rateLimit.set(clientIP, { count: 1, resetTime: now + RATE_LIMIT_WINDOW, lastRequest: now });
      }
    } else {
      rateLimit.set(clientIP, { count: 1, resetTime: now + RATE_LIMIT_WINDOW, lastRequest: now });
    }

    // Parse search types and parameters
    const allowedTypes = ['posts', 'users', 'vendors', 'forum', 'rfps', 'jobs'];
    const types = typesParam
      ? typesParam.split(',').filter((type) => allowedTypes.includes(type))
      : allowedTypes;

    const limit = limitParam ? parseInt(limitParam) : 100; // WordPress max limit
    // Remove page parameter - no pagination

    console.log(
      `[SEARCH API] Starting search - Query: "${query}", Limit: ${limit}, Types: [${types.join(
        ', '
      )}]`
    );

    // Create cache key (no page since no pagination)
    const cacheKey = `${query}-${typesParam || 'all'}-${limitParam || '100'}`;

    // Check cache
    const cached = searchCache.get(cacheKey);
    if (cached && now - cached.timestamp < CACHE_TTL) {
      console.log(`[SEARCH API] Cache hit for key: ${cacheKey}`);
      return NextResponse.json(cached.data);
    }
    console.log(`[SEARCH API] Cache miss for key: ${cacheKey}`);

    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    // Setup headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: wordPressCookies,
    };

    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Create search promises for each content type
    const searchPromises: Promise<any>[] = [];
    const results: SearchResults = {
      posts: [],
      users: [],
      vendors: [],
      forum: [],
      rfps: [],
      jobs: [],
      total: 0,
    };

    // Search Posts - conditional behavior based on limit
    if (types.includes('posts')) {
      searchPromises.push(
        (async () => {
          try {
            // If limit is high (>=100), fetch ALL results. Otherwise, just fetch what's requested.
            const shouldFetchAll = limit >= 100;
            const postsPerPage = shouldFetchAll ? 100 : Math.min(limit, 100);

            const allPosts: any[] = [];

            // Fetch first page to get total
            const firstUrl = `${WP_API_URL}/wp/v2/posts?search=${encodeURIComponent(
              query
            )}&per_page=${postsPerPage}&page=1&_embed=1`;
            console.log(
              `[SEARCH API] Fetching posts page 1 from: ${firstUrl} (fetchAll: ${shouldFetchAll})`
            );

            const firstResponse = await fetch(firstUrl, { headers, credentials: 'include' });
            if (!firstResponse.ok) {
              results.posts = [];
              results.totalPosts = 0;
              return;
            }

            const firstPosts = await firstResponse.json();
            const totalPosts = parseInt(firstResponse.headers.get('X-WP-Total') || '0');
            const totalPages = parseInt(firstResponse.headers.get('X-WP-TotalPages') || '1');

            console.log(`[SEARCH API] Posts total: ${totalPosts}, pages: ${totalPages}`);
            allPosts.push(...firstPosts);

            // Fetch ALL pages in parallel if we want all results
            if (shouldFetchAll && totalPages > 1) {
              console.log(`[SEARCH API] Fetching ${totalPages - 1} additional pages in parallel`);

              // Create all requests at once for parallel execution
              const allPageRequests = [];
              for (let p = 2; p <= Math.min(totalPages, 10); p++) {
                // Limit to 10 pages max for safety
                const pageUrl = `${WP_API_URL}/wp/v2/posts?search=${encodeURIComponent(
                  query
                )}&per_page=100&page=${p}&_embed=1`;
                allPageRequests.push(
                  fetch(pageUrl, { headers, credentials: 'include' })
                    .then((res) => (res.ok ? res.json() : []))
                    .catch(() => [])
                );
              }

              // Execute all requests in parallel
              if (allPageRequests.length > 0) {
                const allAdditionalPosts = await Promise.all(allPageRequests);
                allAdditionalPosts.forEach((posts) => {
                  if (Array.isArray(posts)) {
                    allPosts.push(...posts);
                  }
                });
              }
            }

            // Limit results if not fetching all
            const finalPosts = shouldFetchAll ? allPosts : allPosts.slice(0, limit);

            console.log(
              `[SEARCH API] Posts result: ${finalPosts.length} posts found, total available: ${totalPosts}`
            );

            results.posts = finalPosts.map((post: any) => ({
              id: post.id,
              title: decodeHtmlEntities(post.title?.rendered || ''),
              excerpt: decodeHtmlEntities(post.excerpt?.rendered?.replace(/<[^>]*>/g, '') || ''),
              link: `/posts/${post.slug}`,
              date: post.date,
              author: post._embedded?.author?.[0]?.name || '',
              featured_media: post._embedded?.['wp:featuredmedia']?.[0]?.source_url || '',
              type: 'post',
            }));

            results.totalPosts = totalPosts;
          } catch (error) {
            console.error('Error fetching posts:', error);
            results.posts = [];
            results.totalPosts = 0;
          }
        })()
      );
    }

    // Search Users - use direct WordPress endpoint with search
    if (types.includes('users')) {
      searchPromises.push(
        fetch(
          `${WP_API_URL}/tourismiq/v1/members?per_page=${limit}&search=${encodeURIComponent(
            query
          )}`,
          {
            headers,
            credentials: 'include',
          }
        )
          .then((res) => {
            if (!res.ok) {
              console.log('User search failed:', res.status, res.statusText);
              return { founders: [], members: [] };
            }
            return res.json();
          })
          .then((membersData) => {
            const allMembers = [...(membersData.founders || []), ...(membersData.members || [])];

            results.users = allMembers.slice(0, limit).map((member: any) => ({
              id: member.id,
              name: member.name,
              username: member.username,
              avatar_url: member.avatarUrl || member.avatar || '',
              job_title: member.job_title || '',
              company: member.company || '',
              link: `/profile/${member.username || member.slug}`,
              type: 'user',
            }));
          })
          .catch((error) => {
            console.error('User search error:', error);
            results.users = [];
          })
      );
    }

    // Search Vendors - search both regular and paid vendors
    if (types.includes('vendors')) {
      const vendorSearchPromises = [
        // Search regular vendors
        fetch(`${WP_API_URL}/wp/v2/vendors?search=${encodeURIComponent(query)}&per_page=${limit}`, {
          headers,
          credentials: 'include',
        }).then((res) => (res.ok ? res.json() : [])),

        // Search paid vendors separately
        fetch(`${WP_API_URL}/tourismiq/v1/vendors/paid`, {
          headers,
          credentials: 'include',
        }).then((res) => (res.ok ? res.json() : [])),
      ];

      searchPromises.push(
        Promise.all(vendorSearchPromises)
          .then(([regularVendors, paidVendors]) => {
            // Filter paid vendors by search query
            const filteredPaidVendors = paidVendors.filter((vendor: any) => {
              const searchLower = query.toLowerCase();
              return (
                vendor.title?.rendered?.toLowerCase().includes(searchLower) ||
                vendor.content?.rendered?.toLowerCase().includes(searchLower) ||
                vendor.excerpt?.rendered?.toLowerCase().includes(searchLower)
              );
            });

            // Combine and deduplicate vendors
            const allVendors = [...regularVendors];
            const regularIds = new Set(regularVendors.map((v: any) => v.id));

            filteredPaidVendors.forEach((vendor: any) => {
              if (!regularIds.has(vendor.id)) {
                allVendors.push(vendor);
              }
            });

            results.vendors = allVendors.slice(0, limit).map((vendor: any) => ({
              id: vendor.id,
              title: decodeHtmlEntities(vendor.title?.rendered || ''),
              excerpt: decodeHtmlEntities(vendor.excerpt?.rendered?.replace(/<[^>]*>/g, '') || ''),
              link: `/vendors/${vendor.slug}`,
              categories: vendor.vendor_categories?.map((cat: any) => cat.name) || [],
              website: vendor.vendor_meta?.website || '',
              is_paid: vendor.vendor_meta?.is_paid || false,
              featured_media: vendor.vendor_meta?.logo_url || vendor.featured_media || '',
              type: 'vendor',
            }));
          })
          .catch(() => {
            results.vendors = [];
          })
      );
    }

    // Search Forum
    if (types.includes('forum')) {
      searchPromises.push(
        fetch(
          `${WP_API_URL}/tourismiq/v1/forum?per_page=${limit}&search=${encodeURIComponent(query)}`,
          {
            headers,
            credentials: 'include',
          }
        )
          .then((res) => (res.ok ? res.json() : { questions: [] }))
          .then((forumData) => {
            const forumPosts = forumData.questions || [];

            results.forum = forumPosts.slice(0, limit).map((post: any) => ({
              id: post.id,
              question: decodeHtmlEntities(post.title?.rendered || ''),
              content: decodeHtmlEntities(
                post.content?.rendered?.replace(/<[^>]*>/g, '').substring(0, 150) || ''
              ),
              author: post.author_info?.name || '',
              link: `/forum/${post.id}`,
              votes: 0,
              comments_count: parseInt(post.total_comments) || 0,
              date: post.date,
              type: 'forum',
            }));
          })
          .catch(() => {
            results.forum = [];
          })
      );
    }

    // Search RFPs
    if (types.includes('rfps')) {
      searchPromises.push(
        fetch(
          `${WP_API_URL}/tourismiq/v1/rfps/search?search=${encodeURIComponent(
            query
          )}&per_page=${limit}`,
          {
            headers,
            credentials: 'include',
          }
        )
          .then((res) => (res.ok ? res.json() : { rfps: [] }))
          .then((rfpData) => {
            const rfps = rfpData.rfps || [];

            results.rfps = rfps.map((rfp: any) => ({
              id: rfp.id,
              title: decodeHtmlEntities(rfp.title?.rendered || ''),
              description: decodeHtmlEntities(
                rfp.content?.rendered?.replace(/<[^>]*>/g, '').substring(0, 150) || ''
              ),
              location:
                [rfp.rfp_states?.[0], rfp.rfp_countries?.[0]].filter(Boolean).join(', ') || '',
              category: rfp.rfp_categories?.[0] || '',
              company: rfp.rfp_meta?.rfp_organization || '',
              budget: '',
              deadline: rfp.rfp_meta?.rfp_deadline || '',
              status: rfp.rfp_meta?.rfp_status || '',
              date: rfp.date,
              link: `/rfps/${rfp.slug}`,
              type: 'rfp',
            }));
          })
          .catch(() => {
            results.rfps = [];
          })
      );
    }

    // Search Jobs
    if (types.includes('jobs')) {
      searchPromises.push(
        fetch(
          `${WP_API_URL}/wp/v2/jobs?search=${encodeURIComponent(query)}&per_page=${limit}&_embed=1`,
          {
            headers,
            credentials: 'include',
          }
        )
          .then((res) => (res.ok ? res.json() : []))
          .then((jobs) => {
            results.jobs = jobs.map((job: any) => ({
              id: job.id,
              title: decodeHtmlEntities(job.title?.rendered || ''),
              excerpt: decodeHtmlEntities(job.excerpt?.rendered?.replace(/<[^>]*>/g, '') || ''),
              content: decodeHtmlEntities(
                job.content?.rendered?.replace(/<[^>]*>/g, '').substring(0, 150) || ''
              ),
              link: `/jobs/${job.slug}`,
              date: job.date,
              author: job._embedded?.author?.[0]?.name || '',
              featured_media: job._embedded?.['wp:featuredmedia']?.[0]?.source_url || '',
              location:
                [job.job_states?.[0], job.job_countries?.[0]].filter(Boolean).join(', ') || '',
              company: job.job_meta?.job_organization || '',
              job_type: job.acf?.job_type || '',
              salary_range: job.acf?.salary_range || '',
              deadline: job.job_meta?.job_deadline || '',
              status: job.job_meta?.job_status || '',
              type: 'job',
            }));
          })
          .catch(() => {
            results.jobs = [];
          })
      );
    }

    // Wait for all searches to complete
    await Promise.all(searchPromises);

    // Debug: Log all result counts
    console.log(`[SEARCH API] Results breakdown:`, {
      posts: results.posts.length,
      users: results.users.length,
      vendors: results.vendors.length,
      forum: results.forum.length,
      rfps: results.rfps.length,
      jobs: results.jobs.length,
      limit,
    });

    // Calculate total results
    const actualPostsTotal = results.totalPosts || results.posts.length;
    results.total =
      actualPostsTotal +
      results.users.length +
      results.vendors.length +
      results.forum.length +
      results.rfps.length +
      results.jobs.length;

    // Remove hasMore since no pagination
    const totalResults =
      results.posts.length +
      results.users.length +
      results.vendors.length +
      results.forum.length +
      results.rfps.length +
      results.jobs.length;

    console.log(`[SEARCH API] Final result: ${totalResults} total items, total: ${results.total}`);

    // Cache the results
    searchCache.set(cacheKey, { data: results, timestamp: now });

    // Clean up old cache entries (simple cleanup every 100 requests)
    if (Math.random() < 0.01) {
      for (const [key, value] of searchCache.entries()) {
        if (now - value.timestamp > CACHE_TTL) {
          searchCache.delete(key);
        }
      }
    }

    return NextResponse.json(results);
  } catch (error) {
    console.error('Error in unified search:', error);
    return NextResponse.json(
      {
        posts: [],
        users: [],
        vendors: [],
        forum: [],
        rfps: [],
        jobs: [],
        total: 0,
        error: 'Search failed',
      },
      { status: 500 }
    );
  }
}
