import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Get all cookies for WordPress cookie authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Check for WordPress authentication cookie
    const hasWordpressCookie = allCookies.some((c) => c.name.startsWith('wordpress_logged_in_'));

    if (!hasWordpressCookie) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get WordPress API URL
    const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const WP_API_URL = `${WP_BASE_URL}/wp-json`;

    // Get the form data from the request
    const formData = await request.formData();

    // Create a new FormData object for the WordPress API
    const wpFormData = new FormData();

    // Copy all form fields to the new FormData
    for (const [key, value] of formData.entries()) {
      wpFormData.append(key, value);
    }

    // Setup headers for WordPress API request
    const headers: HeadersInit = {
      Cookie: cookieHeader,
      // Don't set Content-Type for FormData - let the browser set it with boundary
    };

    // Upload to WordPress media endpoint
    const response = await fetch(`${WP_API_URL}/wp/v2/media`, {
      method: 'POST',
      headers,
      body: wpFormData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        {
          error: 'Failed to upload file',
          details: errorText,
          status: response.status,
        },
        { status: response.status }
      );
    }

    const mediaData = await response.json();

    return NextResponse.json({
      success: true,
      media: mediaData,
      url: mediaData.source_url,
      id: mediaData.id,
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to upload file',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
