import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  try {
    const { sessionId } = await params;

    if (!sessionId) {
      console.error('No session ID provided');
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Clean session ID of any URL encoding and trailing characters
    const cleanSessionId = decodeURIComponent(sessionId).replace(/[{}]/g, '').trim();

    // Initialize Stripe with your secret key
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error(
        'STRIPE_SECRET_KEY not configured - available env vars:',
        Object.keys(process.env).filter((k) => k.includes('STRIPE'))
      );
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });
    }

    const Stripe = (await import('stripe')).default;
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

    // Retrieve the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(cleanSessionId);

    if (session.payment_status === 'paid') {
      // Get vendor ID and optionally user ID from client_reference_id
      const clientReferenceId = session.client_reference_id;

      if (!clientReferenceId) {
        console.error('No client_reference_id found in session');
        return NextResponse.json(
          {
            success: false,
            message: 'No reference ID found in payment session',
          },
          { status: 400 }
        );
      }

      // Support both formats: "vendorId" (old) and "vendorId:userId" (new)
      const [vendorId, userId] = clientReferenceId.split(':');

      if (!vendorId) {
        console.error('No vendor ID found in client_reference_id');
        return NextResponse.json(
          {
            success: false,
            message: 'No vendor ID found in payment session',
          },
          { status: 400 }
        );
      }

      if (vendorId) {
        const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

        // First, get the vendor details
        const vendorResponse = await fetch(
          `${wpApiUrl}/wp-json/tourismiq/v1/vendors/by-id/${vendorId}`
        );

        if (!vendorResponse.ok) {
          const vendorError = await vendorResponse.text();
          console.error('Failed to fetch vendor details:', vendorResponse.status, vendorError);
          throw new Error(
            `Failed to fetch vendor details: ${vendorResponse.status} ${vendorError}`
          );
        }

        const vendor = await vendorResponse.json();

        // Update vendor to paid status via our proxy endpoint
        const baseUrl =
          process.env.NEXT_PUBLIC_APP_URL ||
          'https://hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com';
        const updateUrl = `${baseUrl}/api/wp-proxy/vendor-upgrade/${vendorId}`;

        const updatePayload = {
          session_id: sessionId,
          payment_status: session.payment_status,
          subscription_start: new Date().toISOString(),
          stripe_customer_id: session.customer,
          stripe_subscription_id: session.subscription,
          amount_total: session.amount_total,
          currency: session.currency,
          paying_user_id: userId || null,
        };

        const updateResponse = await fetch(updateUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatePayload),
        });

        if (!updateResponse.ok) {
          const updateError = await updateResponse.text();
          console.error('Failed to update vendor paid status:', updateResponse.status, updateError);
          throw new Error(`Failed to update vendor: ${updateResponse.status} ${updateError}`);
        }
        updateResponse.json();

        return NextResponse.json({
          success: true,
          vendorSlug: vendor.data?.slug || vendor.slug,
          vendorName: vendor.data?.title?.rendered || vendor.title?.rendered,
          message: 'Vendor successfully upgraded to paid status',
        });
      }
    }

    return NextResponse.json(
      {
        success: false,
        message: 'Payment not completed or vendor ID not found',
      },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error verifying Stripe session:', error);
    return NextResponse.json({ error: 'Failed to verify session' }, { status: 500 });
  }
}
