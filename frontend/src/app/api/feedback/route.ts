/*********************************************
# frontend/src/app/api/feedback/route.ts
# 02/07/2025 12:55pm Created API route for feedback form submissions
# Updated to proxy to WordPress REST API
**********************************************/

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const { name, email, category, rating, feedback } = body;

    // Validate required fields
    if (!name || !email || !category || !rating || !feedback) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Forward to WordPress REST API
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const response = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name,
        email,
        category,
        rating,
        feedback,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('WordPress API error:', error);
      return NextResponse.json({ error: 'Failed to submit feedback' }, { status: response.status });
    }

    const result = await response.json();
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error('Error processing feedback:', error);
    return NextResponse.json({ error: 'Failed to submit feedback' }, { status: 500 });
  }
}
