import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

interface MemberData {
  id: number;
  name?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  slug?: string;
  avatarUrl?: string;
  avatar?: string;
  profile_picture?: string | null;
  job_title?: string;
  company?: string;
  bio?: string;
  roles?: string[];
}

interface MembersResponse {
  founders?: MemberData[];
  members?: MemberData[];
}

export async function GET(request: NextRequest) {
  try {
    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    if (!wordPressCookies) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Get search query from URL parameters
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');

    if (!query || query.length < 2) {
      return NextResponse.json([]);
    }

    // Use the custom members endpoint instead of wp/v2/users to avoid permission issues
    const searchUrl = `${wpApiUrl}/wp-json/tourismiq/v1/members?per_page=50`;

    const wpResponse = await fetch(searchUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: wordPressCookies,
      },
    });

    if (!wpResponse.ok) {
      const errorData = await wpResponse.json().catch(() => ({ message: 'Search failed' }));
      return NextResponse.json(
        { error: errorData.message || 'Failed to search users' },
        { status: wpResponse.status }
      );
    }

    const membersData: MembersResponse = await wpResponse.json();

    // The custom endpoint returns { founders: [], members: [] } - combine them
    const allMembers = [...(membersData.founders || []), ...(membersData.members || [])];

    // Filter members based on search query
    const filteredMembers = allMembers.filter((member: MemberData) => {
      const searchLower = query.toLowerCase();
      const nameMatch = member.name?.toLowerCase().includes(searchLower) || false;
      const usernameMatch = member.username?.toLowerCase().includes(searchLower) || false;
      const firstNameMatch = member.firstName?.toLowerCase().includes(searchLower) || false;
      const lastNameMatch = member.lastName?.toLowerCase().includes(searchLower) || false;
      const jobTitleMatch = member.job_title?.toLowerCase().includes(searchLower) || false;
      const companyMatch = member.company?.toLowerCase().includes(searchLower) || false;

      return (
        nameMatch ||
        usernameMatch ||
        firstNameMatch ||
        lastNameMatch ||
        jobTitleMatch ||
        companyMatch
      );
    });

    // Transform to match VendorTeamMember interface
    const transformedUsers = filteredMembers.slice(0, 10).map((member: MemberData) => ({
      id: member.id,
      name: member.name,
      first_name: member.firstName || '',
      last_name: member.lastName || '',
      username: member.username,
      slug: member.slug || member.username,
      email: '', // Don't expose email in search results
      avatar_url: member.avatarUrl || member.avatar || '',
      profile_picture: member.profile_picture || null,
      job_title: member.job_title || '',
      company: member.company || '',
      bio: member.bio || '',
      roles: member.roles || ['member'],
    }));

    return NextResponse.json(transformedUsers);
  } catch (error) {
    console.error('Error searching users:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
