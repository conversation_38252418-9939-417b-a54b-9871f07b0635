import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Await params in Next.js 15
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Get WordPress API URL with fallback
    const wpApiUrl =
      process.env.WORDPRESS_API_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL ||
      'http://tourismiq.local';

    // Make request to WordPress API
    const wpResponse = await fetch(
      `${wpApiUrl}/wp-json/tourismiq/v1/users/${userId}/comments-count`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!wpResponse.ok) {
      const errorData = await wpResponse.json().catch(() => ({ message: 'Request failed' }));
      console.error('WordPress API error:', wpResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to get user comments count' },
        { status: wpResponse.status }
      );
    }

    const data = await wpResponse.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('User comments count error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
