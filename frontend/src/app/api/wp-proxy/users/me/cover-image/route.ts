import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  console.log('Cover image upload endpoint called');
  try {
    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const wpCookies = cookieStore
      .getAll()
      .filter((cookie) => cookie.name.startsWith('wordpress_') || cookie.name.startsWith('wp-'));

    // Get form data from the request
    const formData = await request.formData();

    // Create new FormData for WordPress
    const wpFormData = new FormData();

    // Transfer the file
    const coverImage = formData.get('cover_image');
    if (coverImage instanceof File) {
      wpFormData.append('cover_image', coverImage);
    } else {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Forward the request to WordPress
    const wpUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const response = await fetch(`${wpUrl}/wp-json/tourismiq/v1/users/me/cover-image`, {
      method: 'POST',
      headers: {
        Cookie: wpCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; '),
      },
      body: wpFormData,
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error uploading cover image:', error);
    return NextResponse.json({ error: 'Failed to upload cover image' }, { status: 500 });
  }
}
