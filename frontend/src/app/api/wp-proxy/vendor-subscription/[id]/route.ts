import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id: vendorId } = await params;

  try {
    // Get WordPress API URL from environment
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Forward request to WordPress with credentials
    const response = await fetch(
      `${wpApiUrl}/wp-json/tourismiq/v1/vendors/${vendorId}/subscription-status`,
      {
        method: 'GET',
        headers: {
          Cookie: request.headers.get('cookie') || '',
          'User-Agent': request.headers.get('user-agent') || '',
          Accept: 'application/json',
        },
      }
    );

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch subscription status' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Subscription status proxy error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
