import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id: vendorId } = await params;

  try {
    // Get WordPress API URL from environment
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // First, get the vendor details to get the slug
    const vendorResponse = await fetch(
      `${wpApiUrl}/wp-json/tourismiq/v1/vendors/by-id/${vendorId}`,
      {
        headers: {
          Cookie: request.headers.get('cookie') || '',
          'User-Agent': request.headers.get('user-agent') || '',
          Accept: 'application/json',
        },
      }
    );

    let returnUrl = `${request.nextUrl.origin}/vendors?upgraded=true`; // fallback URL

    if (vendorResponse.ok) {
      const vendor = await vendorResponse.json();
      if (vendor.slug) {
        returnUrl = `${request.nextUrl.origin}/vendors/${vendor.slug}?upgraded=true`;
      }
    }

    // Forward request to WordPress with credentials
    const response = await fetch(
      `${wpApiUrl}/wp-json/tourismiq/v1/vendors/${vendorId}/customer-portal`,
      {
        method: 'POST',
        headers: {
          Cookie: request.headers.get('cookie') || '',
          'User-Agent': request.headers.get('user-agent') || '',
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          return_url: returnUrl,
        }),
      }
    );

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to create customer portal session' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Customer portal proxy error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
