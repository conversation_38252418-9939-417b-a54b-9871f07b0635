import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    const { userIds } = await request.json();

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'userIds array is required' }, { status: 400 });
    }

    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    if (!wordPressCookies) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpApiUrl}/wp-json/tourismiq/v1/connections/bulk-status`;

    // Make request to WordPress API
    const wpResponse = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: wordPressCookies,
      },
      body: JSON.stringify({ userIds }),
    });

    if (!wpResponse.ok) {
      const errorData = await wpResponse.json().catch(() => ({ message: 'Request failed' }));
      console.error(`[Bulk Connections Status] WordPress API error:`, wpResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to get bulk connection status' },
        { status: wpResponse.status }
      );
    }

    const data = await wpResponse.json();
    // Don't cache bulk connection status as it needs to be real-time
    const response = NextResponse.json(data);
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    return response;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[Bulk Connections Status] Error after ${duration}ms:`, error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
