import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ otherUserId: string }> }
) {
  const startTime = Date.now();

  // Await params in Next.js 15
  const { otherUserId } = await params;

  if (!otherUserId || isNaN(parseInt(otherUserId))) {
    console.error('[Connections Status] Invalid user ID:', otherUserId);
    return NextResponse.json({ error: 'Valid user ID is required' }, { status: 400 });
  }

  try {
    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    if (!wordPressCookies) {
      console.error('[Connections Status] No authentication cookies found');
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get WordPress API URL - use consistent pattern with messaging routes
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpApiUrl}/wp-json/tourismiq/v1/connections/status/${otherUserId}`;

    // Create timeout controller
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.error(`[Connections Status] Request timeout for user ${otherUserId} after 8 seconds`);
      controller.abort();
    }, 8000);

    // Make request to WordPress API with timeout
    const wpResponse = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: wordPressCookies,
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!wpResponse.ok) {
      let errorData;
      try {
        errorData = await wpResponse.json();
      } catch (jsonError) {
        console.error(`[Connections Status] JSON parse error for user ${otherUserId}:`, jsonError);
        errorData = { message: 'Request failed' };
      }

      console.error(
        `[Connections Status] WordPress API error for user ${otherUserId}:`,
        wpResponse.status,
        errorData
      );
      return NextResponse.json(
        { error: errorData.message || 'Failed to get connection status' },
        { status: wpResponse.status }
      );
    }

    let data;
    try {
      data = await wpResponse.json();
    } catch (jsonError) {
      console.error(`[Connections Status] JSON parse error for user ${otherUserId}:`, jsonError);
      return NextResponse.json({ error: 'Invalid response format' }, { status: 502 });
    }

    // Don't cache connection status responses as they need to be real-time
    const response = NextResponse.json(data);
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    return response;
  } catch (error) {
    const duration = Date.now() - startTime;

    if (error instanceof Error && error.name === 'AbortError') {
      console.error(
        `[Connections Status] Request aborted for user ${otherUserId} after ${duration}ms`
      );
      return NextResponse.json({ error: 'Request timeout' }, { status: 504 });
    }

    console.error(`[Connections Status] Error for user ${otherUserId} after ${duration}ms:`, error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
