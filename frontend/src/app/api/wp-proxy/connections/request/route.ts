import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    if (!wordPressCookies) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get WordPress API URL - use consistent pattern with messaging routes
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Make request to WordPress API
    const wpResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/connections/request`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: wordPressCookies,
      },
      body: JSON.stringify({ user_id: userId }), // WordPress expects user_id, not userId
    });

    if (!wpResponse.ok) {
      const errorData = await wpResponse.json().catch(() => ({ message: 'Request failed' }));
      console.error('WordPress API error:', wpResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to send connection request' },
        { status: wpResponse.status }
      );
    }

    const data = await wpResponse.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Connection request error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
