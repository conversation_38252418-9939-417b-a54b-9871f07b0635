import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    if (!wordPressCookies) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get WordPress API URL - use consistent pattern with messaging routes
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Get userId from query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    // Build the API URL - if userId is provided, get that user's connections, otherwise get current user's
    const apiEndpoint = userId
      ? `${wpApiUrl}/wp-json/tourismiq/v1/connections?userId=${userId}`
      : `${wpApiUrl}/wp-json/tourismiq/v1/connections`;

    // Forward the request to WordPress
    const wpResponse = await fetch(apiEndpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: wordPressCookies,
      },
    });

    if (!wpResponse.ok) {
      const errorData = await wpResponse.json().catch(() => ({ message: 'Request failed' }));
      console.error('WordPress API error:', wpResponse.status, errorData);

      return NextResponse.json(
        { error: errorData.message || 'Failed to fetch connections from WordPress' },
        { status: wpResponse.status }
      );
    }

    const connections = await wpResponse.json();
    return NextResponse.json(connections);
  } catch (error) {
    console.error('Error fetching connections:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
