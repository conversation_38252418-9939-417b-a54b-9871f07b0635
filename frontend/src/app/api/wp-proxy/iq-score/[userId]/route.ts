import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Add timeout utility
const fetchWithTimeout = async (url: string, options: RequestInit, timeout = 10000) => {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(id);
    return response;
  } catch (error) {
    clearTimeout(id);
    throw error;
  }
};

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  const startTime = Date.now();
  let resolvedParams;

  try {
    // Await params in Next.js 15 with error handling
    try {
      resolvedParams = await params;
    } catch (paramError) {
      console.error('Error resolving params:', paramError);
      return NextResponse.json({ error: 'Invalid request parameters' }, { status: 400 });
    }

    const { userId } = resolvedParams;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Validate userId is a number
    const userIdNum = parseInt(userId, 10);
    if (isNaN(userIdNum) || userIdNum <= 0) {
      return NextResponse.json({ error: 'Invalid user ID format' }, { status: 400 });
    }

    // Get all cookies for WordPress authentication
    let cookieHeader = '';
    try {
      const cookieStore = await cookies();
      const allCookies = cookieStore.getAll();
      cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');
    } catch (cookieError) {
      console.error('Error reading cookies:', cookieError);
      // Continue without cookies - API might still work for public data
    }

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpApiUrl}/wp-json/tourismiq/v1/iq-score/${userIdNum}`;

    // Forward the request to WordPress with timeout
    let response;
    try {
      response = await fetchWithTimeout(
        apiUrl,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Cookie: cookieHeader,
            'User-Agent': request.headers.get('user-agent') || '',
            'X-Forwarded-For': request.headers.get('x-forwarded-for') || '',
            'X-Real-IP': request.headers.get('x-real-ip') || '',
          },
        },
        8000
      ); // 8 second timeout
    } catch (fetchError: unknown) {
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('WordPress API timeout after 8s:', apiUrl);
        return NextResponse.json(
          { error: 'Request timeout - WordPress API did not respond in time' },
          { status: 504 }
        );
      }
      console.error('WordPress API fetch error:', fetchError);
      return NextResponse.json(
        {
          error: 'Failed to connect to WordPress API',
          details: fetchError instanceof Error ? fetchError.message : 'Unknown error',
        },
        { status: 503 }
      );
    }

    // Try to parse response
    let data;
    const responseText = await response.text();

    try {
      data = JSON.parse(responseText);
    } catch {
      console.error('Failed to parse WordPress response:', responseText.substring(0, 200));
      return NextResponse.json(
        { error: 'Invalid response from WordPress API', details: 'Response was not valid JSON' },
        { status: 502 }
      );
    }

    if (!response.ok) {
      console.error('WordPress API error:', response.status, data);
      return NextResponse.json(
        { error: 'Failed to fetch user IQ score', details: data },
        { status: response.status }
      );
    }

    // Add cache headers for successful responses
    const headers = new Headers();
    headers.set('Cache-Control', 'private, max-age=60'); // Cache for 1 minute

    return NextResponse.json(data, { headers });
  } catch (error: unknown) {
    const duration = Date.now() - startTime;
    console.error('Unexpected error in IQ score proxy after', duration, 'ms:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userId: resolvedParams?.userId,
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
