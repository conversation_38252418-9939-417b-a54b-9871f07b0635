import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET() {
  const debugInfo = {
    timestamp: new Date().toISOString(),
    environment: {
      nodeEnv: process.env.NODE_ENV,
      wpApiUrl: process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local',
    },
    cookies: {},
    tests: [] as Array<{ name: string; status: string; details?: string }>,
    memory: {} as Record<string, string>,
    summary: {} as Record<string, unknown>,
  };

  try {
    // Test 1: Check cookie access
    try {
      const cookieStore = await cookies();
      const allCookies = cookieStore.getAll();
      debugInfo.cookies = {
        count: allCookies.length,
        hasAuth: allCookies.some((c) => c.name.includes('wordpress_logged_in')),
      };
      debugInfo.tests.push({ name: 'Cookie Access', status: 'passed' });
    } catch (error: unknown) {
      debugInfo.tests.push({
        name: 'Cookie Access',
        status: 'failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 2: Check WordPress API connectivity
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    try {
      const testUrl = `${wpApiUrl}/wp-json/tourismiq/v1/debug/cors`;
      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(5000),
      });

      debugInfo.tests.push({
        name: 'WordPress API Connectivity',
        status: response.ok ? 'passed' : 'failed',
        details: `Status: ${response.status}`,
      });
    } catch (error: unknown) {
      debugInfo.tests.push({
        name: 'WordPress API Connectivity',
        status: 'failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 3: Test a sample IQ score request
    try {
      const testUserId = 1; // Admin user typically exists
      const testUrl = `${wpApiUrl}/wp-json/tourismiq/v1/iq-score/${testUserId}`;

      const cookieStore = await cookies();
      const allCookies = cookieStore.getAll();
      const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Cookie: cookieHeader,
        },
        signal: AbortSignal.timeout(5000),
      });

      const responseText = await response.text();
      let data;
      try {
        data = JSON.parse(responseText);
      } catch {
        data = { parseError: true, responseText: responseText.substring(0, 100) };
      }

      debugInfo.tests.push({
        name: 'Sample IQ Score Request',
        status: response.ok ? 'passed' : 'failed',
        details: `Status: ${response.status}, hasData: ${!!data}, dataType: ${typeof data}`,
      });
    } catch (error: unknown) {
      debugInfo.tests.push({
        name: 'Sample IQ Score Request',
        status: 'failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Test 4: Memory usage
    if (process.memoryUsage) {
      const memoryUsage = process.memoryUsage();
      debugInfo.memory = {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
        rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
      };
    }

    // Summary
    const failedTests = debugInfo.tests.filter((t) => t.status === 'failed');
    debugInfo.summary = {
      totalTests: debugInfo.tests.length,
      passed: debugInfo.tests.filter((t) => t.status === 'passed').length,
      failed: failedTests.length,
      issues: failedTests.map((t) => t.name),
    };

    return NextResponse.json(debugInfo, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error: unknown) {
    return NextResponse.json(
      {
        error: 'Debug endpoint error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
