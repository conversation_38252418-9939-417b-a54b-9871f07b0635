import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { user_id, points, activity_type } = body;

    // Validate required fields
    if (!user_id || !points) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields: user_id and points' },
        { status: 400 }
      );
    }

    // Get WordPress API URL
    const wpApiUrl =
      process.env.WORDPRESS_API_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL ||
      'http://tourismiq.local';

    // Forward request to WordPress
    const wpResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/iq-score/award`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forward any authentication headers
        ...(request.headers.get('authorization') && {
          Authorization: request.headers.get('authorization')!,
        }),
        // Forward cookies for WordPress authentication
        ...(request.headers.get('cookie') && {
          Cookie: request.headers.get('cookie')!,
        }),
      },
      body: JSON.stringify({
        user_id,
        points,
        activity_type: activity_type || 'manual',
      }),
    });

    const result = await wpResponse.json();

    // Return the WordPress response
    return NextResponse.json(result, { status: wpResponse.status });
  } catch (error) {
    console.error('Error in IQ score award proxy:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
