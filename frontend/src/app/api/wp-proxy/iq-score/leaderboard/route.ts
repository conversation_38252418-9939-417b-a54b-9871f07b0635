import { NextRequest, NextResponse } from 'next/server';

// Get the WordPress API URL from environment variables
const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters from the request
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') || '20';
    const forceRefresh = searchParams.get('force_refresh');
    const cacheBuster = searchParams.get('_');

    // Build WordPress API URL with parameters
    let wpUrl = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/leaderboard?limit=${limit}`;
    if (forceRefresh) {
      wpUrl += `&force_refresh=${forceRefresh}`;
    }
    if (cacheBuster) {
      wpUrl += `&_=${cacheBuster}`;
    }

    // Get the cookie header from the request
    const cookieHeader = request.headers.get('cookie');

    // Make the request to WordPress
    const response = await fetch(wpUrl, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
        // Forward cookies for authentication
        ...(cookieHeader ? { Cookie: cookieHeader } : {}),
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      throw new Error(`WordPress API returned ${response.status}`);
    }

    // Parse the response
    const data = await response.json();

    // Return the response with proper headers
    const nextResponse = NextResponse.json(data);
    nextResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    nextResponse.headers.set('Pragma', 'no-cache');
    nextResponse.headers.set('Expires', '0');

    return nextResponse;
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch leaderboard',
        data: [],
        total: 0,
      },
      { status: 500 }
    );
  }
}
