import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    const { userIds } = await request.json();

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'userIds array is required' }, { status: 400 });
    }

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpApiUrl}/wp-json/tourismiq/v1/iq-score/bulk`;

    // Use basic auth with application password if available
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '',
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Make request to WordPress API
    const wpResponse = await fetch(apiUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({ userIds }),
    });

    if (!wpResponse.ok) {
      const errorData = await wpResponse.json().catch(() => ({ message: 'Request failed' }));
      console.error(`[Bulk IQ Score] WordPress API error:`, wpResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to get bulk IQ scores' },
        { status: wpResponse.status }
      );
    }

    const data = await wpResponse.json();
    // Add cache headers
    const response = NextResponse.json(data);
    response.headers.set('Cache-Control', 'private, max-age=300'); // 5 minutes cache for IQ scores
    return response;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[Bulk IQ Score] Error after ${duration}ms:`, error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
