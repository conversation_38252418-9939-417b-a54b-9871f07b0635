import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    // Get all cookies for WordPress authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpApiUrl}/wp-json/tourismiq/v1/iq-score/ranks`;

    // Forward the request to WordPress
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: cookieHeader,
        'User-Agent': request.headers.get('user-agent') || '',
        'X-Forwarded-For': request.headers.get('x-forwarded-for') || '',
        'X-Real-IP': request.headers.get('x-real-ip') || '',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('WordPress API error:', response.status, data);
      return NextResponse.json(
        { error: 'Failed to fetch ranks', details: data },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error proxying ranks request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
