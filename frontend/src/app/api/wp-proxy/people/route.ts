import { NextRequest, NextResponse } from 'next/server';
import { rateLimit, rateLimitConfigs } from '@/lib/rate-limit';

const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

export async function GET(request: NextRequest) {
  // Apply rate limiting
  const limiter = rateLimit(rateLimitConfigs.api);
  const rateLimitResult = limiter.check(request);

  if (!rateLimitResult.success) {
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      { status: 429 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const searchTerm = searchParams.get('search');
    const moveType = searchParams.get('move_type');
    const positionLevel = searchParams.get('position_level');
    const year = searchParams.get('year');
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = parseInt(searchParams.get('per_page') || '100');

    // Use simple WordPress pagination with proper sorting
    const queryParams = new URLSearchParams({
      _embed: 'true',
      per_page: perPage.toString(),
      page: page.toString(),
      orderby: 'date',
      order: 'desc',
    });

    if (searchTerm) {
      queryParams.set('search', searchTerm);
    }

    // Add meta query filters for ACF fields
    if (moveType && moveType !== 'all') {
      queryParams.set('meta_key', 'move_type');
      queryParams.set('meta_value', moveType);
    }

    // For multiple meta queries, we need to build a more complex query
    if ((moveType && moveType !== 'all') || (positionLevel && positionLevel !== 'all') || year) {
      // Use the custom endpoint for complex filtering
      const customParams = new URLSearchParams({
        per_page: perPage.toString(),
        page: page.toString(),
      });

      if (searchTerm) customParams.set('search', searchTerm);
      if (moveType && moveType !== 'all') customParams.set('move_type', moveType);
      if (positionLevel && positionLevel !== 'all')
        customParams.set('position_level', positionLevel);
      if (year) customParams.set('year', year);

      const endpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/people/filtered?${customParams.toString()}`;

      const response = await fetch(endpoint, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'User-Agent': 'TourismIQ-Frontend/1.0',
          Cookie: request.headers.get('cookie') || '',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        // Fallback to standard endpoint if custom endpoint fails
        console.warn('Custom endpoint failed, falling back to standard endpoint');
        return await fetchStandardEndpoint(request, queryParams);
      }

      const result = await response.json();
      return NextResponse.json(result);
    } else {
      return await fetchStandardEndpoint(request, queryParams);
    }
  } catch (error) {
    console.error('Error fetching people:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function fetchStandardEndpoint(request: NextRequest, queryParams: URLSearchParams) {
  const endpoint = `${WORDPRESS_API_URL}/wp-json/wp/v2/people?${queryParams.toString()}`;

  const response = await fetch(endpoint, {
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'User-Agent': 'TourismIQ-Frontend/1.0',
      Cookie: request.headers.get('cookie') || '',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    console.error('WordPress API error:', response.status, response.statusText);
    throw new Error(`Failed to fetch people: ${response.statusText}`);
  }

  const people = await response.json();
  const totalPages = parseInt(response.headers.get('x-wp-totalpages') || '1');
  const totalItems = parseInt(response.headers.get('x-wp-total') || '0');

  const data = {
    people,
    totalPages,
    currentPage: parseInt(queryParams.get('page') || '1'),
    totalItems,
  };

  return NextResponse.json(data);
}
