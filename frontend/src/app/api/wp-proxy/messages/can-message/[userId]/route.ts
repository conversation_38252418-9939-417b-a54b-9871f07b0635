import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

interface RouteParams {
  params: Promise<{ userId: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await params;

    // Get the authorization cookie from the request
    const cookie = request.headers.get('cookie');

    const response = await fetch(
      `${WP_BASE_URL}/wp-json/tourismiq/v1/messages/can-message/${userId}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(cookie && { Cookie: cookie }),
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Can message check proxy error:', error);
    return NextResponse.json({ error: 'Failed to check messaging permissions' }, { status: 500 });
  }
}
