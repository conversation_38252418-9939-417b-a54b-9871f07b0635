import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

interface RouteParams {
  params: Promise<{ userId: string }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await params;
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Get the authorization cookie from the request
    const cookie = request.headers.get('cookie');

    // Build the WordPress API URL with query parameters
    const wpUrl = new URL(`${WP_BASE_URL}/wp-json/tourismiq/v1/messages/conversation/${userId}`);

    // Forward query parameters
    if (searchParams.get('page')) {
      wpUrl.searchParams.set('page', searchParams.get('page')!);
    }
    if (searchParams.get('per_page')) {
      wpUrl.searchParams.set('per_page', searchParams.get('per_page')!);
    }

    const response = await fetch(wpUrl.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(cookie && { Cookie: cookie }),
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Get conversation messages proxy error:', error);
    return NextResponse.json({ error: 'Failed to fetch conversation messages' }, { status: 500 });
  }
}
