import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization cookie from the request
    const cookie = request.headers.get('cookie');

    const response = await fetch(`${WP_BASE_URL}/wp-json/tourismiq/v1/messages/unread-count`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(cookie && { Cookie: cookie }),
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Get unread count proxy error:', error);
    return NextResponse.json({ error: 'Failed to fetch unread count' }, { status: 500 });
  }
}
