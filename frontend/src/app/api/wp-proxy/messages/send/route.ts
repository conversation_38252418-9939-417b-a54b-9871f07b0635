import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Get the authorization cookie from the request
    const cookie = request.headers.get('cookie');

    const response = await fetch(`${WP_BASE_URL}/wp-json/tourismiq/v1/messages/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(cookie && { Cookie: cookie }),
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Send message proxy error:', error);
    return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
  }
}
