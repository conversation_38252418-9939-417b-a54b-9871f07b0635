import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params before using
    const { id } = await params;

    // Get the authorization cookie from the request
    const cookie = request.headers.get('cookie');

    const response = await fetch(
      `${WP_BASE_URL}/wp-json/tourismiq/v1/legacy-messages/conversation/${id}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(cookie && { Cookie: cookie }),
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Get legacy conversation messages proxy error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch legacy conversation messages' },
      { status: 500 }
    );
  }
}
