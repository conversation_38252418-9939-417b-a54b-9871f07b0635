import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
const WP_API_URL = `${WP_BASE_URL}/wp-json`;

export async function GET(request: NextRequest) {
  try {
    const endpoint = `${WP_API_URL}/wp/v2/vendor-categories?hide_empty=false&per_page=100`;

    // Add authentication headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '',
    };

    // Add basic auth if available
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    const response = await fetch(endpoint, {
      headers,
      credentials: 'include',
    });

    if (!response.ok) {
      console.error(`Error fetching vendor categories: ${response.status}`);
      const errorText = await response.text();
      return NextResponse.json(
        { error: 'Failed to fetch vendor categories', details: errorText },
        { status: response.status }
      );
    }

    const categories = await response.json();
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching vendor categories:', error);
    return NextResponse.json({ error: 'Failed to fetch vendor categories' }, { status: 500 });
  }
}
