import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Get WordPress API URL with fallback
const wpApiUrl =
  process.env.WORDPRESS_API_URL ||
  process.env.NEXT_PUBLIC_WORDPRESS_URL ||
  process.env.NEXT_PUBLIC_WORDPRESS_API_URL ||
  'http://tourismiq.local';

export async function GET() {
  try {
    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    if (!wordPressCookies) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Setup headers for WordPress API request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: wordPressCookies,
    };

    // Fetch assigned vendors from WordPress
    const response = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/user/assigned-vendors`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      console.error(`WordPress API error: ${response.status} - ${errorText}`);

      return NextResponse.json(
        { error: `Failed to fetch assigned vendors: ${response.status}` },
        { status: response.status }
      );
    }

    const vendors = await response.json();
    return NextResponse.json(vendors);
  } catch (error) {
    console.error('Error fetching assigned vendors:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
