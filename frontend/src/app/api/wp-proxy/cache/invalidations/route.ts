import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy handler for cache invalidations
 * Forwards requests to WordPress cache invalidation endpoint
 */
export async function GET(request: NextRequest) {
  try {
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json/tourismiq/v1`;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const since = searchParams.get('since') || '0';

    // Construct WordPress API URL
    const wpUrl = `${apiUrl}/cache/invalidations?since=${since}`;

    // Fetch from WordPress
    const response = await fetch(wpUrl, {
      headers: {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('cookie') || '',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch invalidations' },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Return with no-cache headers to ensure fresh data
    const nextResponse = NextResponse.json(data);
    nextResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    nextResponse.headers.set('Pragma', 'no-cache');
    nextResponse.headers.set('Expires', '0');

    return nextResponse;
  } catch (error) {
    console.error('Error in cache invalidations proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
