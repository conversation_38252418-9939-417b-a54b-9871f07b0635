import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST() {
  try {
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();

    // Build cookie header string from all cookies
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress URL from env or use default
    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8000';

    // Prepare headers for WordPress request - only using Cookie header for authentication
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: cookieHeader,
    };

    // Forward request to WordPress REST API
    const wpEndpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/notifications/read-all`;

    const response = await fetch(wpEndpoint, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text();

      return NextResponse.json(
        {
          error: `WordPress API returned ${response.status}`,
          details: errorText,
        },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Don't cache notification update responses
    const nextResponse = NextResponse.json(data);
    nextResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    nextResponse.headers.set('Pragma', 'no-cache');
    nextResponse.headers.set('Expires', '0');
    return nextResponse;
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to proxy notification request to WordPress',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
