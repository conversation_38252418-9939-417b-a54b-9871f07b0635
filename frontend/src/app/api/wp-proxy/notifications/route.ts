import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') || '50';
    const offset = searchParams.get('offset') || '0';
    const unread_only = searchParams.get('unread_only') || 'false';

    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();

    // Build cookie header string from all cookies
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress URL from env or use default
    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8000';

    // Prepare headers for WordPress request - only using Cookie header for authentication
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: cookieHeader,
    };

    // Build query string
    const queryParams = new URLSearchParams({
      limit,
      offset,
      unread_only,
    });

    // Forward request to WordPress REST API
    const wpEndpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/notifications?${queryParams}`;

    const response = await fetch(wpEndpoint, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text();

      return NextResponse.json(
        {
          error: `WordPress API returned ${response.status}`,
          details: errorText,
        },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Don't cache notification responses as they need to be real-time
    const nextResponse = NextResponse.json(data);
    nextResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    nextResponse.headers.set('Pragma', 'no-cache');
    nextResponse.headers.set('Expires', '0');
    return nextResponse;
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to proxy notification request to WordPress',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Notification ID is required' }, { status: 400 });
    }

    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();

    // Build cookie header string from all cookies
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress URL from env or use default
    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8000';

    // Prepare headers for WordPress request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: cookieHeader,
    };

    // Forward DELETE request to WordPress REST API
    const wpEndpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/notifications/${id}`;

    const response = await fetch(wpEndpoint, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        {
          error: `WordPress API returned ${response.status}`,
          details: errorText,
        },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Don't cache deletion responses
    const nextResponse = NextResponse.json(data);
    nextResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    nextResponse.headers.set('Pragma', 'no-cache');
    nextResponse.headers.set('Expires', '0');
    return nextResponse;
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to delete notification',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
