import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  try {
    // Get request body
    const body = await request.json();

    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();

    // Build cookie header string from all cookies
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress URL from env or use default
    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8000';

    // Prepare headers for WordPress request - only using Cookie header for authentication
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: cookieHeader,
    };

    // Forward request to WordPress REST API
    const wpEndpoint = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/notifications/send`;

    const response = await fetch(wpEndpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      // credentials: 'include' is not needed for server-side fetch when manually setting Cookie header
    });

    if (!response.ok) {
      const errorText = await response.text();

      return NextResponse.json(
        {
          error: `WordPress API returned ${response.status}`,
          details: errorText,
        },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to proxy notification request to WordPress',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
