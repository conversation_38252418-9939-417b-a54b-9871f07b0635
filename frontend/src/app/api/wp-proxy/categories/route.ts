import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy handler for WordPress categories
 */
export async function GET(request: NextRequest) {
  try {
    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const perPage = searchParams.get('per_page') || '100';
    const slug = searchParams.get('slug');

    // Construct the WordPress API URL for categories
    let wpUrl = `${apiUrl}/wp/v2/categories?per_page=${perPage}`;

    // Add slug filter if provided
    if (slug) {
      wpUrl += `&slug=${encodeURIComponent(slug)}`;
    }

    // Use basic auth with application password if available
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Fetch categories from WordPress
    const response = await fetch(wpUrl, {
      headers,
      credentials: 'include',
    });

    if (!response.ok) {
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch categories from WordPress' },
        { status: response.status }
      );
    }

    // Get the categories data
    const categories = await response.json();

    // Return the categories
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error in categories proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
