import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: vendorId } = await params;
    const body = await request.json();

    if (!vendorId) {
      return NextResponse.json({ error: 'Vendor ID is required' }, { status: 400 });
    }

    // Get WordPress API URL from environment
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Forward request to WordPress with credentials
    const response = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/vendors/${vendorId}/upgrade`, {
      method: 'POST',
      headers: {
        Cookie: request.headers.get('cookie') || '',
        'User-Agent': request.headers.get('user-agent') || '',
        Accept: 'application/json',
        'Content-Type': 'application/json',
        // Add WordPress auth if needed
        // 'Authorization': `Bearer ${process.env.WORDPRESS_API_TOKEN}`,
      },
      body: JSON.stringify({
        session_id: body.session_id,
        payment_status: body.payment_status,
        subscription_start: body.subscription_start,
        stripe_customer_id: body.stripe_customer_id,
        stripe_subscription_id: body.stripe_subscription_id,
        amount_total: body.amount_total,
        currency: body.currency,
        paying_user_id: body.paying_user_id,
        tier: body.tier,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('WordPress API error:', errorText);
      return NextResponse.json(
        { error: 'Failed to update vendor status in WordPress' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Vendor upgrade proxy error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
