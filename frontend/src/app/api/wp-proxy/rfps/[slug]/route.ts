import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy handler for individual WordPress RFP by slug
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  try {
    const { slug } = await params;

    if (!slug) {
      return NextResponse.json({ error: 'RFP slug is required' }, { status: 400 });
    }

    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Construct the WordPress API URL for single RFP by slug
    const wpUrl = `${apiUrl}/tourismiq/v1/rfps/slug/${encodeURIComponent(slug)}`;

    // Use environment variables for WordPress credentials (server-side only)
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Fetch RFP from WordPress
    const response = await fetch(wpUrl, {
      headers,
      credentials: 'include',
    });

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json({ error: 'RFP not found' }, { status: 404 });
      }
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch RFP from WordPress' },
        { status: response.status }
      );
    }

    // Get the RFP data
    const rfp = await response.json();

    return NextResponse.json(rfp);
  } catch (error) {
    console.error('Error in RFP slug proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
