import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy handler for WordPress RFP countries
 */
export async function GET(request: NextRequest) {
  try {
    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const perPage = searchParams.get('per_page') || '100';
    const page = searchParams.get('page') || '1';
    const orderby = searchParams.get('orderby') || 'name';
    const order = searchParams.get('order') || 'asc';

    // Construct the WordPress API URL for RFP countries
    const wpUrl = `${apiUrl}/wp/v2/rfp-countries?per_page=${perPage}&page=${page}&orderby=${orderby}&order=${order}`;

    // Use environment variables for WordPress credentials (server-side only)
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Fetch RFP countries from WordPress
    const response = await fetch(wpUrl, {
      headers,
      credentials: 'include',
    });

    if (!response.ok) {
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch RFP countries from WordPress' },
        { status: response.status }
      );
    }

    // Get the countries data
    const countries = await response.json();

    // Create response with countries data
    const nextResponse = NextResponse.json(countries);

    // Forward WordPress pagination headers
    const totalPages = response.headers.get('X-WP-TotalPages');
    const total = response.headers.get('X-WP-Total');

    if (totalPages) {
      nextResponse.headers.set('X-WP-TotalPages', totalPages);
    }
    if (total) {
      nextResponse.headers.set('X-WP-Total', total);
    }

    return nextResponse;
  } catch (error) {
    console.error('Error in RFP countries proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
