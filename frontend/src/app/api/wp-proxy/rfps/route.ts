import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy handler for WordPress RFPs
 * Handles fetching RFPs with various filters including categories, states, and countries
 */
export async function GET(request: NextRequest) {
  try {
    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const perPage = searchParams.get('per_page') || '2000';
    const page = searchParams.get('page') || '1';
    const category = searchParams.get('category') || '';
    const status = searchParams.get('status') || '';
    const organization = searchParams.get('organization') || '';
    const state = searchParams.get('state') || '';
    const country = searchParams.get('country') || '';
    const orderby = searchParams.get('orderby') || 'date';
    const order = searchParams.get('order') || 'desc';

    // Construct the WordPress API URL for RFP search
    let wpUrl = `${apiUrl}/tourismiq/v1/rfps/search?per_page=${perPage}&page=${page}&orderby=${orderby}&order=${order}`;

    // Add search parameter if provided
    if (search) {
      wpUrl += `&search=${encodeURIComponent(search)}`;
    }

    // Add category filter if provided
    if (category) {
      wpUrl += `&category=${encodeURIComponent(category)}`;
    }

    // Add status filter if provided
    if (status) {
      wpUrl += `&status=${encodeURIComponent(status)}`;
    }

    // Add organization filter if provided
    if (organization) {
      wpUrl += `&organization=${encodeURIComponent(organization)}`;
    }

    // Add state filter if provided
    if (state) {
      wpUrl += `&state=${encodeURIComponent(state)}`;
    }

    // Add country filter if provided
    if (country) {
      wpUrl += `&country=${encodeURIComponent(country)}`;
    }

    // Use environment variables for WordPress credentials (server-side only)
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Fetch RFPs from WordPress
    const response = await fetch(wpUrl, {
      headers,
      credentials: 'include',
      cache: 'no-store',
    });

    if (!response.ok) {
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch RFPs from WordPress' },
        { status: response.status }
      );
    }

    // Get the RFPs data
    const data = await response.json();

    // Create response with RFPs data
    const nextResponse = NextResponse.json(data);

    // Forward WordPress pagination headers if available
    const totalPages = response.headers.get('X-WP-TotalPages');
    const total = response.headers.get('X-WP-Total');

    if (totalPages) {
      nextResponse.headers.set('X-WP-TotalPages', totalPages);
    }
    if (total) {
      nextResponse.headers.set('X-WP-Total', total);
    }

    return nextResponse;
  } catch (error) {
    console.error('Error in RFPs proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
