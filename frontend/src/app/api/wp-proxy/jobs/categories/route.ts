import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const params = searchParams.toString();

    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const wpApiUrl = `${wpBaseUrl}/wp-json/wp/v2/job-categories${params ? `?${params}` : ''}`;

    const response = await fetch(wpApiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(process.env.WORDPRESS_USERNAME && process.env.WORDPRESS_APPLICATION_PASSWORD
          ? {
              Authorization: `Basic ${Buffer.from(
                `${process.env.WORDPRESS_USERNAME}:${process.env.WORDPRESS_APPLICATION_PASSWORD}`
              ).toString('base64')}`,
            }
          : {}),
      },
      credentials: 'include',
    });

    if (!response.ok) {
      console.error('Job categories API error:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to fetch job categories', status: response.status },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Job categories proxy error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
