import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const params = searchParams.toString();

    // Try custom endpoint first
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const customApiUrl = `${wpBaseUrl}/wp-json/tourismiq/v1/jobs/search${params ? `?${params}` : ''}`;

    const headers = {
      'Content-Type': 'application/json',
      ...(process.env.WORDPRESS_USERNAME && process.env.WORDPRESS_APPLICATION_PASSWORD
        ? {
            Authorization: `Basic ${Buffer.from(
              `${process.env.WORDPRESS_USERNAME}:${process.env.WORDPRESS_APPLICATION_PASSWORD}`
            ).toString('base64')}`,
          }
        : {}),
    };

    let response = await fetch(customApiUrl, {
      method: 'GET',
      headers,
      credentials: 'include',
    });

    // If custom endpoint fails, fall back to standard WP API
    if (!response.ok && response.status === 404) {
      const wpParams = new URLSearchParams();

      // Convert custom params to WP params
      wpParams.append('per_page', searchParams.get('per_page') || '20');
      wpParams.append('page', searchParams.get('page') || '1');
      wpParams.append('orderby', searchParams.get('orderby') || 'date');
      wpParams.append('order', searchParams.get('order') || 'desc');
      wpParams.append('_embed', 'true');

      if (searchParams.get('search')) {
        wpParams.append('search', searchParams.get('search') || '');
      }

      const wpApiUrl = `${wpBaseUrl}/wp-json/wp/v2/jobs?${wpParams.toString()}`;

      response = await fetch(wpApiUrl, {
        method: 'GET',
        headers,
        credentials: 'include',
      });

      if (response.ok) {
        const jobs = await response.json();
        const total = parseInt(response.headers.get('X-WP-Total') || '0', 10);

        // Transform to match custom endpoint format
        return NextResponse.json({
          jobs,
          total,
          page: parseInt(searchParams.get('page') || '1', 10),
          per_page: parseInt(searchParams.get('per_page') || '20', 10),
        });
      }
    }

    if (!response.ok) {
      console.error('Jobs search API error:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to search jobs', status: response.status },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Jobs search proxy error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
