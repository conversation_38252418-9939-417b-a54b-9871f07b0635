import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const params = searchParams.toString();

    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const wpApiUrl = `${wpBaseUrl}/wp-json/wp/v2/jobs${params ? `?${params}` : ''}`;

    const response = await fetch(wpApiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(process.env.WORDPRESS_USERNAME && process.env.WORDPRESS_APPLICATION_PASSWORD
          ? {
              Authorization: `Basic ${Buffer.from(
                `${process.env.WORDPRESS_USERNAME}:${process.env.WORDPRESS_APPLICATION_PASSWORD}`
              ).toString('base64')}`,
            }
          : {}),
      },
      credentials: 'include',
    });

    if (!response.ok) {
      console.error('Jobs API error:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to fetch jobs', status: response.status },
        { status: response.status }
      );
    }

    const data = await response.json();
    const total = response.headers.get('X-WP-Total');
    const totalPages = response.headers.get('X-WP-TotalPages');

    return NextResponse.json(data, {
      headers: {
        'X-WP-Total': total || '0',
        'X-WP-TotalPages': totalPages || '0',
      },
    });
  } catch (error) {
    console.error('Jobs proxy error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
