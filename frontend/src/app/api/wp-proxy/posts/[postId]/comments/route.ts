import { NextRequest, NextResponse } from 'next/server';

// Get the WordPress API URL from environment variables
const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

export async function GET(_request: NextRequest, context: { params: Promise<{ postId: string }> }) {
  const { postId } = await context.params;

  try {
    // Use our custom endpoint that includes author_info
    const url = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/posts/${postId}/comments`;

    // Make the request to WordPress with aggressive cache busting
    const response = await fetch(url + `?nocache=${Date.now()}`, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
      cache: 'no-store',
    });

    if (!response.ok) {
      throw new Error(`WordPress API returned ${response.status}`);
    }

    // Parse the response
    const data = await response.json();

    // Return the response as-is since our custom endpoint already returns the expected format
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch comments', comments: [] }, // Include comments: [] for consistency
      { status: 500 }
    );
  }
}

// POST handler for creating a new comment
export async function POST(request: NextRequest, context: { params: Promise<{ postId: string }> }) {
  const { postId } = await context.params;
  const data = await request.json();

  try {
    // IMPORTANT: Get the cookie header exactly as sent by the browser
    const cookieHeader = request.headers.get('cookie');

    // Direct approach: forward the request to WordPress
    const wpResponse = await fetch(`${WORDPRESS_API_URL}/wp-json/tourismiq/v1/comments/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // This is critical - forward the exact same cookies the browser sent us
        ...(cookieHeader ? { Cookie: cookieHeader } : {}),
      },
      // Do NOT include credentials here as we're in a server context
      body: JSON.stringify({
        post_id: parseInt(postId, 10),
        content: data.content,
      }),
    });

    // Get response data for better error handling
    let responseData;
    try {
      responseData = await wpResponse.json();
    } catch {
      responseData = { message: 'Could not parse response' };
    }

    // Handle error response
    if (!wpResponse.ok) {
      console.error('WordPress API error:', {
        status: wpResponse.status,
        statusText: wpResponse.statusText,
        data: responseData,
      });

      return NextResponse.json(
        {
          success: false,
          error: responseData.message || 'Failed to create comment',
          details: responseData,
        },
        { status: wpResponse.status }
      );
    }

    // Return successful comment data
    return NextResponse.json({
      success: true,
      comment: responseData,
    });
  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
