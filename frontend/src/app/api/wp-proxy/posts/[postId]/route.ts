import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy handler for individual WordPress posts
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ postId: string }> }
) {
  try {
    const { postId } = await params;

    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const embed = searchParams.get('_embed') ? '&_embed=1' : '';

    // Build the WordPress API URL for the specific post
    const wpUrl = `${apiUrl}/wp/v2/posts/${postId}?${embed ? embed.substring(1) : ''}`;

    // Use basic auth with application password if available
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Fetch post from WordPress
    const response = await fetch(wpUrl, {
      headers,
      credentials: 'include',
    });

    if (!response.ok) {
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch post from WordPress' },
        { status: response.status }
      );
    }

    // Get the post data
    const post = await response.json();

    return NextResponse.json(post);
  } catch (error) {
    console.error('Error in post proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Edit a post
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ postId: string }> }
) {
  try {
    const { postId } = await params;

    // Get FormData from request
    let formData;
    try {
      formData = await request.formData();
    } catch (formDataError) {
      const contentType = request.headers.get('content-type');
      throw new Error('FormData parsing failed. Content-Type: ' + contentType);
    }

    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Build the WordPress API URL for the custom edit endpoint
    const wpUrl = `${apiUrl}/tourismiq/v1/posts/${postId}/edit`;

    // Use basic auth with application password if available
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Send edit request to WordPress with FormData
    const response = await fetch(wpUrl, {
      method: 'POST',
      headers,
      body: formData, // Send FormData directly
      credentials: 'include',
    });

    if (!response.ok) {
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { error: 'Failed to edit post', details: errorData },
        { status: response.status }
      );
    }

    // Get the response data
    const result = await response.json();

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in post edit proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Delete a post
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ postId: string }> }
) {
  try {
    const { postId } = await params;

    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Build the WordPress API URL for the custom delete endpoint
    const wpUrl = `${apiUrl}/tourismiq/v1/posts/${postId}/delete`;

    // Use basic auth with application password if available
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Send delete request to WordPress
    const response = await fetch(wpUrl, {
      method: 'DELETE',
      headers,
      credentials: 'include',
    });

    if (!response.ok) {
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { error: 'Failed to delete post', details: errorData },
        { status: response.status }
      );
    }

    // Get the response data
    const result = await response.json();

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in post delete proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
