import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(_request: Request, { params }: { params: Promise<{ postId: string }> }) {
  try {
    const resolvedParams = await params;
    const postId = resolvedParams.postId;

    // Get cookies for authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();

    // Build cookie header string
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress URL from env or use default
    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8000';

    // Prepare headers for WordPress request - only using Cookie header for authentication
    const headers: HeadersInit = {
      Cookie: cookieHeader,
    };

    // Forward request to WordPress REST API
    const response = await fetch(
      `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/posts/${postId}/upvotes`,
      {
        headers,
        // credentials: 'include' is not needed for server-side fetch when manually setting Cookie header
      }
    );

    // Handle response errors
    if (!response.ok) {
      return NextResponse.json(
        { error: `WordPress API returned ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Return the response data
    return NextResponse.json(data);
  } catch (error) {
    console.error('Upvotes API Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch upvote status',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
