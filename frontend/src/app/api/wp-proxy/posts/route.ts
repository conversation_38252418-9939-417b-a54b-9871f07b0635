import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy handler for WordPress posts
 * Handles fetching posts with various filters including categories
 */
export async function GET(request: NextRequest) {
  try {
    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const perPage = searchParams.get('per_page') || '10';
    const categories = searchParams.get('categories');
    const embed = searchParams.get('_embed') ? '&_embed=1' : '';
    const page = searchParams.get('page') || '1';
    const orderby = searchParams.get('orderby') || 'date';
    const order = searchParams.get('order') || 'desc';
    const sticky = searchParams.get('sticky');
    const slug = searchParams.get('slug');
    const metaKey = searchParams.get('meta_key');
    const excludeVendorPosts = searchParams.get('exclude_vendor_posts');

    // Construct the WordPress API URL for posts with filters
    let wpUrl = `${apiUrl}/wp/v2/posts?per_page=${perPage}&page=${page}&orderby=${orderby}&order=${order}${embed}`;

    // Add meta_key if provided (for ordering by meta values)
    if (metaKey) {
      wpUrl += `&meta_key=${encodeURIComponent(metaKey)}`;
    }

    // Add category filter if provided
    if (categories) {
      wpUrl += `&categories=${categories}`;

      // Let's also try to get the category details for debugging
      try {
        const categoryCheckUrl = `${apiUrl}/wp/v2/categories/${categories}`;
        const categoryCheckResponse = await fetch(categoryCheckUrl, {
          headers: {
            'Content-Type': 'application/json',
            Cookie: request.headers.get('cookie') || '',
          },
          credentials: 'include',
        });

        if (categoryCheckResponse.ok) {
          await categoryCheckResponse.json();
        }
      } catch {
        // Category check failed, continue without it
      }
    }

    // Add sticky filter if provided
    if (sticky) {
      wpUrl += `&sticky=${sticky}`;
    }

    // Add slug filter if provided
    if (slug) {
      wpUrl += `&slug=${encodeURIComponent(slug)}`;
    }

    // Add vendor post exclusion filter if provided
    if (excludeVendorPosts === 'true') {
      // Use meta_query to exclude posts that have a post_vendor_id meta field set
      const metaQuery = JSON.stringify([
        {
          key: 'post_vendor_id',
          compare: 'NOT EXISTS',
        },
      ]);
      wpUrl += `&meta_query=${encodeURIComponent(metaQuery)}`;
    }

    // Use environment variables for WordPress credentials (server-side only)
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Fetch posts from WordPress
    let response = await fetch(wpUrl, {
      headers,
      credentials: 'include',
    });

    // If meta sorting fails, fall back to date sorting
    if (!response.ok && metaKey) {
      console.warn(`Meta sorting failed (${response.status}), falling back to date sorting`);

      // Construct fallback URL without meta sorting
      let fallbackUrl = `${apiUrl}/wp/v2/posts?per_page=${perPage}&page=${page}&orderby=date&order=desc${embed}`;

      // Add category filter if provided
      if (categories) {
        fallbackUrl += `&categories=${categories}`;
      }

      // Add sticky filter if provided
      if (sticky) {
        fallbackUrl += `&sticky=${sticky}`;
      }

      // Add slug filter if provided
      if (slug) {
        fallbackUrl += `&slug=${encodeURIComponent(slug)}`;
      }

      // Add vendor post exclusion filter if provided
      if (excludeVendorPosts === 'true') {
        const metaQuery = JSON.stringify([
          {
            key: 'post_vendor_id',
            compare: 'NOT EXISTS',
          },
        ]);
        fallbackUrl += `&meta_query=${encodeURIComponent(metaQuery)}`;
      }

      response = await fetch(fallbackUrl, {
        headers,
        credentials: 'include',
      });
    }

    if (!response.ok) {
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch posts from WordPress' },
        { status: response.status }
      );
    }

    // Get the posts data
    const posts = await response.json();

    // Create response with posts data
    const nextResponse = NextResponse.json(posts);

    // Disable caching for posts to ensure fresh data
    nextResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    nextResponse.headers.set('Pragma', 'no-cache');
    nextResponse.headers.set('Expires', '0');

    // Forward WordPress pagination headers
    const totalPages = response.headers.get('X-WP-TotalPages');
    const total = response.headers.get('X-WP-Total');

    if (totalPages) {
      nextResponse.headers.set('X-WP-TotalPages', totalPages);
    }
    if (total) {
      nextResponse.headers.set('X-WP-Total', total);
    }

    return nextResponse;
  } catch (error) {
    console.error('Error in posts proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
