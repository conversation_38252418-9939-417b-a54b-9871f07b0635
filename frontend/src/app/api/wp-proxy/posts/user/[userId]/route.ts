import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Await params in Next.js 15
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    if (!wordPressCookies) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get WordPress API URL with fallback
    const wpApiUrl =
      process.env.WORDPRESS_API_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL ||
      'http://tourismiq.local';

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const perPage = searchParams.get('per_page') || '10';

    // Build query parameters for WordPress API
    // Exclude vendor posts by filtering out posts with post_vendor_id > 0
    const queryParams = new URLSearchParams({
      page,
      per_page: perPage,
      author: userId,
      _embed: '1',
      orderby: 'date',
      order: 'desc',
      // Exclude vendor posts: only include posts where post_vendor_id doesn't exist or equals 0
      'meta_query[relation]': 'OR',
      'meta_query[0][key]': 'post_vendor_id',
      'meta_query[0][compare]': 'NOT EXISTS',
      'meta_query[1][key]': 'post_vendor_id',
      'meta_query[1][value]': '0',
      'meta_query[1][compare]': '=',
      'meta_query[2][key]': 'post_vendor_id',
      'meta_query[2][value]': '',
      'meta_query[2][compare]': '=',
    });

    // Make request to WordPress API
    const wpResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/posts?${queryParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: wordPressCookies,
      },
    });

    if (!wpResponse.ok) {
      const errorData = await wpResponse.json().catch(() => ({ message: 'Request failed' }));
      console.error('WordPress API error:', wpResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to fetch user posts' },
        { status: wpResponse.status }
      );
    }

    const posts = await wpResponse.json();

    // Additional client-side filtering to ensure vendor posts are excluded
    // This is a backup in case the WordPress meta_query doesn't work properly
    const filteredPosts = posts.filter((post: { meta?: { post_vendor_id?: string | number } }) => {
      const vendorId = post.meta?.post_vendor_id;
      // Exclude posts that have a vendor ID greater than 0
      return !vendorId || vendorId === 0 || vendorId === '0' || vendorId === '';
    });

    // Get pagination info from headers
    const totalPages = parseInt(wpResponse.headers.get('X-WP-TotalPages') || '1', 10);
    const totalPosts = parseInt(wpResponse.headers.get('X-WP-Total') || '0', 10);

    const response = {
      posts: filteredPosts,
      pagination: {
        page: parseInt(page, 10),
        per_page: parseInt(perPage, 10),
        total_pages: totalPages,
        total: totalPosts, // Use actual total from WordPress
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('User posts error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
