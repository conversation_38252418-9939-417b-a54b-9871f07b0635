import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  try {
    // Parse the request body to get the post IDs
    const { postIds } = await request.json();

    if (!Array.isArray(postIds) || postIds.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request. Expected an array of post IDs' },
        { status: 400 }
      );
    }

    // Limit the number of posts that can be fetched at once
    const limitedPostIds = postIds.slice(0, 50);

    // Get cookies for authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();

    // Build cookie header string
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress URL from env or use default
    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8000';

    // Prepare headers for WordPress request
    const headers: HeadersInit = {
      Cookie: cookieHeader,
      'Content-Type': 'application/json',
    };

    // Forward request to WordPress REST API
    // Note: We need to create a corresponding WordPress endpoint that accepts batch requests
    const response = await fetch(`${WORDPRESS_API_URL}/wp-json/tourismiq/v1/posts/batch-upvotes`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ postIds: limitedPostIds }),
    });

    // Handle response errors
    if (!response.ok) {
      // If the WordPress endpoint doesn't exist yet, fall back to individual requests
      if (response.status === 404) {
        const results = await Promise.all(
          limitedPostIds.map(async (postId) => {
            try {
              const res = await fetch(
                `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/posts/${postId}/upvotes`,
                {
                  headers,
                }
              );

              if (res.ok) {
                const data = await res.json();
                return { postId, ...data };
              }
              return {
                postId,
                upvoted: false,
                count: 0,
                error: `Status ${res.status}`,
              };
            } catch (err) {
              return { postId, upvoted: false, count: 0, error: String(err) };
            }
          })
        );

        // Transform results into an object keyed by post ID
        const upvotesData = results.reduce(
          (acc, result) => {
            acc[result.postId] = {
              upvoted: result.upvoted,
              count: result.count,
            };
            return acc;
          },
          {} as Record<number, { upvoted: boolean; count: number }>
        );

        return NextResponse.json(upvotesData);
      }

      return NextResponse.json(
        { error: `WordPress API returned ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Return the response data
    return NextResponse.json(data);
  } catch (error) {
    console.error('Batch Upvotes API Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch batch upvote status',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
