import { NextRequest, NextResponse } from 'next/server';

/**
 * Optimized proxy handler for WordPress posts by category slug
 * Handles category lookup and posts fetching in a single optimized request
 */

// Cache for category IDs to avoid repeated lookups
const categoryCache = new Map<string, number>();

export async function GET(request: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  try {
    const { slug } = await params;

    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const perPage = searchParams.get('per_page') || '20';
    const page = searchParams.get('page') || '1';
    const orderby = searchParams.get('orderby') || 'date';
    const order = searchParams.get('order') || 'desc';
    const embed = searchParams.get('_embed') ? '&_embed=1' : '';
    const metaKey = searchParams.get('meta_key');

    // Use environment variables for WordPress credentials (server-side only)
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Check if category ID is cached
    let categoryId = categoryCache.get(slug);

    if (!categoryId) {
      // Fetch category to get ID
      const categoryUrl = `${apiUrl}/wp/v2/categories?slug=${encodeURIComponent(slug)}`;
      const categoryResponse = await fetch(categoryUrl, {
        headers,
        credentials: 'include',
      });

      if (!categoryResponse.ok) {
        console.error(
          `Category lookup error: ${categoryResponse.status} ${categoryResponse.statusText}`
        );
        return NextResponse.json({ error: 'Category not found' }, { status: 404 });
      }

      const categories = await categoryResponse.json();

      if (!Array.isArray(categories) || categories.length === 0) {
        return NextResponse.json({ error: 'Category not found' }, { status: 404 });
      }

      categoryId = categories[0].id;
      // Cache the category ID for future requests
      if (categoryId) {
        categoryCache.set(slug, categoryId);
      }
    }

    // Construct the WordPress API URL for posts with category filter
    let wpUrl = `${apiUrl}/wp/v2/posts?categories=${categoryId}&per_page=${perPage}&page=${page}&orderby=${orderby}&order=${order}${embed}`;

    // Add meta_key if provided (for ordering by meta values)
    if (metaKey) {
      wpUrl += `&meta_key=${encodeURIComponent(metaKey)}`;
    }

    // Fetch posts from WordPress
    let response = await fetch(wpUrl, {
      headers,
      credentials: 'include',
    });

    // If meta sorting fails, fall back to date sorting
    if (!response.ok && metaKey) {
      console.warn(
        `Meta sorting failed for category ${slug} (${response.status}), falling back to date sorting`
      );

      // Construct fallback URL without meta sorting
      const fallbackUrl = `${apiUrl}/wp/v2/posts?categories=${categoryId}&per_page=${perPage}&page=${page}&orderby=date&order=desc${embed}`;

      response = await fetch(fallbackUrl, {
        headers,
        credentials: 'include',
      });
    }

    if (!response.ok) {
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch posts from WordPress' },
        { status: response.status }
      );
    }

    // Get the posts data
    const posts = await response.json();

    // Create response with posts data
    const nextResponse = NextResponse.json(posts);

    // Forward WordPress pagination headers
    const totalPages = response.headers.get('X-WP-TotalPages');
    const total = response.headers.get('X-WP-Total');

    if (totalPages) {
      nextResponse.headers.set('X-WP-TotalPages', totalPages);
    }
    if (total) {
      nextResponse.headers.set('X-WP-Total', total);
    }

    return nextResponse;
  } catch (error) {
    console.error('Error in posts by category proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
