import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Get WordPress auth cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const wordPressCookies = allCookies
      .filter((cookie) => cookie.name.startsWith('wordpress_'))
      .map((cookie) => `${cookie.name}=${cookie.value}`)
      .join('; ');

    if (!wordPressCookies) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get WordPress API URL with fallback
    const wpApiUrl =
      process.env.WORDPRESS_API_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL ||
      'http://tourismiq.local';

    // Check content type to determine how to parse the request
    const contentType = request.headers.get('content-type') || '';
    let title = '';
    let content = '';
    let featuredImage = null;
    let categories = [];
    let vendorId = null;
    let acfFields = null;

    // Handle multipart form data (for file uploads)
    const acfFileUploads: Record<string, File> = {};

    if (contentType.includes('multipart/form-data')) {
      const formData = await request.formData();
      title = formData.get('title') as string;
      content = formData.get('content') as string;
      featuredImage = formData.get('featured_media') as File | null;
      vendorId = formData.get('vendor_id')
        ? parseInt(formData.get('vendor_id') as string, 10)
        : null;

      // Parse ACF fields if provided
      const acfFieldsData = formData.get('acf_fields') as string;
      if (acfFieldsData) {
        try {
          acfFields = JSON.parse(acfFieldsData);
        } catch (error) {
          console.error('Failed to parse ACF fields:', error);
        }
      }

      // Extract ACF file uploads
      for (const [key, value] of formData.entries()) {
        if (key.startsWith('acf_file_')) {
          const fieldName = key.replace('acf_file_', '');
          if (value instanceof File) {
            acfFileUploads[fieldName] = value;
          }
        }
      }

      // Extract categories from form data
      for (const [key, value] of formData.entries()) {
        if (key.startsWith('categories[')) {
          categories.push(parseInt(value as string, 10));
        }
      }
    }
    // Handle JSON data
    else {
      const data = await request.json();
      title = data.title;
      content = data.content;
      categories = data.categories || [];
      vendorId = data.vendorId || null;
      acfFields = data.acfFields || null;
    }

    // Create the post in WordPress
    const postData: {
      title: string;
      content: string;
      status: string;
      categories?: number[];
      acf?: Record<string, any>;
    } = {
      title,
      content,
      status: 'publish',
    };

    // Add categories if provided
    if (categories.length > 0) {
      postData.categories = categories;
    }

    // Initialize ACF object
    const acfData: Record<string, any> = {};

    // Add vendor ACF field if posting as vendor
    if (vendorId) {
      acfData.post_vendor = vendorId;
    }

    // Add category-specific ACF fields
    if (acfFields) {
      Object.assign(acfData, acfFields);
    }

    // Add ACF data to post if any fields exist
    if (Object.keys(acfData).length > 0) {
      postData.acf = acfData;
    }

    const postResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: wordPressCookies,
      },
      body: JSON.stringify(postData),
    });

    if (!postResponse.ok) {
      const errorData = await postResponse.json().catch(() => ({ message: 'Request failed' }));
      return NextResponse.json(
        { error: errorData.message || 'Failed to create post' },
        { status: postResponse.status }
      );
    }

    const post = await postResponse.json();

    // Update ACF fields using custom endpoint if they exist
    if (acfFields && Object.keys(acfFields).length > 0) {
      try {
        // Use our custom endpoint for ACF updates
        const acfUpdateResponse = await fetch(
          `${wpApiUrl}/wp-json/tourismiq/v1/posts/${post.id}/acf`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Cookie: wordPressCookies,
            },
            body: JSON.stringify({
              fields: acfFields,
            }),
          }
        );

        if (!acfUpdateResponse.ok) {
          const errorText = await acfUpdateResponse.text();
          console.error('Failed to update ACF fields via custom endpoint:', errorText);
        }
      } catch (error) {
        console.error('Error updating ACF fields:', error);
      }
    }

    // Fallback: Set vendor ACF field using direct WordPress function call
    if (vendorId) {
      try {
        // Try using WordPress's update_field function via a custom endpoint
        const acfUpdateResponse = await fetch(
          `${wpApiUrl}/wp-json/tourismiq/v1/posts/${post.id}/set-vendor`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Cookie: wordPressCookies,
            },
            body: JSON.stringify({
              vendor_id: vendorId,
            }),
          }
        );

        if (!acfUpdateResponse.ok) {
          const standardUpdateResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/posts/${post.id}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Cookie: wordPressCookies,
            },
            body: JSON.stringify({
              acf: {
                post_vendor: vendorId,
              },
            }),
          });

          if (!standardUpdateResponse.ok) {
            await standardUpdateResponse.json().catch(() => ({}));
          }
        }
      } catch {
        // Silently handle error
      }
    }

    // Force-update categories if they don't match what was requested
    if (
      categories.length > 0 &&
      JSON.stringify(post.categories.sort()) !== JSON.stringify(categories.sort())
    ) {
      try {
        const updateResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/posts/${post.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Cookie: wordPressCookies,
          },
          body: JSON.stringify({
            categories: categories,
          }),
        });

        if (updateResponse.ok) {
          await updateResponse.json();
        }
      } catch {
        // Silently handle error
      }
    }

    // If we have a featured image, upload it and set it as the post's featured media
    if (featuredImage) {
      try {
        // Convert the File object to a Blob with proper content type
        const arrayBuffer = await featuredImage.arrayBuffer();
        const blob = new Blob([arrayBuffer], { type: featuredImage.type });

        // Create form data for media upload
        const mediaFormData = new FormData();
        mediaFormData.append('file', blob, featuredImage.name);

        const mediaResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/media`, {
          method: 'POST',
          headers: {
            Cookie: wordPressCookies,
          },
          body: mediaFormData,
        });

        if (mediaResponse.ok) {
          const media = await mediaResponse.json();

          // Set the uploaded media as the post's featured media
          await fetch(`${wpApiUrl}/wp-json/wp/v2/posts/${post.id}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Cookie: wordPressCookies,
            },
            body: JSON.stringify({
              featured_media: media.id,
            }),
          });
        }
      } catch {
        // Silently handle error
      }
    }

    // Handle ACF file uploads
    if (Object.keys(acfFileUploads).length > 0) {
      const uploadedFiles: Record<string, number> = {};

      // Upload each ACF file
      for (const [fieldName, file] of Object.entries(acfFileUploads)) {
        try {
          const arrayBuffer = await file.arrayBuffer();
          const blob = new Blob([arrayBuffer], { type: file.type });

          const mediaFormData = new FormData();
          mediaFormData.append('file', blob, file.name);

          const mediaResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/media`, {
            method: 'POST',
            headers: {
              Cookie: wordPressCookies,
            },
            body: mediaFormData,
          });

          if (mediaResponse.ok) {
            const media = await mediaResponse.json();
            uploadedFiles[fieldName] = media.id;
          }
        } catch (error) {
          console.error(`Failed to upload ACF file for field ${fieldName}:`, error);
        }
      }

      // Update ACF fields with uploaded file IDs
      if (Object.keys(uploadedFiles).length > 0) {
        try {
          const acfUpdateResponse = await fetch(
            `${wpApiUrl}/wp-json/tourismiq/v1/posts/${post.id}/acf`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Cookie: wordPressCookies,
              },
              body: JSON.stringify({
                fields: uploadedFiles,
              }),
            }
          );

          if (!acfUpdateResponse.ok) {
            console.error('Failed to update ACF file fields:', await acfUpdateResponse.text());
          }
        } catch (error) {
          console.error('Error updating ACF file fields:', error);
        }
      }
    }

    return NextResponse.json(post, { status: 201 });
  } catch {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
