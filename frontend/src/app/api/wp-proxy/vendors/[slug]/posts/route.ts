import { NextRequest, NextResponse } from 'next/server';

const WP_API_URL =
  process.env.WORDPRESS_API_URL ||
  process.env.NEXT_PUBLIC_WORDPRESS_URL ||
  process.env.NEXT_PUBLIC_WORDPRESS_API_URL ||
  'http://tourismiq.local';

export async function GET(request: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);

    // Forward query parameters (like _embed)
    const queryString = searchParams.toString();
    const wpUrl = `${WP_API_URL}/wp-json/tourismiq/v1/vendors/slug/${slug}/posts${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(wpUrl, {
      headers: {
        Cookie: request.headers.get('cookie') || '',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Request failed' }));
      return NextResponse.json(errorData, { status: response.status });
    }

    const posts = await response.json();
    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching vendor posts:', error);
    return NextResponse.json({ error: 'Failed to fetch vendor posts' }, { status: 500 });
  }
}
