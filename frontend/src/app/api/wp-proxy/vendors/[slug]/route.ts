import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
const WP_API_URL = `${WP_BASE_URL}/wp-json`;

interface AssignedMember {
  id: number;
  name?: string;
  email?: string;
  avatar_url?: string;
}

interface VendorUpdateData {
  title?: string;
  content?: string;
  website?: string;
  cover_photo?: string;
  logo_url?: string;
  resources?: string[];
  assigned_members?: number[];
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Get all cookies for WordPress cookie authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Setup headers for WordPress API request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: cookieHeader,
    };

    // Check if the slug is numeric (ID) or a string (slug)
    const isNumeric = /^\d+$/.test(slug);
    let vendorResponse;

    if (isNumeric) {
      // Fetch by ID
      vendorResponse = await fetch(`${WP_API_URL}/wp/v2/vendors/${slug}?_embed`, {
        method: 'GET',
        headers,
      });
    } else {
      // Fetch by slug
      vendorResponse = await fetch(`${WP_API_URL}/wp/v2/vendors?slug=${slug}&_embed`, {
        method: 'GET',
        headers,
      });
    }

    if (!vendorResponse.ok) {
      if (vendorResponse.status === 404) {
        return NextResponse.json({ error: 'Vendor not found' }, { status: 404 });
      }
      return NextResponse.json(
        { error: 'Failed to fetch vendor' },
        { status: vendorResponse.status }
      );
    }

    let vendor;
    if (isNumeric) {
      // For ID requests, the response is a single vendor object
      vendor = await vendorResponse.json();
    } else {
      // For slug requests, the response is an array
      const vendors = await vendorResponse.json();
      if (!vendors || vendors.length === 0) {
        return NextResponse.json({ error: 'Vendor not found' }, { status: 404 });
      }
      vendor = vendors[0];
    }

    // Get vendor meta using the custom endpoint
    const metaResponse = await fetch(`${WP_API_URL}/tourismiq/v1/vendor/${vendor.id}/meta`, {
      method: 'GET',
      headers,
    });

    let vendorMeta = {};
    if (metaResponse.ok) {
      vendorMeta = await metaResponse.json();
    }

    // Get vendor categories
    const categoriesResponse = await fetch(
      `${WP_API_URL}/wp/v2/vendor_category?post=${vendor.id}`,
      {
        method: 'GET',
        headers,
      }
    );

    let vendorCategories = [];
    if (categoriesResponse.ok) {
      vendorCategories = await categoriesResponse.json();
    }

    // Combine all data
    const vendorData = {
      ...vendor,
      vendor_meta: vendorMeta,
      vendor_categories: vendorCategories,
    };

    return NextResponse.json(vendorData);
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to fetch vendor',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  try {
    const { slug } = await params;

    // Get all cookies for WordPress cookie authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Setup headers for WordPress API request
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: cookieHeader,
    };

    // First, get the vendor by slug to get the ID
    const vendorResponse = await fetch(`${WP_API_URL}/wp/v2/vendors?slug=${slug}`, {
      method: 'GET',
      headers,
    });

    if (!vendorResponse.ok) {
      return NextResponse.json({ error: 'Vendor not found' }, { status: 404 });
    }

    const vendors = await vendorResponse.json();
    if (!vendors || vendors.length === 0) {
      return NextResponse.json({ error: 'Vendor not found' }, { status: 404 });
    }

    const vendor = vendors[0];
    const vendorId = vendor.id;

    // Check if current user is an assigned member
    const currentUserResponse = await fetch(`${WP_API_URL}/wp/v2/users/me`, {
      method: 'GET',
      headers,
    });

    if (!currentUserResponse.ok) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const currentUser = await currentUserResponse.json();

    // Get vendor meta to check assigned members
    const metaResponse = await fetch(`${WP_API_URL}/tourismiq/v1/vendor/${vendorId}/meta`, {
      method: 'GET',
      headers,
    });

    if (!metaResponse.ok) {
      return NextResponse.json({ error: 'Failed to fetch vendor meta' }, { status: 500 });
    }

    const vendorMeta = await metaResponse.json();
    const assignedMembers = vendorMeta.assigned_members || [];

    // Check if current user is in assigned members
    const isAssignedMember = assignedMembers.some(
      (member: AssignedMember) => member.id === currentUser.id
    );

    if (!isAssignedMember) {
      return NextResponse.json(
        { error: 'You are not authorized to edit this vendor' },
        { status: 403 }
      );
    }

    // Parse the request body
    const updateData = await request.json();

    // Prepare all update data for our custom endpoint
    const allUpdateData: VendorUpdateData = {};

    // Add post data
    if (updateData.name) {
      allUpdateData.title = updateData.name;
    }

    if (updateData.content) {
      allUpdateData.content = updateData.content;
    }

    // Add meta data
    if (updateData.website !== undefined) {
      allUpdateData.website = updateData.website;
    }

    if (updateData.cover_photo !== undefined) {
      allUpdateData.cover_photo = updateData.cover_photo;
    }

    if (updateData.logo_url !== undefined) {
      allUpdateData.logo_url = updateData.logo_url;
    }

    if (updateData.resources !== undefined) {
      allUpdateData.resources = updateData.resources;
    }

    if (updateData.assigned_members !== undefined) {
      allUpdateData.assigned_members = updateData.assigned_members;
    }

    // Update through our custom endpoint that handles both post and meta data
    const updateResponse = await fetch(`${WP_API_URL}/tourismiq/v1/vendor/${vendorId}/update`, {
      method: 'POST',
      headers,
      body: JSON.stringify(allUpdateData),
    });

    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      return NextResponse.json(
        { error: 'Failed to update vendor', details: errorText },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Vendor updated successfully',
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to update vendor',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // First get the vendor by slug to get the ID
    const getResponse = await fetch(`${WP_API_URL}/tourismiq/v1/vendors/slug/${slug}`);
    if (!getResponse.ok) {
      return NextResponse.json({ error: 'Vendor not found' }, { status: 404 });
    }

    const vendor = await getResponse.json();
    const vendorId = vendor.id;

    // Delete the vendor
    const wpUrl = `${WP_API_URL}/wp/v2/vendors/${vendorId}`;

    const response = await fetch(wpUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('cookie') || '',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting vendor:', error);
    return NextResponse.json({ error: 'Failed to delete vendor' }, { status: 500 });
  }
}
