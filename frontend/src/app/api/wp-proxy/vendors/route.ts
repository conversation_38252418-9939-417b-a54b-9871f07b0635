import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
const WP_API_URL = `${WP_BASE_URL}/wp-json`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const perPage = searchParams.get('per_page') || '20';
    const page = searchParams.get('page') || '1';
    const orderby = searchParams.get('orderby') || 'title';
    const order = searchParams.get('order') || 'asc';

    // Build query parameters for enhanced search endpoint
    const params = new URLSearchParams();
    params.append('per_page', perPage);
    params.append('page', page);
    params.append('orderby', orderby);
    params.append('order', order);

    if (search) params.append('search', search);
    if (category && category !== 'all-categories') params.append('vendor-categories', category);

    // Use the standard WordPress vendors endpoint
    const endpoint = `${WP_API_URL}/wp/v2/vendors?${params.toString()}`;

    // Add authentication headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '',
    };

    // Add basic auth if available
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    const response = await fetch(endpoint, {
      headers,
      credentials: 'include',
    });

    if (!response.ok) {
      console.error(`Error fetching vendors: ${response.status}`);
      const errorText = await response.text();
      return NextResponse.json(
        { error: 'Failed to fetch vendors', details: errorText },
        { status: response.status }
      );
    }

    // Standard WordPress endpoint returns vendors array
    const vendorsData = await response.json();

    // Transform to expected format
    const transformedResponse = {
      vendors: vendorsData,
      pagination: {
        total: parseInt(response.headers.get('X-WP-Total') || '0'),
        total_pages: parseInt(response.headers.get('X-WP-TotalPages') || '1'),
        current_page: parseInt(page),
        per_page: parseInt(perPage),
      },
    };

    // Create response with pagination headers
    const nextResponse = NextResponse.json(transformedResponse);

    // Forward pagination headers
    const total = response.headers.get('X-WP-Total');
    const totalPages = response.headers.get('X-WP-TotalPages');
    if (total) nextResponse.headers.set('X-WP-Total', total);
    if (totalPages) nextResponse.headers.set('X-WP-TotalPages', totalPages);

    return nextResponse;
  } catch (error) {
    console.error('Error fetching vendors:', error);
    return NextResponse.json({ error: 'Failed to fetch vendors' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Forward the request to WordPress
    const wpUrl = `${WP_API_URL}/wp/v2/vendors`;

    const response = await fetch(wpUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: request.headers.get('cookie') || '',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }

    const vendor = await response.json();
    return NextResponse.json(vendor);
  } catch (error) {
    console.error('Error creating vendor:', error);
    return NextResponse.json({ error: 'Failed to create vendor' }, { status: 500 });
  }
}
