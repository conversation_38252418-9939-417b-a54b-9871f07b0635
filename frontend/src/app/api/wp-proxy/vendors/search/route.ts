/*********************************************
# frontend/src/app/api/wp-proxy/vendors/search/route.ts
# 02/05/2025 11:45pm Created enhanced vendor search endpoint for more targeted results
# 02/06/2025 2:30pm Unified fetch logic, added full pagination, paid prioritization, and sorting for both search and non-search queries
**********************************************/

import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
const WP_API_URL = `${WP_BASE_URL}/wp-json`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const search = searchParams.get('search')?.trim() || '';
    const category = searchParams.get('category');
    const perPageStr = searchParams.get('per_page') || '20';
    const pageStr = searchParams.get('page') || '1';

    const perPage = parseInt(perPageStr);
    const page = parseInt(pageStr);
    const excludePaid = searchParams.get('exclude_paid') === 'true';

    // Headers setup
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '',
    };

    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Fetch paid vendors
    const paidEndpoint = `${WP_API_URL}/tourismiq/v1/vendors/paid`;
    const paidResponse = await fetch(paidEndpoint, {
      headers,
      credentials: 'include',
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });

    let paidVendors = [];
    if (paidResponse.ok) {
      paidVendors = await paidResponse.json();
    } else {
      console.error('Failed to fetch paid vendors');
    }

    const paidIds = new Set(paidVendors.map((v: any) => v.id));

    // Fetch ALL vendors with pagination loop
    let allVendors: any[] = [];
    let currentPage = 1;
    const fetchPerPage = 100; // Efficient batch size

    const allVendorsParams = new URLSearchParams();
    allVendorsParams.append('per_page', fetchPerPage.toString());
    if (category && category !== 'all-categories') {
      allVendorsParams.append('vendor-categories', category);
    }

    while (true) {
      allVendorsParams.set('page', currentPage.toString());
      const endpoint = `${WP_API_URL}/wp/v2/vendors?${allVendorsParams.toString()}`;

      const response = await fetch(endpoint, {
        headers,
        credentials: 'include',
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });

      if (!response.ok) {
        console.error(`Failed to fetch vendors page ${currentPage}`);
        break;
      }

      const vendors = await response.json();
      if (vendors.length === 0) break;

      allVendors = allVendors.concat(vendors);
      currentPage++;
    }

    if (excludePaid) {
      allVendors = allVendors.filter((vendor) => !paidIds.has(vendor.id));
    } else {
      // When not excluding paid vendors (search/filter mode), ensure paid vendors are included
      // Add any paid vendors that might not be in the regular vendor list
      const allVendorIds = new Set(allVendors.map((v) => v.id));
      const missingPaidVendors = paidVendors.filter((pv) => !allVendorIds.has(pv.id));

      // Apply category filter to missing paid vendors if needed
      if (category && category !== 'all-categories') {
        const filteredMissingPaid = missingPaidVendors.filter((v: any) =>
          v.vendor_categories?.some((cat: any) => cat.id === parseInt(category))
        );
        allVendors = allVendors.concat(filteredMissingPaid);
      } else {
        allVendors = allVendors.concat(missingPaidVendors);
      }
    }

    // If category filter, filter paid vendors to match category client-side for tracking
    if (category && category !== 'all-categories') {
      paidVendors = paidVendors.filter((v: any) =>
        v.vendor_categories?.some((cat: any) => cat.id === parseInt(category))
      );
      paidIds.clear();
      paidVendors.forEach((v: any) => paidIds.add(v.id));
    }

    // Score and mark vendors
    const searchWords = search.toLowerCase().split(/\s+/);
    const scoredVendors = allVendors.map((vendor) => {
      let score = 0;

      if (search) {
        const title = vendor.title?.rendered?.toLowerCase() || '';
        const content = vendor.content?.rendered?.toLowerCase().replace(/<[^>]*>/g, '') || '';
        const excerpt = vendor.excerpt?.rendered?.toLowerCase().replace(/<[^>]*>/g, '') || '';
        const categories =
          vendor.vendor_categories?.map((cat: any) => cat.name?.toLowerCase()).join(' ') || '';
        const website = vendor.vendor_meta?.website?.toLowerCase() || '';
        const assignedMembers =
          vendor.vendor_meta?.assigned_members
            ?.map((member: any) => member.name?.toLowerCase() || '')
            .join(' ') || '';

        searchWords.forEach((word) => {
          if (title.includes(word)) {
            score += title === word ? 100 : title.startsWith(word) ? 80 : 50;
          }
          if (categories.includes(word)) score += 70;
          if (website.includes(word)) score += 60;
          if (assignedMembers.includes(word)) score += 60;
          if (content.includes(word)) score += 30;
          if (excerpt.includes(word)) score += 25;
        });

        const allText = `${title} ${categories} ${website} ${assignedMembers} ${content} ${excerpt}`;
        const matchedWords = searchWords.filter((word) => allText.includes(word));
        if (matchedWords.length > 1) {
          score += matchedWords.length * 10;
        }
      }

      const isPaid = paidIds.has(vendor.id);
      return { vendor, score, isPaid };
    });

    // Filter relevant (score > 0 if searching, all if not)
    let relevant = scoredVendors;
    if (search) {
      relevant = relevant.filter((item) => item.score > 0);
    }

    // Sort: paid first, then by score descending
    relevant.sort((a, b) => {
      // First priority: paid vendors come first
      if (a.isPaid && !b.isPaid) return -1;
      if (!a.isPaid && b.isPaid) return 1;

      // Second priority: higher scores come first
      return b.score - a.score;
    });

    // Extract sorted vendors
    const sortedVendors = relevant.map((item) => item.vendor);

    // Apply pagination
    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedVendors = sortedVendors.slice(startIndex, endIndex);

    // Add is_paid field to each paginated vendor
    paginatedVendors.forEach((vendor) => {
      vendor.is_paid = paidIds.has(vendor.id);
    });

    const transformedResponse = {
      vendors: paginatedVendors,
      pagination: {
        total: sortedVendors.length,
        total_pages: Math.ceil(sortedVendors.length / perPage),
        current_page: page,
        per_page: perPage,
      },
    };

    return NextResponse.json(transformedResponse);
  } catch (error) {
    console.error('Error in vendor search:', error);
    return NextResponse.json({ error: 'Failed to search vendors' }, { status: 500 });
  }
}
