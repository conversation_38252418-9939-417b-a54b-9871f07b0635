import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
const WP_API_URL = `${WP_BASE_URL}/wp-json`;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Build WordPress API URL with query parameters
    const wpParams = new URLSearchParams();

    // Pagination
    const page = searchParams.get('page') || '1';
    const perPage = searchParams.get('per_page') || '100';
    wpParams.append('page', page);
    wpParams.append('per_page', perPage);

    // Search
    const search = searchParams.get('search');
    if (search) {
      wpParams.append('search', search);
    }

    // Order by
    const orderBy = searchParams.get('orderby') || 'name';
    const order = searchParams.get('order') || 'asc';
    wpParams.append('orderby', orderBy);
    wpParams.append('order', order);

    const wpUrl = `${WP_API_URL}/wp/v2/vendor-categories?${wpParams.toString()}`;

    const response = await fetch(wpUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`WordPress API error: ${response.status}`);
    }

    const categories = await response.json();

    // Get total count from headers for pagination
    const totalCount = response.headers.get('X-WP-Total') || '0';
    const totalPages = response.headers.get('X-WP-TotalPages') || '1';

    return NextResponse.json({
      categories,
      pagination: {
        page: parseInt(page),
        per_page: parseInt(perPage),
        total: parseInt(totalCount),
        total_pages: parseInt(totalPages),
      },
    });
  } catch (error) {
    console.error('Error fetching vendor categories:', error);
    return NextResponse.json({ error: 'Failed to fetch vendor categories' }, { status: 500 });
  }
}
