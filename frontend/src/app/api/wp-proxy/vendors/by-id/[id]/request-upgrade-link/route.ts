import { NextRequest, NextResponse } from 'next/server';

const WP_BASE_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json();
    const { id } = await params;
    
    // Get the authorization cookie from the request
    const cookie = request.headers.get('cookie');

    // Make request to WordPress
    const response = await fetch(
      `${WP_BASE_URL}/wp-json/tourismiq/v1/vendors/${id}/request-upgrade-link`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(cookie && { Cookie: cookie }),
        },
        body: JSON.stringify(body),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        {
          code: data.code || 'unknown_error',
          message: data.message || 'Failed to request upgrade link',
          status: response.status,
        },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error requesting vendor upgrade link:', error);
    return NextResponse.json(
      { 
        code: 'server_error',
        message: 'Failed to request upgrade link' 
      },
      { status: 500 }
    );
  }
}