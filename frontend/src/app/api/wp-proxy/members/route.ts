import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy handler for WordPress users/members
 */
export async function GET(request: NextRequest) {
  try {
    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const perPage = searchParams.get('per_page') || '12';
    const page = searchParams.get('page') || '1';
    const search = searchParams.get('search');

    // Use our custom members endpoint instead of wp/v2/users
    let wpUrl = `${apiUrl}/tourismiq/v1/members?per_page=${perPage}&page=${page}`;

    // Add search parameter if provided
    if (search) {
      wpUrl += `&search=${encodeURIComponent(search)}`;
    }

    // Use basic auth with application password if available
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Fetch users from WordPress
    const response = await fetch(wpUrl, {
      headers,
      credentials: 'include',
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        `WordPress API error: ${response.status} ${response.statusText}`,
        'URL:',
        wpUrl,
        'Response:',
        errorText
      );
      return NextResponse.json(
        { error: 'Failed to fetch users from WordPress', details: errorText },
        { status: response.status }
      );
    }

    // Get the users data
    const users = await response.json();

    // Create response with users data
    const nextResponse = NextResponse.json(users);

    // Forward WordPress pagination headers
    const totalPages = response.headers.get('X-WP-TotalPages');
    const total = response.headers.get('X-WP-Total');

    if (totalPages) {
      nextResponse.headers.set('X-WP-TotalPages', totalPages);
    }
    if (total) {
      nextResponse.headers.set('X-WP-Total', total);
    }

    return nextResponse;
  } catch (error) {
    console.error('Error in members proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
