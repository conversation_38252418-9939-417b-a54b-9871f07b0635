import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy handler for custom WordPress members endpoint
 */
export async function GET(request: NextRequest) {
  try {
    // WordPress API URL from environment variable
    const wpBaseUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const apiUrl = `${wpBaseUrl}/wp-json`;

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const perPage = searchParams.get('per_page') || '100';
    const page = searchParams.get('page') || '1';

    // Build the WordPress API URL for the custom members endpoint
    const wpUrl = `${apiUrl}/tourismiq/v1/members?per_page=${perPage}&page=${page}`;

    // Use basic auth with application password if available
    const username = process.env.WORDPRESS_USERNAME;
    const password = process.env.WORDPRESS_APPLICATION_PASSWORD;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      Cookie: request.headers.get('cookie') || '', // Forward cookies for authentication
    };

    // Add authorization header if credentials are available
    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      headers['Authorization'] = `Basic ${auth}`;
    }

    // Fetch members from WordPress custom endpoint
    const response = await fetch(wpUrl, {
      headers,
      credentials: 'include',
    });

    if (!response.ok) {
      console.error(`WordPress API error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch members from WordPress' },
        { status: response.status }
      );
    }

    // Get the members data
    const members = await response.json();

    return NextResponse.json(members);
  } catch (error) {
    console.error('Error in custom members proxy:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
