import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // Get all cookies for WordPress authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get request data
    const { title, content } = await request.json();

    if (!title || !content) {
      return NextResponse.json({ error: 'Title and content are required' }, { status: 400 });
    }

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Create the forum question using our custom endpoint
    const createResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/forum/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: cookieHeader,
      },
      body: JSON.stringify({
        title,
        content,
      }),
    });

    if (!createResponse.ok) {
      const errorData = await createResponse
        .json()
        .catch(() => ({ message: 'Failed to create question' }));
      console.error('WordPress API error:', createResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to create forum question' },
        { status: createResponse.status }
      );
    }

    const data = await createResponse.json();

    // IQ points are already awarded in the WordPress backend
    // No need to make a separate call here

    // Send notification to socket server for real-time updates
    try {
      const notificationResponse = await fetch(
        `${process.env.SOCKET_SERVER_URL || 'http://localhost:3000'}/api/internal/send-notification`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'forum_question',
            content: `New forum question: ${title}`,
            referenceId: data.question?.id,
            referenceType: 'forum_question',
            senderId: data.question?.author,
            activityType: 'forum_question',
            pointsEarned: 3,
          }),
        }
      );

      if (!notificationResponse.ok) {
        console.error('Failed to send notification to socket server');
      }
    } catch (notificationError) {
      console.error('Failed to send notification:', notificationError);
      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      success: true,
      question: data.question,
    });
  } catch (error) {
    console.error('Create forum question error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
