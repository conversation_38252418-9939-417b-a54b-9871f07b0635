import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    // Get all cookies for WordPress authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const perPage = searchParams.get('per_page') || '10';
    const sort = searchParams.get('sort') || 'new';
    const search = searchParams.get('search') || '';

    // Build query parameters for our custom forum API
    const queryParams = new URLSearchParams({
      page,
      per_page: perPage,
      sort,
    });

    // Add search parameter if provided
    if (search) {
      queryParams.append('search', search);
    }

    // Make request to our custom forum API
    const wpResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/forum?${queryParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: cookieHeader,
      },
    });

    if (!wpResponse.ok) {
      const errorData = await wpResponse.json().catch(() => ({ message: 'Request failed' }));
      console.error('WordPress API error:', wpResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to fetch forum questions' },
        { status: wpResponse.status }
      );
    }

    const data = await wpResponse.json();

    // The response already includes pagination from our custom endpoint
    const response = {
      questions: data.questions || [],
      pagination: data.pagination || {
        page: parseInt(page, 10),
        per_page: parseInt(perPage, 10),
        total_pages: 1,
        total: 0,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Forum questions error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
