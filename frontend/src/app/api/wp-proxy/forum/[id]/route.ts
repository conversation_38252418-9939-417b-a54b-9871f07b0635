import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(_request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params in Next.js 15
    const { id } = await params;

    if (!id) {
      return NextResponse.json({ error: 'Question ID is required' }, { status: 400 });
    }

    // Get all cookies for WordPress authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Get the forum question using our custom endpoint
    const questionResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/forum/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: cookieHeader,
      },
    });

    if (!questionResponse.ok) {
      const errorData = await questionResponse
        .json()
        .catch(() => ({ message: 'Question not found' }));
      console.error('WordPress API error:', questionResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to fetch forum question' },
        { status: questionResponse.status }
      );
    }

    const questionDetail = await questionResponse.json();

    return NextResponse.json(questionDetail);
  } catch (error) {
    console.error('Forum question detail error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
