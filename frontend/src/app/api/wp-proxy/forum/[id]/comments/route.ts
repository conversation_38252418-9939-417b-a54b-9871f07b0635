import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(_request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params in Next.js 15
    const { id } = await params;

    if (!id) {
      return NextResponse.json({ error: 'Question ID is required' }, { status: 400 });
    }

    // Get all cookies for WordPress authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Get comments for this forum question using our custom endpoint
    const commentsResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/forum/${id}/comments`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Cookie: cookieHeader,
      },
    });

    if (!commentsResponse.ok) {
      const errorData = await commentsResponse
        .json()
        .catch(() => ({ message: 'Failed to fetch comments' }));
      console.error('WordPress API error:', commentsResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to fetch forum comments' },
        { status: commentsResponse.status }
      );
    }

    const commentsData = await commentsResponse.json();

    return NextResponse.json(commentsData);
  } catch (error) {
    console.error('Forum comments error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params in Next.js 15
    const { id } = await params;

    if (!id) {
      return NextResponse.json({ error: 'Question ID is required' }, { status: 400 });
    }

    // Get all cookies for WordPress authentication
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get request data
    const { content } = await request.json();

    if (!content) {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 });
    }

    // Get WordPress API URL
    const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Create the comment using the existing WordPress comments endpoint
    const createResponse = await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/comments/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: cookieHeader,
      },
      body: JSON.stringify({
        post_id: parseInt(id, 10),
        content,
      }),
    });

    if (!createResponse.ok) {
      const errorData = await createResponse
        .json()
        .catch(() => ({ message: 'Failed to create comment' }));
      console.error('WordPress API error:', createResponse.status, errorData);
      return NextResponse.json(
        { error: errorData.message || 'Failed to create forum comment' },
        { status: createResponse.status }
      );
    }

    const newComment = await createResponse.json();

    // Award IQ points for responding to a forum question (1 point)
    try {
      await fetch(`${wpApiUrl}/wp-json/tourismiq/v1/iq-score/award`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: cookieHeader,
        },
        body: JSON.stringify({
          user_id: newComment.comment?.author || newComment.comment?.author_id,
          points: 1,
          activity_type: 'forum_response',
        }),
      });
    } catch (iqError) {
      console.error('Failed to award IQ points:', iqError);
      // Don't fail the request if IQ points fail
    }

    // Send notification to socket server for real-time updates
    try {
      const notificationResponse = await fetch(
        `${process.env.SOCKET_SERVER_URL || 'http://localhost:3000'}/api/internal/send-notification`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'forum_response',
            content: `New response to forum question`,
            referenceId: id,
            referenceType: 'forum_question',
            senderId: newComment.comment?.author || newComment.comment?.author_id,
            activityType: 'forum_response',
            pointsEarned: 1,
          }),
        }
      );

      if (!notificationResponse.ok) {
        console.error('Failed to send notification to socket server');
      }
    } catch (notificationError) {
      console.error('Failed to send notification:', notificationError);
      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      success: true,
      comment: newComment.comment,
    });
  } catch (error) {
    console.error('Create forum comment error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
