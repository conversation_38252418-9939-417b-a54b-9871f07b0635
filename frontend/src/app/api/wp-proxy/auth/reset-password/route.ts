import { NextRequest, NextResponse } from 'next/server';
import { getApiUrl } from '@/lib/api/utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Forward the request to WordPress
    const response = await fetch(`${getApiUrl()}/tourismiq/v1/auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Password reset proxy error:', error);
    return NextResponse.json(
      { error: 'Failed to process password reset request' },
      { status: 500 }
    );
  }
}
