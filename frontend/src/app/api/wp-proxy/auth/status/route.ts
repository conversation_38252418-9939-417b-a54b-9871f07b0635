import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { rateLimit, rateLimitConfigs } from '@/lib/rate-limit';

const limiter = rateLimit(rateLimitConfigs.auth);

export async function GET(request: NextRequest) {
  // Apply rate limiting
  const rateLimitResult = limiter.check(request);
  if (!rateLimitResult.success) {
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      {
        status: 429,
        headers: {
          'X-RateLimit-Limit': rateLimitConfigs.auth.max.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimitResult.resetTime?.toString() || '',
        },
      }
    );
  }
  try {
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();

    // Build cookie header string from all cookies
    const cookieHeader = allCookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

    // Get WordPress URL from env or use default
    const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';

    // Headers for WordPress request - ONLY the cookie header is needed for cookie auth
    const headers: HeadersInit = {
      Cookie: cookieHeader,
    };

    // Use our custom auth status endpoint that includes ACF fields
    const targetUrl = `${WORDPRESS_API_URL}/wp-json/tourismiq/v1/auth/status`;

    const response = await fetch(targetUrl, {
      method: 'GET',
      headers,
      // credentials: 'include' is not used for server-to-server fetch when manually setting Cookie header.
    });

    if (!response.ok) {
      // If auth/status returns a 401/403, it means the user is not authenticated via cookie.
      // This is an expected state for a logged-out user.
      if (response.status === 401 || response.status === 403) {
        return NextResponse.json({
          loggedIn: false,
          isLoggedIn: false,
          user: null,
        });
      }
      // For other errors, return the error from WordPress
      await response.text();
      return NextResponse.json(
        {
          error: `WordPress API auth/status returned ${response.status}`,
          loggedIn: false,
          isLoggedIn: false,
          user: null,
        },
        { status: response.status }
      );
    }

    const authData = await response.json();

    // The custom endpoint returns loggedIn and user/wpUser separately
    if (!authData.loggedIn) {
      return NextResponse.json({
        loggedIn: false,
        isLoggedIn: false,
        user: null,
      });
    }

    // Return the response with the properly formatted user data
    return NextResponse.json({
      loggedIn: true,
      isLoggedIn: true,
      user: authData.wpUser || authData.user, // Use wpUser which has ACF fields
      wpUser: authData.wpUser || authData.user,
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to check auth status',
        loggedIn: false,
        isLoggedIn: false,
        user: null,
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
