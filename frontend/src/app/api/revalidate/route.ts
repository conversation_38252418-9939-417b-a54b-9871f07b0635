import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag, revalidatePath } from 'next/cache';

/**
 * Server-side cache revalidation endpoint
 * Called when posts are updated in WordPress to clear Next.js cache
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, slug, id } = body;

    // Verify the request has the required fields
    if (!type) {
      return NextResponse.json({ error: 'Missing type parameter' }, { status: 400 });
    }

    switch (type) {
      case 'post':
        if (slug) {
          // Revalidate specific post by slug
          revalidateTag(`post-${slug}`);
          revalidatePath(`/posts/${slug}`);
          console.log(`[Revalidation] Post slug: ${slug}`);
        }
        if (id) {
          // Revalidate specific post by ID
          revalidateTag(`post-id-${id}`);
          console.log(`[Revalidation] Post ID: ${id}`);
        }
        // Always revalidate general posts cache
        revalidateTag('posts');
        break;

      case 'category':
        // Revalidate category-based caches
        revalidateTag('posts');
        revalidateTag('categories');
        if (slug) {
          revalidateTag(`category-${slug}`);
          revalidatePath(`/category/${slug}`);
        }
        console.log(`[Revalidation] Category: ${slug || id}`);
        break;

      case 'all':
        // Revalidate everything
        revalidateTag('posts');
        revalidateTag('categories');
        revalidateTag('users');
        revalidatePath('/');
        console.log('[Revalidation] All caches cleared');
        break;

      default:
        return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: `Cache revalidated for ${type}`,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in revalidation endpoint:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
