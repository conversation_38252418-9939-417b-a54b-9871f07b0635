import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Get all the WordPress API URL environment variables
    const wpApiUrl1 = process.env.WORDPRESS_API_URL;
    const wpApiUrl2 = process.env.NEXT_PUBLIC_WORDPRESS_URL;
    const wpApiUrl3 = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;

    // Same logic as connections endpoint
    const finalUrl =
      process.env.WORDPRESS_API_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL ||
      'http://tourismiq.local';

    // Test the connections endpoint
    const connectionsEndpoint = `${finalUrl}/wp-json/tourismiq/v1/connections`;
    const testResponse = await fetch(connectionsEndpoint, {
      method: 'GET',
    });

    const testResult: {
      status: number;
      statusText: string;
      ok: boolean;
      body?: string;
    } = {
      status: testResponse.status,
      statusText: testResponse.statusText,
      ok: testResponse.ok,
    };

    try {
      const responseText = await testResponse.text();
      testResult.body = responseText;
    } catch {
      testResult.body = 'Could not read response body';
    }

    return NextResponse.json({
      environmentVariables: {
        WORDPRESS_API_URL: wpApiUrl1,
        NEXT_PUBLIC_WORDPRESS_URL: wpApiUrl2,
        NEXT_PUBLIC_WORDPRESS_API_URL: wpApiUrl3,
      },
      finalUrl,
      connectionsEndpoint,
      testResult,
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Debug endpoint error',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
