import { Metadata } from 'next';
import { searchRfps, Rfp } from '@/lib/api/rfps';
import { RfpsHub } from '@/components/rfps/RfpsHub';

// Mark this page as dynamic since it uses fresh data from the API
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'RFPs Hub | TourismIQ',
  description:
    'Discover Request for Proposal opportunities in the tourism and travel industry. Find your next business opportunity.',
  keywords: [
    'tourism RFPs',
    'travel RFPs',
    'hospitality RFPs',
    'request for proposal',
    'business opportunities',
  ],
};

export default async function RfpsPage() {
  // Fetch initial RFPs server-side for better SEO and initial load performance
  let initialRfps: Rfp[] = [];
  let initialTotal = 0;

  try {
    const response = await searchRfps({
      per_page: 20,
      page: 1,
      orderby: 'date',
      order: 'desc',
    });

    initialRfps = response.rfps;
    initialTotal = response.total;
  } catch (error) {
    console.error('Failed to fetch initial RFPs:', error);
    // Continue with empty state - RfpsHub will handle loading
  }

  return (
    <div className="min-h-screen bg-white">
      <RfpsHub initialRfps={initialRfps} initialTotal={initialTotal} />
    </div>
  );
}
