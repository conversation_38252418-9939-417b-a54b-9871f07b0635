import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { MapPin, Building2, Calendar, ExternalLink, Clock } from 'lucide-react';
import { getRfpBySlug, formatRfpDeadline, getCategoryLabel, getStatusLabel } from '@/lib/api/rfps';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BackToSectionHeader } from '@/components/ui/BackToSectionHeader';
import { decodeHtmlEntities } from '@/lib/utils';

interface RfpPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: RfpPageProps): Promise<Metadata> {
  try {
    const { slug } = await params;
    const rfp = await getRfpBySlug(slug);

    return {
      title: `${decodeHtmlEntities(rfp.title.rendered)} at ${rfp.rfp_meta?.rfp_organization || 'Unknown'} | TourismIQ RFPs`,
      description: decodeHtmlEntities(rfp.excerpt.rendered.replace(/<[^>]*>/g, '')).substring(0, 160),
      keywords: [
        'RFP',
        'request for proposal',
        'tourism',
        'travel',
        ...(rfp.rfp_categories || []),
        rfp.rfp_meta?.rfp_organization || '',
        ...(rfp.rfp_countries || []),
      ].filter(Boolean),
    };
  } catch {
    return {
      title: 'RFP Not Found | TourismIQ',
      description: 'The requested RFP could not be found.',
    };
  }
}

export default async function RfpPage({ params }: RfpPageProps) {
  let rfp;

  try {
    const { slug } = await params;
    rfp = await getRfpBySlug(slug);
  } catch {
    notFound();
  }

  const { title, content, excerpt, date, rfp_meta } = rfp;

  const {
    rfp_status = '',
    rfp_organization = '',
    rfp_source_url = '',
    rfp_deadline = '',
  } = rfp_meta || {};

  // Create location string
  const state = rfp.rfp_states?.[0] || '';
  const country = rfp.rfp_countries?.[0] || '';
  const category = rfp.rfp_categories?.[0] || '';
  const location = [state, country].filter(Boolean).join(', ') || 'Remote';

  // Format dates
  const postingDate = new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // Get deadline info
  const deadlineInfo = formatRfpDeadline(rfp_deadline);
  const isExpired = rfp_status?.toUpperCase() === 'EXPIRED' || deadlineInfo === 'Expired';
  const isUrgent = deadlineInfo.includes('day') && !isExpired;

  // Check for content availability
  const hasDescription = content?.rendered?.trim() || excerpt?.rendered?.trim();
  const hasSourceUrl = rfp_source_url?.trim();

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'NEW':
        return 'bg-[#22c55e] text-white border-[#22c55e]';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'ACTIVE':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-[#eff1f4]">
      {/* Sticky Back to RFPs Header */}
      <BackToSectionHeader href="/rfps" text="Back to RFPs" />

      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-white to-purple-50 border-b">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-5 leading-tight">
              {rfp_status && rfp_status.toUpperCase() === 'NEW' && (
                <Badge className="bg-[#22c55e] text-white border-[#22c55e] text-sm px-2 py-1 mr-3 inline-block align-middle">
                  New
                </Badge>
              )}
              {decodeHtmlEntities(title.rendered)}
            </h1>

            {rfp_organization && (
              <div className="flex items-center justify-center gap-2 mb-5">
                <Building2 className="h-5 w-5 text-purple-600" />
                <span className="text-lg font-semibold text-gray-700">{rfp_organization}</span>
              </div>
            )}

            <div className="flex flex-wrap items-center justify-center gap-6 text-gray-600 mb-6">
              {(state || country) && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>{location}</span>
                </div>
              )}

              {rfp_deadline ? (
                <div
                  className={`flex items-center gap-2 ${isUrgent ? 'text-orange-600 font-medium' : ''}`}
                >
                  <Clock className="h-4 w-4" />
                  <span>Deadline: {deadlineInfo}</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>Posted {postingDate}</span>
                </div>
              )}
            </div>

            <div className="flex flex-wrap items-center justify-center gap-3 mb-6">
              {rfp_status && rfp_status.trim() && rfp_status.toUpperCase() !== 'NEW' && (
                <Badge className={`${getStatusColor(rfp_status)} text-sm px-3 py-1`}>
                  {getStatusLabel(rfp_status)}
                </Badge>
              )}
              {category && category.trim() && (
                <Badge variant="outline" className="text-sm px-3 py-1">
                  {getCategoryLabel(category)}
                </Badge>
              )}
            </div>

            {(hasSourceUrl || isExpired) && (
              <div className="flex justify-center">
                {hasSourceUrl && !isExpired && (
                  <Button size="lg" className="px-8 py-3 text-lg" asChild>
                    <a
                      href={rfp_source_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2"
                    >
                      View RFP Details
                      <ExternalLink className="h-5 w-5" />
                    </a>
                  </Button>
                )}

                {isExpired && (
                  <Button size="lg" className="px-8 py-3 text-lg" disabled>
                    RFP Closed
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      {hasDescription && (
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-sm border p-6 md:p-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">RFP Description</h2>
            <div
              className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
              dangerouslySetInnerHTML={{
                __html: decodeHtmlEntities(content?.rendered || excerpt?.rendered || ''),
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
