import { Suspense } from 'react';
import { getFeaturedPosts } from '@/lib/api/posts';
import HomeClient from './HomeClient';

// Disable caching for development to ensure fresh data
export const revalidate = 0;

export default async function Home() {
  const { posts, totalPages, totalPosts } = await getFeaturedPosts(1, 20, 'date', undefined, false);

  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-[#eff1f4] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#5cc8ff] mx-auto mb-4"></div>
            <p className="text-gray-600 text-lg">Loading your feed...</p>
          </div>
        </div>
      }
    >
      <HomeClient
        initialPosts={posts}
        initialTotalPages={totalPages}
        initialTotalPosts={totalPosts}
      />
    </Suspense>
  );
}
