import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { Member<PERSON>ro<PERSON><PERSON>, Member } from '@/components/member-profile/MemberProfile';

// Client-side wrapper component

async function getMemberByUsername(username: string): Promise<Member | null> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
    const response = await fetch(
      `${apiUrl}/wp-json/tourismiq/v1/members/${encodeURIComponent(username)}`,
      {
        next: { revalidate: 60 }, // Revalidate every 60 seconds
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null; // Member not found
      }
      throw new Error(`Failed to fetch member: ${response.statusText}`);
    }

    const member = await response.json();
    return member || null;
  } catch (error) {
    console.error('Error fetching member:', error);
    return null;
  }
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ username: string }>;
}): Promise<Metadata> {
  try {
    const { username } = await params;
    const member = await getMemberByUsername(username);

    if (!member) {
      return {
        title: 'Member Not Found | TourismIQ',
        description: 'The requested member could not be found.',
      };
    }

    return {
      title: `${member.name} | TourismIQ Member`,
      description: member.bio || `View ${member.name}'s profile on TourismIQ`,
      openGraph: {
        title: `${member.name} | TourismIQ Member`,
        description: member.bio || `View ${member.name}'s profile on TourismIQ`,
        type: 'profile',
        images: [
          {
            url:
              typeof member.profile_picture === 'string'
                ? member.profile_picture
                : member.profile_picture?.url || member.avatar || '',
            width: 300,
            height: 300,
            alt: member.name,
          },
        ],
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Member Profile | TourismIQ',
      description: 'View member profile on TourismIQ',
    };
  }
}

export default async function MemberProfilePage({
  params,
  searchParams,
}: {
  params: Promise<{ username: string }>;
  searchParams: Promise<{ tab?: string }>;
}) {
  try {
    const { username } = await params;
    const { tab } = await searchParams;
    const member = await getMemberByUsername(username);

    if (!member) {
      notFound();
    }

    // Ensure required fields have default values
    const memberWithDefaults: Member = {
      ...member,
      name:
        member.name ||
        member.username ||
        `${member.firstName || ''} ${member.lastName || ''}`.trim() ||
        'Unknown',
      location: member.location || '',
      contactInfo: member.contactInfo || { email: '', phone: '', website: '' },
      socialLinks: member.socialLinks || {
        website: '',
        twitter: '',
        facebook: '',
        linkedin: '',
        instagram: '',
      },
      categories: member.categories || [],
      featured: member.featured || false,
      roles: member.roles || ['member'],
      user_registered: member.user_registered || new Date().toISOString(),
    };

    return <MemberProfile member={memberWithDefaults} initialTab={tab || 'about'} />;
  } catch (error) {
    console.error('Error in MemberProfilePage:', error);
    notFound();
  }
}
