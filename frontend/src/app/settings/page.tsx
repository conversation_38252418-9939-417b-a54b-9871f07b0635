'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { Loader2, Save, MapPin } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { User } from '@/contexts/auth-context';
import {
  FacebookSolid,
  TwitterSolid,
  LinkedinSolid,
  InstagramSolid,
} from '@/components/ui/SocialIcons';

// Form schemas
const profileFormSchema = z.object({
  firstName: z.string().min(2, {
    message: 'First name must be at least 2 characters.',
  }),
  lastName: z.string().min(2, {
    message: 'Last name must be at least 2 characters.',
  }),
  email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  bio: z
    .string()
    .max(500, {
      message: 'Bio must not exceed 500 characters.',
    })
    .optional(),
  jobTitle: z.string().optional(),
  company: z.string().optional(),
  location: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().optional(),
  twitter: z.string().optional(),
  linkedin: z.string().optional(),
  facebook: z.string().optional(),
  instagram: z.string().optional(),
});

const passwordFormSchema = z
  .object({
    currentPassword: z.string().min(1, {
      message: 'Current password is required.',
    }),
    newPassword: z.string().min(8, {
      message: 'New password must be at least 8 characters.',
    }),
    confirmPassword: z.string().min(1, {
      message: 'Please confirm your new password.',
    }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match.",
    path: ['confirmPassword'],
  });

export default function SettingsPage() {
  const { user, updateProfile, refreshAuth } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPasswordSubmitting, setIsPasswordSubmitting] = useState(false);
  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setIsLoading(true);
        // Use the user from auth context - no need for separate profile state
      } catch (error) {
        console.error('Error fetching user profile:', error);
        toast.error('Failed to load profile data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [user]);

  // Form handling
  const form = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      bio: '',
      jobTitle: '',
      company: '',
      location: '',
      phone: '',
      website: '',
      twitter: '',
      linkedin: '',
      facebook: '',
      instagram: '',
    },
    // This setting improves form state synchronization
    reValidateMode: 'onChange',
  });

  const passwordForm = useForm<z.infer<typeof passwordFormSchema>>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // Update form values when user changes
  useEffect(() => {
    if (user) {
      const formData = {
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        bio: user.bio || user.acf?.bio || '',
        jobTitle: user.jobTitle || user.acf?.job_title || '',
        company: user.company || user.acf?.company || '',
        location: user.location || user.acf?.location || '',
        phone: user.phone || user.acf?.phone || '',
        website: user.acf?.website || '',
        twitter: user.socialLinks?.twitter || user.acf?.twitter || '',
        linkedin: user.socialLinks?.linkedin || user.acf?.linkedin || '',
        facebook: user.socialLinks?.facebook || user.acf?.facebook || '',
        instagram: user.socialLinks?.instagram || user.acf?.instagram || '',
      };

      form.reset(formData);
    }
  }, [user, form]);

  const onSubmit = async (data: z.infer<typeof profileFormSchema>) => {
    try {
      // Set loading state
      setIsLoading(true);
      setIsSubmitting(true);

      // Prepare the update data - always include fields even if empty
      const updateData: Partial<User> = {
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        bio: data.bio || '',
        jobTitle: data.jobTitle || '',
        company: data.company || '',
        location: data.location || '',
        phone: data.phone || '',
        website: data.website || '',
        roles: user?.roles || [],
        socialLinks: {
          twitter: data.twitter || '',
          linkedin: data.linkedin || '',
          facebook: data.facebook || '',
          instagram: data.instagram || '',
        },
        acf: {
          ...(user?.acf || {}),
          bio: data.bio || '',
          job_title: data.jobTitle || '',
          company: data.company || '',
          location: data.location || '',
          phone: data.phone || '',
          website: data.website || '',
          twitter: data.twitter || '',
          linkedin: data.linkedin || '',
          facebook: data.facebook || '',
          instagram: data.instagram || '',
        },
        username: user?.username || '',
        name: `${data.firstName} ${data.lastName}`.trim() || user?.name || '',
        email: data.email || user?.email || '',
      };

      // Send the profile update request
      await updateProfile(updateData);

      // Force refresh auth data to get the latest from server without caching
      await refreshAuth();

      // Show success message immediately
      toast.success('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to update profile. Please try again.'
      );
    } finally {
      // Turn off the general loading state
      setIsLoading(false);

      // Set a timeout for the button loading state to ensure
      // it stays visible longer than the general loading state
      setTimeout(() => {
        setIsSubmitting(false);
      }, 500); // Additional 500ms delay for button
    }
  };

  const onPasswordSubmit = async (data: z.infer<typeof passwordFormSchema>) => {
    try {
      setIsPasswordSubmitting(true);

      const response = await fetch('/api/user/password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          currentPassword: data.currentPassword,
          newPassword: data.newPassword,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to update password');
      }

      // Reset the form
      passwordForm.reset();

      toast.success('Password updated successfully!');
    } catch (error) {
      console.error('Error updating password:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to update password. Please try again.'
      );
    } finally {
      setIsPasswordSubmitting(false);
    }
  };

  return (
    <div className="py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">Manage your account settings and preferences</p>
        </div>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full max-w-[400px] grid-cols-2 bg-white border border-gray-200 p-1 px-[5px] h-12 rounded-full">
          <TabsTrigger
            value="profile"
            className="data-[state=active]:bg-[#5cc8ff] data-[state=active]:text-[#3d405b] data-[state=active]:font-bold py-2 rounded-full"
          >
            Profile
          </TabsTrigger>
          <TabsTrigger
            value="account"
            className="data-[state=active]:bg-[#5cc8ff] data-[state=active]:text-[#3d405b] data-[state=active]:font-bold py-2 rounded-full"
          >
            Account
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Profile</CardTitle>
              <CardDescription>This is how others will see you on the platform.</CardDescription>
            </CardHeader>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      placeholder="John"
                      className="rounded-full"
                      {...form.register('firstName')}
                    />
                    {form.formState.errors.firstName && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.firstName.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      placeholder="Doe"
                      className="rounded-full"
                      {...form.register('lastName')}
                    />
                    {form.formState.errors.lastName && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.lastName.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="rounded-full"
                    {...form.register('email')}
                  />
                  {form.formState.errors.email && (
                    <p className="text-sm text-red-500">{form.formState.errors.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="jobTitle">Job Title</Label>
                  <Input
                    id="jobTitle"
                    placeholder="e.g. Tourism Specialist"
                    className="rounded-full"
                    {...form.register('jobTitle')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    placeholder="e.g. ABC Tourism Board"
                    className="rounded-full"
                    {...form.register('company')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="location"
                      placeholder="City, Country"
                      className="pl-10 rounded-full"
                      {...form.register('location')}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    placeholder="e.g. +****************"
                    className="rounded-full"
                    {...form.register('phone')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    type="url"
                    placeholder="https://www.example.com"
                    className="rounded-full"
                    {...form.register('website')}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Social Links</Label>
                  <div className="grid gap-3">
                    <div className="relative">
                      <TwitterSolid className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        placeholder="Twitter username"
                        className="pl-10 rounded-full"
                        {...form.register('twitter')}
                      />
                    </div>
                    <div className="relative">
                      <LinkedinSolid className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        placeholder="LinkedIn profile URL"
                        className="pl-10 rounded-full"
                        {...form.register('linkedin')}
                      />
                    </div>
                    <div className="relative">
                      <FacebookSolid className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        placeholder="Facebook profile URL"
                        className="pl-10 rounded-full"
                        {...form.register('facebook')}
                      />
                    </div>
                    <div className="relative">
                      <InstagramSolid className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        placeholder="Instagram username"
                        className="pl-10 rounded-full"
                        {...form.register('instagram')}
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio">Bio</Label>
                  <Textarea
                    id="bio"
                    placeholder="Tell us a little bit about yourself"
                    className="min-h-[100px] rounded-[20px]"
                    {...form.register('bio')}
                  />
                  <p className="text-sm text-muted-foreground">
                    This will be displayed on your public profile.
                  </p>
                  {form.formState.errors.bio && (
                    <p className="text-sm text-red-500">{form.formState.errors.bio.message}</p>
                  )}
                </div>
              </CardContent>
              <CardFooter className="px-6 py-4 pb-[34px]">
                <Button type="submit" disabled={isSubmitting || isLoading} className="relative">
                  {isSubmitting && (
                    <span className="absolute inset-0 flex items-center justify-center bg-primary rounded-full">
                      <Loader2 className="h-5 w-5 animate-spin text-primary-foreground" />
                    </span>
                  )}
                  <span className={isSubmitting ? 'opacity-0' : 'flex items-center'}>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </span>
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="account" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Account</CardTitle>
              <CardDescription>
                Update your account settings. Manage your password and security preferences.
              </CardDescription>
            </CardHeader>
            <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Change Password</Label>
                  <div className="grid gap-3">
                    <div className="space-y-2">
                      <Input
                        type="password"
                        placeholder="Current Password"
                        className="rounded-full"
                        {...passwordForm.register('currentPassword')}
                      />
                      {passwordForm.formState.errors.currentPassword && (
                        <p className="text-sm text-red-500">
                          {passwordForm.formState.errors.currentPassword.message}
                        </p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Input
                        type="password"
                        placeholder="New Password"
                        className="rounded-full"
                        {...passwordForm.register('newPassword')}
                      />
                      {passwordForm.formState.errors.newPassword && (
                        <p className="text-sm text-red-500">
                          {passwordForm.formState.errors.newPassword.message}
                        </p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Input
                        type="password"
                        placeholder="Confirm New Password"
                        className="rounded-full"
                        {...passwordForm.register('confirmPassword')}
                      />
                      {passwordForm.formState.errors.confirmPassword && (
                        <p className="text-sm text-red-500">
                          {passwordForm.formState.errors.confirmPassword.message}
                        </p>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Use a strong, unique password that you don&apos;t use elsewhere.
                  </p>
                </div>
              </CardContent>
              <CardFooter className="px-6 py-4 pb-[34px]">
                <Button type="submit" disabled={isPasswordSubmitting} className="relative">
                  {isPasswordSubmitting && (
                    <span className="absolute inset-0 flex items-center justify-center bg-primary rounded-full">
                      <Loader2 className="h-5 w-5 animate-spin text-primary-foreground" />
                    </span>
                  )}
                  <span className={isPasswordSubmitting ? 'opacity-0' : 'flex items-center'}>
                    Update Password
                  </span>
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export const dynamic = 'force-dynamic';
