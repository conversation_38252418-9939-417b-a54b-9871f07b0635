/* Custom Swiper styles for people cards */
.people-card-swiper {
  height: 100%;
  width: 100%;
}

.people-card-swiper .swiper-button-next,
.people-card-swiper .swiper-button-prev {
  width: 30px;
  height: 30px;
  margin-top: -15px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  color: #0ea5e9 !important;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.people-card-swiper .swiper-button-next:after,
.people-card-swiper .swiper-button-prev:after {
  font-size: 12px;
}

.people-card-swiper .swiper-button-next:hover,
.people-card-swiper .swiper-button-prev:hover {
  background: rgba(255, 255, 255, 1);
}

.people-card-swiper .swiper-pagination {
  bottom: 8px;
}

.people-card-swiper .swiper-pagination-bullet {
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 1;
}

.people-card-swiper .swiper-pagination-bullet-active {
  background: #0ea5e9;
}
