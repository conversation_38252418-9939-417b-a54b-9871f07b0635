/*********************************************
# frontend/src/app/people/page.tsx
# 02/06/2025 11:45am Replaced custom search input with consistent SearchBar component using Jobs Hub styling
# 02/07/2025 12:00pm Updated filter menus to use pill-shaped rounded-full styling for consistency
**********************************************/

'use client';

import { useState, useEffect, useRef, useCallback, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import SearchBar from '@/components/forum/search-bar';
import Image from 'next/image';

import './swiper-styles.css';

interface PersonMetaData {
  people_gallery?: GalleryImage[];
  move_type?: string;
  position_level?: string;
  from_company?: string;
  new_company?: string;
  position_title?: string;
  location?: string;
}

interface Person {
  id: number;
  title: { rendered: string };
  content: { rendered: string };
  slug: string;
  date: string;
  featured_media_url?: string;
  people_meta: PersonMetaData;
}

interface GalleryImage {
  ID: number;
  url: string;
  alt: string;
  title: string;
  sizes: {
    full: string;
    large: string;
    medium: string;
    thumbnail: string;
  };
}

interface PaginatedResponse {
  people: Person[];
  totalPages: number;
  currentPage: number;
  totalItems: number;
}

// Component to render the new image grid layout - simplified using 4-image template
function PeopleImageGrid({ images }: { images: GalleryImage[] }) {
  // Helper to get image URL with fallback to different sizes
  const getImageUrl = (image: GalleryImage): string => {
    return image.sizes.large || image.sizes.medium || image.url || '';
  };

  // Determine grid layout based on image count
  const getGridCols = (count: number): string => {
    if (count === 1) return 'grid-cols-1 max-w-[140px]';
    if (count === 2) return 'grid-cols-2 max-w-[284px]';
    if (count === 3) return 'grid-cols-3 max-w-[426px]';
    return 'grid-cols-4'; // 4 or more images
  };

  // Split images into rows of maximum 4
  const createRows = (images: GalleryImage[]) => {
    const rows = [];
    for (let i = 0; i < images.length; i += 4) {
      rows.push(images.slice(i, i + 4));
    }
    return rows;
  };

  const rows = createRows(images);

  return (
    <div
      className="w-full py-10 px-10 rounded-lg"
      style={{ backgroundColor: 'rgba(170, 215, 238, 0.6)' }}
    >
      <div className="space-y-2">
        {rows.map((row, rowIndex) => (
          <div key={rowIndex} className={`grid gap-2 mx-auto ${getGridCols(row.length)}`}>
            {row.map((image, index) => (
              <div
                key={image.ID || index}
                className="aspect-square overflow-hidden bg-muted rounded-lg max-w-[140px] max-h-[140px]"
              >
                <Image
                  src={getImageUrl(image)}
                  alt={image.alt || 'Person image'}
                  width={140}
                  height={140}
                  className="object-cover w-full h-full"
                  priority={false}
                />
              </div>
            ))}
          </div>
        ))}
        {images.length > 10 && (
          <div className="text-center text-sm text-gray-500 mt-2">
            +{images.length - 10} more images
          </div>
        )}
      </div>
    </div>
  );
}

function PeoplePageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const headerRef = useRef<HTMLDivElement>(null);

  const currentPage = parseInt(searchParams.get('page') || '1');
  const searchTerm = searchParams.get('search') || '';

  const [, setPeople] = useState<Person[]>([]);
  const [groupedPeople, setGroupedPeople] = useState<Record<string, Person[]>>({});
  const [expandedYears, setExpandedYears] = useState<Record<string, boolean>>({});
  const [, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchInput, setSearchInput] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  const fetchPeople = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: '100',
        ...(debouncedSearchTerm && { search: debouncedSearchTerm }),
      });

      const response = await fetch(`/api/wp-proxy/people?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch people');
      }

      const data: PaginatedResponse = await response.json();
      setPeople(data.people);
      setTotalPages(data.totalPages);
      setTotalItems(data.totalItems);

      // Group people by year
      const grouped = data.people.reduce(
        (acc, person) => {
          const year = new Date(person.date).getFullYear().toString();
          if (!acc[year]) {
            acc[year] = [];
          }
          acc[year].push(person);
          return acc;
        },
        {} as Record<string, Person[]>
      );

      setGroupedPeople(grouped);

      // Initialize expanded years (all expanded by default)
      const years = Object.keys(grouped);
      setExpandedYears(years.reduce((acc, year) => ({ ...acc, [year]: true }), {}));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch people');
    } finally {
      setLoading(false);
    }
  }, [currentPage, debouncedSearchTerm]);

  useEffect(() => {
    setSearchInput(searchTerm);
  }, [searchTerm]);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchInput);
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchInput]);

  useEffect(() => {
    fetchPeople();
  }, [fetchPeople]);

  useEffect(() => {
    // Add overflow-y-scroll to html element to always show scrollbar
    document.documentElement.style.overflowY = 'scroll';

    return () => {
      // Clean up when component unmounts
      document.documentElement.style.overflowY = '';
    };
  }, []);

  const toggleYear = (year: string) => {
    setExpandedYears((prev) => ({
      ...prev,
      [year]: !prev[year],
    }));
  };

  const toggleAllYears = () => {
    const areAllCollapsed = Object.values(expandedYears).every((value) => !value);
    setExpandedYears(
      Object.keys(groupedPeople).reduce((acc, year) => ({ ...acc, [year]: areAllCollapsed }), {})
    );
  };

  const areAllCollapsed = Object.values(expandedYears).every((value) => !value);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const decodeHtmlEntities = (text: string) => {
    const textArea = document.createElement('textarea');
    textArea.innerHTML = text;
    return textArea.value;
  };

  if (error) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-[#3d405b] mb-4">Error Loading People</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={fetchPeople}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-0 overflow-x-hidden">
      {/* Hero Section */}
      <div className="relative w-full aspect-[1118/486] bg-gray-100">
        <Image
          src="/images/people-move.jpg"
          alt="People on the Move - Tourism Industry Personnel Updates"
          fill
          className="object-cover"
          priority
        />
      </div>

      <Card className="shadow-sm -mt-6 rounded-none rounded-b-lg">
        <div className="px-6 py-8">
          {/* Category Tag and Heading */}
          <div ref={headerRef} className="relative z-10 mb-8">
            <Badge className="bg-pink-600 text-white mb-2">People on the Move</Badge>
            <h1 className="text-3xl font-bold text-gray-700">It&apos;s Who You Know...</h1>
          </div>

          {/* Search and Filters Section */}
          <div className="space-y-4 mb-8">
            {/* Search Bar */}
            <SearchBar
              onSearch={(query) => setSearchInput(query)}
              placeholder="Search by name, company, or position..."
              defaultValue={searchInput}
              inputClassName="bg-white rounded-full h-12 border border-gray-200 px-6 text-base"
            />

            {/* Clear Search Button */}
            {debouncedSearchTerm && (
              <div className="flex justify-end">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchInput('');
                    setDebouncedSearchTerm('');
                    router.push('/people');
                  }}
                  className="text-gray-600 hover:text-gray-800"
                >
                  <X className="h-4 w-4 mr-1" />
                  Clear search
                </Button>
              </div>
            )}
          </div>

          {/* Results Info */}
          {!loading && (
            <div className="mb-4 text-sm text-gray-600">
              {debouncedSearchTerm ? (
                <p>
                  Found {totalItems} result{totalItems !== 1 ? 's' : ''} for &quot;
                  {debouncedSearchTerm}&quot;
                </p>
              ) : (
                <p>Showing {totalItems} people</p>
              )}
            </div>
          )}

          {/* Loading State */}
          {loading && (
            <div className="space-y-8">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-12 bg-gray-200 rounded mb-4"></div>
                  <div className="border-b pb-8">
                    <div className="flex flex-col md:flex-row gap-6">
                      <div className="w-full md:w-1/3 h-[300px] bg-gray-200 rounded"></div>
                      <div className="w-full md:w-2/3 space-y-4">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 rounded"></div>
                          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                        </div>
                        <div className="flex gap-2">
                          <div className="h-6 bg-gray-200 rounded w-20"></div>
                          <div className="h-6 bg-gray-200 rounded w-24"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* People Feed */}
          {!loading && (
            <div className="space-y-3">
              <div className="flex justify-end mb-2 mt-1">
                <button
                  onClick={toggleAllYears}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  {areAllCollapsed ? 'Expand all' : 'Collapse all'}
                </button>
              </div>
              {Object.entries(groupedPeople)
                .sort(([yearA], [yearB]) => parseInt(yearB) - parseInt(yearA))
                .map(([year, yearPeople]) => (
                  <div key={year} className="space-y-8">
                    <Button
                      variant="ghost"
                      onClick={() => toggleYear(year)}
                      className="w-full flex justify-between items-center py-4 text-xl font-semibold bg-[#EBF5FF]/75 hover:bg-[#EBF5FF]/75 rounded-full"
                    >
                      {year}
                      <span>{expandedYears[year] ? '−' : '+'}</span>
                    </Button>

                    {expandedYears[year] && (
                      <div className="space-y-8">
                        {yearPeople.map((person) => {
                          const hasGallery =
                            person.people_meta?.people_gallery &&
                            person.people_meta.people_gallery.length > 0;

                          return (
                            <div key={person.id} className="border-b pb-8">
                              <div className="space-y-6">
                                {/* Image Grid - Full width above content */}
                                <div className="w-full">
                                  {hasGallery ? (
                                    <PeopleImageGrid images={person.people_meta.people_gallery!} />
                                  ) : person.featured_media_url ? (
                                    <div
                                      className="w-full py-10 px-10 rounded-lg"
                                      style={{ backgroundColor: 'rgba(170, 215, 238, 0.6)' }}
                                    >
                                      <div className="grid grid-cols-1 max-w-[140px] mx-auto">
                                        <div className="aspect-square overflow-hidden bg-muted rounded-lg max-w-[140px] max-h-[140px]">
                                          <Image
                                            src={person.featured_media_url}
                                            alt={person.title.rendered}
                                            width={140}
                                            height={140}
                                            className="object-cover w-full h-full"
                                            priority={false}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  ) : (
                                    <div
                                      className="w-full py-10 px-10 rounded-lg"
                                      style={{ backgroundColor: 'rgba(170, 215, 238, 0.6)' }}
                                    >
                                      <div className="grid grid-cols-1 max-w-[140px] mx-auto">
                                        <div className="aspect-square flex items-center justify-center bg-gray-100 rounded-lg text-gray-600 text-3xl max-w-[140px] max-h-[140px]">
                                          {getInitials(
                                            decodeHtmlEntities(person.title.rendered) || 'Unknown'
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>

                                {/* Content - Full width below images */}
                                <div className="w-full">
                                  <p className="text-sm text-gray-500 mb-2">
                                    {new Date(person.date).toLocaleDateString('en-US', {
                                      year: 'numeric',
                                      month: 'long',
                                      day: 'numeric',
                                    })}
                                  </p>
                                  <h3 className="text-xl font-bold text-[#3d405b] mb-2">
                                    {decodeHtmlEntities(person.title.rendered)}
                                  </h3>
                                  <div className="text-lg mb-4 prose prose-sm max-w-none">
                                    <div
                                      dangerouslySetInnerHTML={{
                                        __html: person.content.rendered,
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                ))}

              {/* No results message */}
              {Object.keys(groupedPeople).length === 0 && (
                <div className="text-center py-12">
                  <h3 className="text-lg font-medium text-[#3d405b] mb-2">No people found</h3>
                  <p className="text-gray-600">
                    {debouncedSearchTerm
                      ? 'Try adjusting your search terms.'
                      : 'No people have been added yet.'}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

export default function PeoplePage() {
  return (
    <Suspense
      fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}
    >
      <PeoplePageContent />
    </Suspense>
  );
}
