'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthStatus } from '@/hooks/queries/useAuth';
import { useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';

export default function DebugAuthPage() {
  const { isLoggedIn, isLoading, user, error } = useAuthStatus();
  const queryClient = useQueryClient();

  const forceRefreshAuth = () => {
    // Invalidate and refetch the auth query
    queryClient.invalidateQueries({ queryKey: queryKeys.auth.status() });
  };

  const clearAuthCache = () => {
    // Remove the auth query from cache entirely
    queryClient.removeQueries({ queryKey: queryKeys.auth.status() });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Authentication Debug</h1>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Current Auth Status</CardTitle>
            <CardDescription>Debug information for authentication state</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p>
                <strong>Is Logged In:</strong> {isLoggedIn ? '✅ Yes' : '❌ No'}
              </p>
              <p>
                <strong>Is Loading:</strong> {isLoading ? '⏳ Yes' : '✅ No'}
              </p>
              <p>
                <strong>User ID:</strong> {user?.id || 'None'}
              </p>
              <p>
                <strong>Username:</strong> {user?.username || 'None'}
              </p>
              <p>
                <strong>Display Name:</strong> {user?.display_name || 'None'}
              </p>
              <p>
                <strong>Email:</strong> {user?.email || 'None'}
              </p>
              <p>
                <strong>Error:</strong> {error?.message || 'None'}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Debug Actions</CardTitle>
            <CardDescription>Tools to force refresh authentication state</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-x-4">
              <Button onClick={forceRefreshAuth} variant="outline">
                Force Refresh Auth
              </Button>
              <Button onClick={clearAuthCache} variant="outline">
                Clear Auth Cache
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-2">
              <li>Check if you see correct authentication status above</li>
              <li>If not logged in but you should be, click &quot;Force Refresh Auth&quot;</li>
              <li>
                If still not working, click &quot;Clear Auth Cache&quot; then &quot;Force Refresh
                Auth&quot;
              </li>
              <li>Check browser cookies for wordpress_logged_in_* cookie</li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
