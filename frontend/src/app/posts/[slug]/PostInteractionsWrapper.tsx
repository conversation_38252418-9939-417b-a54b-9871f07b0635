'use client';

import { useState } from 'react';
import { PostInteractions } from '@/components/feed/PostInteractions';
import { PostComments } from '@/components/feed/PostComments';

interface PostInteractionsWrapperProps {
  postId: string | number;
  authorId: string | number;
  initialUpvotes: number;
  initialCommentCount: number;
  postTitle: string;
}

export function PostInteractionsWrapper({
  postId,
  authorId,
  initialUpvotes,
  initialCommentCount,
  postTitle,
}: PostInteractionsWrapperProps) {
  const [showComments, setShowComments] = useState(false);

  return (
    <div className="w-full">
      {/* Interactive Footer */}
      <div className="border-t border-gray-200 bg-gray-100/50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto p-4 flex justify-center">
          <PostInteractions
            postId={postId}
            authorId={authorId}
            initialUpvotes={initialUpvotes}
            initialCommentCount={initialCommentCount}
            postTitle={postTitle}
            layout="full-width"
            onCommentsToggle={setShowComments}
          />
        </div>
      </div>

      {/* Comments Section */}
      <PostComments
        postId={postId}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        commentCount={initialCommentCount}
      />
    </div>
  );
}
