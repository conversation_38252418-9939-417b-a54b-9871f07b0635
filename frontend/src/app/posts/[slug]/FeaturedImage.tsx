'use client';

import Image from 'next/image';
import { useImageObjectFit } from '@/components/feed/feed-item-helpers';

interface FeaturedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  priority?: boolean;
}

export function FeaturedImage({
  src,
  alt,
  width = 1200,
  height = 675,
  priority = true,
}: FeaturedImageProps) {
  const { objectFitClass, handleImageLoad } = useImageObjectFit();

  return (
    <div className="aspect-video overflow-hidden bg-muted">
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={`${objectFitClass} w-full h-full post-image-glow`}
        onLoad={handleImageLoad}
        priority={priority}
      />
    </div>
  );
}
