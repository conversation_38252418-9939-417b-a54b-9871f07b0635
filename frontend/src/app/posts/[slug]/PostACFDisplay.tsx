import React from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  Calendar,
  MapPin,
  Link as LinkIcon,
  User,
  Clock,
  Building2,
  Play,
  Download,
  ExternalLink,
  Mic,
  Video,
  BookOpen,
  Briefcase,
  DollarSign,
  Users,
} from 'lucide-react';

interface ACFImage {
  url?: string;
  [key: string]: unknown;
}

interface Host {
  host_name: string;
}

interface Creator {
  creator_name: string;
}

interface Presenter {
  presenter_name: string;
}

interface ACFFields {
  // Common fields
  url?: string;
  image_caption?: string;

  // Blog Post fields
  title?: string;
  author?: string;
  website_logo?: string | ACFImage | ACFImage[];
  post_publish_date?: string;
  post_thoughts?: string;

  // Event fields
  event_title?: string;
  event_date?: string;
  event_time?: string;
  event_end_date?: string;
  event_location?: string;
  host_company_organization?: string;
  host_company_organization_logo?: string | ACFImage | ACFImage[];
  registration_link_url?: string;
  additional_details_link_url?: string;
  event_description?: string;

  // Job fields
  position_title?: string;
  company?: string;
  company_logo?: string | ACFImage | ACFImage[];
  location?: string;
  job_type?: string;
  experience_level?: string;
  compensation?: string;
  posting_link_url?: string;
  description?: string;

  // Webinar fields
  webinar_type?: string;
  date?: string;
  time?: string;
  time_zone?: string;
  presenters?: Presenter[];
  webinar_host_company_organization?: string;
  webinar_host_company_organization_logo?: string | ACFImage | ACFImage[];
  register_url?: string;
  video_type?: string; // For recorded webinars
  youtube_url?: string;
  vimeo_url?: string;
  twitter?: string;
  linkedin_iframe?: string;

  // Video fields
  video_title?: string;
  creators?: Creator[];
  creator_logo?: string | ACFImage | ACFImage[];
  video_source?: string;
  youtube_id?: string;
  vimeo_id?: string;
  x?: string;

  // Course fields
  course_url?: string;
  sign_up_url?: string;

  // Presentation fields
  presentation_title?: string;
  pdf_upload?: string | ACFImage | ACFImage[];

  // Whitepaper/Case Study fields
  companyorganization_logo?: string | ACFImage | ACFImage[];
  upload_pdf?: string | ACFImage | ACFImage[];

  // Book fields
  book_title?: string;
  author_names?: string;
  purchase_url?: string;

  // Podcast fields
  podcast_episode_title?: string;
  podcast_logo?: string | ACFImage | ACFImage[];
  podcast_name?: string;
  hosts?: Host[];
  episode_description?: string;
  episode_url?: string;
  audio_file?: string;

  // Press Release fields
  press_release_url?: string;

  // Template fields
  creator_name?: string;
  logo?: string | ACFImage | ACFImage[];
  file_upload?: string | ACFImage | ACFImage[];

  [key: string]: unknown;
}

interface PostACFDisplayProps {
  postType: string;
  acf: ACFFields;
}

// Helper function to get ACF image URL
const getAcfImageUrl = (imageField: unknown): string | null => {
  if (!imageField) return null;
  try {
    if (typeof imageField === 'object' && imageField !== null) {
      if ('url' in imageField && typeof imageField.url === 'string') {
        return imageField.url.trim();
      }
      if (Array.isArray(imageField) && imageField[0]?.url) {
        return String(imageField[0].url).trim();
      }
    }
    if (typeof imageField === 'string' && imageField.trim()) {
      return imageField.trim();
    }
  } catch (error) {
    console.warn('Error processing ACF image field:', error);
  }
  return null;
};

// Helper function to get button styles that match feed items
const getButtonStyles = (): string => {
  // Use our consistent button styling from feed items
  return 'text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold';
};

export function PostACFDisplay({ postType, acf }: PostACFDisplayProps) {
  if (!acf) return null;

  const buttonStyles = getButtonStyles();

  switch (postType) {
    case 'blogPost':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Blog Post Details</h3>

          {acf.website_logo && (
            <div className="mb-4">
              <Image
                src={getAcfImageUrl(acf.website_logo) || ''}
                alt="Website logo"
                width={200}
                height={80}
                className="object-contain post-image-glow"
              />
            </div>
          )}

          {acf.author && (
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-primary" />
              <span>Author: {acf.author}</span>
            </div>
          )}

          {acf.post_publish_date && (
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              <span>Published: {acf.post_publish_date}</span>
            </div>
          )}

          {acf.post_thoughts && (
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-medium mb-2">Thoughts:</h4>
              <div dangerouslySetInnerHTML={{ __html: acf.post_thoughts }} />
            </div>
          )}

          <div className="flex flex-wrap gap-3">
            {acf.url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Read Original Article
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'news':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">News Article Details</h3>
          <div className="flex flex-wrap gap-3">
            {acf.url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Read Full Article
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'event':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Event Details</h3>

          <div className="grid md:grid-cols-2 gap-4">
            {acf.event_date && (
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-primary" />
                <span>Start Date: {acf.event_date}</span>
              </div>
            )}
            {acf.event_end_date && (
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-primary" />
                <span>End Date: {acf.event_end_date}</span>
              </div>
            )}
            {acf.event_time && (
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-primary" />
                <span>
                  Time: {acf.event_time} {acf.time_zone || ''}
                </span>
              </div>
            )}
            {acf.event_location && (
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-primary" />
                <span>{acf.event_location}</span>
              </div>
            )}
          </div>

          {acf.host_company_organization && (
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Building2 className="h-5 w-5 text-primary" />
                <span>Hosted by: {acf.host_company_organization}</span>
              </div>
              {acf.host_company_organization_logo && (
                <Image
                  src={getAcfImageUrl(acf.host_company_organization_logo) || ''}
                  alt={`${acf.host_company_organization} logo`}
                  width={200}
                  height={80}
                  className="object-contain post-image-glow"
                />
              )}
            </div>
          )}

          {acf.event_description && (
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-medium mb-2">Event Description:</h4>
              <div dangerouslySetInnerHTML={{ __html: acf.event_description }} />
            </div>
          )}

          <div className="flex flex-wrap gap-3">
            {acf.additional_details_link_url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.additional_details_link_url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Details
                </a>
              </Button>
            )}
            {acf.registration_link_url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.registration_link_url} target="_blank" rel="noopener noreferrer">
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Register
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'job':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Job Details</h3>

          <div className="grid md:grid-cols-2 gap-4">
            {acf.position_title && (
              <div className="flex items-center gap-2">
                <Briefcase className="h-5 w-5 text-primary" />
                <span className="font-medium">{acf.position_title}</span>
              </div>
            )}
            {acf.company && (
              <div className="flex items-center gap-2">
                <Building2 className="h-5 w-5 text-primary" />
                <span>{acf.company}</span>
              </div>
            )}
            {acf.location && (
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-primary" />
                <span>{acf.location}</span>
              </div>
            )}
            {acf.job_type && (
              <div className="flex items-center gap-2">
                <Briefcase className="h-5 w-5 text-primary" />
                <span className="capitalize">{acf.job_type.replace('-', ' ')}</span>
              </div>
            )}
            {acf.experience_level && (
              <div className="flex items-center gap-2">
                <User className="h-5 w-5 text-primary" />
                <span>{acf.experience_level}</span>
              </div>
            )}
            {acf.compensation && (
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-primary" />
                <span>{acf.compensation}</span>
              </div>
            )}
          </div>

          {acf.company_logo && (
            <div>
              <Image
                src={getAcfImageUrl(acf.company_logo) || ''}
                alt={`${acf.company} logo`}
                width={200}
                height={80}
                className="object-contain post-image-glow"
              />
            </div>
          )}

          {acf.description && (
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-medium mb-2">Job Description:</h4>
              <div dangerouslySetInnerHTML={{ __html: acf.description }} />
            </div>
          )}

          <div className="flex flex-wrap gap-3">
            {acf.posting_link_url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.posting_link_url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Apply Now
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'webinar':
      // Helper function to get webinar type value
      const getWebinarType = () => {
        if (!acf.webinar_type) return null;
        if (typeof acf.webinar_type === 'string') return acf.webinar_type;
        if (
          typeof acf.webinar_type === 'object' &&
          acf.webinar_type &&
          'value' in acf.webinar_type
        ) {
          return (acf.webinar_type as { value: string }).value;
        }
        return null;
      };

      const webinarType = getWebinarType();
      const isLiveWebinar = webinarType === 'live';

      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Webinar Details</h3>

          {webinarType && (
            <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-700">
              {isLiveWebinar ? 'Live Webinar' : 'Recorded Webinar'}
            </div>
          )}

          {/* Live webinar details */}
          {isLiveWebinar && (
            <div className="grid md:grid-cols-2 gap-4">
              {acf.date && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  <span>Date: {acf.date}</span>
                </div>
              )}
              {acf.time && (
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-primary" />
                  <span>
                    Time: {acf.time} {acf.time_zone ? ` ${acf.time_zone}` : ''}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Presenters - always shown */}
          {acf.presenters && Array.isArray(acf.presenters) && acf.presenters.length > 0 && (
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <span>
                Presenters:{' '}
                {acf.presenters.map((presenter: Presenter) => presenter.presenter_name).join(', ')}
              </span>
            </div>
          )}

          {/* Host company - always shown */}
          {acf.webinar_host_company_organization && (
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Building2 className="h-5 w-5 text-primary" />
                <span>Hosted by: {acf.webinar_host_company_organization}</span>
              </div>
              {acf.webinar_host_company_organization_logo && (
                <Image
                  src={getAcfImageUrl(acf.webinar_host_company_organization_logo) || ''}
                  alt={`${acf.webinar_host_company_organization} logo`}
                  width={200}
                  height={80}
                  className="object-contain post-image-glow"
                />
              )}
            </div>
          )}

          {/* Buttons */}
          <div className="flex flex-wrap gap-3">
            {/* Registration button for live webinars */}
            {acf.register_url && isLiveWebinar && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.register_url} target="_blank" rel="noopener noreferrer">
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Register for Webinar
                </a>
              </Button>
            )}

            {/* For recorded webinars, video is embedded at the top, so no video buttons needed */}
          </div>
        </div>
      );

    case 'video':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Video Details</h3>

          {acf.creators && Array.isArray(acf.creators) && acf.creators.length > 0 && (
            <div className="flex items-center gap-2">
              <Video className="h-5 w-5 text-primary" />
              <span>
                Creators: {acf.creators.map((creator: Creator) => creator.creator_name).join(', ')}
              </span>
            </div>
          )}

          {acf.creator_logo && (
            <Image
              src={getAcfImageUrl(acf.creator_logo) || ''}
              alt="Creator logo"
              width={200}
              height={80}
              className="object-contain post-image-glow"
            />
          )}

          {acf.video_source && (
            <div className="text-sm text-muted-foreground">Source: {acf.video_source}</div>
          )}

          {/* For Twitter videos, show external link button */}
          {acf.video_source === 'twitter' &&
            acf.x &&
            (() => {
              // Extract Twitter URL from embed HTML
              const twitterUrlMatch = acf.x.match(/href="(https:\/\/twitter\.com\/[^"]+)"/);
              const twitterUrl = twitterUrlMatch ? twitterUrlMatch[1] : null;

              return twitterUrl ? (
                <Button asChild>
                  <a href={twitterUrl} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View on X/Twitter
                  </a>
                </Button>
              ) : null;
            })()}
        </div>
      );

    case 'course':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Course Details</h3>

          {acf.company && (
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-primary" />
              <span>Provider: {acf.company}</span>
            </div>
          )}

          {acf.company_logo && (
            <Image
              src={getAcfImageUrl(acf.company_logo) || ''}
              alt={`${acf.company} logo`}
              width={200}
              height={80}
              className="object-contain post-image-glow"
            />
          )}

          <div className="flex flex-wrap gap-3">
            {acf.course_url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.course_url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Course
                </a>
              </Button>
            )}
            {acf.sign_up_url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.sign_up_url} target="_blank" rel="noopener noreferrer">
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Sign Up
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'presentation':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Presentation Details</h3>

          {acf.host_company_organization && (
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Building2 className="h-5 w-5 text-primary" />
                <span>Presented by: {acf.host_company_organization}</span>
              </div>
              {acf.host_company_organization_logo && (
                <Image
                  src={getAcfImageUrl(acf.host_company_organization_logo) || ''}
                  alt={`${acf.host_company_organization} logo`}
                  width={200}
                  height={80}
                  className="object-contain post-image-glow"
                />
              )}
            </div>
          )}

          <div className="flex flex-wrap gap-3">
            {acf.pdf_upload && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a
                  href={getAcfImageUrl(acf.pdf_upload) || ''}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Presentation
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'whitepaper':
    case 'caseStudy':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">
            {postType === 'caseStudy' ? 'Case Study' : 'Whitepaper'} Details
          </h3>

          {acf.author && (
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-primary" />
              <span>Author: {acf.author}</span>
            </div>
          )}

          {acf.companyorganization_logo && acf.companyorganization_logo !== 'false' && (
            <Image
              src={getAcfImageUrl(acf.companyorganization_logo) || ''}
              alt="Organization logo"
              width={200}
              height={80}
              className="object-contain post-image-glow"
            />
          )}

          <div className="flex flex-wrap gap-3">
            {acf.upload_pdf && acf.upload_pdf !== 'false' && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a
                  href={getAcfImageUrl(acf.upload_pdf) || ''}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'book':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Book Details</h3>

          {acf.book_title && <h4 className="text-lg font-medium">{acf.book_title}</h4>}

          {acf.author_names && (
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-primary" />
              <span>Author(s): {acf.author_names}</span>
            </div>
          )}

          <div className="flex flex-wrap gap-3">
            {acf.purchase_url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.purchase_url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Purchase Book
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'podcast':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Podcast Details</h3>

          <div className="space-y-4">
            {acf.podcast_name && <h4 className="text-lg font-medium">{acf.podcast_name}</h4>}

            {acf.hosts && Array.isArray(acf.hosts) && acf.hosts.length > 0 && (
              <div className="flex items-center gap-2">
                <Mic className="h-5 w-5 text-primary" />
                <span>Hosts: {acf.hosts.map((host: Host) => host.host_name).join(', ')}</span>
              </div>
            )}

            <div className="flex flex-wrap gap-3">
              {acf.episode_url && (
                <Button asChild variant="outline" className={buttonStyles}>
                  <a href={acf.episode_url} target="_blank" rel="noopener noreferrer">
                    <Play className="h-4 w-4 mr-2" />
                    Listen to Episode
                  </a>
                </Button>
              )}
            </div>
          </div>
        </div>
      );

    case 'pressRelease':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Press Release Details</h3>

          <div className="flex flex-wrap gap-3">
            {acf.press_release_url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.press_release_url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Read Press Release
                </a>
              </Button>
            )}

            {acf.upload_pdf && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a
                  href={getAcfImageUrl(acf.upload_pdf) || ''}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'template':
      return (
        <div className="bg-gray-50 p-6 rounded-lg space-y-6">
          <h3 className="text-xl font-semibold mb-4">Template Details</h3>

          {acf.creator_name && (
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-primary" />
              <span>Created by: {acf.creator_name}</span>
            </div>
          )}

          {acf.logo && (
            <Image
              src={getAcfImageUrl(acf.logo) || ''}
              alt="Creator logo"
              width={200}
              height={80}
              className="object-contain post-image-glow"
            />
          )}

          <div className="flex flex-wrap gap-3">
            {acf.file_upload && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a
                  href={getAcfImageUrl(acf.file_upload) || ''}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Template
                </a>
              </Button>
            )}
            {acf.url && (
              <Button asChild variant="outline" className={buttonStyles}>
                <a href={acf.url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Preview Online
                </a>
              </Button>
            )}
          </div>
        </div>
      );

    case 'thoughtLeadership':
    case 'leadership':
      // Thought leadership posts typically don't have specific ACF fields beyond image_caption
      return null;

    default:
      return null;
  }
}
