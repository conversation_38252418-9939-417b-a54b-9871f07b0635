/*********************************************
# frontend/src/app/posts/[slug]/page.tsx  
# 01/27/2025 9:45pm Updated to use sticky BackToSectionHeader component
# 01/27/2025 10:00pm Removed top corner radius and full-width media styling
# 01/27/2025 10:15pm Removed top padding and video container rounded corners
**********************************************/

import { notFound } from 'next/navigation';
import { getPostBySlug } from '@/lib/api';
import { format } from 'date-fns';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Calendar,
  MapPin,
  Link as LinkIcon,
  User,
  Newspaper,
  Briefcase,
  GraduationCap,
  Mic,
  Play,
  BookOpen,
  FileText,
  Presentation,
  Download,
  Lightbulb,
  Users,
  Video,
} from 'lucide-react';
import type { Metadata } from 'next';
import { PostACFDisplay } from './PostACFDisplay';
import { PostInteractionsWrapper } from './PostInteractionsWrapper';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import { BackToSectionHeader } from '@/components/ui/BackToSectionHeader';
import { FeaturedImage } from './FeaturedImage';
import { yoastToNextMetadata, generateStructuredData } from '@/lib/seo/yoast-parser';
import { decodeHtml } from '@/lib/api/utils';
import Script from 'next/script';

// Helper function to get category styles and icon based on category name
function getCategoryInfo(categoryName: string): {
  color: string;
  icon: React.ComponentType<{ className?: string }>;
} {
  const name = categoryName.toLowerCase().trim();

  // News category
  if (name === 'news' || name.includes('news')) {
    return { color: '#ff5ce0', icon: Newspaper };
  }

  // Jobs
  if (
    name === 'jobs' ||
    name.includes('job') ||
    name.includes('career') ||
    name.includes('hiring') ||
    name.includes('employment')
  ) {
    return { color: '#ff625c', icon: Briefcase };
  }

  // Events
  if (
    name === 'events' ||
    name.includes('event') ||
    name.includes('conference') ||
    name.includes('meetup')
  ) {
    return { color: '#1dd05b', icon: Users };
  }

  // Courses
  if (name.includes('courses') || name.includes('course') || name.includes('tutorial')) {
    return { color: '#ffa15c', icon: GraduationCap };
  }

  // Podcasts
  if (name.includes('podcast')) {
    return { color: '#ffa15c', icon: Mic };
  }

  // Videos
  if (name.includes('video') || name === 'videos') {
    return { color: '#ffa15c', icon: Play };
  }

  // Books
  if (name.includes('book') || name === 'books') {
    return { color: '#ffa15c', icon: BookOpen };
  }

  // Webinars
  if (name.includes('webinar') || name === 'webinars') {
    return { color: '#ffa15c', icon: Video };
  }

  // Presentations
  if (name.includes('presentation') || name === 'presentations') {
    return { color: '#ffa15c', icon: Presentation };
  }

  // Case Studies/Whitepapers/Templates
  if (
    name.includes('case studies') ||
    name.includes('case study') ||
    name.includes('whitepaper') ||
    name.includes('template') ||
    name === 'whitepapers' ||
    name === 'templates'
  ) {
    return { color: '#ffa15c', icon: FileText };
  }

  // Thought Leadership and Questions
  if (
    name === 'thought leadership' ||
    name.includes('thought') ||
    name === 'questions' ||
    name.includes('question') ||
    name.includes('opinion') ||
    name.includes('insight')
  ) {
    return { color: '#5cc8ff', icon: Lightbulb };
  }

  // Press Releases
  if (
    name.includes('press-release') ||
    name.includes('press release') ||
    name === 'press-releases'
  ) {
    return { color: '#ffa15c', icon: Newspaper };
  }

  // Blog Posts
  if (name.includes('blog-post') || name.includes('blog post') || name === 'blog-posts') {
    return { color: '#ffa15c', icon: FileText };
  }

  // Resources/Tools/Downloads (catch all)
  if (
    name.includes('resource') ||
    name.includes('tool') ||
    name.includes('guide') ||
    name.includes('download')
  ) {
    return { color: '#ffa15c', icon: Download };
  }

  // Default fallback
  return { color: '#5cc8ff', icon: FileText };
}

interface PostPageProps {
  params: Promise<{
    slug: string;
  }>;
}

interface Category {
  id: number;
  name: string;
  slug: string;
}

interface FeaturedMedia {
  id: number;
  source_url: string;
  alt_text?: string;
}

interface VendorResponse {
  id: number;
  title?: {
    rendered: string;
  };
  name?: string;
  logo_url?: string;
  acf?: {
    logo?: {
      url?: string;
    };
  };
}

interface Host {
  host_name: string;
}

interface Creator {
  creator_name: string;
}

interface Presenter {
  presenter_name: string;
}

interface ACFImage {
  url?: string;
  [key: string]: unknown;
}

interface ACFFields {
  // Common fields
  url?: string;
  author?: string;
  post_thoughts?: string;

  // News fields (article_image removed - now uses featured_image)
  image_caption?: string;

  // Job fields
  company?: string;
  location?: string;
  job_type?: string;
  compensation?: string;
  posting_link_url?: string;

  // Podcast fields
  podcast_name?: string;
  podcast_logo?: string | ACFImage | ACFImage[];
  podcast_episode_title?: string;
  hosts?: Host[];
  episode_description?: string;
  podcast_preview_image?: string | ACFImage | ACFImage[];
  audio_file?: string;
  episode_url?: string;

  // Video fields
  creators?: Creator[];
  video_source?: string;
  youtube_id?: string;
  vimeo_id?: string;
  x?: string;

  // Book fields
  author_names?: string;
  purchase_url?: string;

  // Course fields
  course_url?: string;
  sign_up_url?: string;

  // Webinar fields
  webinar_type?: string;
  date?: string;
  time?: string;
  time_zone?: string;
  presenters?: Presenter[];
  webinar_host_company_organization?: string;
  webinar_host_company_organization_logo?: string | ACFImage | ACFImage[];
  register_url?: string;
  video_type?: string;
  youtube_url?: string;
  vimeo_url?: string;
  twitter?: string;
  linkedin_iframe?: string;

  // Case Study/Whitepaper fields
  upload_pdf?: string | ACFImage | ACFImage[];

  // Event fields
  event_start_date?: string;
  event_end_date?: string;
  event_location?: string;
  event_host?: string;
  event_host_logo?: ACFImage;
  event_registration_link?: string;
  event_details_link?: string;
  // featured_image removed - now uses WordPress featured_image

  [key: string]: unknown;
}

export async function generateMetadata({ params }: PostPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const post = await getPostBySlug(resolvedParams.slug);

  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  // Use Yoast SEO data if available, otherwise fall back to basic metadata
  const fallbackTitle = decodeHtml(post.title.rendered);
  const fallbackDescription = decodeHtml(post.excerpt.rendered.replace(/<[^>]*>/g, '')).slice(0, 160);

  return yoastToNextMetadata(post.yoast_head_json, fallbackTitle, fallbackDescription);
}

export default async function PostPage({ params }: PostPageProps): Promise<React.ReactElement> {
  const resolvedParams = await params;
  const post = await getPostBySlug(resolvedParams.slug);

  if (!post) {
    notFound();
  }

  const authorData = post._embedded?.author?.[0] as any;
  const authorId = authorData?.id || 1;
  const categories = (post._embedded?.['wp:term']?.[0] || []) as Category[];
  const featuredMedia = post._embedded?.['wp:featuredmedia']?.[0] as FeaturedMedia | undefined;

  // Check if this is a vendor post and get vendor data
  const vendorId = post.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);
  const vendorData = post.vendor_info || post.vendor_data || null;

  // We'll need to fetch vendor data if this is a vendor post but we don't have it
  let author: string = authorData?.name || 'Unknown Author';
  let fetchedVendorData = null;

  if (isVendorPost && !vendorData) {
    try {
      // Fetch vendor data directly from WordPress API in server component
      const wpApiUrl =
        process.env.WORDPRESS_API_URL ||
        process.env.NEXT_PUBLIC_WORDPRESS_URL ||
        'http://tourismiq.local';
      const vendorResponse = await fetch(`${wpApiUrl}/wp-json/wp/v2/vendors/${vendorId}`, {
        next: { revalidate: 300 }, // Cache for 5 minutes
      });
      if (vendorResponse.ok) {
        fetchedVendorData = (await vendorResponse.json()) as VendorResponse;
        author = fetchedVendorData.title?.rendered || fetchedVendorData.name || author;
      }
    } catch (error) {
      console.error('Error fetching vendor data:', error);
    }
  } else if (isVendorPost && vendorData) {
    // Use vendor data from post
    author = vendorData.name || author;
  }

  // Use the appropriate vendor data source
  const finalVendorData = vendorData || fetchedVendorData;

  // Post type detection mapping
  const POST_TYPE_SLUGS = {
    event: ['event', 'events'],
    webinar: ['webinar', 'webinars'],
    job: ['job', 'jobs'],
    course: ['course', 'courses'],
    news: ['news'],
    podcast: ['podcast', 'podcasts'],
    video: ['video', 'videos'],
    book: ['book', 'books'],
    caseStudy: ['case-study', 'case-studies'],
    presentation: ['presentation', 'presentations'],
    pressRelease: ['press-release', 'press-releases'],
    whitepaper: ['whitepaper', 'whitepapers'],
    template: ['template', 'templates'],
    blogPost: ['blog-post', 'blog-posts'],
    thoughtLeadership: ['thought-leadership'],
  };

  // Helper function to detect post type based on categories
  const getPostType = (categories: Category[]): string | null => {
    for (const [postType, slugs] of Object.entries(POST_TYPE_SLUGS)) {
      if (categories.some((category) => slugs.includes(category.slug))) {
        return postType;
      }
    }
    return null;
  };

  const postType = getPostType(categories);
  const isEvent = postType === 'event';
  const isThoughtLeadership = postType === 'thoughtLeadership';
  const isVideo = postType === 'video';
  const isWebinar = postType === 'webinar';

  // Format event date if this is an event post
  const acf = post.acf as ACFFields | undefined;

  // Check if this is a sponsored post
  const isSponsored = acf?.sponsored === true;
  const backgroundColor = (acf?.background as string) || '#e5e7eb';
  const thankYouBannerHtml = acf?.thank_you_banner || 'Thank you for supporting our community.';

  const eventDate =
    isEvent && acf?.event_start_date
      ? acf.event_end_date
        ? `${acf.event_start_date} - ${acf.event_end_date}`
        : acf.event_start_date
      : null;

  // Helper function to get ACF image URL (currently unused but may be needed)
  /*
  const getAcfImageUrl = (imageField: unknown): string | null => {
    if (!imageField) return null;

    try {
      // Handle different ACF image return formats
      if (typeof imageField === 'object' && imageField !== null) {
        if ('url' in imageField && typeof imageField.url === 'string' && imageField.url.trim()) {
          return imageField.url.trim();
        }
        // Handle array format [0].url
        if (Array.isArray(imageField) && imageField[0]?.url) {
          return String(imageField[0].url).trim();
        }
      }

      // Handle direct URL string
      if (typeof imageField === 'string' && imageField.trim()) {
        return imageField.trim();
      }
    } catch (error) {
      console.warn('Error processing ACF image field:', error);
    }

    return null;
  };
  */

  // Get featured image with fallback for thought leadership posts
  const getFeaturedImageUrl = (): string | null => {
    // First try WordPress featured media
    if (featuredMedia?.source_url) {
      return featuredMedia.source_url;
    }

    // ACF featured_image removed - now uses WordPress featured_image

    return null;
  };

  const featuredImageUrl = getFeaturedImageUrl();

  // Helper functions for video content
  const extractYouTubeId = (url: string) => {
    const match = url.match(
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    );
    return match ? match[1] : null;
  };

  const extractVimeoId = (url: string) => {
    const match = url.match(/(?:vimeo\.com\/)([0-9]+)/);
    return match ? match[1] : null;
  };

  // Function to render embedded video content for video posts
  const renderVideoEmbed = (): React.ReactNode => {
    if (!acf?.video_source) return null;

    const videoSource = acf.video_source;

    switch (videoSource) {
      case 'youtube':
        const youtubeUrl = acf.youtube_id;
        if (!youtubeUrl) return null;
        const youtubeId = extractYouTubeId(youtubeUrl);
        if (!youtubeId) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted">
            <iframe
              src={`https://www.youtube.com/embed/${youtubeId}`}
              title="YouTube video player"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              className="w-full h-full"
            />
          </div>
        );

      case 'vimeo':
        const vimeoUrl = acf.vimeo_id;
        if (!vimeoUrl) return null;
        const vimeoId = extractVimeoId(vimeoUrl);
        if (!vimeoId) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted">
            <iframe
              src={`https://player.vimeo.com/video/${vimeoId}`}
              title="Vimeo video player"
              allow="autoplay; fullscreen; picture-in-picture"
              allowFullScreen
              className="w-full h-full"
            />
          </div>
        );

      case 'twitter':
        // Twitter videos will be handled in the ACF display section
        return null;

      case 'linkedin':
        const linkedinEmbed = acf.linkedin_iframe;
        if (!linkedinEmbed) return null;
        return (
          <div className="overflow-hidden bg-muted p-4 flex items-center justify-center">
            <div dangerouslySetInnerHTML={{ __html: linkedinEmbed }} />
          </div>
        );

      default:
        return null;
    }
  };

  // Function to render embedded video content for recorded webinars
  const renderWebinarVideoEmbed = (): React.ReactNode => {
    if (!isWebinar || !acf?.webinar_type || !acf?.video_type) return null;

    // Handle ACF array format for webinar_type
    const webinarType =
      typeof acf.webinar_type === 'string'
        ? acf.webinar_type
        : acf.webinar_type && typeof acf.webinar_type === 'object' && 'value' in acf.webinar_type
          ? (acf.webinar_type as { value: string }).value
          : undefined;

    if (webinarType !== 'recorded') return null;

    const videoType = acf.video_type;

    switch (videoType) {
      case 'youtube':
        const youtubeUrl = acf.youtube_url;
        if (!youtubeUrl) return null;
        const youtubeId = extractYouTubeId(youtubeUrl);
        if (!youtubeId) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted">
            <iframe
              src={`https://www.youtube.com/embed/${youtubeId}`}
              title="YouTube video player"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              className="w-full h-full"
            />
          </div>
        );

      case 'vimeo':
        const vimeoUrl = acf.vimeo_url;
        if (!vimeoUrl) return null;
        const vimeoId = extractVimeoId(vimeoUrl);
        if (!vimeoId) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted">
            <iframe
              src={`https://player.vimeo.com/video/${vimeoId}`}
              title="Vimeo video player"
              allow="autoplay; fullscreen; picture-in-picture"
              allowFullScreen
              className="w-full h-full"
            />
          </div>
        );

      case 'twitter':
        // Twitter videos will be handled in the ACF display section
        return null;

      case 'linkedin':
        const linkedinEmbed = acf.linkedin_iframe;
        if (!linkedinEmbed) return null;
        return (
          <div className="overflow-hidden bg-muted p-4 flex items-center justify-center">
            <div dangerouslySetInnerHTML={{ __html: linkedinEmbed }} />
          </div>
        );

      default:
        return null;
    }
  };

  // Function to render video content for thought leadership
  const renderVideoContent = (): React.ReactNode => {
    if (!isThoughtLeadership || acf?.media_type !== 'video') return null;

    const videoType = acf.video_type;

    switch (videoType) {
      case 'youtube':
        const youtubeUrl = acf.youtube_url;
        if (!youtubeUrl) return null;
        const youtubeId = extractYouTubeId(youtubeUrl);
        if (!youtubeId) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted">
            <iframe
              src={`https://www.youtube.com/embed/${youtubeId}`}
              title="YouTube video player"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              className="w-full h-full"
            />
          </div>
        );

      case 'vimeo':
        const vimeoUrl = acf.vimeo_url;
        if (!vimeoUrl) return null;
        const vimeoId = extractVimeoId(vimeoUrl);
        if (!vimeoId) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted">
            <iframe
              src={`https://player.vimeo.com/video/${vimeoId}`}
              title="Vimeo video player"
              allow="autoplay; fullscreen; picture-in-picture"
              allowFullScreen
              className="w-full h-full"
            />
          </div>
        );

      case 'twitter':
        // Twitter videos will be handled in the ACF display section
        return null;

      case 'linkedin':
        const linkedinEmbed = acf.linkedin_iframe;
        if (!linkedinEmbed) return null;
        return (
          <div className="overflow-hidden bg-muted p-4 flex items-center justify-center">
            <div dangerouslySetInnerHTML={{ __html: linkedinEmbed }} />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="pb-4">
      {/* Sticky Back to Feed Header */}
      <BackToSectionHeader href="/" text="Back to Feed" />

      {/* Sponsored Post Banner - At the very top */}
      {isSponsored && (
        <div className="px-4 py-1.5 flex items-center justify-between bg-gradient-to-r from-[#f7dd78] via-[#d6aa3f] via-[#e6af21] via-[#ebd074] via-[#f9be0e] via-[#f9be0e] to-[#f6d973]">
          <div className="flex items-center justify-start gap-2">
            <span className="text-sm font-bold text-white">Founding Sponsor Post</span>
            <span
              className="text-xs text-dark m-0"
              dangerouslySetInnerHTML={{ __html: thankYouBannerHtml }}
            />
          </div>
        </div>
      )}

      <div className="bg-white rounded-b-lg shadow-lg border mb-24">
        {/* Featured Media Section - Image or Video */}
        {featuredImageUrl ||
        renderVideoContent() ||
        renderVideoEmbed() ||
        renderWebinarVideoEmbed() ? (
          <div className="pb-6">
            {/* Show video content for video posts */}
            {isVideo && renderVideoEmbed()}
            {isWebinar && acf?.webinar_type === 'recorded' && renderWebinarVideoEmbed()}
            {isThoughtLeadership && acf?.media_type === 'video' && renderVideoContent()}
            {featuredImageUrl &&
              !isVideo &&
              !(isWebinar && acf?.webinar_type === 'recorded') &&
              !(isThoughtLeadership && acf?.media_type === 'video') && (
                <FeaturedImage src={featuredImageUrl} alt={post.title.rendered} />
              )}

            {/* Image caption if available - right under the media */}
            {acf?.image_caption &&
              (featuredImageUrl ||
                (isThoughtLeadership && acf?.media_type === 'video') ||
                isVideo ||
                (isWebinar &&
                  (typeof acf.webinar_type === 'string'
                    ? acf.webinar_type === 'recorded'
                    : acf.webinar_type &&
                        typeof acf.webinar_type === 'object' &&
                        'value' in acf.webinar_type
                      ? (acf.webinar_type as { value: string }).value === 'recorded'
                      : false))) && (
                <p className="text-xs text-gray-400 mt-2 text-right italic">
                  {String(acf.image_caption)}
                </p>
              )}
          </div>
        ) : null}

        <article
          className={`px-6 pb-6 ${
            !featuredImageUrl &&
            !renderVideoContent() &&
            !renderVideoEmbed() &&
            !renderWebinarVideoEmbed()
              ? 'pt-6'
              : ''
          }`}
          style={isSponsored ? { backgroundColor: backgroundColor } : {}}
        >
          <header className="mb-8">
            {/* Author Info - styled like feed items */}
            <div className="flex items-center gap-4 mb-6">
              {isVendorPost && finalVendorData ? (
                // Show vendor logo for vendor posts
                <>
                  <div className="h-10 w-10 ring-2 ring-[#5cc8ff]/20 rounded-full overflow-hidden">
                    <img
                      src={
                        finalVendorData.logo_url ||
                        (finalVendorData as VendorResponse).acf?.logo?.url ||
                        '/images/avatar-placeholder.svg'
                      }
                      alt={author}
                      className="h-10 w-10 object-cover rounded-full"
                    />
                  </div>
                  <div className="flex flex-col">
                    <div className="font-semibold text-[#3d405b] dark:text-gray-100">{author}</div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <time dateTime={post.date}>
                        {format(new Date(post.date), 'MMMM d, yyyy')}
                      </time>
                    </div>
                  </div>
                </>
              ) : (
                // Show user avatar for personal posts
                <>
                  <UserAvatarWithRank
                    userId={authorId}
                    avatarUrl={
                      authorData?.acf?.profile_picture?.url ||
                      authorData?.avatar_urls?.['96'] ||
                      '/images/avatar-placeholder.svg'
                    }
                    displayName={author}
                    size="h-10 w-10"
                    containerSize="w-12 h-12"
                    userRoles={authorData?.roles || []}
                  />
                  <div className="flex flex-col">
                    <div className="font-semibold text-[#3d405b] dark:text-gray-100">{author}</div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <time dateTime={post.date}>
                        {format(new Date(post.date), 'MMMM d, yyyy')}
                      </time>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Category badges with icons */}
            {categories.length > 0 && (
              <div className="mb-4">
                {categories.map((category: Category) => {
                  const categoryInfo = getCategoryInfo(category.name);
                  const IconComponent = categoryInfo.icon;
                  return (
                    <div
                      key={category.id}
                      className="inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full capitalize mr-2 mb-2"
                      style={{
                        backgroundColor: categoryInfo.color,
                        color: 'white',
                      }}
                    >
                      <IconComponent className="h-4 w-4 text-white" />
                      {category.name}
                    </div>
                  );
                })}
              </div>
            )}

            <h1
              className="text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-6"
              dangerouslySetInnerHTML={{ __html: post.title.rendered }}
            />

            {isEvent ? (
              <div className="space-y-4 mb-6">
                {eventDate && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-primary" />
                    <span>{eventDate}</span>
                  </div>
                )}

                {acf?.event_location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-primary" />
                    <span>{String(acf.event_location)}</span>
                  </div>
                )}

                {acf?.event_host && (
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5 text-primary" />
                    <span>Hosted by {String(acf.event_host)}</span>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center text-muted-foreground text-sm mb-6">
                <span>By {author}</span>
                <span className="mx-2">•</span>
                <time dateTime={post.date}>{format(new Date(post.date), 'MMMM d, yyyy')}</time>
              </div>
            )}

            {/* Event host logo for events */}
            {isEvent && acf?.event_host_logo?.url && (
              <div className="relative w-full max-w-xs h-32 mb-8">
                <Image
                  src={acf.event_host_logo.url}
                  alt={acf.event_host || 'Event host'}
                  width={200}
                  height={100}
                  className="object-contain post-image-glow"
                  priority
                />
              </div>
            )}
          </header>

          <div
            className="prose max-w-none prose-headings:mt-8 prose-headings:mb-4 prose-p:mb-4 prose-img:rounded-lg prose-img:shadow-lg text-[#3d405b] dark:text-gray-300"
            dangerouslySetInnerHTML={{ __html: post.content.rendered }}
          />

          {isEvent && (
            <div className="mt-8 flex flex-wrap gap-4">
              {acf?.event_registration_link && (
                <Button asChild>
                  <Link
                    href={acf.event_registration_link}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Register for Event
                  </Link>
                </Button>
              )}

              {acf?.event_details_link && (
                <Button variant="outline" asChild>
                  <Link href={acf.event_details_link} target="_blank" rel="noopener noreferrer">
                    <LinkIcon className="h-4 w-4 mr-2" />
                    More Details
                  </Link>
                </Button>
              )}
            </div>
          )}

          {/* Category-specific ACF Fields */}
          {postType && acf && (
            <div className="mt-8 border-t pt-8">
              <PostACFDisplay postType={postType} acf={acf} />
            </div>
          )}
        </article>

        {/* Interactive Footer - Client Component */}
        <PostInteractionsWrapper
          postId={post.id}
          authorId={authorId}
          initialUpvotes={(() => {
            // First try the REST API upvotes field
            if (post.upvotes && typeof post.upvotes.count === 'number') {
              return post.upvotes.count;
            }
            // Fallback to meta field
            return Number(post.meta?._upvotes) || 0;
          })()}
          initialCommentCount={
            Number(post.total_comments) || Number(post.meta?._comments_count) || 0
          }
          postTitle={post.title.rendered}
        />
      </div>

      {/* Structured Data for SEO */}
      <Script id="post-structured-data" type="application/ld+json">
        {generateStructuredData('Article', {
          headline: post.title.rendered,
          description: post.excerpt.rendered.replace(/<[^>]*>/g, '').slice(0, 160),
          author: {
            '@type': 'Person',
            name: author,
          },
          publisher: {
            '@type': 'Organization',
            name: 'TourismIQ',
            logo: {
              '@type': 'ImageObject',
              url: 'https://mytourismiq.wpenginepowered.com/wp-content/uploads/2024/02/logo.png',
            },
          },
          datePublished: post.date,
          dateModified: post.date,
          url: `https://mytourismiq.com/posts/${post.slug}`,
          ...(featuredImageUrl && {
            image: {
              '@type': 'ImageObject',
              url: featuredImageUrl,
            },
          }),
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': `https://mytourismiq.com/posts/${post.slug}`,
          },
        })}
      </Script>
    </div>
  );
}

// Generate static params for better performance
export async function generateStaticParams() {
  // In a real app, you might want to fetch all post slugs here
  // For now, we'll return an empty array and let Next.js handle dynamic rendering
  return [];
}
