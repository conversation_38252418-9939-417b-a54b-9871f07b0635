import type { <PERSON>ada<PERSON> } from 'next';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import Script from 'next/script';
import './globals.css';
import { MainLayout } from '@/components/layout/MainLayout';
import { Header } from '@/components/layout/Header';
import { Sidebar } from '@/components/layout/Sidebar';
import { RightSidebar } from '@/components/layout/RightSidebar';
import { AuthProvider } from '@/contexts/auth-context';
import { NotificationProvider } from '@/contexts/NotificationContext';
import { UpvoteProvider } from '@/contexts/UpvoteContext';
import { MessagingProvider } from '@/components/messaging';
import { IQScoreProvider } from '@/contexts/iq-score-context';
import { ConnectionProvider } from '@/contexts/connection-context';
import { Toaster } from 'sonner';
import { IQPointsToastContainer } from '@/components/ui/IQPointsToast';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { ReactQueryProvider } from '@/components/providers/ReactQueryProvider';
import { CacheInvalidationProvider } from '@/components/providers/CacheInvalidationProvider';
import { WelcomeModal } from '@/components/ui/WelcomeModal';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'TourismIQ',
  description:
    'Your premier destination for tourism industry insights, resources, and community connections.',
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
    ],
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-[#eff1f4]`}
      >
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-7S7748SWVQ"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-7S7748SWVQ');
          `}
        </Script>
        <ErrorBoundary>
          <ReactQueryProvider>
            <CacheInvalidationProvider>
              <AuthProvider>
                <NotificationProvider>
                  <UpvoteProvider>
                    <IQScoreProvider>
                      <ConnectionProvider>
                        <MessagingProvider>
                          <div className="flex flex-col min-h-screen">
                            <Header />
                            <MainLayout>
                              <Sidebar />
                              <ErrorBoundary>{children}</ErrorBoundary>
                              <RightSidebar />
                            </MainLayout>
                          </div>
                          <Toaster position="top-right" />
                          <IQPointsToastContainer />
                          <WelcomeModal />
                        </MessagingProvider>
                      </ConnectionProvider>
                    </IQScoreProvider>
                  </UpvoteProvider>
                </NotificationProvider>
              </AuthProvider>
            </CacheInvalidationProvider>
          </ReactQueryProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
