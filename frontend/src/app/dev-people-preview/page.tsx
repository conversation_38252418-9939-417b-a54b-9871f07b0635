/*********************************************
# frontend/src/app/dev-people-preview/page.tsx
# 01/30/2025 7:15pm Created development preview page for People on the Move layouts
# 01/30/2025 8:00pm Updated opacity to 60%, square images with max height 140px, added horizontal padding, consistent dropshadow
# 01/30/2025 8:15pm Simplified to use 4-image template for all variations - consistent square images, centered rows
**********************************************/

'use client';

import Image from 'next/image';
import { Users } from 'lucide-react';

interface GalleryImage {
  ID: number;
  url: string;
  alt: string;
  title: string;
  sizes: {
    full: string;
    large: string;
    medium: string;
    thumbnail: string;
  };
}

// Component to render the new image grid layout - simplified using 4-image template
function PeopleImageGrid({ images }: { images: GalleryImage[] }) {
  // Helper to get image URL with fallback to different sizes
  const getImageUrl = (image: GalleryImage): string => {
    return image.sizes.large || image.sizes.medium || image.url || '';
  };

  // Determine grid layout based on image count
  const getGridCols = (count: number): string => {
    if (count === 1) return 'grid-cols-1 max-w-[140px]';
    if (count === 2) return 'grid-cols-2 max-w-[284px]';
    if (count === 3) return 'grid-cols-3 max-w-[426px]';
    return 'grid-cols-4'; // 4 or more images
  };

  // Split images into rows of maximum 4
  const createRows = (images: GalleryImage[]) => {
    const rows = [];
    for (let i = 0; i < images.length; i += 4) {
      rows.push(images.slice(i, i + 4));
    }
    return rows;
  };

  const rows = createRows(images);

  return (
    <div
      className="w-full py-10 px-10 rounded-lg"
      style={{ backgroundColor: 'rgba(170, 215, 238, 0.6)' }}
    >
      <div className="space-y-2">
        {rows.map((row, rowIndex) => (
          <div key={rowIndex} className={`grid gap-2 mx-auto ${getGridCols(row.length)}`}>
            {row.map((image, index) => (
              <div
                key={image.ID || index}
                className="aspect-square overflow-hidden bg-muted rounded-lg max-w-[140px] max-h-[140px]"
              >
                <Image
                  src={getImageUrl(image)}
                  alt={image.alt || 'Person image'}
                  width={140}
                  height={140}
                  className="object-cover w-full h-full"
                  priority={false}
                />
              </div>
            ))}
          </div>
        ))}
        {images.length > 10 && (
          <div className="text-center text-sm text-gray-500 mt-2">
            +{images.length - 10} more images
          </div>
        )}
      </div>
    </div>
  );
}

// Generate mock images using picsum.photos
function generateMockImages(count: number): GalleryImage[] {
  return Array.from({ length: count }, (_, index) => {
    const imageId = 100 + index; // Use different IDs for variety
    return {
      ID: imageId,
      url: `https://picsum.photos/400/400?random=${imageId}`,
      alt: `Person ${index + 1}`,
      title: `Person Image ${index + 1}`,
      sizes: {
        full: `https://picsum.photos/800/800?random=${imageId}`,
        large: `https://picsum.photos/600/600?random=${imageId}`,
        medium: `https://picsum.photos/400/400?random=${imageId}`,
        thumbnail: `https://picsum.photos/200/200?random=${imageId}`,
      },
    };
  });
}

export default function DevPeoplePreviewPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-[#3d405b] mb-4">
            🧪 People on the Move - Layout Preview
          </h1>
          <p className="text-lg text-gray-600 mb-4">
            Development preview showing all possible image grid layouts (1-10 images) for People on
            the Move posts.
          </p>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-yellow-800 mb-2">🔧 Testing Notes:</h3>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>
                • <strong>Template:</strong> Uses 4-image variation as template for all layouts
              </li>
              <li>
                • <strong>Background:</strong> Light blue (#aad7ee) at 60% opacity with 40px
                horizontal padding
              </li>
              <li>
                • <strong>Images:</strong> All square, same size (300x300px), centered rows
              </li>
              <li>
                • <strong>Rows:</strong> Maximum 4 images per row, centered based on count
              </li>
              <li>
                • <strong>Simplified Code:</strong> Single logic handles all variations (1-10+
                images)
              </li>
            </ul>
          </div>
        </header>

        <div className="space-y-8">
          {Array.from({ length: 10 }, (_, i) => {
            const imageCount = i + 1;
            const mockImages = generateMockImages(imageCount);

            return (
              <div
                key={imageCount}
                className="bg-white shadow-lg border rounded-lg overflow-hidden"
              >
                <div className="bg-gray-50 px-6 py-3 border-b">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-700">
                      {imageCount} Image{imageCount > 1 ? 's' : ''} Layout
                    </h2>
                    <div className="flex items-center gap-2 bg-purple-100 px-3 py-1 rounded-full">
                      <Users className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium text-purple-700">
                        People on the Move
                      </span>
                    </div>
                  </div>
                </div>

                <PeopleImageGrid images={mockImages} />

                <div className="p-6">
                  <h3 className="text-xl font-bold text-[#3d405b] mb-2">
                    Sample People on the Move Post with {imageCount} Image
                    {imageCount > 1 ? 's' : ''}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    This is a sample layout showing how the simplified grid behaves with{' '}
                    {imageCount} professional headshot{imageCount > 1 ? 's' : ''}. All images are
                    the same size (300x300px) and centered based on count per row.
                  </p>

                  <div className="text-sm text-gray-500">
                    Layout:{' '}
                    {imageCount === 1
                      ? 'Single centered image'
                      : imageCount === 2
                        ? 'Two centered images'
                        : imageCount === 3
                          ? 'Three centered images'
                          : imageCount === 4
                            ? 'Four images (full row)'
                            : imageCount <= 8
                              ? `${Math.ceil(imageCount / 4)} rows, last row centered`
                              : 'Multiple rows, max 4 per row, all centered'}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-12 p-6 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-3">📋 Simplified Implementation:</h3>
          <div className="text-sm text-blue-700 space-y-2">
            <p>
              <strong>Template Approach:</strong> Uses 4-image layout as base template for all
              variations
            </p>
            <p>
              <strong>Consistent Sizing:</strong> All images are 300x300px squares, no size
              variations
            </p>
            <p>
              <strong>Smart Centering:</strong> Rows automatically center based on image count (1-4
              per row)
            </p>
            <p>
              <strong>Background:</strong> Light blue (#aad7ee) at 60% opacity with 40px horizontal
              padding
            </p>
            <p>
              <strong>Simplified Logic:</strong> Single function handles all layouts, much cleaner
              code
            </p>
            <p>
              <strong>Responsive:</strong> Grids adapt while maintaining consistent square aspect
              ratios
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
