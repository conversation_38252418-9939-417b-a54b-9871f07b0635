/**
 * Dynamic robots.txt generation for Next.js
 *
 * This replaces the WordPress robots.txt with proper Next.js routing
 */

export async function GET() {
  const robotsTxt = `# Robots.txt for mytourismiq.com
# Generated by Next.js application

User-agent: *
Allow: /

# Block admin and internal paths
Disallow: /api/
Disallow: /admin/
Disallow: /_next/
Disallow: /wp-admin/
Disallow: /wp-content/
Disallow: /wp-includes/

# Allow important paths
Allow: /posts/
Allow: /category/
Allow: /forum/
Allow: /jobs/
Allow: /rfps/
Allow: /members/
Allow: /vendors/

# Block search and filter URLs
Disallow: /search?
Disallow: /*?filter=
Disallow: /*?page=
Disallow: /*?sort=

# Block temporary and development paths
Disallow: /test/
Disallow: /dev/
Disallow: /staging/

# Sitemap location
Sitemap: https://mytourismiq.com/sitemap.xml

# Crawl delay for politeness
Crawl-delay: 1`;

  return new Response(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
    },
  });
}
