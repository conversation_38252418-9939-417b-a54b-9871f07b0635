/*********************************************
# frontend/src/app/test-error-dialogs/page.tsx
# 02/07/2025 8:00pm Created test page to demonstrate error page styling and custom confirmation dialogs
**********************************************/

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { useConfirmation } from '@/components/ui/ConfirmationDialog';

// Component that throws an error for testing
function ErrorThrower() {
  const [shouldThrow, setShouldThrow] = useState(false);

  if (shouldThrow) {
    throw new Error('This is a test error to demonstrate the error page styling!');
  }

  return (
    <div className="text-center">
      <p className="mb-4">
        Click the button below to trigger an error and see the styled error page:
      </p>
      <Button
        onClick={() => setShouldThrow(true)}
        className="bg-red-600 hover:bg-red-700 text-white"
      >
        Trigger Error
      </Button>
    </div>
  );
}

export default function TestErrorDialogsPage() {
  const { confirm, ConfirmationDialogComponent } = useConfirmation();
  const [lastResult, setLastResult] = useState<string>('');

  const testBasicConfirm = async () => {
    const result = await confirm(
      'This is a basic confirmation dialog. Do you want to proceed?',
      'Basic Confirmation'
    );
    setLastResult(result ? 'Confirmed' : 'Cancelled');
  };

  const testDestructiveConfirm = async () => {
    const result = await confirm(
      'This is a destructive action that cannot be undone. Are you absolutely sure?',
      'Destructive Action',
      {
        confirmText: 'Delete Permanently',
        cancelText: 'Keep Safe',
        variant: 'destructive',
      }
    );
    setLastResult(result ? 'Destructive action confirmed' : 'Action cancelled');
  };

  const testCustomConfirm = async () => {
    const result = await confirm(
      'Would you like to save your changes before continuing?',
      'Save Changes',
      {
        confirmText: 'Save & Continue',
        cancelText: 'Continue Without Saving',
      }
    );
    setLastResult(result ? 'Changes saved' : 'Continued without saving');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Error Page & Dialog Styling Test</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Error Boundary Test */}
        <Card>
          <CardHeader>
            <CardTitle>Error Page Styling Test</CardTitle>
          </CardHeader>
          <CardContent>
            <ErrorBoundary>
              <ErrorThrower />
            </ErrorBoundary>
          </CardContent>
        </Card>

        {/* Confirmation Dialog Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Custom Confirmation Dialogs</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-600 mb-2">
                Test different types of confirmation dialogs:
              </p>
              <div className="space-y-3">
                <Button onClick={testBasicConfirm} variant="outline" className="w-full">
                  Basic Confirmation
                </Button>

                <Button
                  onClick={testDestructiveConfirm}
                  variant="outline"
                  className="w-full border-red-200 text-red-700 hover:bg-red-50"
                >
                  Destructive Confirmation
                </Button>

                <Button onClick={testCustomConfirm} variant="outline" className="w-full">
                  Custom Text Confirmation
                </Button>
              </div>
            </div>

            {lastResult && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Last Result:</strong> {lastResult}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Instructions */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>How to Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Error Page Styling:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Click &quot;Trigger Error&quot; to see the styled error page</li>
                <li>
                  The &quot;Refresh Page&quot; button should match the newsletter signup button
                  styling
                </li>
                <li>Notice the rounded-full design, proper colors, and hover states</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Custom Confirmation Dialogs:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Click any of the confirmation buttons to see custom dialogs</li>
                <li>These replace the system&apos;s native window.confirm dialogs</li>
                <li>Notice the consistent styling with your site&apos;s design system</li>
                <li>Different variants: default, destructive, and custom text</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-2">Styling Features:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Rounded-full buttons matching your design system</li>
                <li>Proper color scheme: #5cc8ff background, #3d405b text</li>
                <li>Consistent hover states and transitions</li>
                <li>Loading states with spinners</li>
                <li>Responsive design that works on all screen sizes</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Custom confirmation dialog component */}
      <ConfirmationDialogComponent />
    </div>
  );
}
