'use client';

import { useState } from 'react';
import { cacheInvalidationManager } from '@/lib/cache-invalidation';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';

export default function TestCacheInvalidation() {
  const [lastInvalidation, setLastInvalidation] = useState<string>('');
  const queryClient = useQueryClient();

  // Example query to test invalidation
  const { data: posts, isLoading } = useQuery({
    queryKey: queryKeys.posts.list({}),
    queryFn: async () => {
      const response = await fetch('/api/wp-proxy/posts?per_page=5&_embed=1');
      if (!response.ok) throw new Error('Failed to fetch posts');
      return response.json();
    },
    staleTime: 30 * 1000,
  });

  const testInvalidation = async (type: 'post' | 'category' | 'all', id?: number) => {
    try {
      await cacheInvalidationManager.invalidateCache(type, id);
      setLastInvalidation(
        `Invalidated ${type} ${id ? `(ID: ${id})` : ''} at ${new Date().toLocaleTimeString()}`
      );
    } catch (error) {
      console.error('Invalidation failed:', error);
      setLastInvalidation(`Failed to invalidate ${type} - check console`);
    }
  };

  const checkCacheStatus = async () => {
    try {
      const response = await fetch('/api/wp-proxy/cache/invalidations?since=0');
      const data = await response.json();
      console.log('Cache invalidations:', data);
      alert(`Found ${data.invalidations.length} recent invalidations. Check console for details.`);
    } catch (error) {
      console.error('Error checking cache:', error);
      alert('Error checking cache status');
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Cache Invalidation Test</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Test Controls */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Test Controls</h2>

          <div className="space-y-3">
            <button
              onClick={() => testInvalidation('post', 123)}
              className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Invalidate Post Cache (ID: 123)
            </button>

            <button
              onClick={() => testInvalidation('category', 5)}
              className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Invalidate Category Cache (ID: 5)
            </button>

            <button
              onClick={() => testInvalidation('all')}
              className="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
            >
              Invalidate All Caches
            </button>

            <button
              onClick={checkCacheStatus}
              className="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
            >
              Check Cache Status
            </button>

            <button
              onClick={() => queryClient.invalidateQueries()}
              className="w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Manual React Query Invalidation
            </button>
          </div>

          {lastInvalidation && (
            <div className="mt-4 p-3 bg-gray-100 rounded">
              <strong>Last Action:</strong> {lastInvalidation}
            </div>
          )}
        </div>

        {/* Posts Display */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Sample Posts (Test Data)</h2>

          {isLoading ? (
            <p>Loading posts...</p>
          ) : (
            <div className="space-y-2">
              {posts?.map((post: any) => (
                <div key={post.id} className="p-2 border rounded">
                  <h3 className="font-medium">{post.title.rendered}</h3>
                  <p className="text-sm text-gray-600">ID: {post.id}</p>
                  <p className="text-xs text-gray-500">
                    Modified: {new Date(post.modified).toLocaleString()}
                  </p>
                </div>
              )) || <p>No posts found</p>}
            </div>
          )}

          <div className="mt-4 text-sm text-gray-500">
            Cache should refresh automatically when posts are updated in WordPress admin. Use the
            test buttons to simulate invalidation events.
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">How to Test:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Open WordPress admin and edit a post</li>
          <li>Save the post in WordPress</li>
          <li>Return to this page - the cache should automatically invalidate</li>
          <li>Posts should refresh with updated content within 5-30 seconds</li>
          <li>Use the test buttons above to manually trigger cache invalidation</li>
          <li>Check browser console for cache invalidation logs</li>
        </ol>
      </div>
    </div>
  );
}
