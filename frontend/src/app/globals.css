/*********************************************
# frontend/src/app/globals.css
# 02/07/2025 4:20pm Added sophisticated inner glow styling for post images to prevent white background bleed
# 02/07/2025 10:45pm Added global cursor pointer styles for navigation links and interactive elements
# 02/07/2025 11:30pm Enhanced global cursor pointer implementation with comprehensive coverage for all clickable elements sitewide
**********************************************/

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 225 6% 25%;

    --card: 0 0% 100%;
    --card-foreground: 225 6% 25%;

    --popover: 0 0% 100%;
    --popover-foreground: 225 6% 25%;

    --primary: 196 100% 68%;
    --primary-foreground: 225 6% 25%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 225 6% 25%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 196 100% 68%;
    --accent-foreground: 225 6% 25%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 196 100% 68%;

    --radius: 0.5rem;
  }

  * {
    @apply border-border;
    box-sizing: border-box;
  }

  /* Remove default focus outlines and prevent unwanted borders */
  *:focus {
    outline: none !important;
  }

  /* Custom focus styles for accessibility */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    outline: 2px solid var(--ring) !important;
    outline-offset: 2px !important;
  }

  html {
    overflow-x: hidden;
  }

  body {
    background-color: #eff1f4;
    color: #3d405b;
    overflow-x: hidden;
  }

  /* Default text elements to use the custom dark text color */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  span,
  div,
  a {
    color: inherit;
  }

  /* Global cursor pointer styles for navigation and interactive elements */
  a,
  button,
  [role="button"],
  [role="link"],
  .cursor-pointer,
  /* Dropdown menu items */
  [data-radix-dropdown-menu-item],
  [data-radix-dropdown-menu-trigger],
  [data-radix-dropdown-menu-sub-trigger],
  /* Shadcn UI components */
  [data-slot="dropdown-menu-item"],
  [data-slot="dropdown-menu-trigger"],
  [data-slot="dropdown-menu-sub-trigger"],
  /* Additional interactive elements */
  .dropdown-menu-item,
  .dropdown-menu-trigger,
  .dropdown-menu-sub-trigger,
  /* Link-like elements */
  [onclick],
  [onClick],
  /* Custom interactive classes */
  .interactive,
  .clickable {
    cursor: pointer !important;
  }

  /* Specific navigation cursor styles */
  nav a,
  nav button,
  aside a,
  aside button,
  /* Header elements */
  header a,
  header button,
  /* Sidebar elements */
  .sidebar a,
  .sidebar button,
  /* Dropdown elements */
  .dropdown a,
  .dropdown button,
  /* Menu elements */
  .menu a,
  .menu button,
  /* Profile dropdown */
  .profile-dropdown a,
  .profile-dropdown button {
    cursor: pointer !important;
  }

  /* Exception for disabled elements */
  a[aria-disabled='true'],
  button[disabled],
  button[aria-disabled='true'],
  .cursor-not-allowed {
    cursor: not-allowed !important;
  }

  /* Exception for text/default cursor elements */
  .cursor-default,
  .cursor-text {
    cursor: default !important;
  }

  /* Target all elements with hover states - they should be interactive */
  *:hover {
    cursor: inherit;
  }

  /* Ensure elements with hover:bg-* classes show pointer cursor */
  [class*='hover:bg-'],
  [class*='hover:text-'],
  [class*='hover:border-'],
  [class*='hover:shadow-'] {
    cursor: pointer !important;
  }
}

@layer components {
  .sidebar-item {
    @apply flex items-center space-x-3 px-2 py-2 rounded-md transition-colors cursor-pointer;
  }

  .sidebar-item:hover {
    @apply bg-accent text-primary;
  }

  .sidebar-item.active {
    @apply bg-primary/10 text-primary font-medium;
  }

  .post-card {
    @apply border border-gray-200 rounded-xl overflow-hidden transition-all duration-200;
  }

  .post-card:hover {
    @apply shadow-md border-primary/20;
    transform: translateY(-2px);
  }

  .connect-button {
    @apply text-sm font-medium px-3 py-1 rounded-full border border-primary text-primary hover:bg-primary/10 transition-colors cursor-pointer;
  }

  /* Subtle inner glow only for video thumbnails to prevent white background bleed */
  .video-thumbnail-glow {
    position: relative;
  }

  .video-thumbnail-glow::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
    box-shadow: inset 0 0 30px 10px rgba(61, 64, 91, 0.01);
    border-radius: inherit;
  }

  /* Enhanced prose styles - 20% larger font size and spacing */
  .prose {
    font-size: 1.2em; /* 20% larger base font size */
    line-height: 1.8; /* Increased line height for better spacing */
  }

  .prose p {
    margin-bottom: 1.44em; /* 20% more spacing between paragraphs (1.2 * 1.2) */
    line-height: 1.8;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    margin-top: 1.44em; /* 20% more spacing above headings */
    margin-bottom: 0.72em; /* 20% more spacing below headings */
    line-height: 1.4;
  }

  .prose ul,
  .prose ol {
    margin-bottom: 1.44em; /* 20% more spacing for lists */
    padding-left: 1.44em; /* 20% more indentation */
  }

  .prose li {
    margin-bottom: 0.36em; /* 20% more spacing between list items */
  }

  .prose blockquote {
    margin: 1.44em 0; /* 20% more spacing for blockquotes */
    padding-left: 1.44em; /* 20% more padding */
  }

  .prose img {
    width: 100%;
    height: auto;
  }

  /* Specific adjustments for different prose sizes */
  .prose-sm {
    font-size: 1.08em; /* 20% larger than default prose-sm (0.9 * 1.2) */
  }

  .prose-lg {
    font-size: 1.32em; /* 20% larger than default prose-lg (1.1 * 1.2) */
  }

  /* Hide scrollbar utility */
  .scrollbar-hide {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }
}
