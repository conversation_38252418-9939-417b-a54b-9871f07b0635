'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Zap, Crown, Trophy, MessageCircle, Heart, UserPlus, Calendar, Star } from 'lucide-react';
import { useAuthStatus } from '@/hooks/queries/useAuth';
import { useNotifications } from '@/contexts/NotificationContext';

interface ActivityType {
  id: string;
  name: string;
  points: number;
  description: string;
  icon: React.ReactNode;
  color: string;
}

interface TestResult {
  activity: string;
  points: number;
  timestamp: string;
  success: boolean;
  newTotal?: number;
  error?: string;
}

const activities: ActivityType[] = [
  {
    id: 'thought_leadership_post',
    name: 'Thought Leadership Post',
    points: 8,
    description: 'Share industry insights and expertise',
    icon: <Star className="h-4 w-4" />,
    color: 'bg-purple-100 text-purple-600',
  },
  {
    id: 'resource_post',
    name: 'Resource Post',
    points: 5,
    description: 'Share valuable resources with the community',
    icon: <Trophy className="h-4 w-4" />,
    color: 'bg-blue-100 text-blue-600',
  },
  {
    id: 'comment',
    name: 'Comment',
    points: 3,
    description: 'Engage with community posts',
    icon: <MessageCircle className="h-4 w-4" />,
    color: 'bg-green-100 text-green-600',
  },
  {
    id: 'upvote',
    name: 'Upvote',
    points: 3,
    description: 'Appreciate quality content',
    icon: <Heart className="h-4 w-4" />,
    color: 'bg-red-100 text-red-600',
  },
  {
    id: 'news_post',
    name: 'News Post',
    points: 2,
    description: 'Share industry news and updates',
    icon: <Calendar className="h-4 w-4" />,
    color: 'bg-orange-100 text-orange-600',
  },
  {
    id: 'add_connection',
    name: 'New Connection',
    points: 1,
    description: 'Connect with other members',
    icon: <UserPlus className="h-4 w-4" />,
    color: 'bg-pink-100 text-pink-600',
  },
  {
    id: 'daily_visit',
    name: 'Daily Visit',
    points: 1,
    description: 'Stay active in the community',
    icon: <Zap className="h-4 w-4" />,
    color: 'bg-yellow-100 text-yellow-600',
  },
];

export default function TestIQNotificationsPage() {
  const { isLoggedIn, user } = useAuthStatus();
  const { notifications, unreadCount, socket } = useNotifications();
  const [loading, setLoading] = useState<string | null>(null);
  const [results, setResults] = useState<TestResult[]>([]);

  const testSocketConnection = () => {
    if (socket) {
      // Test emit
      socket.emit('test_connection', {
        message: 'Test from client',
        timestamp: new Date().toISOString(),
        userId: user?.id,
      });

      alert(
        `Socket ${socket.connected ? 'is connected' : 'is disconnected'}. Check console for details.`
      );
    } else {
      alert('Socket is not initialized');
    }
  };

  const triggerActivity = async (activity: ActivityType) => {
    if (!user?.id) {
      alert('Please log in to test notifications');
      return;
    }

    setLoading(activity.id);

    try {
      // Call the test API endpoint that any logged-in user can use
      const response = await fetch('/api/wp-proxy/iq-score/test-award', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          activity_type: activity.id,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setResults((prev) => [
          {
            activity: activity.name,
            points: activity.points,
            timestamp: new Date().toLocaleTimeString(),
            success: true,
            newTotal: result.data?.new_score || 'Unknown',
          },
          ...prev.slice(0, 9),
        ]); // Keep last 10 results
      } else {
        setResults((prev) => [
          {
            activity: activity.name,
            points: activity.points,
            timestamp: new Date().toLocaleTimeString(),
            success: false,
            error: result.message || 'Failed to award points',
          },
          ...prev.slice(0, 9),
        ]);
      }
    } catch (error) {
      console.error('Error awarding points:', error);
      setResults((prev) => [
        {
          activity: activity.name,
          points: activity.points,
          timestamp: new Date().toLocaleTimeString(),
          success: false,
          error: 'Network error',
        },
        ...prev.slice(0, 9),
      ]);
    } finally {
      setLoading(null);
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please log in to test the IQ points notification system.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">IQ Points Notification Test</h1>
          <p className="text-gray-600">
            Test the real-time notification system by triggering different point-awarding
            activities. Each activity will award points and send a socket notification.
          </p>
        </div>

        {/* Current Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-600">Current User</p>
                  <p className="font-semibold">{user?.display_name || user?.username}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-amber-500" />
                <div>
                  <p className="text-sm text-gray-600">Unread Notifications</p>
                  <p className="font-semibold">{unreadCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm text-gray-600">Total Notifications</p>
                  <p className="font-semibold">{notifications.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Socket Debug */}
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>Socket Debug</CardTitle>
            <CardDescription>Test socket connection and debug information.</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" onClick={testSocketConnection} className="mb-2">
              Test Socket Connection
            </Button>
            <p className="text-xs text-gray-500">
              Socket Status: {socket?.connected ? '✅ Connected' : '❌ Disconnected'}
              {socket?.id && ` (ID: ${socket.id})`}
            </p>
          </CardContent>
        </Card>

        {/* Activity Triggers */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Trigger Activities</CardTitle>
            <CardDescription>
              Click any activity below to simulate earning points and receiving notifications.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {activities.map((activity) => (
                <Button
                  key={activity.id}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start gap-2"
                  onClick={() => triggerActivity(activity)}
                  disabled={loading === activity.id}
                >
                  <div className="flex items-center gap-2 w-full">
                    <div className={`p-2 rounded-full ${activity.color}`}>{activity.icon}</div>
                    <div className="flex-1 text-left">
                      <p className="font-medium">{activity.name}</p>
                      <Badge variant="secondary" className="text-xs">
                        +{activity.points} points
                      </Badge>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 text-left">{activity.description}</p>
                  {loading === activity.id && (
                    <div className="text-xs text-blue-500">Awarding points...</div>
                  )}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Results */}
        {results.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Results from your recent point-awarding activities.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {results.map((result, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border ${
                      result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{result.activity}</p>
                        <p className="text-sm text-gray-600">
                          {result.success ? `+${result.points} points awarded` : result.error}
                        </p>
                        {result.success && result.newTotal && (
                          <p className="text-xs text-gray-500">
                            New total: {result.newTotal} points
                          </p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="text-xs text-gray-500">{result.timestamp}</p>
                        <Badge
                          variant={result.success ? 'default' : 'destructive'}
                          className="text-xs"
                        >
                          {result.success ? 'Success' : 'Failed'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>How to Test</CardTitle>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>
                Make sure you&apos;re logged in and the notification bell is visible in the header
              </li>
              <li>Click any activity button above to simulate earning points</li>
              <li>
                Watch for the notification bell to show a red badge indicating new notifications
              </li>
              <li>Click the notification bell to see your IQ points notification with details</li>
              <li>Try different activities to see various point values and messages</li>
              <li>
                Earn enough points to trigger a rank promotion for special celebration notifications
              </li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
