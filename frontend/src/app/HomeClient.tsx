/*********************************************
# frontend/src/app/HomeClient.tsx
# 01/27/2025 8:55pm Removed left and right padding from feed container
# 01/27/2025 8:57pm Added back top padding only (pt-4) for proper spacing
**********************************************/

'use client';

import { useState, useCallback, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import dynamic from 'next/dynamic';
import { X } from 'lucide-react';
import { Feed, SortOption } from '@/components/feed';
import { getCategoryDisplayName } from '@/lib/utils/categoryNames';
import { FeedProvider } from '@/contexts/FeedContext';
import { WPPost } from '@/lib/api/wordpress';

const PostCreator = dynamic(
  () => import('@/components/feed/PostCreator').then((mod) => ({ default: mod.PostCreator })),
  {
    ssr: false,
    loading: () => <div className="h-20 bg-gray-50 rounded-lg animate-pulse" />,
  }
);

interface HomeClientProps {
  initialPosts: WPPost[];
  initialTotalPages: number;
  initialTotalPosts: number;
}

export default function HomeClient({
  initialPosts,
  initialTotalPages,
  initialTotalPosts,
}: HomeClientProps) {
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const searchParams = useSearchParams();

  const handleSortChange = (newSort: SortOption) => {
    setSortBy(newSort);
  };

  const handleCategoryChange = useCallback((category: string | null) => {
    setActiveCategory(category);
  }, []);

  // Handle initial category filter from URL
  useEffect(() => {
    const category = searchParams.get('category');
    if (category) {
      setActiveCategory(category);
    }
  }, [searchParams]);

  // Clear the active category filter
  const handleClearFilter = useCallback(() => {
    setActiveCategory(null);
  }, []);

  return (
    <FeedProvider initialPosts={initialPosts}>
      <div className="pt-4">
        <div className="border-b-2 border-gray-200 pb-6 mb-6">
          <PostCreator />
        </div>

        {/* Posts header with filter */}
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold mb-4">
            {activeCategory ? getCategoryDisplayName(activeCategory) : 'All Posts'}
          </h2>
          {activeCategory && (
            <button
              onClick={handleClearFilter}
              className="text-sm text-[#3d405b] bg-white hover:bg-gray-50 flex items-center gap-1 h-8 px-4 rounded-full border border-gray-200 transition-colors"
            >
              <span>Clear Filter</span>
              <X className="h-4 w-4" />
            </button>
          )}
          {/* <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Sort by:</span>
            <SortDropdown currentSort={sortBy} onSortChange={handleSortChange} />
          </div> */}
        </div>

        {/* Feed with infinite scroll and category filtering */}
        <Feed
          initialPosts={initialPosts}
          initialTotalPages={initialTotalPages}
          initialTotalPosts={initialTotalPosts}
          categoryFilter={true}
          sortBy={sortBy}
          onSortChange={handleSortChange}
          activeCategory={activeCategory}
          onClearFilter={handleClearFilter}
          onCategoryChange={handleCategoryChange}
        />
      </div>
    </FeedProvider>
  );
}
