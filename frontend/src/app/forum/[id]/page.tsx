'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter, notFound } from 'next/navigation';

import QuestionDetailComponent from '@/components/forum/question-detail';
import { getQuestionById } from '@/lib/api/forum';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Lock } from 'lucide-react';
import Link from 'next/link';
import { BackToSectionHeader } from '@/components/ui/BackToSectionHeader';

export default function QuestionPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;

  const [question, setQuestion] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [notFoundError, setNotFoundError] = useState(false);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push(`/login?redirect=/forum/${id}`);
    }
  }, [isAuthenticated, authLoading, router, id]);

  useEffect(() => {
    if (isAuthenticated && id) {
      const fetchQuestion = async () => {
        try {
          setLoading(true);
          const fetchedQuestion = await getQuestionById(id);

          if (!fetchedQuestion) {
            setNotFoundError(true);
            return;
          }

          setQuestion(fetchedQuestion);
        } catch (error) {
          console.error('Error loading question:', error);
          setNotFoundError(true);
        } finally {
          setLoading(false);
        }
      };

      fetchQuestion();
    }
  }, [isAuthenticated, id]);

  if (notFoundError) {
    notFound();
  }

  if (authLoading || loading) {
    return (
      <>
        <BackToSectionHeader href="/forum" text="Back to Q&A" />
        <div className="min-h-screen bg-[#eff1f4]">
          <div className="max-w-[1360px] mx-auto">
            <div className="bg-white rounded-b-lg shadow-sm border border-gray-200">
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <p>Loading...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (!isAuthenticated) {
    return (
      <>
        <BackToSectionHeader href="/forum" text="Back to Q&A" />
        <div className="min-h-screen bg-[#eff1f4]">
          <div className="max-w-[1360px] mx-auto">
            <div className="bg-white rounded-b-lg shadow-sm border border-gray-200">
              <div className="flex items-center justify-center min-h-[400px]">
                <Card className="w-full max-w-md">
                  <CardHeader className="text-center">
                    <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                      <Lock className="h-6 w-6 text-gray-600" />
                    </div>
                    <CardTitle>Authentication Required</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center space-y-4">
                    <p className="text-muted-foreground">
                      Please log in to view forum questions and participate in discussions.
                    </p>
                    <div className="space-y-2">
                      <Link href={`/login?redirect=/forum/${id}`} passHref>
                        <Button className="w-full">Log In</Button>
                      </Link>
                      <Link href={`/register?redirect=/forum/${id}`} passHref>
                        <Button variant="outline" className="w-full">
                          Create Account
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (!question) {
    return (
      <>
        <BackToSectionHeader href="/forum" text="Back to Q&A" />
        <div className="min-h-screen bg-[#eff1f4]">
          <div className="max-w-[1360px] mx-auto">
            <div className="bg-white rounded-b-lg shadow-sm border border-gray-200">
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <p>Loading question...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      {/* Sticky Back to Forum Header */}
      <BackToSectionHeader href="/forum" text="Back to Q&A" />

      <div className="min-h-screen bg-[#eff1f4]">
        <div className="max-w-[1360px] mx-auto">
          {/* Main Content */}
          <div className="bg-white rounded-b-lg shadow-sm border border-gray-200">
            <QuestionDetailComponent question={question} />
          </div>
        </div>
      </div>
    </>
  );
}
