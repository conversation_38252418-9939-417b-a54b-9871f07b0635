import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { MessageCircle } from 'lucide-react';

export default function ForumNotFound() {
  return (
    <div className="min-h-screen bg-[#eff1f4] flex items-center justify-center">
      <div className="max-w-md mx-auto text-center">
        <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-dark-text mb-2">Question Not Found</h1>
        <p className="text-gray-600 mb-6">
          The question you&apos;re looking for doesn&apos;t exist or may have been removed.
        </p>
        <div className="space-y-3">
          <Link href="/forum">
            <Button className="w-full">Back to Forum</Button>
          </Link>
          <Link href="/forum/new">
            <Button variant="outline" className="w-full">
              Ask a Question
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
