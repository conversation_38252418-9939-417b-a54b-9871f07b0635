'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import ForumLayout from '@/components/forum/forum-layout';
import QuestionsList from '@/components/forum/questions-list';
import { getQuestions, transformWPPostToQuestion } from '@/lib/api/forum';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Lock } from 'lucide-react';
import Link from 'next/link';

function ForumPageContent() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [questions, setQuestions] = useState<any[]>([]);
  const [totalPages, setTotalPages] = useState(1);

  const page = 1;
  const sort = (searchParams.get('sort') as 'new' | 'trending') || 'new';

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login?redirect=/forum');
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    if (isAuthenticated) {
      const fetchQuestions = async () => {
        try {
          const { questions: fetchedQuestions, totalPages: fetchedTotalPages } = await getQuestions(
            { page, sort }
          );
          const transformedQuestions = fetchedQuestions.map(transformWPPostToQuestion);
          setQuestions(transformedQuestions);
          setTotalPages(fetchedTotalPages);
        } catch (error) {
          console.error('Error loading forum questions:', error);
          setQuestions([]);
          setTotalPages(1);
        } finally {
        }
      };

      fetchQuestions();
    }
  }, [isAuthenticated, page, sort]);

  if (authLoading) {
    return (
      <ForumLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <p>Loading...</p>
          </div>
        </div>
      </ForumLayout>
    );
  }

  if (!isAuthenticated) {
    return (
      <ForumLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                <Lock className="h-6 w-6 text-gray-600" />
              </div>
              <CardTitle>Authentication Required</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-muted-foreground">
                Please log in to access the community forum and share knowledge with tourism
                professionals.
              </p>
              <div className="space-y-2">
                <Link href="/login?redirect=/forum" passHref>
                  <Button className="w-full">Log In</Button>
                </Link>
                <Link href="/register?redirect=/forum" passHref>
                  <Button variant="outline" className="w-full">
                    Create Account
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </ForumLayout>
    );
  }

  return (
    <>
      <ForumLayout>
        <QuestionsList
          initialQuestions={questions}
          totalPages={totalPages}
          currentPage={page}
          currentSort={sort}
        />
      </ForumLayout>
    </>
  );
}

export default function ForumPage() {
  return (
    <Suspense
      fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}
    >
      <ForumPageContent />
    </Suspense>
  );
}
