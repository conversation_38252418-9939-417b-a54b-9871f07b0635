'use client';

import { useEffect } from 'react';
import ForumLayout from '@/components/forum/forum-layout';
import NewQuestionForm from '@/components/forum/new-question-form';
import { useAuth } from '@/contexts/auth-context';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Lock } from 'lucide-react';
import Link from 'next/link';

export default function NewQuestionPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login?redirect=/forum/new');
    }
  }, [isAuthenticated, authLoading, router]);

  if (authLoading) {
    return (
      <ForumLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <p>Loading...</p>
          </div>
        </div>
      </ForumLayout>
    );
  }

  if (!isAuthenticated) {
    return (
      <ForumLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                <Lock className="h-6 w-6 text-gray-600" />
              </div>
              <CardTitle>Authentication Required</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-muted-foreground">
                Please log in to ask questions in the community forum.
              </p>
              <div className="space-y-2">
                <Link href="/login?redirect=/forum/new" passHref>
                  <Button className="w-full">Log In</Button>
                </Link>
                <Link href="/register?redirect=/forum/new" passHref>
                  <Button variant="outline" className="w-full">
                    Create Account
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </ForumLayout>
    );
  }

  return (
    <ForumLayout>
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Ask a Question</h1>
          <NewQuestionForm />
        </div>
      </div>
    </ForumLayout>
  );
}
