/*********************************************
# frontend/src/app/vendors/page.tsx
# 02/05/2025 12:00am Created vendor directory page
# 02/05/2025 3:45pm Fixed styling flash by making auth states consistent with main component layout
# 02/05/2025 11:30pm Added condensed search results view with VendorSearchItem component
# 02/05/2025 11:45pm Added clear filters button and enhanced search endpoint for better relevance
# 02/06/2025 12:00am Completely rewrote search logic and implemented infinite scroll pagination
# 02/06/2025 11:50am Removed extra left padding (pl-[30px]) from all three containers to match content width of other pages
# 02/07/2025 12:05pm Updated video accordion corner radius to 20px for pill-style appearance
**********************************************/

'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { VendorCard } from '@/components/vendor/vendor-card';
import { VendorSearchItem } from '@/components/vendor/vendor-search-item';
import { Loader2, ChevronDown, ChevronUp, Plus } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { VendorCardData, VendorListResponse, transformVendorToCardData } from '@/types/vendor';
import SearchBar from '@/components/forum/search-bar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

export default function VendorDirectoryPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all-categories');
  const [categories, setCategories] = useState<
    { id: number; name: string; slug: string; count: number }[]
  >([]);
  const [allVendors, setAllVendors] = useState<VendorCardData[]>([]);
  const [paidVendors, setPaidVendors] = useState<VendorCardData[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [paidLoading, setPaidLoading] = useState(false);
  const [, setCategoriesLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [totalVendors, setTotalVendors] = useState(0);
  const headerRef = useRef<HTMLDivElement>(null);
  const [videoMinimized, setVideoMinimized] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  // Vendor suggestion form state
  const [suggestionDialogOpen, setSuggestionDialogOpen] = useState(false);
  const [suggestionForm, setSuggestionForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    organization: '',
    website: '',
    message: '',
  });
  const [suggestionLoading, setSuggestionLoading] = useState(false);

  // Alert dialog state
  const [alertDialogOpen, setAlertDialogOpen] = useState(false);
  const [alertDialogContent, setAlertDialogContent] = useState({
    title: '',
    description: '',
    isError: false,
  });

  // Removed authentication gating - vendors page is now public

  // Fetch paid vendors (featured vendors)
  const fetchPaidVendors = useCallback(async () => {
    setPaidLoading(true);
    try {
      const response = await fetch('/api/wp-proxy/vendors/paid');
      if (response.ok) {
        const data = await response.json();
        const transformedPaidVendors = data.map(transformVendorToCardData);
        setPaidVendors(transformedPaidVendors);
      } else {
        console.error('Failed to fetch paid vendors');
        setPaidVendors([]);
      }
    } catch (error) {
      console.error('Error fetching paid vendors:', error);
      setPaidVendors([]);
    } finally {
      setPaidLoading(false);
    }
  }, []);

  // Fetch vendor categories
  const fetchCategories = useCallback(async () => {
    setCategoriesLoading(true);
    try {
      const response = await fetch('/api/wp-proxy/vendor-categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      } else {
        console.error('Failed to fetch vendor categories');
        setCategories([]);
      }
    } catch (error) {
      console.error('Error fetching vendor categories:', error);
      setCategories([]);
    } finally {
      setCategoriesLoading(false);
    }
  }, []);

  const fetchVendors = useCallback(
    async (page: number = 1, isLoadMore: boolean = false) => {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setAllVendors([]); // Clear vendors when starting fresh
      }

      try {
        const queryParams = new URLSearchParams({
          page: page.toString(),
          per_page: '20',
        });

        if (searchTerm.trim()) {
          queryParams.append('search', searchTerm.trim());
        }

        if (selectedCategory !== 'all-categories') {
          queryParams.append('category', selectedCategory);
        }

        // When not filtering (no search term or category), exclude paid vendors to avoid duplication
        const isFiltering = searchTerm.trim() || selectedCategory !== 'all-categories';
        if (!isFiltering) {
          queryParams.append('exclude_paid', 'true');
        }

        // Use enhanced search endpoint when searching, regular endpoint otherwise
        const endpoint = searchTerm.trim()
          ? `/api/wp-proxy/vendors/search?${queryParams.toString()}`
          : `/api/wp-proxy/vendors/search?${queryParams.toString()}`;

        const response = await fetch(endpoint);
        if (response.ok) {
          const data: VendorListResponse = await response.json();

          // Transform vendors
          const transformedVendors = data.vendors.map(transformVendorToCardData);

          // Sort vendors alphabetically by name (for non-filtered view)
          if (!isFiltering) {
            transformedVendors.sort((a, b) => a.name.localeCompare(b.name));
          }

          if (isLoadMore) {
            // Append to existing vendors and re-sort if not filtering
            setAllVendors((prev) => {
              const combined = [...prev, ...transformedVendors];
              if (!isFiltering) {
                combined.sort((a, b) => a.name.localeCompare(b.name));
              }
              return combined;
            });
          } else {
            // Replace vendors
            setAllVendors(transformedVendors);
          }

          // Update pagination info
          if (data.pagination) {
            setHasMorePages(page < data.pagination.total_pages);
            setTotalVendors(data.pagination.total);
          } else {
            setHasMorePages(false);
            setTotalVendors(transformedVendors.length);
          }
        } else {
          console.error('Failed to fetch vendors');
          if (!isLoadMore) {
            setAllVendors([]);
          }
        }
      } catch (error) {
        console.error('Error fetching vendors:', error);
        if (!isLoadMore) {
          setAllVendors([]);
        }
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [searchTerm, selectedCategory]
  );

  // Load more vendors for infinite scroll
  const loadMoreVendors = useCallback(() => {
    if (!loadingMore && hasMorePages) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchVendors(nextPage, true);
    }
  }, [loadingMore, hasMorePages, currentPage, fetchVendors]);

  // Reset page and fetch vendors when search term or category changes
  useEffect(() => {
    setCurrentPage(1);
    fetchVendors(1, false);
  }, [searchTerm, selectedCategory, fetchVendors]);

  // Fetch paid vendors on component mount
  useEffect(() => {
    fetchPaidVendors();
  }, [fetchPaidVendors]);

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Infinite scroll functionality
  useEffect(() => {
    const handleScroll = () => {
      if (loadingMore || !hasMorePages) return;

      const scrollTop = window.pageYOffset;
      const windowHeight = window.innerHeight;
      const docHeight = document.documentElement.scrollHeight;

      // Load more when user is within 300px of the bottom
      if (scrollTop + windowHeight >= docHeight - 300) {
        loadMoreVendors();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [loadingMore, hasMorePages, loadMoreVendors]);

  const clearSearch = () => {
    setSearchTerm('');
    setSelectedCategory('all-categories');
  };

  // Handle vendor suggestion form submission
  const handleSuggestionSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSuggestionLoading(true);

    try {
      const response = await fetch('/api/vendor-suggestion', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(suggestionForm),
      });

      if (response.ok) {
        // Reset form and close dialog
        setSuggestionForm({
          firstName: '',
          lastName: '',
          email: '',
          organization: '',
          website: '',
          message: '',
        });
        setSuggestionDialogOpen(false);

        // Show success alert after a small delay to ensure dialog is closed
        setTimeout(() => {
          setAlertDialogContent({
            title: 'Success! 🎉',
            description:
              "Thank you! Your vendor suggestion has been submitted successfully. We'll review it and get back to you soon.",
            isError: false,
          });
          setAlertDialogOpen(true);
        }, 100);
      } else {
        // Close dialog first
        setSuggestionDialogOpen(false);

        // Show error alert after a small delay
        setTimeout(() => {
          setAlertDialogContent({
            title: 'Submission Failed',
            description:
              'There was an error submitting your suggestion. Please try again or contact support if the problem persists.',
            isError: true,
          });
          setAlertDialogOpen(true);
        }, 100);
      }
    } catch (error) {
      console.error('Error submitting vendor suggestion:', error);
      // Close dialog first
      setSuggestionDialogOpen(false);

      // Show error alert after a small delay
      setTimeout(() => {
        setAlertDialogContent({
          title: 'Connection Error',
          description:
            'Unable to submit your suggestion due to a connection issue. Please check your internet connection and try again.',
          isError: true,
        });
        setAlertDialogOpen(true);
      }, 100);
    } finally {
      setSuggestionLoading(false);
    }
  };

  // Handle form field changes
  const handleSuggestionFormChange = (field: string, value: string) => {
    setSuggestionForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Determine if we're in search mode
  const isFiltered = searchTerm.trim().length > 0 || selectedCategory !== 'all-categories';

  // For display, use allVendors directly as it now includes prioritized vendors
  const displayVendors = allVendors;

  // Set video thumbnail to 6 seconds
  const handleVideoLoadedMetadata = (videoElement: HTMLVideoElement) => {
    if (videoElement) {
      videoElement.currentTime = 6;
    }
  };

  // Removed authentication check - vendors page is now public

  // Removed authentication check - vendors page is now public

  return (
    <div className="min-h-screen bg-[#eff1f4]">
      <style jsx>{`
        video::-webkit-media-controls-panel {
          background: linear-gradient(
            to bottom,
            transparent 0%,
            rgba(61, 64, 91, 0.3) 50%,
            rgba(61, 64, 91, 0.8) 100%
          );
        }

        video::-webkit-media-controls-timeline {
          background: rgba(61, 64, 91, 0.2);
        }

        video::-webkit-media-controls-timeline::-webkit-slider-thumb {
          background: #3d405b;
        }

        /* Firefox */
        video::-moz-media-controls {
          background: linear-gradient(
            to bottom,
            transparent 0%,
            rgba(61, 64, 91, 0.3) 50%,
            rgba(61, 64, 91, 0.8) 100%
          );
        }
      `}</style>
      <div className="max-w-[1360px] mx-auto py-8">
        <div className="mb-6" ref={headerRef}>
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold">Vendor Directory</h1>
            <Dialog open={suggestionDialogOpen} onOpenChange={setSuggestionDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[#5cc8ff] hover:bg-[#4db8ef] text-white rounded-full px-4 py-2 flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Suggest Vendor
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Suggest a Vendor</DialogTitle>
                  <DialogDescription>
                    Know a tourism business that should be in our directory? Let us know!
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSuggestionSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">First Name *</Label>
                        <Input
                          id="firstName"
                          value={suggestionForm.firstName}
                          onChange={(e) => handleSuggestionFormChange('firstName', e.target.value)}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName">Last Name *</Label>
                        <Input
                          id="lastName"
                          value={suggestionForm.lastName}
                          onChange={(e) => handleSuggestionFormChange('lastName', e.target.value)}
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={suggestionForm.email}
                        onChange={(e) => handleSuggestionFormChange('email', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="organization">Organization Name *</Label>
                      <Input
                        id="organization"
                        value={suggestionForm.organization}
                        onChange={(e) => handleSuggestionFormChange('organization', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        type="url"
                        placeholder="https://"
                        value={suggestionForm.website}
                        onChange={(e) => handleSuggestionFormChange('website', e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="message">Additional Message</Label>
                      <Textarea
                        id="message"
                        placeholder="Tell us why this vendor would be a great addition..."
                        value={suggestionForm.message}
                        onChange={(e) => handleSuggestionFormChange('message', e.target.value)}
                        rows={3}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setSuggestionDialogOpen(false)}
                      disabled={suggestionLoading}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={suggestionLoading}
                      className="bg-[#5cc8ff] hover:bg-[#4db8ef]"
                    >
                      {suggestionLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        'Submit Suggestion'
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
          <p className="text-muted-foreground">
            Discover tourism businesses and service providers. Search by name, category, website, or
            assigned team members.
          </p>

          {/* Search and Category Filter Bar (moved up) */}
          <div className="flex flex-col sm:flex-row gap-3 mt-6">
            <div className="flex-[2_1_0%] min-w-0">
              <SearchBar
                onSearch={setSearchTerm}
                placeholder="Search vendors..."
                defaultValue={searchTerm}
                inputClassName="bg-white rounded-full h-12 border border-gray-200 px-6 text-base"
              />
            </div>
            <div className="flex gap-3 flex-wrap">
              <div className="flex-[1_1_0%] max-w-xs min-w-0">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="bg-white rounded-full border !h-12 px-6 font-semibold w-full">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[350px] overflow-y-auto">
                    <SelectItem value="all-categories">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {(searchTerm || selectedCategory !== 'all-categories') && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('all-categories');
                  }}
                  className="h-12 px-4 rounded-full bg-red-50 border border-red-200 text-red-700 hover:bg-red-100 hover:border-red-300 font-medium"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          </div>

          {/* Video Section with Minimize/Expand */}
          <div
            className={`mt-6 bg-white rounded-[20px] relative transition-all duration-300 !shadow-none border border-gray-200 ${
              videoMinimized
                ? 'flex items-center h-[44px] p-0 px-5 overflow-hidden cursor-pointer hover:bg-white hover:border hover:border-[#5cc8ff]'
                : 'p-5'
            }`}
            style={{ boxShadow: 'none' }}
            onClick={videoMinimized ? () => setVideoMinimized(!videoMinimized) : undefined}
          >
            <div
              className={`flex items-center justify-between w-full${videoMinimized ? '' : ' mb-2'}`}
            >
              <span className="font-semibold text-[#3d405b] text-base">
                Watch the Vendor Directory Intro Video
              </span>
              {videoMinimized ? (
                <ChevronDown className="h-4 w-4 text-gray-600" />
              ) : (
                <button
                  type="button"
                  onClick={() => setVideoMinimized(!videoMinimized)}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <ChevronUp className="h-4 w-4 text-gray-600" />
                </button>
              )}
            </div>

            {!videoMinimized && (
              <video
                ref={videoRef}
                controls
                preload="metadata"
                className="w-full rounded-lg border border-gray-200"
                style={{ boxShadow: 'none' }}
                onLoadedMetadata={() => handleVideoLoadedMetadata(videoRef.current!)}
              >
                <source src="/video/how-to-vendors.mp4" type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            )}
          </div>
        </div>

        {/* Featured Vendors - Only show when not filtering */}
        {!isFiltered && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Partner Showcase</h2>
            {paidLoading ? (
              <div className="flex items-center justify-center py-6">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <p className="text-sm text-muted-foreground">Loading featured vendors...</p>
                </div>
              </div>
            ) : paidVendors.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {paidVendors.map((vendor) => (
                  <VendorCard key={vendor.id} vendor={vendor} featured={true} />
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-6">
                No featured vendors available.
              </p>
            )}
          </div>
        )}

        {/* Results Count */}
        <div className="mb-4 flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            {isFiltered ? (
              <>
                Search results: {displayVendors.length} vendor
                {displayVendors.length !== 1 ? 's' : ''} found
                {totalVendors > displayVendors.length && <span> of {totalVendors} total</span>}
              </>
            ) : (
              <>
                Directory: {displayVendors.length} vendor{displayVendors.length !== 1 ? 's' : ''}
                {totalVendors > displayVendors.length && <span> of {totalVendors} total</span>}
              </>
            )}
          </div>
          {(loading || loadingMore) && <Loader2 className="h-4 w-4 animate-spin" />}
        </div>

        {loading && displayVendors.length === 0 ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <p>Loading vendors...</p>
            </div>
          </div>
        ) : displayVendors.length > 0 ? (
          <>
            {isFiltered ? (
              /* Search Results View - Condensed List */
              <div className="space-y-3">
                {displayVendors.map((vendor) => (
                  <VendorSearchItem key={vendor.id} vendor={vendor} />
                ))}
              </div>
            ) : (
              /* Normal Directory View */
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {displayVendors.map((vendor) => (
                  <VendorCard key={vendor.id} vendor={vendor} featured={vendor.isPaid} />
                ))}
              </div>
            )}

            {/* Loading indicator for infinite scroll */}
            {loadingMore && (
              <div ref={loadingRef} className="flex justify-center mt-8 py-6">
                <div className="flex items-center gap-3">
                  <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                  <span className="text-[#3d405b] font-medium">Loading more vendors...</span>
                </div>
              </div>
            )}

            {/* End of results indicator */}
            {!hasMorePages && displayVendors.length > 0 && (
              <div className="text-center mt-8 py-4">
                <p className="text-sm text-muted-foreground">
                  {isFiltered ? 'End of search results' : "You've reached the end of the directory"}
                </p>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12 px-4">
            <p className="text-lg font-medium">No vendors found</p>
            <p className="text-muted-foreground">
              {isFiltered ? 'Try adjusting your search terms' : 'Check back later for new vendors'}
            </p>
            {isFiltered && (
              <Button variant="outline" className="mt-4" onClick={clearSearch}>
                Clear search
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Success/Error Alert Dialog */}
      <AlertDialog
        open={alertDialogOpen}
        onOpenChange={(open) => {
          setAlertDialogOpen(open);
          // Force a re-render to ensure overlay is removed
          if (!open) {
            setTimeout(() => {
              document.body.style.overflow = 'auto';
              document.body.style.pointerEvents = 'auto';
            }, 0);
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle
              className={alertDialogContent.isError ? 'text-red-600' : 'text-green-600'}
            >
              {alertDialogContent.title}
            </AlertDialogTitle>
            <AlertDialogDescription>{alertDialogContent.description}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() => setAlertDialogOpen(false)}
              className={
                alertDialogContent.isError
                  ? 'bg-red-600 hover:bg-red-700'
                  : 'bg-green-600 hover:bg-green-700'
              }
            >
              OK
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
