/*********************************************
# frontend/src/app/vendors/[slug]/page.tsx  
# 01/27/2025 9:45pm Updated to use sticky BackToSectionHeader component
# 01/27/2025 10:00pm Removed top corner radius for seamless header integration
# 01/27/2025 10:15pm Removed top padding for seamless header positioning
# 02/07/2025 12:35pm Updated to use solid social icons instead of line versions
# 02/07/2025 5:00pm Changed header background to white and updated tabs styling with white background and light blue active state
# 02/07/2025 5:15pm Added padding to tabs container, full opacity for active state, and removed top padding from About section
# 02/07/2025 5:30pm Fixed tab container padding for better button centering and reduced spacing below About header
# 02/07/2025 5:45pm Made tab container taller and further reduced spacing below About header
# 02/07/2025 6:00pm Made active state text bolder in tabbed navigation
# 02/07/2025 6:15pm Made tabbed navigation bar and active state buttons pill-shaped
# 02/07/2025 6:30pm Added 1px horizontal padding to fix tab container alignment
# 02/07/2025 6:45pm Changed About section body copy to use prose-sm to match post body copy styling
# 02/07/2025 7:00pm Increased padding for header and body copy sections by 30%
# 02/07/2025 7:15pm Removed bottom margin from paragraph tags in About section
# 02/07/2025 8:00pm Updated contact form styling: made Name, Email, and Subject fields pill-style (rounded-full) and Message field with 20px corner radius
**********************************************/

'use client';

import { useEffect, useState, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Building,
  FileText,
  FileIcon,
  Globe,
  Download,
  Users,
  Loader2,
  CheckCircle,
  X,
} from 'lucide-react';
import { Vendor } from '@/types/vendor';
import { VendorEditForm } from '@/components/vendor/vendor-edit-form';
import { VendorPosts } from '@/components/vendor/vendor-posts';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import { useAuth } from '@/contexts/auth-context';
import { decodeHtmlEntities } from '@/lib/text-utils';
import { BackToSectionHeader } from '@/components/ui/BackToSectionHeader';
import {
  FacebookSolid,
  TwitterSolid,
  LinkedinSolid,
  InstagramSolid,
  YoutubeSolid,
} from '@/components/ui/SocialIcons';

interface VendorDetailPageProps {
  params: Promise<{ slug: string }>;
}

export default function VendorDetailPage({ params }: VendorDetailPageProps) {
  const [vendor, setVendor] = useState<Vendor | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [slug, setSlug] = useState<string>('');
  const [showUpgradeSuccess, setShowUpgradeSuccess] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState<string | null>(null);
  const [emailSent, setEmailSent] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { user, isAuthenticated } = useAuth();
  const searchParams = useSearchParams();

  useEffect(() => {
    const getSlug = async () => {
      const resolvedParams = await params;
      setSlug(resolvedParams.slug);
    };
    getSlug();
  }, [params]);

  const fetchVendor = useCallback(async () => {
    if (!slug) return;

    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/wp-proxy/vendors/${slug}`);
      if (response.ok) {
        const vendorData = await response.json();
        setVendor(vendorData);
      } else if (response.status === 404) {
        setError('Vendor not found');
      } else {
        setError('Failed to load vendor');
      }
    } catch (error) {
      console.error('Error fetching vendor:', error);
      setError('Failed to load vendor');
    } finally {
      setLoading(false);
    }
  }, [slug]);

  useEffect(() => {
    fetchVendor();
  }, [fetchVendor]);

  // Check for upgrade success parameter
  useEffect(() => {
    if (searchParams.get('upgraded') === 'true') {
      setShowUpgradeSuccess(true);
      // Auto-hide after 10 seconds
      const timer = setTimeout(() => setShowUpgradeSuccess(false), 10000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [searchParams]);

  const handleUpgradeClick = () => {
    if (!vendor || !user) return;
    setShowEmailDialog(true);
    setEmail(user.email || '');
    setEmailError(null);
    setEmailSent(false);
  };

  const handleEmailSubmit = async () => {
    if (!vendor || !email) return;

    setIsProcessing(true);
    setEmailError(null);

    try {
      // Request upgrade link via email
      const response = await fetch(`/api/wp-proxy/vendors/by-id/${vendor.id}/request-upgrade-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send upgrade link');
      }

      await response.json();
      setEmailSent(true);
      
      // Auto-close dialog after 5 seconds
      setTimeout(() => {
        setShowEmailDialog(false);
        setEmailSent(false);
      }, 5000);
    } catch (err) {
      console.error('Error requesting upgrade link:', err);
      setEmailError(err instanceof Error ? err.message : 'Failed to send upgrade link');
    } finally {
      setIsProcessing(false);
    }
  };

  // Remove visual distinction between paid and unpaid vendors

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.includes('pdf')) return FileText;
    if (mimeType.includes('image')) return FileIcon;
    return FileIcon;
  };

  // Check if current user is an assigned member
  const isAssignedMember = () => {
    if (!isAuthenticated || !user || !vendor?.vendor_meta.assigned_members) {
      return false;
    }

    return vendor.vendor_meta.assigned_members.some((member) => member.id === user.id);
  };

  if (loading) {
    return (
      <div className="pb-4">
        {/* Sticky Back to Directory Header */}
        <BackToSectionHeader href="/vendors" text="Back to Directory" />

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <p>Loading vendor profile...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !vendor) {
    return (
      <div className="pb-4">
        {/* Sticky Back to Directory Header */}
        <BackToSectionHeader href="/vendors" text="Back to Directory" />

        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">Vendor Not Found</h1>
          <p className="text-muted-foreground mb-6">
            {error || 'The vendor you are looking for does not exist.'}
          </p>
        </div>
      </div>
    );
  }

  const isPaid = vendor.vendor_meta.is_paid;
  const socialLinks = vendor.vendor_meta.social_links || {};

  return (
    <div className="pb-4">
      {/* Sticky Back to Directory Header */}
      <BackToSectionHeader href="/vendors" text="Back to Directory" />

      {/* Upgrade Success Banner */}
      {showUpgradeSuccess && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CheckCircle className="h-6 w-6 text-green-600" />
            <div>
              <h3 className="text-green-800 font-medium">Upgrade Successful!</h3>
              <p className="text-green-700 text-sm">
                Welcome to your premium vendor account. You now have access to all premium features!
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowUpgradeSuccess(false)}
            className="text-green-600 hover:text-green-700"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Header Section */}
      <div className="relative overflow-hidden rounded-b-lg mb-8 border bg-white">
        {/* Cover Photo */}
        <div className="h-48 relative">
          {isPaid && vendor.vendor_meta.cover_photo ? (
            <>
              <img
                src={vendor.vendor_meta.cover_photo}
                alt={`${vendor.title.rendered} cover`}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/20"></div>
            </>
          ) : (
            <div className="w-full h-full bg-gray-200"></div>
          )}

          {/* Removed featured badge */}
        </div>

        {/* Profile Info */}
        <div className="p-6 relative bg-white">
          {/* Logo */}
          <Avatar className="h-24 w-24 border-4 border-white absolute -top-12 left-6 shadow-lg">
            <AvatarImage src={vendor.vendor_meta.logo_url || ''} alt={vendor.title.rendered} />
            <AvatarFallback>
              <Building className="h-12 w-12 text-muted-foreground" />
            </AvatarFallback>
          </Avatar>

          <div className="pt-14">
            <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
              <div className="flex-1">
                <h1 className="text-3xl font-bold mb-2">
                  {decodeHtmlEntities(vendor.title.rendered)}
                </h1>
                <div className="flex flex-wrap items-center gap-2 mb-4">
                  {vendor.vendor_categories.map((category, index) => (
                    <span
                      key={index}
                      className="text-sm text-muted-foreground bg-muted px-3 py-1 rounded-full"
                    >
                      {decodeHtmlEntities(category.name)}
                    </span>
                  ))}
                </div>
              </div>

              {/* Contact Info */}
              <div className="flex flex-col gap-2">
                {vendor.vendor_meta.website && (
                  <Button variant="outline" asChild>
                    <a href={vendor.vendor_meta.website} target="_blank" rel="noopener noreferrer">
                      <Globe className="h-4 w-4 mr-2" />
                      Visit Website
                    </a>
                  </Button>
                )}

                {/* Edit Profile Button for Assigned Members */}
                {isAssignedMember() && <VendorEditForm vendor={vendor} onUpdate={fetchVendor} />}
              </div>
            </div>

            {/* Social Links for Paid Vendors */}
            {isPaid && Object.keys(socialLinks).length > 0 && (
              <div className="flex gap-2 mt-4">
                {socialLinks.facebook && (
                  <Button variant="outline" size="icon" asChild>
                    <a href={socialLinks.facebook} target="_blank" rel="noopener noreferrer">
                      <FacebookSolid className="h-4 w-4 text-[#3d405b]" />
                    </a>
                  </Button>
                )}
                {socialLinks.instagram && (
                  <Button variant="outline" size="icon" asChild>
                    <a href={socialLinks.instagram} target="_blank" rel="noopener noreferrer">
                      <InstagramSolid className="h-4 w-4 text-[#3d405b]" />
                    </a>
                  </Button>
                )}
                {socialLinks.twitter && (
                  <Button variant="outline" size="icon" asChild>
                    <a href={socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                      <TwitterSolid className="h-4 w-4 text-[#3d405b]" />
                    </a>
                  </Button>
                )}
                {socialLinks.linkedin && (
                  <Button variant="outline" size="icon" asChild>
                    <a href={socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                      <LinkedinSolid className="h-4 w-4 text-[#3d405b]" />
                    </a>
                  </Button>
                )}
                {socialLinks.youtube && (
                  <Button variant="outline" size="icon" asChild>
                    <a href={socialLinks.youtube} target="_blank" rel="noopener noreferrer">
                      <YoutubeSolid className="h-4 w-4 text-[#3d405b]" />
                    </a>
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content Tabs for Paid Vendors */}
      {isPaid ? (
        <Tabs defaultValue="about" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-white border border-gray-200 p-1 px-[5px] h-12 rounded-full">
            <TabsTrigger
              value="about"
              className="data-[state=active]:bg-[#5cc8ff] data-[state=active]:text-[#3d405b] data-[state=active]:font-bold py-2 rounded-full"
            >
              About
            </TabsTrigger>
            <TabsTrigger
              value="posts"
              className="data-[state=active]:bg-[#5cc8ff] data-[state=active]:text-[#3d405b] data-[state=active]:font-bold py-2 rounded-full"
            >
              Posts
            </TabsTrigger>
            <TabsTrigger
              value="resources"
              className="data-[state=active]:bg-[#5cc8ff] data-[state=active]:text-[#3d405b] data-[state=active]:font-bold py-2 rounded-full"
            >
              Resources
            </TabsTrigger>
            <TabsTrigger
              value="contact"
              className="data-[state=active]:bg-[#5cc8ff] data-[state=active]:text-[#3d405b] data-[state=active]:font-bold py-2 rounded-full"
            >
              Contact
            </TabsTrigger>
          </TabsList>

          <TabsContent value="about" className="space-y-6">
            {vendor.content.rendered && (
              <Card>
                <CardHeader className="pb-2 px-8 pt-8">
                  <CardTitle>About {decodeHtmlEntities(vendor.title.rendered)}</CardTitle>
                </CardHeader>
                <CardContent className="px-8 pb-8">
                  <div
                    className="prose prose-sm max-w-none [&>p]:mb-0 [&>h1]:mb-3 [&>h1]:mt-0 [&>h2]:mb-3 [&>h2]:mt-6 [&>h3]:mb-3 [&>h3]:mt-6 [&>h4]:mb-3 [&>h4]:mt-6 [&>h5]:mb-3 [&>h5]:mt-6 [&>h6]:mb-3 [&>h6]:mt-6 [&>ul]:mb-4 [&>ol]:mb-4 [&>blockquote]:mb-4 [&>div]:mb-4"
                    dangerouslySetInnerHTML={{
                      __html: vendor.content.rendered,
                    }}
                  />
                </CardContent>
              </Card>
            )}

            {/* Team Members */}
            {vendor.vendor_meta.assigned_members &&
              vendor.vendor_meta.assigned_members.length > 0 && (
                <Card>
                  <CardHeader className="px-8 pt-8">
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Team
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="px-8 pb-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {vendor.vendor_meta.assigned_members.map((member, index) => (
                        <div
                          key={`member-${member.id}-${index}`}
                          className="flex items-center gap-3 p-3 border rounded-lg"
                        >
                          <UserAvatarWithRank
                            userId={member.id}
                            avatarUrl={member.profile_picture?.url || member.avatar_url || ''}
                            displayName={
                              member.first_name && member.last_name
                                ? `${member.first_name} ${member.last_name}`
                                : member.name
                            }
                            size="h-12 w-12"
                            containerSize="w-14 h-14"
                            showRankBadge={true}
                            userRoles={member.roles || []}
                          />
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">
                              {member.first_name && member.last_name
                                ? `${member.first_name} ${member.last_name}`
                                : member.name}
                            </p>
                            {member.job_title && (
                              <p className="text-sm text-muted-foreground truncate">
                                {member.job_title}
                              </p>
                            )}
                            {member.company && (
                              <p className="text-xs text-muted-foreground truncate">
                                {member.company}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
          </TabsContent>

          <TabsContent value="posts" className="space-y-6">
            <VendorPosts
              vendorSlug={vendor.slug}
              vendorName={decodeHtmlEntities(vendor.title.rendered)}
              vendorLogo={vendor.vendor_meta.logo_url || ''}
              canEditPosts={isAssignedMember()}
            />
          </TabsContent>

          <TabsContent value="resources" className="space-y-6">
            <Card>
              <CardHeader className="px-8 pt-8">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Resources & Downloads
                </CardTitle>
              </CardHeader>
              <CardContent className="px-8 pb-8">
                {vendor.vendor_meta.resources && vendor.vendor_meta.resources.length > 0 ? (
                  <div className="space-y-4">
                    {vendor.vendor_meta.resources.map((resource, index) => {
                      const FileIconComponent = getFileIcon(resource.file.mime_type);
                      return (
                        <div
                          key={index}
                          className="flex items-center justify-between p-4 border rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            <FileIconComponent className="h-8 w-8 text-muted-foreground" />
                            <div>
                              <h4 className="font-medium">{resource.title}</h4>
                              {resource.description && (
                                <p className="text-sm text-muted-foreground">
                                  {resource.description}
                                </p>
                              )}
                              <p className="text-xs text-muted-foreground">
                                {resource.file.mime_type.toUpperCase()} •{' '}
                                {formatFileSize(resource.file.filesize)}
                              </p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm" asChild>
                            <a href={resource.file.url} download>
                              <Download className="h-4 w-4 mr-2" />
                              Download
                            </a>
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <p className="text-muted-foreground">No resources available.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contact" className="space-y-6">
            {vendor.vendor_meta.lead_form_enabled ? (
              <Card>
                <CardHeader className="px-8 pt-8">
                  <CardTitle>Contact {decodeHtmlEntities(vendor.title.rendered)}</CardTitle>
                </CardHeader>
                <CardContent>
                  <form className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name" className="mb-2 block">
                          Name
                        </Label>
                        <Input id="name" placeholder="Your name" className="rounded-full" />
                      </div>
                      <div>
                        <Label htmlFor="email" className="mb-2 block">
                          Email
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="Your email"
                          className="rounded-full"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="subject" className="mb-2 block">
                        Subject
                      </Label>
                      <Input id="subject" placeholder="Subject" className="rounded-full" />
                    </div>
                    <div>
                      <Label htmlFor="message" className="mb-2 block">
                        Message
                      </Label>
                      <Textarea
                        id="message"
                        placeholder="Your message"
                        rows={5}
                        className="rounded-[20px]"
                      />
                    </div>
                    <Button type="submit">Send Message</Button>
                  </form>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader className="px-8 pt-8">
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="px-8 pb-8 space-y-4">
                  {vendor.vendor_meta.website && (
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <a
                        href={vendor.vendor_meta.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-[#5cc8ff] hover:text-[#4bb8e8] hover:underline transition-colors"
                      >
                        {vendor.vendor_meta.website}
                      </a>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      ) : (
        /* Simple layout for unpaid vendors */
        <Card>
          <CardContent className="p-6">
            <div className="text-center space-y-6">
              <h2 className="text-xl font-semibold">Contact Information</h2>

              {/* Enhanced Upgrade Call-to-Action */}
              <div className="bg-gradient-to-br from-[#5cc8ff]/10 to-[#5cc8ff]/5 border border-[#5cc8ff]/20 rounded-xl p-6">
                <div className="space-y-4">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Want to showcase more information?
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Unlock premium features to grow your tourism business with enhanced profile
                      visibility, social media integration, and priority support.
                    </p>
                  </div>

                  <button 
                    onClick={handleUpgradeClick}
                    className="w-full bg-[#5cc8ff] text-[#3d405b] py-3 px-6 rounded-full font-semibold transition-all duration-200 shadow-md hover:shadow-lg hover:bg-[#5cc8ff]/80"
                  >
                    Upgrade to Premium - $99/month
                  </button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Email Verification Dialog */}
      {showEmailDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full p-8 shadow-2xl">
            {!emailSent ? (
              <>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Verify Your Email</h3>
                <p className="text-gray-600 mb-6">
                  We'll send a secure payment link to your email address. This link will be valid for 24 hours.
                </p>
                
                <div className="mb-6">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value);
                      setEmailError(null);
                    }}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#5cc8ff] focus:border-[#5cc8ff] transition-colors"
                    disabled={isProcessing}
                  />
                  {emailError && (
                    <p className="mt-2 text-sm text-red-600">{emailError}</p>
                  )}
                </div>

                <div className="flex gap-4">
                  <button
                    onClick={() => setShowEmailDialog(false)}
                    disabled={isProcessing}
                    className="flex-1 px-6 py-3 border border-[#e1e5eb] rounded-full font-semibold text-[#3d405b] bg-white hover:bg-gray-50 transition-all duration-200 disabled:opacity-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleEmailSubmit}
                    disabled={!email || isProcessing}
                    className="flex-1 bg-[#5cc8ff] text-[#3d405b] px-6 py-3 rounded-full font-semibold hover:bg-[#4bb8e8] transition-all duration-200 shadow-md hover:shadow-lg disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {isProcessing ? (
                      <>
                        <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                        </svg>
                        Sending...
                      </>
                    ) : (
                      'Send Upgrade Link'
                    )}
                  </button>
                </div>

                <p className="mt-4 text-xs text-gray-500 text-center">
                  By continuing, you agree to receive an email with payment instructions.
                </p>
              </>
            ) : (
              <div className="text-center">
                <div className="mb-4">
                  <svg className="w-16 h-16 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Email Sent!</h3>
                <p className="text-gray-600 mb-6">
                  We've sent an upgrade link to <strong>{email}</strong>. Please check your inbox and click the link to complete your upgrade.
                </p>
                <p className="text-sm text-gray-500">
                  This dialog will close automatically...
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
