/**
 * Dynamic Sitemap Generation for Next.js
 *
 * This generates a comprehensive sitemap for the headless WordPress + Next.js setup
 * Replaces the old WordPress sitemaps that are showing errors in Google Search Console
 */

import { getPostsForSitemap, getCategories } from '@/lib/api';

export async function GET() {
  const baseUrl = 'https://mytourismiq.com';

  try {
    // Try to fetch dynamic data from WordPress, fall back to static if API unavailable
    let posts: any[] = [];
    let categories: any[] = [];
    let hasDynamicData = false;

    try {
      [posts, categories] = await Promise.all([getPostsForSitemap(), getCategories()]);
      hasDynamicData = true;
      console.log(
        `Sitemap: Generated with ${posts.length} posts and ${categories.length} categories`
      );
      
      // Log sample post dates for debugging
      if (posts.length > 0) {
        console.log('Sample post date formats:', {
          first: posts[0].date,
          firstModified: posts[0].modified,
          last: posts[posts.length - 1].date,
          lastModified: posts[posts.length - 1].modified
        });
      }
    } catch (apiError) {
      console.error('Sitemap API error, using static fallback:', apiError);
      // Use static fallback data
      categories = [
        { slug: 'news', name: 'News' },
        { slug: 'jobs', name: 'Jobs' },
        { slug: 'events', name: 'Events' },
        { slug: 'courses', name: 'Courses' },
        { slug: 'books', name: 'Books' },
        { slug: 'podcasts', name: 'Podcasts' },
        { slug: 'videos', name: 'Videos' },
        { slug: 'webinars', name: 'Webinars' },
      ];
    }

    // Generate sitemap XML
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
        
  <!-- Homepage -->
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  
  <!-- Static Pages -->
  <url>
    <loc>${baseUrl}/forum</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  
  <url>
    <loc>${baseUrl}/jobs</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
  
  <url>
    <loc>${baseUrl}/rfps</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
  
  <url>
    <loc>${baseUrl}/leaderboard</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>
  
  <url>
    <loc>${baseUrl}/member-directory</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>
  
  <url>
    <loc>${baseUrl}/vendors</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>

  <!-- Category Pages -->
  ${categories
    .map(
      (category) => `
  <url>
    <loc>${baseUrl}/category/${category.slug}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.7</priority>
  </url>`
    )
    .join('')}

  <!-- Posts -->
  ${posts
    .map(
      (post) => {
        // Ensure date is properly formatted as ISO 8601
        let lastmodDate = '';
        try {
          const dateValue = post.modified || post.date;
          if (dateValue) {
            // Parse the date and convert to ISO format
            const parsedDate = new Date(dateValue);
            if (!isNaN(parsedDate.getTime())) {
              lastmodDate = parsedDate.toISOString();
            } else {
              // If date is invalid, use current date
              lastmodDate = new Date().toISOString();
            }
          } else {
            // If no date provided, use current date
            lastmodDate = new Date().toISOString();
          }
        } catch (error) {
          // On any error, use current date
          lastmodDate = new Date().toISOString();
        }

        // Safely get the title
        const postTitle = post.title?.rendered || post.title || 'Untitled';
        
        return `
  <url>
    <loc>${baseUrl}/posts/${post.slug}</loc>
    <lastmod>${lastmodDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
    ${
      post.featured_media_url
        ? `
    <image:image>
      <image:loc>${post.featured_media_url}</image:loc>
      <image:title>${postTitle.replace(/[<>&"']/g, (char) => {
        const entities: { [key: string]: string } = {
          '<': '&lt;',
          '>': '&gt;',
          '&': '&amp;',
          '"': '&quot;',
          "'": '&apos;',
        };
        return entities[char] || char;
      })}</image:title>
    </image:image>`
        : ''
    }
  </url>`;
      }
    )
    .join('')}
  
  <!-- Sitemap generated ${hasDynamicData ? 'dynamically' : 'with static fallback'} at ${new Date().toISOString()} -->

</urlset>`;

    return new Response(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);
    return new Response('Error generating sitemap', { status: 500 });
  }
}
