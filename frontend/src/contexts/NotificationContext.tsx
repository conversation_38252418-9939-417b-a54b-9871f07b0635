'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from '@/contexts/auth-context';

// Types
export interface Notification {
  id: string;
  type:
    | 'upvote'
    | 'comment'
    | 'message'
    | 'system'
    | 'connection_request'
    | 'iq_points_earned'
    | 'connection_accepted';
  content: string;
  timestamp: number;
  read: boolean;
  referenceId?: number;
  referenceType?: string;
  senderId?: number;
  senderName?: string;
  senderAvatar?: string;
  // Additional fields for IQ points notifications
  activityType?: string;
  pointsEarned?: number;
  newTotal?: number;
  rankedUp?: boolean;
  newRank?: string;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  socket: Socket | null;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => Promise<void>;
  updateNotification: (id: string, updates: Partial<Notification>) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  clearNotifications: () => void;
  sendNotification: (
    recipientId: number,
    type: string,
    content: string,
    referenceId?: number,
    referenceType?: string
  ) => Promise<void>;
  connected: boolean;
  fetchNotifications: () => Promise<void>;
  refreshNotifications: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Storage key for notifications - will be made user-specific
const getStorageKey = (userId?: number) => `tourismiq_notifications_${userId || 'anonymous'}`;

// Check if we're running in a browser
const isBrowser = typeof window !== 'undefined';

// Set to track recently received notifications to avoid duplicates
const recentNotifications = new Set<string>();

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const { isAuthenticated, user } = useAuth();

  const unreadCount = notifications.filter((n) => !n.read && n.type !== 'message').length;

  // Initialize socket connection when user is logged in
  useEffect(() => {
    if (!isAuthenticated || !user?.id) {
      if (socket) {
        socket.disconnect();
        setSocket(null);
        setConnected(false);
      }
      // Clear notifications when user logs out
      setNotifications([]);
      return () => {};
    }

    // Get socket URL from environment variable
    const socketUrl =
      process.env.NODE_ENV === 'production'
        ? process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000'
        : process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';

    // Create socket connection with specific configuration for cookie support
    const socketInstance = io(socketUrl, {
      transports: ['websocket'],
      autoConnect: true,
      withCredentials: true, // Essential for sending cookies cross-domain
      extraHeaders: {
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });

    socketInstance.on('connect', () => {
      setConnected(true);

      // Authenticate with just the user ID
      if (user && user.id) {
        // Send a simple authentication request
        socketInstance.emit('authenticate', {
          userId: user.id,
        });
      } else {
        console.error('Socket authentication failed: No user ID available');
      }
    });

    socketInstance.on('disconnect', () => {
      setConnected(false);
    });

    socketInstance.on('notification', (notification: Omit<Notification, 'read'>) => {
      // Filter out message notifications - they have their own UI
      if (notification.type === 'message') {
        return;
      }

      // Create a deduplication key based on notification properties
      const dedupeKey = `${notification.type}:${notification.referenceId}:${notification.senderId}:${notification.content}`;

      // Check if we've recently received this notification
      if (recentNotifications.has(dedupeKey)) {
        return;
      }

      // Add to recent notifications set and set a timeout to remove it
      recentNotifications.add(dedupeKey);
      setTimeout(() => {
        recentNotifications.delete(dedupeKey);
      }, 10000); // Clear after 10 seconds to prevent long-term memory issues

      const newNotification: Notification = {
        ...notification,
        read: false,
      };

      setNotifications((prev) => [newNotification, ...prev]);

      // Show browser notification if supported
      if (isBrowser && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('TourismIQ', {
          body: notification.content,
        });
      }
    });

    setSocket(socketInstance);

    // Clean up on unmount
    return () => {
      socketInstance.disconnect();
      setSocket(null);
      setConnected(false);
    };
  }, [isAuthenticated, user?.id]);

  // Load notifications from localStorage when user changes
  useEffect(() => {
    if (isBrowser && user?.id) {
      // Clean up old non-user-specific storage (migration)
      const oldStorageKey = 'tourismiq_notifications';
      if (localStorage.getItem(oldStorageKey)) {
        localStorage.removeItem(oldStorageKey);
      }

      const storageKey = getStorageKey(user.id);
      const storedNotifications = localStorage.getItem(storageKey);
      if (storedNotifications) {
        try {
          setNotifications(JSON.parse(storedNotifications));
        } catch (error) {
          console.error('Failed to parse stored notifications', error);
        }
      }
    } else if (isBrowser && !user?.id) {
      // Clear notifications when user logs out
      setNotifications([]);
    }
  }, [user?.id]);

  // Save notifications to localStorage whenever they change
  useEffect(() => {
    if (isBrowser && user?.id && notifications.length > 0) {
      const storageKey = getStorageKey(user.id);
      localStorage.setItem(storageKey, JSON.stringify(notifications));
    }
  }, [notifications, user?.id]);

  // Request notification permission on mount
  useEffect(() => {
    if (isBrowser && 'Notification' in window && Notification.permission !== 'granted') {
      Notification.requestPermission();
    }
  }, []);

  // Fetch notifications from database
  const fetchNotifications = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      return;
    }

    try {
      const response = await fetch('/api/wp-proxy/notifications', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error('Failed to fetch notifications:', response.statusText);
        return;
      }

      const data = await response.json();

      if (data.success && data.notifications) {
        // Merge with existing real-time notifications, avoiding duplicates and filtering out messages
        setNotifications((prev) => {
          const existingIds = new Set(prev.map((n) => n.id));
          const newNotifications = data.notifications.filter(
            (n: Notification) => !existingIds.has(n.id) && n.type !== 'message'
          );

          if (newNotifications.length > 0) {
            // Sort all notifications by timestamp (newest first)
            const combined = [...newNotifications, ...prev].sort(
              (a, b) => b.timestamp - a.timestamp
            );
            return combined;
          }
          return prev;
        });
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  }, [isAuthenticated, user?.id]);

  // Fetch notifications from database when user logs in
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      fetchNotifications();
    }
  }, [isAuthenticated, user?.id, fetchNotifications]);

  // Mark a notification as read
  const markAsRead = (id: string) => {
    setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)));
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  // Remove a notification completely
  const removeNotification = async (id: string) => {
    // Optimistically update UI first
    setNotifications((prev) => {
      const updatedNotifications = prev.filter((n) => n.id !== id);

      // Immediately update localStorage
      if (isBrowser && user?.id) {
        const storageKey = getStorageKey(user.id);
        if (updatedNotifications.length > 0) {
          localStorage.setItem(storageKey, JSON.stringify(updatedNotifications));
        } else {
          localStorage.removeItem(storageKey);
        }
      }

      return updatedNotifications;
    });

    // Only delete from database if the ID is numeric (database notifications)
    // Socket-generated notifications have string IDs and only exist locally
    const isNumericId = /^\d+$/.test(id);

    if (isNumericId) {
      try {
        const response = await fetch(`/api/wp-proxy/notifications?id=${id}`, {
          method: 'DELETE',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          console.error('Failed to delete notification from database:', response.statusText);
          // Optionally, we could revert the UI change here if the API call fails
        }
      } catch (error) {
        console.error('Error deleting notification:', error);
        // Optionally, we could revert the UI change here if the API call fails
      }
    }
    // If it's a socket-generated notification (string ID), just remove locally (already done above)
  };

  // Update a notification
  const updateNotification = (id: string, updates: Partial<Notification>) => {
    setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, ...updates } : n)));
  };

  // Add a new notification
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Math.random().toString(36).substring(2, 11),
      timestamp: Date.now(),
      read: false,
    };

    setNotifications((prev) => [newNotification, ...prev]);
  };

  // Clear all notifications
  const clearNotifications = () => {
    setNotifications([]);
    if (isBrowser && user?.id) {
      const storageKey = getStorageKey(user.id);
      localStorage.removeItem(storageKey);
    }
  };

  // Refresh notifications (for the refresh button)
  const refreshNotifications = async () => {
    if (!isAuthenticated || !user?.id) {
      return;
    }

    try {
      const response = await fetch('/api/wp-proxy/notifications', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error('Failed to refresh notifications:', response.statusText);
        return;
      }

      const data = await response.json();

      if (data.success && data.notifications) {
        // Replace all notifications with fresh data from database
        setNotifications(data.notifications);

        // Update localStorage with fresh data
        if (isBrowser && user?.id) {
          const storageKey = getStorageKey(user.id);
          if (data.notifications.length > 0) {
            localStorage.setItem(storageKey, JSON.stringify(data.notifications));
          } else {
            localStorage.removeItem(storageKey);
          }
        }
      }
    } catch (error) {
      console.error('Error refreshing notifications:', error);
    }
  };

  // Send a notification to another user
  const sendNotification = async (
    recipientId: number,
    type: string,
    content: string,
    referenceId?: number,
    referenceType?: string
  ) => {
    let socketSent = false;

    // Try sending through socket first
    if (socket && connected) {
      try {
        socket.emit('send_notification', {
          recipientId,
          type,
          content,
          referenceId,
          referenceType,
          senderId: user?.id, // Add sender ID for deduplication
        });

        socketSent = true;
      } catch (socketError) {
        console.error('Error sending notification via socket:', socketError);
      }
    }

    // Also send through WordPress REST API as fallback
    try {
      const response = await fetch('/api/wp-proxy/notifications/send', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recipient_id: recipientId,
          type,
          content,
          reference_id: referenceId,
          reference_type: referenceType,
          sender_id: user?.id, // Add sender ID for deduplication
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Notification API error:', errorText);

        // Only throw if socket also failed
        if (!socketSent) {
          throw new Error('Failed to send notification via REST API');
        }
      }
    } catch (error) {
      // Only throw if socket also failed
      if (!socketSent) {
        throw error;
      }
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        socket,
        markAsRead,
        markAllAsRead,
        removeNotification,
        updateNotification,
        addNotification,
        clearNotifications,
        sendNotification,
        connected,
        fetchNotifications,
        refreshNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

// Hook for using the notification context
export const useNotifications = () => {
  const context = useContext(NotificationContext);

  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }

  return context;
};
