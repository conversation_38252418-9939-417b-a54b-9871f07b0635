'use client';

import React, { createContext, useContext, useState, useCallback, useRef } from 'react';

interface ConnectionStatus {
  status: 'none' | 'pending' | 'accepted';
  pending_type?: 'sent' | 'received';
}

interface ConnectionContextType {
  getConnectionStatus: (userId: number) => Promise<ConnectionStatus | null>;
  batchGetConnectionStatuses: (userIds: number[]) => Promise<Record<number, ConnectionStatus>>;
  clearCache: () => void;
  getCachedConnectionStatus: (userId: number) => ConnectionStatus | null;
  invalidateUserStatus: (userId: number) => void;
}

const ConnectionContext = createContext<ConnectionContextType | undefined>(undefined);

interface ConnectionProviderProps {
  children: React.ReactNode;
}

export function ConnectionProvider({ children }: ConnectionProviderProps) {
  // Cache for storing connection status data
  const [cache, setCache] = useState<Record<number, ConnectionStatus>>({});

  // Track pending requests to avoid duplicates
  const pendingRequests = useRef<Record<number, Promise<ConnectionStatus | null>>>({});

  // Get cached connection status without making a request
  const getCachedConnectionStatus = useCallback(
    (userId: number): ConnectionStatus | null => {
      return cache[userId] || null;
    },
    [cache]
  );

  // Single user connection status fetch with caching and deduplication
  const getConnectionStatus = useCallback(
    async (userId: number): Promise<ConnectionStatus | null> => {
      // Return cached data if available
      if (cache[userId]) {
        return cache[userId];
      }

      // Return pending request if already in progress
      if (userId in pendingRequests.current) {
        return (await pendingRequests.current[userId]) ?? null;
      }

      // Create new request
      const request = async (): Promise<ConnectionStatus | null> => {
        try {
          // Create timeout controller
          const controller = new AbortController();
          const timeoutId = setTimeout(() => {
            controller.abort();
          }, 10000); // 10 second timeout

          const response = await fetch(`/api/wp-proxy/connections/status/${userId}`, {
            credentials: 'include',
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`Failed to fetch connection status: ${response.status}`);
          }

          const data = await response.json();

          // The API returns the status directly, not wrapped in a success object
          if (data && data.status) {
            const status: ConnectionStatus = {
              status: data.status || 'none',
              pending_type: data.pending_type,
            };

            // Update cache
            setCache((prev) => ({
              ...prev,
              [userId]: status,
            }));

            return status;
          }

          // If no status field, assume no connection
          const noConnection: ConnectionStatus = {
            status: 'none',
          };
          return noConnection;
        } catch (error) {
          console.error(`Error fetching connection status for user ${userId}:`, error);
          return null;
        } finally {
          // Clean up pending request
          delete pendingRequests.current[userId];
        }
      };

      // Store and execute request
      pendingRequests.current[userId] = request();
      return pendingRequests.current[userId];
    },
    [cache]
  );

  // Batch fetch multiple connection statuses
  const batchGetConnectionStatuses = useCallback(
    async (userIds: number[]): Promise<Record<number, ConnectionStatus>> => {
      const result: Record<number, ConnectionStatus> = {};
      const uncachedIds: number[] = [];

      // Separate cached and uncached IDs
      userIds.forEach((userId) => {
        if (cache[userId]) {
          result[userId] = cache[userId];
        } else {
          uncachedIds.push(userId);
        }
      });

      // Fetch uncached IDs in parallel (but limit concurrency)
      if (uncachedIds.length > 0) {
        const BATCH_SIZE = 5; // Limit concurrent requests
        const batches: number[][] = [];

        for (let i = 0; i < uncachedIds.length; i += BATCH_SIZE) {
          batches.push(uncachedIds.slice(i, i + BATCH_SIZE));
        }

        for (const batch of batches) {
          const promises = batch.map((userId) => getConnectionStatus(userId));
          const results = await Promise.all(promises);

          results.forEach((status, index) => {
            if (status && batch[index] !== undefined) {
              result[batch[index]] = status;
            }
          });
        }
      }

      return result;
    },
    [cache, getConnectionStatus]
  );

  // Invalidate a specific user's status (useful after connection actions)
  const invalidateUserStatus = useCallback((userId: number) => {
    setCache((prev) => {
      const newCache = { ...prev };
      delete newCache[userId];
      return newCache;
    });
    delete pendingRequests.current[userId];
  }, []);

  // Clear cache
  const clearCache = useCallback(() => {
    setCache({});
    pendingRequests.current = {};
  }, []);

  const value: ConnectionContextType = {
    getConnectionStatus,
    batchGetConnectionStatuses,
    clearCache,
    getCachedConnectionStatus,
    invalidateUserStatus,
  };

  return <ConnectionContext.Provider value={value}>{children}</ConnectionContext.Provider>;
}

export function useConnectionCache() {
  const context = useContext(ConnectionContext);
  if (context === undefined) {
    throw new Error('useConnectionCache must be used within a ConnectionProvider');
  }
  return context;
}
