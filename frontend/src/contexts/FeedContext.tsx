'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { WPPost as Post } from '@/lib/api/wordpress';

interface FeedContextType {
  posts: Post[];
  addPost: (post: Post) => void;
  setPosts: (posts: Post[]) => void;
  appendPosts: (newPosts: Post[]) => void;
  updatePost: (postId: number, updates: Partial<Post>) => void;
  removePost: (postId: number) => void;
}

const FeedContext = createContext<FeedContextType | undefined>(undefined);

export const useFeed = () => {
  const context = useContext(FeedContext);
  if (!context) {
    throw new Error('useFeed must be used within a FeedProvider');
  }
  return context;
};

interface FeedProviderProps {
  children: ReactNode;
  initialPosts?: Post[];
}

export function FeedProvider({ children, initialPosts = [] }: FeedProviderProps) {
  const [posts, setPosts] = useState<Post[]>(initialPosts);

  // Add a new post to the beginning of the feed
  const addPost = useCallback((post: Post) => {
    setPosts((prev) => [post, ...prev]);
  }, []);

  // Update posts array
  const setPostsCallback = useCallback((newPosts: Post[]) => {
    setPosts(newPosts);
  }, []);

  // Append posts to the end of the array
  const appendPosts = useCallback((newPosts: Post[]) => {
    setPosts((prev) => [...prev, ...newPosts]);
  }, []);

  // Update a specific post
  const updatePost = useCallback((postId: number, updates: Partial<Post>) => {
    setPosts((prev) => prev.map((post) => (post.id === postId ? { ...post, ...updates } : post)));
  }, []);

  // Remove a post from the feed
  const removePost = useCallback((postId: number) => {
    setPosts((prev) => prev.filter((post) => post.id !== postId));
  }, []);

  const contextValue: FeedContextType = {
    posts,
    addPost,
    setPosts: setPostsCallback,
    appendPosts,
    updatePost,
    removePost,
  };

  return <FeedContext.Provider value={contextValue}>{children}</FeedContext.Provider>;
}
