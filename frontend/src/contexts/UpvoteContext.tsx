/*********************************************
# frontend/src/contexts/UpvoteContext.tsx
# 01/27/2025 9:20pm Increased cache expiration to 1hr, batch throttle to 10s to reduce API calls
**********************************************/

'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { useAuthStatus } from '@/hooks/queries/useAuth';

interface UpvoteData {
  [postId: number]: {
    upvoted: boolean;
    count: number;
    lastFetched: number;
  };
}

interface UpvoteContextType {
  getUpvoteStatus: (postId: number) => { upvoted: boolean; count: number } | null;
  fetchUpvoteStatus: (postId: number) => Promise<{ upvoted: boolean; count: number }>;
  toggleUpvote: (postId: number) => Promise<{ upvoted: boolean; count: number }>;
  batchFetchUpvotes: (postIds: number[]) => Promise<void>;
}

const UpvoteContext = createContext<UpvoteContextType | undefined>(undefined);

// Cache expiration time (1 hour - upvotes don't change frequently)
const CACHE_EXPIRATION = 60 * 60 * 1000;

// Track in-flight requests to prevent duplicate API calls
const pendingRequests: Record<number, Promise<{ upvoted: boolean; count: number }>> = {};

export function UpvoteProvider({ children }: { children: ReactNode }) {
  const [upvoteData, setUpvoteData] = useState<UpvoteData>({});
  const { isLoggedIn } = useAuthStatus();

  // Use a ref to track if batch fetch has been done
  const batchFetchDoneRef = useRef<Record<string, boolean>>({});

  // Clear cache when user logs in/out
  useEffect(() => {
    setUpvoteData({});
    // Also clear the batch fetch tracking
    batchFetchDoneRef.current = {};
    // Clear pending requests
    Object.keys(pendingRequests).forEach((key) => delete pendingRequests[Number(key)]);
  }, [isLoggedIn]);

  const getUpvoteStatus = (postId: number) => {
    const data = upvoteData[postId];
    if (!data) return null;

    // Check if cache is expired
    if (Date.now() - data.lastFetched > CACHE_EXPIRATION) {
      return null;
    }

    return { upvoted: data.upvoted, count: data.count };
  };

  const fetchUpvoteStatus = async (
    postId: number
  ): Promise<{ upvoted: boolean; count: number }> => {
    // Check cache first
    const cachedData = getUpvoteStatus(postId);
    if (cachedData) return cachedData;

    // Check if there's already a request in flight for this post
    if (postId in pendingRequests) {
      const result = await pendingRequests[postId];
      return result || { upvoted: false, count: 0 };
    }

    // Create a new request and store it
    const request = (async () => {
      try {
        const response = await fetch(`/api/wp-proxy/posts/${postId}/upvotes`, {
          credentials: 'include',
          // Add cache control headers
          headers: {
            'Cache-Control': 'max-age=300', // 5 minutes
          },
        });

        if (response.ok) {
          const data = await response.json();

          // Update cache
          setUpvoteData((prev) => ({
            ...prev,
            [postId]: {
              upvoted: data.upvoted,
              count: data.count,
              lastFetched: Date.now(),
            },
          }));

          return { upvoted: data.upvoted, count: data.count };
        }

        throw new Error('Failed to fetch upvote status');
      } catch (error) {
        console.error('Error fetching upvote status:', error);
        return { upvoted: false, count: 0 };
      } finally {
        // Remove from pending requests when done
        delete pendingRequests[postId];
      }
    })();

    pendingRequests[postId] = request;
    return request;
  };

  // Track the last batch fetch time to prevent too frequent fetches
  const lastBatchFetchTimeRef = useRef<number>(0);
  // Minimum time between batch fetches (10 seconds)
  const BATCH_FETCH_THROTTLE = 10000;

  const batchFetchUpvotes = async (postIds: number[]) => {
    // Create a unique key for this batch of posts
    const batchKey = postIds.sort().join(',');

    // If we've already fetched this exact batch, don't fetch again
    if (batchFetchDoneRef.current[batchKey]) {
      return;
    }

    // Throttle batch fetches to prevent too many requests
    const now = Date.now();
    if (now - lastBatchFetchTimeRef.current < BATCH_FETCH_THROTTLE) {
      return;
    }
    lastBatchFetchTimeRef.current = now;

    // Filter out posts that have valid cache or pending requests
    const postsToFetch = postIds.filter((id) => !getUpvoteStatus(id) && !pendingRequests[id]);

    if (postsToFetch.length === 0) {
      batchFetchDoneRef.current[batchKey] = true;
      return;
    }

    try {
      // Use our new batch endpoint instead of multiple individual requests
      const response = await fetch('/api/wp-proxy/posts/batch-upvotes', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'max-age=300', // 5 minutes
        },
        body: JSON.stringify({ postIds: postsToFetch }),
      });

      if (response.ok) {
        const batchData = await response.json();

        // Update cache with all the fetched data
        const now = Date.now();
        setUpvoteData((prev) => {
          const newData = { ...prev };

          // Add each post's data to the cache
          Object.entries(batchData as Record<string, { upvoted: boolean; count: number }>).forEach(
            ([postId, data]) => {
              newData[Number(postId)] = {
                upvoted: data.upvoted,
                count: data.count,
                lastFetched: now,
              };
            }
          );

          return newData;
        });
      } else {
        console.error('Error fetching batch upvotes:', await response.text());
      }
    } catch (error) {
      console.error('Error in batch fetch:', error);
    }

    // Mark this batch as done regardless of success/failure
    batchFetchDoneRef.current[batchKey] = true;
  };

  const toggleUpvote = async (postId: number) => {
    // Get current status
    const current = upvoteData[postId] || { upvoted: false, count: 0, lastFetched: 0 };

    // Optimistically update
    const newUpvoted = !current.upvoted;
    const newCount = current.count + (newUpvoted ? 1 : -1);

    setUpvoteData((prev) => ({
      ...prev,
      [postId]: {
        ...current,
        upvoted: newUpvoted,
        count: newCount,
        lastFetched: Date.now(),
      },
    }));

    try {
      const response = await fetch(`/api/wp-proxy/posts/${postId}/upvote`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to toggle upvote');
      }

      const result = await response.json();

      // Update with actual server data
      setUpvoteData((prev) => ({
        ...prev,
        [postId]: {
          upvoted: result.upvoted,
          count: result.count,
          lastFetched: Date.now(),
        },
      }));

      return { upvoted: result.upvoted, count: result.count };
    } catch (error) {
      console.error('Error toggling upvote:', error);

      // Revert optimistic update
      setUpvoteData((prev) => ({
        ...prev,
        [postId]: current,
      }));

      return { upvoted: current.upvoted, count: current.count };
    }
  };

  return (
    <UpvoteContext.Provider
      value={{
        getUpvoteStatus,
        fetchUpvoteStatus,
        toggleUpvote,
        batchFetchUpvotes,
      }}
    >
      {children}
    </UpvoteContext.Provider>
  );
}

export function useUpvotes() {
  const context = useContext(UpvoteContext);
  if (context === undefined) {
    throw new Error('useUpvotes must be used within an UpvoteProvider');
  }
  return context;
}
