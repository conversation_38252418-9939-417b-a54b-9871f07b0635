'use client';

import React, { createContext, useContext, useState, useCallback, useRef } from 'react';
import { iqScoreAPI, type UserIQData } from '@/lib/api/iq-score';

interface IQScoreContextType {
  getUserIQ: (userId: number) => Promise<UserIQData | null>;
  batchGetUserIQs: (userIds: number[]) => Promise<Record<number, UserIQData>>;
  clearCache: () => void;
  getCachedUserIQ: (userId: number) => UserIQData | null;
}

const IQScoreContext = createContext<IQScoreContextType | undefined>(undefined);

interface IQScoreProviderProps {
  children: React.ReactNode;
}

export function IQScoreProvider({ children }: IQScoreProviderProps) {
  // Cache for storing IQ data
  const [cache, setCache] = useState<Record<number, UserIQData>>({});

  // Track pending requests to avoid duplicates
  const pendingRequests = useRef<Record<number, Promise<UserIQData | null>>>({});

  // Get cached IQ data without making a request
  const getCachedUserIQ = useCallback(
    (userId: number): UserIQData | null => {
      return cache[userId] || null;
    },
    [cache]
  );

  // Single user IQ fetch with caching and deduplication
  const getUserIQ = useCallback(
    async (userId: number): Promise<UserIQData | null> => {
      // Validate userId
      if (!userId || isNaN(userId) || userId <= 0) {
        console.warn('Invalid userId provided to getUserIQ:', userId);
        return null;
      }

      // Return cached data if available
      if (cache[userId]) {
        return cache[userId];
      }

      // Return pending request if already in progress
      if (userId in pendingRequests.current && pendingRequests.current[userId]) {
        try {
          return await pendingRequests.current[userId];
        } catch (error) {
          console.error(`Error awaiting pending request for user ${userId}:`, error);
          return null;
        }
      }

      // Create new request
      const request = async (): Promise<UserIQData | null> => {
        try {
          const response = await iqScoreAPI.getUserIQ(userId);
          if (response?.success && response.data) {
            // Update cache atomically
            setCache((prev) => ({
              ...prev,
              [userId]: response.data,
            }));
            return response.data;
          }
          return null;
        } catch (error) {
          console.error(`Error fetching IQ score for user ${userId}:`, error);
          // Don't throw, return null to prevent cascading errors
          return null;
        } finally {
          // Clean up pending request with a small delay to handle race conditions
          setTimeout(() => {
            delete pendingRequests.current[userId];
          }, 100);
        }
      };

      // Store and execute request
      try {
        pendingRequests.current[userId] = request();
        return await pendingRequests.current[userId];
      } catch (error) {
        console.error(`Critical error in getUserIQ for user ${userId}:`, error);
        delete pendingRequests.current[userId];
        return null;
      }
    },
    [cache]
  );

  // Batch fetch multiple user IQs
  const batchGetUserIQs = useCallback(
    async (userIds: number[]): Promise<Record<number, UserIQData>> => {
      const result: Record<number, UserIQData> = {};
      const uncachedIds: number[] = [];

      // Separate cached and uncached IDs
      userIds.forEach((userId) => {
        if (cache[userId]) {
          result[userId] = cache[userId];
        } else {
          uncachedIds.push(userId);
        }
      });

      // Fetch uncached IDs in parallel (but limit concurrency)
      if (uncachedIds.length > 0) {
        const BATCH_SIZE = 5; // Limit concurrent requests
        const batches: number[][] = [];

        for (let i = 0; i < uncachedIds.length; i += BATCH_SIZE) {
          batches.push(uncachedIds.slice(i, i + BATCH_SIZE));
        }

        for (const batch of batches) {
          const promises = batch.map((userId) => getUserIQ(userId));
          const results = await Promise.all(promises);

          results.forEach((data, index) => {
            if (data && batch[index] !== undefined) {
              result[batch[index]] = data;
            }
          });
        }
      }

      return result;
    },
    [cache, getUserIQ]
  );

  // Clear cache
  const clearCache = useCallback(() => {
    setCache({});
    pendingRequests.current = {};
  }, []);

  const value: IQScoreContextType = {
    getUserIQ,
    batchGetUserIQs,
    clearCache,
    getCachedUserIQ,
  };

  return <IQScoreContext.Provider value={value}>{children}</IQScoreContext.Provider>;
}

export function useIQScore() {
  const context = useContext(IQScoreContext);
  if (context === undefined) {
    throw new Error('useIQScore must be used within an IQScoreProvider');
  }
  return context;
}
