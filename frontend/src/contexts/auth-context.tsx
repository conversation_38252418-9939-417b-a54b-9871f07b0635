'use client';

import { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { User, checkAuthStatus, loginUser, logoutUser, updateUserProfile } from '@/lib/api';

// Re-export the User type to maintain backward compatibility
export type { User };

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// Constants
const AUTH_STORAGE_KEY = 'tourismiq_auth';
const AUTH_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

// Helper functions for localStorage
const getStoredAuth = () => {
  if (typeof window === 'undefined') return null;
  try {
    const stored = localStorage.getItem(AUTH_STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      // Check if cached data is still valid
      if (parsed.timestamp && Date.now() - parsed.timestamp < AUTH_CACHE_DURATION) {
        return parsed;
      }
    }
  } catch {
    // Ignore errors
  }
  return null;
};

const setStoredAuth = (user: User | null, isAuthenticated: boolean) => {
  if (typeof window === 'undefined') return;
  try {
    if (user && isAuthenticated) {
      localStorage.setItem(
        AUTH_STORAGE_KEY,
        JSON.stringify({
          user,
          isAuthenticated,
          timestamp: Date.now(),
        })
      );
    } else {
      localStorage.removeItem(AUTH_STORAGE_KEY);
    }
  } catch {
    // Ignore errors
  }
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Initialize with SSR-safe defaults
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(true); // Always start with loading true
  const [lastAuthCheck, setLastAuthCheck] = useState<number>(0);
  const [isHydrated, setIsHydrated] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  // Handle localStorage initialization after hydration
  useEffect(() => {
    const storedAuth = getStoredAuth();
    if (storedAuth) {
      setUser(storedAuth.user);
      setIsAuthenticated(storedAuth.isAuthenticated);
      setLastAuthCheck(storedAuth.timestamp);
      // Only set loading to false if we have valid cached data
      setIsLoading(false);
    } else {
      // If no cached data, we'll check auth when hydrated
      setIsLoading(true);
    }
    setIsHydrated(true);
  }, []);

  const checkAuth = useCallback(
    async (forceRefresh = false) => {
      // Skip if we recently checked auth (unless forced)
      const now = Date.now();
      if (!forceRefresh && lastAuthCheck && now - lastAuthCheck < AUTH_CACHE_DURATION) {
        setIsLoading(false); // Set loading to false if we're using cached data
        return;
      }

      // If force refresh, clear the cache first to ensure we get fresh data
      if (forceRefresh && typeof window !== 'undefined') {
        localStorage.removeItem(AUTH_STORAGE_KEY);
        // Also invalidate React Query caches that might contain role data
        queryClient.invalidateQueries({ queryKey: ['newest-members'] });
        queryClient.invalidateQueries({ queryKey: ['members'] });
        // Also invalidate any user-specific IQ score cache that might affect badges
        if (user?.id) {
          queryClient.invalidateQueries({ queryKey: ['iq-score', 'me'] });
          queryClient.invalidateQueries({ queryKey: ['iq-score', 'user', user.id] });
        }
      }

      setIsLoading(true);
      try {
        const authStatusResult = await checkAuthStatus();
        if (authStatusResult.isAuthenticated && authStatusResult.user) {
          setUser(authStatusResult.user);
          setIsAuthenticated(true);
          setStoredAuth(authStatusResult.user, true);
        } else {
          setUser(null);
          setIsAuthenticated(false);
          setStoredAuth(null, false);
        }
        setLastAuthCheck(now);
      } catch {
        setUser(null);
        setIsAuthenticated(false);
        setStoredAuth(null, false);
      } finally {
        setIsLoading(false);
      }
    },
    [lastAuthCheck, queryClient]
  );

  useEffect(() => {
    // Only check auth after hydration
    if (isHydrated) {
      checkAuth();
    }
  }, [isHydrated, checkAuth]);

  const login = async (usernameInput: string, passwordInput: string) => {
    setIsLoading(true);
    try {
      const result = await loginUser(usernameInput, passwordInput);

      if (result.success && result.user) {
        // Set initial user data from login response
        setUser(result.user);
        setIsAuthenticated(true);
        setStoredAuth(result.user, true); // Store auth data in localStorage
        setLastAuthCheck(Date.now()); // Update auth check timestamp

        // Immediately refresh auth to get complete user data with ACF fields
        // This ensures we have the same data structure as when checking auth status
        try {
          await checkAuth(true);
        } catch (error) {
          console.warn('Failed to refresh auth after login:', error);
          // Don't fail the login if this fails
        }

        return { success: true };
      } else {
        setUser(null);
        setIsAuthenticated(false);
        setStoredAuth(null, false); // Clear stored auth
        return { success: false, error: result.error || 'Login failed' };
      }
    } catch (error) {
      setUser(null);
      setIsAuthenticated(false);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      const success = await logoutUser();
      if (success) {
        setUser(null);
        setIsAuthenticated(false);
        setStoredAuth(null, false); // Clear localStorage
        setLastAuthCheck(0); // Reset auth check timestamp
        router.push('/');
      } else {
        // Potentially show an error to the user
        console.error('Logout failed');
      }
    } catch (error) {
      // Potentially show an error to the user
      console.error('Logout error:', error instanceof Error ? error.message : 'Logout failed');
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (data: Partial<User>) => {
    try {
      const result = await updateUserProfile(data);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update profile');
      }

      if (user) {
        const updatedUser = {
          ...user,
          ...data,
          acf: user.acf
            ? {
                ...user.acf,
                job_title: data.jobTitle !== undefined ? data.jobTitle : user.acf?.job_title || '',
                location: data.location !== undefined ? data.location : user.acf?.location || '',
                profile_picture:
                  data.avatarUrl !== undefined ? data.avatarUrl : user.acf?.profile_picture,
                cover_image:
                  data.coverImageUrl !== undefined ? data.coverImageUrl : user.acf?.cover_image,
              }
            : undefined,
        };
        setUser(updatedUser as User);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAuth = useCallback(async () => {
    await checkAuth(true);
  }, [checkAuth]);

  const value = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    updateProfile,
    refreshAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
