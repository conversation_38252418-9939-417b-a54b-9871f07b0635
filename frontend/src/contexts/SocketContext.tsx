'use client';

import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from './auth-context';
import { useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';

interface Notification {
  id: string;
  type: string;
  content: string;
  referenceId?: number;
  referenceType?: string;
  senderId?: number;
  timestamp: number;
  read: boolean;
}

interface SocketContextType {
  socket: Socket | null;
  connected: boolean;
  notifications: Notification[];
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  markAsRead: (id: string) => void;
  sendNotification: (data: {
    recipientId: number;
    type: string;
    content: string;
    referenceId?: number;
    referenceType?: string;
  }) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export function useSocket() {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
}

interface SocketProviderProps {
  children: React.ReactNode;
}

export function SocketProvider({ children }: SocketProviderProps) {
  const { user, isLoading: authLoading } = useAuth();
  const queryClient = useQueryClient();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const isInitialized = useRef(false);

  useEffect(() => {
    // Prevent multiple initializations
    if (authLoading || isInitialized.current) return undefined;

    if (user?.id) {
      isInitialized.current = true;

      // Connect to the socket server using environment variable
      const socketUrl =
        process.env.NODE_ENV === 'production'
          ? process.env.NEXT_PUBLIC_SOCKET_URL || 'https://tourismqwp-production.up.railway.app'
          : process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';

      const newSocket = io(socketUrl, {
        transports: ['websocket', 'polling'],
        autoConnect: true,
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });

      const handleAddNotification = (notification: Notification) => {
        setNotifications((prev) => [notification, ...prev.slice(0, 49)]); // Keep only last 50 notifications
      };

      newSocket.on('connect', () => {
        setConnected(true);

        // Authenticate with the socket server
        newSocket.emit('authenticate', { userId: user.id });
      });

      newSocket.on('authenticated', (response) => {
        if (response.success) {
        } else {
          console.error('Socket authentication failed:', response.message);
        }
      });

      newSocket.on('disconnect', () => {
        setConnected(false);
      });

      newSocket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
      });

      // Listen for incoming notifications
      newSocket.on('notification', (notification: Notification) => {
        handleAddNotification(notification);

        // Show browser notification for connection-related events
        if (notification.type.startsWith('connection_') && 'Notification' in window) {
          Notification.requestPermission().then((permission) => {
            if (permission === 'granted') {
              new Notification('TourismIQ', {
                body: notification.content,
                icon: '/icon-192x192.png',
              });
            }
          });
        }

        // When we receive a connection_declined or connection_removed notification,
        // invalidate our connection queries. This updates the UI to show we can send a new request
        if (
          (notification.type === 'connection_declined' ||
            notification.type === 'connection_removed') &&
          notification.referenceId
        ) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.connections.status(notification.referenceId),
          });

          // Also invalidate bulk connections
          queryClient.invalidateQueries({
            queryKey: ['bulk-connections'],
            predicate: (query) => {
              const key = query.queryKey;
              if (Array.isArray(key) && key[0] === 'bulk-connections' && Array.isArray(key[1])) {
                return key[1].includes(notification.referenceId);
              }
              return false;
            },
          });

          // Also invalidate our connection list if it's a removal
          if (notification.type === 'connection_removed') {
            queryClient.invalidateQueries({
              queryKey: queryKeys.connections.all,
            });
          }
        }
      });

      // Listen for connection status changes
      newSocket.on('connection_status_changed', (data: { userId: number; type: string }) => {
        // When a connection status changes (accepted, declined, removed),
        // invalidate the relevant queries
        if (data.userId) {
          // Invalidate individual connection status
          queryClient.invalidateQueries({
            queryKey: queryKeys.connections.status(data.userId),
          });

          // Invalidate bulk connections queries that might contain this user
          queryClient.invalidateQueries({
            queryKey: ['bulk-connections'],
            predicate: (query) => {
              const key = query.queryKey;
              if (Array.isArray(key) && key[0] === 'bulk-connections' && Array.isArray(key[1])) {
                return key[1].includes(data.userId);
              }
              return false;
            },
          });

          // If it's a decline event, also invalidate pending connections
          if (data.type === 'connection_declined') {
            queryClient.invalidateQueries({
              queryKey: queryKeys.connections.pending(),
            });
          }
        }
      });

      setSocket(newSocket);

      // Cleanup function
      return () => {
        isInitialized.current = false;
        newSocket.disconnect();
        setSocket(null);
        setConnected(false);
      };
    } else if (!user?.id && socket) {
      // User logged out, clean up
      isInitialized.current = false;
      socket.disconnect();
      setSocket(null);
      setConnected(false);
      setNotifications([]);
      return undefined;
    }

    return undefined;
    // ESLint disable is intentional to prevent infinite loop
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id, authLoading]);

  const addNotification = (notification: Notification) => {
    setNotifications((prev) => [notification, ...prev.slice(0, 49)]); // Keep only last 50 notifications
  };

  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id));
  };

  const clearNotifications = () => {
    setNotifications([]);
  };

  const markAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notif) => (notif.id === id ? { ...notif, read: true } : notif))
    );
  };

  const sendNotification = (data: {
    recipientId: number;
    type: string;
    content: string;
    referenceId?: number;
    referenceType?: string;
  }) => {
    if (socket && connected) {
      socket.emit('send_notification', {
        ...data,
        senderId: user?.id,
      });
    }
  };

  const contextValue: SocketContextType = {
    socket,
    connected,
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    markAsRead,
    sendNotification,
  };

  return <SocketContext.Provider value={contextValue}>{children}</SocketContext.Provider>;
}
