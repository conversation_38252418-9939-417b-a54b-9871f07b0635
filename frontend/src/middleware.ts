import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Force HTTPS redirect (except for localhost development)
  const isLocalhost = request.nextUrl.hostname === 'localhost' ||
                     request.nextUrl.hostname === '127.0.0.1' ||
                     request.nextUrl.hostname.endsWith('.local');

  if (!isLocalhost && request.nextUrl.protocol === 'http:') {
    const httpsUrl = new URL(request.url);
    httpsUrl.protocol = 'https:';
    return NextResponse.redirect(httpsUrl, 301); // Permanent redirect
  }

  // Check for WordPress authentication cookies
  const wordpressLoggedInCookie = Array.from(request.cookies.getAll()).find((cookie) =>
    cookie.name.startsWith('wordpress_logged_in_')
  );

  // Check if user is authenticated by WordPress cookie
  const isAuthenticated = !!wordpressLoggedInCookie;

  const { pathname } = request.nextUrl;

  // Define protected paths that require authentication
  // NOTE: We're using exact path for /profile to only protect the user's own profile page
  const exactProtectedPaths = ['/profile']; // Exact matches
  const protectedPathPrefixes = ['/settings', '/dashboard']; // Prefix matches

  // Check if accessing the exact /profile path (user's own profile)
  const isExactProtectedPath = exactProtectedPaths.includes(pathname);

  // Check if accessing other protected paths that use prefix matching
  const isPrefixProtectedPath = protectedPathPrefixes.some((path) => pathname.startsWith(path));

  // Combined check for any protected path
  const isAccessingProtectedPath = isExactProtectedPath || isPrefixProtectedPath;

  // Only redirect if trying to access a protected path AND not authenticated
  if (isAccessingProtectedPath && !isAuthenticated) {
    // If not authenticated and accessing a protected path, redirect to login
    const loginUrl = new URL('/login', request.url);
    // Add a callback URL to return after login
    loginUrl.searchParams.set('callbackUrl', pathname + request.nextUrl.search);
    return NextResponse.redirect(loginUrl);
  }

  return NextResponse.next();
}

// Define which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public assets (e.g., /images, /fonts)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images|fonts|svg|manifest.json|robots.txt|sw.js|workbox-.*\\.js).*)',
  ],
};

// Example placeholder for token validation function (implement as needed)
// async function validateToken(token: string): Promise<boolean> {
//   try {
//     const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_API_URL;
//     if (!WORDPRESS_API_URL) return false;
//
//     const response = await fetch(`${WORDPRESS_API_URL}/wp-json/jwt-auth/v1/token/validate`, {
//       method: 'POST',
//       headers: {
//         'Authorization': `Bearer ${token}`,
//       },
//     });
//     return response.ok; // Or check for specific data in response.json()
//   } catch (error) {
//     console.error('Token validation error:', error);
//     return false;
//   }
// }
