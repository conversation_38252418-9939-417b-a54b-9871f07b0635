/*********************************************
# frontend/src/hooks/useWindowSize.ts
# 01/27/2025 10:40pm Created file - Hook for responsive sidebar accordion behavior
# 01/27/2025 10:55pm Updated to detect actual sidebar overflow instead of fixed height
# 01/27/2025 11:00pm Simplified to clean height-based trigger to fix glitching
# 01/27/2025 11:05pm Removed height detection - default to accordion mode always
# 02/07/2025 4:00pm Implemented simple height detection for smart accordion mode
**********************************************/

'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface WindowSize {
  width: number;
  height: number;
  shouldUseAccordion: boolean;
}

export function useWindowSize(): WindowSize {
  const [windowSize, setWindowSize] = useState<WindowSize>({
    width: 0,
    height: 0,
    shouldUseAccordion: true, // Start conservative to prevent flash
  });

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const checkHeight = useCallback(() => {
    const viewportHeight = window.innerHeight;

    // Calculate required height for all three boxes to be open
    // Header height: 90px (sticky header)
    // Right sidebar padding: 16px top + 16px bottom = 32px
    // Three card headers with titles: 3 * 60px = 180px
    // Estimated content heights when all open:
    // - My Connections: ~240px (3 connections at ~60px each + padding)
    // - Suggested Connections: ~240px (3 suggestions at ~60px each + padding)
    // - Upcoming Events: ~240px (3 events at ~60px each + padding)
    // CTA buttons: 3 * 60px = 180px (button + padding)
    // Card spacing: 3 * 12px = 36px (space between cards)
    // Buffer for scroll prevention: 100px (increased for safety)
    const REQUIRED_HEIGHT = 90 + 32 + 180 + 720 + 180 + 36 + 100; // ~1338px

    const shouldUseAccordion = viewportHeight < REQUIRED_HEIGHT;

    return {
      width: window.innerWidth,
      height: viewportHeight,
      shouldUseAccordion,
    };
  }, []);

  useEffect(() => {
    function handleResize() {
      // Clear any pending timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Reduced debounce for faster response to prevent scrollbar flash
      timeoutRef.current = setTimeout(() => {
        setWindowSize(checkHeight());
      }, 50); // Reduced from 150ms to 50ms
    }

    // Immediate initial call to prevent flash
    setWindowSize(checkHeight());

    // Also do a second check after a brief delay to catch any layout shifts
    const immediateCheck = setTimeout(() => {
      setWindowSize(checkHeight());
    }, 16); // Next animation frame

    // Add event listener with passive for better performance
    window.addEventListener('resize', handleResize, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(immediateCheck);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [checkHeight]);

  return windowSize;
}
