// Re-export hooks from the new React Query implementation
export {
  useConnections,
  useConnectionStatus,
  useConnectionsList,
  usePendingRequests,
  useSendConnectionRequest,
  useAcceptConnectionRequest,
  useDeclineConnectionRequest,
  useRemoveConnection,
} from './queries/useConnections';

// Keep legacy interfaces for backward compatibility
export type {
  ConnectionStatus,
  ConnectionRequest,
  PendingRequests,
  Connection,
} from './queries/useConnections';
