// Re-export hooks from the new React Query implementation
export {
  usePosts,
  usePostsByCategory,
  usePost,
  useUserPosts,
  useEventPosts,
} from './queries/usePosts';

// Keep legacy interfaces for backward compatibility
export interface Post {
  id: number;
  title: {
    rendered: string;
  };
  excerpt: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  date: string;
  slug: string;
  link: string;
  status: string;
  total_comments?: number;
  upvotes?: {
    count: number;
    upvoted: boolean;
  };
  meta?: {
    likes?: number;
    _upvotes?: number;
    total_comments?: number;
    [key: string]: unknown;
  };
  _embedded?: {
    author?: Array<{
      id: number;
      name: string;
      avatar_urls?: {
        [key: string]: string;
      };
      [key: string]: unknown;
    }>;
    'wp:term'?: Array<
      Array<{
        id: number;
        name: string;
        slug: string;
        [key: string]: unknown;
      }>
    >;
    'wp:featuredmedia'?: Array<{
      id: number;
      source_url: string;
      alt_text: string;
      media_details?: {
        width: number;
        height: number;
        sizes?: {
          [key: string]: {
            source_url: string;
            width: number;
            height: number;
          };
        };
      };
    }>;
  };
  [key: string]: unknown;
}

export interface UserPostsResponse {
  posts: Post[];
  pagination: {
    page: number;
    per_page: number;
    total_pages: number;
    total: number;
  };
}
