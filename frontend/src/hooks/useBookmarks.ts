import { useState, useEffect, useCallback } from 'react';
import { bookmarksAPI } from '@/lib/api/bookmarks';
import { useAuthStatus } from './useAuthStatus';

interface BookmarkState {
  [postId: number]: boolean;
}

export function useBookmarks() {
  const [bookmarks, setBookmarks] = useState<BookmarkState>({});
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuthStatus();

  // Initialize bookmarks from localStorage
  useEffect(() => {
    const loadBookmarks = () => {
      setIsLoading(true);

      if (typeof window === 'undefined') {
        setIsLoading(false);
        return;
      }

      try {
        const storageKey = bookmarksAPI.getStorageKey(user?.id);
        const stored = localStorage.getItem(storageKey);
        const bookmarks = stored ? JSON.parse(stored) : {};
        setBookmarks(bookmarks);
      } catch (error) {
        console.error('Error loading bookmarks from localStorage:', error);
        setBookmarks({});
      } finally {
        setIsLoading(false);
      }
    };

    loadBookmarks();
  }, [user?.id]);

  // Toggle bookmark function
  const toggleBookmark = useCallback(
    (postId: number) => {
      const newState = bookmarksAPI.toggleBookmark(postId, user?.id);

      // Update local state
      setBookmarks((prev) => {
        const newBookmarks = { ...prev };
        if (newState) {
          newBookmarks[postId] = true;
        } else {
          delete newBookmarks[postId];
        }
        return newBookmarks;
      });

      return newState;
    },
    [user?.id]
  );

  // Check if a post is bookmarked
  const isBookmarked = useCallback(
    (postId: number) => {
      return bookmarks[postId] === true;
    },
    [bookmarks]
  );

  // Get all bookmarked post IDs
  const getBookmarkedPostIds = useCallback(() => {
    return Object.keys(bookmarks)
      .filter((postId) => bookmarks[parseInt(postId)] === true)
      .map((postId) => parseInt(postId));
  }, [bookmarks]);

  return {
    isBookmarked,
    toggleBookmark,
    getBookmarkedPostIds,
    isLoading,
  };
}
