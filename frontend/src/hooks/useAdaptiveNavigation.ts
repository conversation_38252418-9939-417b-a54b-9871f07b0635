/*********************************************
# frontend/src/hooks/useAdaptiveNavigation.ts
# 01/27/2025 6:30pm Created file - Adaptive navigation hook for height-based collapsing
# 01/27/2025 6:45pm Fixed infinite re-render issue by moving collapseOrder outside component
# 01/27/2025 6:50pm Temporarily disabled adaptive behavior to prevent infinite re-renders
# 01/27/2025 6:55pm Implemented robust collision detection using IntersectionObserver
# 01/27/2025 7:00pm Reactivated with direct position monitoring and ResizeObserver
**********************************************/

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';

export interface NavItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: NavItem[];
  isFeedFilter?: boolean;
}

export interface AdaptiveNavState {
  visibleItems: NavItem[];
  collapsedItems: NavItem[];
  showMoreButton: boolean;
  isCollapsing: boolean;
}

// Priority order for collapsing (reverse order - last items collapse first)
// Moved outside component to prevent recreating on every render
const COLLAPSE_ORDER = [
  'Events',
  'Community Q&A',
  'Podcasts',
  'Thought Leadership',
  'News',
  'Resources', // This will collapse with its dropdown intact
  'Vendor Directory',
  'RFPs',
  'People on the Move',
  'Jobs Hub',
  // 'Home' - never collapses
];

export function useAdaptiveNavigation(navItems: NavItem[]) {
  const [isColliding, setIsColliding] = useState(false);
  const [availableHeight, setAvailableHeight] = useState(0);

  const navContainerRef = useRef<HTMLDivElement>(null);
  const newsletterRef = useRef<HTMLDivElement>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Memoize the sorted items by collapse priority to prevent recalculation
  const sortedNavItems = useMemo(() => {
    return [...navItems].sort((a, b) => {
      const aIndex = COLLAPSE_ORDER.indexOf(a.label);
      const bIndex = COLLAPSE_ORDER.indexOf(b.label);

      // Items not in collapse order stay visible (like Home)
      if (aIndex === -1 && bIndex === -1) return 0;
      if (aIndex === -1) return -1; // a stays visible
      if (bIndex === -1) return 1; // b stays visible

      return bIndex - aIndex; // Higher index = collapse first
    });
  }, [navItems]);

  // Calculate visible/collapsed items based on available height
  const adaptiveState = useMemo(() => {
    if (!isColliding || availableHeight === 0) {
      return {
        visibleItems: navItems,
        collapsedItems: [],
        showMoreButton: false,
        isCollapsing: false,
      };
    }

    // Each nav item is approximately 52px + 12px gap = 64px
    const itemHeight = 64;
    const maxVisibleItems = Math.floor(availableHeight / itemHeight);

    // Always show at least Home (first item)
    const minVisibleItems = 1;
    const actualVisibleItems = Math.max(minVisibleItems, maxVisibleItems);

    // If we can show all items, no need to collapse
    if (actualVisibleItems >= navItems.length) {
      return {
        visibleItems: navItems,
        collapsedItems: [],
        showMoreButton: false,
        isCollapsing: false,
      };
    }

    // Reserve space for "More" button if needed
    const visibleItemsWithMore = Math.max(minVisibleItems, actualVisibleItems - 1);

    const visibleItems = sortedNavItems.slice(0, visibleItemsWithMore);
    const collapsedItems = sortedNavItems.slice(visibleItemsWithMore);

    return {
      visibleItems,
      collapsedItems,
      showMoreButton: collapsedItems.length > 0,
      isCollapsing: collapsedItems.length > 0,
    };
  }, [isColliding, availableHeight, navItems, sortedNavItems]);

  // Manual recalculation function for external use
  const recalculate = useCallback(() => {
    if (!navContainerRef.current || !newsletterRef.current) return;

    const navRect = navContainerRef.current.getBoundingClientRect();
    const newsletterRect = newsletterRef.current.getBoundingClientRect();

    const gap = newsletterRect.top - navRect.bottom;
    const isColliding = gap < 100;

    setIsColliding(isColliding);

    if (isColliding) {
      const availableSpace = Math.max(0, newsletterRect.top - navRect.top - 50);
      setAvailableHeight(availableSpace);
    }
  }, []);

  // Set up collision detection using direct position monitoring
  useEffect(() => {
    if (!navContainerRef.current || !newsletterRef.current) return undefined;

    const checkCollision = () => {
      const navContainer = navContainerRef.current;
      const newsletter = newsletterRef.current;

      if (!navContainer || !newsletter) return;

      const navRect = navContainer.getBoundingClientRect();
      const newsletterRect = newsletter.getBoundingClientRect();

      // Calculate if navigation is getting close to newsletter
      const gap = newsletterRect.top - navRect.bottom;
      const isColliding = gap < 100; // Start collapsing when less than 100px gap

      setIsColliding(isColliding);

      if (isColliding) {
        const availableSpace = Math.max(0, newsletterRect.top - navRect.top - 50); // Leave 50px buffer
        setAvailableHeight(availableSpace);
      }
    };

    // Initial check
    checkCollision();

    // Set up ResizeObserver to monitor changes
    const resizeObserver = new ResizeObserver(() => {
      // Debounce the check
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      debounceTimeoutRef.current = setTimeout(checkCollision, 100);
    });

    // Observe both elements
    resizeObserver.observe(navContainerRef.current);
    resizeObserver.observe(newsletterRef.current);

    // Also check on scroll (in case newsletter moves)
    const handleScroll = () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      debounceTimeoutRef.current = setTimeout(checkCollision, 50);
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('scroll', handleScroll);
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      recalculate();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [recalculate]);

  return {
    ...adaptiveState,
    navContainerRef,
    newsletterRef,
    recalculate,
  };
}
