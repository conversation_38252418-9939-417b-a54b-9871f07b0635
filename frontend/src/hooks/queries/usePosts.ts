import { useQuery } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';
import { getFeaturedPosts, getPostsByCategory, getEventPosts } from '@/lib/api/posts';
import type { WPPost } from '@/lib/api/wordpress';

interface UsePostsOptions {
  page?: number;
  perPage?: number;
  category?: string;
  author?: number;
  enabled?: boolean;
}

interface UsePostsReturn {
  posts: WPPost[];
  isLoading: boolean;
  error: Error | null;
  isRefetching: boolean;
  refetch: () => void;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
}

/**
 * React Query hook for fetching posts with automatic caching and background updates
 *
 * Features:
 * - Automatic background refetching
 * - Smart caching with 5-minute stale time
 * - Loading and error states
 * - Request deduplication
 * - Optimistic updates support
 */
export function usePosts(options: UsePostsOptions = {}): UsePostsReturn {
  const { page = 1, perPage = 20, category, enabled = true } = options;

  const { data, isLoading, error, isRefetching, refetch } = useQuery({
    queryKey: queryKeys.posts.list({ page, perPage, category: category || undefined }),
    queryFn: () => {
      if (category) {
        return getPostsByCategory(category, perPage, page);
      } else {
        return getFeaturedPosts(page, perPage, 'date', undefined, false);
      }
    },
    enabled,
    staleTime: 30 * 1000, // 30 seconds - much shorter for faster updates
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true, // Refetch when user returns to tab
  });

  return {
    posts: data?.posts || [],
    isLoading,
    error: error as Error | null,
    isRefetching,
    refetch,
    hasNextPage: data ? page < data.totalPages : false,
    isFetchingNextPage: false, // For now, we'll add infinite queries later
  };
}

/**
 * Hook for fetching posts by category with React Query
 */
export function usePostsByCategory(categorySlug: string, page: number = 1, perPage: number = 20) {
  return useQuery({
    queryKey: queryKeys.posts.byCategory(categorySlug, page),
    queryFn: async () => {
      const response = await fetch(
        `/api/wp-proxy/posts/by-category/${encodeURIComponent(categorySlug)}?per_page=${perPage}&page=${page}&_embed=1&orderby=date&order=desc`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch posts for category '${categorySlug}': ${response.status}`);
      }

      const posts = await response.json();

      // Get pagination info from headers
      const totalPages = parseInt(response.headers.get('X-WP-TotalPages') || '0', 10);
      const totalPosts = parseInt(response.headers.get('X-WP-Total') || '0', 10);

      return {
        posts: Array.isArray(posts) ? posts : [],
        totalPages,
        totalPosts,
      };
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000,
  });
}

/**
 * Hook for fetching a single post by ID
 */
export function usePost(postId: number, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.posts.detail(postId),
    queryFn: async () => {
      const response = await fetch(`/api/wp-proxy/posts/${postId}?_embed=1`, {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch post ${postId}: ${response.status}`);
      }

      return response.json();
    },
    enabled: enabled && !!postId,
    staleTime: 30 * 1000, // 30 seconds for consistency
    gcTime: 5 * 60 * 1000,
  });
}

/**
 * Hook for fetching user's posts with pagination support
 * Matches the original interface: { posts, loading, error, pagination, refetch }
 */
export function useUserPosts(userId: number, page: number = 1, perPage: number = 10) {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: queryKeys.posts.userPosts(userId, page, perPage),
    queryFn: async () => {
      const response = await fetch(
        `/api/wp-proxy/posts/user/${userId}?page=${page}&per_page=${perPage}`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch posts');
      }

      return response.json();
    },
    enabled: !!userId,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000,
  });

  // Return the same interface as the original hook for backward compatibility
  return {
    posts: data?.posts || [],
    loading: isLoading,
    error: error?.message || null,
    pagination: data?.pagination || {
      page: 1,
      per_page: 10,
      total_pages: 1,
      total: 0,
    },
    refetch,
  };
}

/**
 * Hook for fetching event posts
 */
export function useEventPosts(limit: number = 3) {
  return useQuery({
    queryKey: queryKeys.posts.byCategory('event', undefined),
    queryFn: () => getEventPosts(limit),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000,
  });
}
