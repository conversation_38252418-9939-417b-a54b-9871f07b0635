/*********************************************
# frontend/src/hooks/queries/useConnections.ts
# 01/27/2025 9:20pm Increased connection status cache time to 10min, disabled window focus refetch
**********************************************/

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';
import { useCurrentUser } from './useAuth';
import { useAuth } from '@/contexts/auth-context';

export interface ConnectionStatus {
  status: 'none' | 'pending' | 'accepted';
  can_request?: boolean;
  can_accept?: boolean;
  can_decline?: boolean;
  can_remove?: boolean;
  pending_type?: 'sent' | 'received';
  message?: string;
}

export interface ConnectionRequest {
  user_id: number;
  username: string;
  display_name: string;
  avatar: string;
  sent_at?: string;
  received_at?: string;
}

export interface PendingRequests {
  sent: ConnectionRequest[];
  received: ConnectionRequest[];
}

export interface Connection {
  id?: number;
  user_id: number;
  username: string;
  display_name: string;
  name?: string;
  avatar: string;
  profile_picture?:
    | string
    | {
        ID: number;
        id: number;
        title: string;
        url: string;
        [key: string]: unknown;
      };
  bio?: string;
  location?: string;
  job_title?: string;
  company?: string;
  connected_at: string;
  slug?: string;
}

/**
 * Hook for managing connection status with a specific user
 */
export function useConnectionStatus(userId: number) {
  // Use auth context for immediate auth state updates
  const { isAuthenticated: authContextAuthenticated } = useAuth();
  // Also get from React Query for consistency with other data
  const { isAuthenticated } = useCurrentUser();

  // Use auth context state as primary, fallback to React Query state
  const isUserAuthenticated = authContextAuthenticated ?? isAuthenticated;

  const {
    data: status,
    isLoading: loading,
    refetch: refreshStatus,
  } = useQuery({
    queryKey: queryKeys.connections.status(userId),
    queryFn: async (): Promise<ConnectionStatus | null> => {
      const response = await fetch(`/api/wp-proxy/connections/status/${userId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to get connection status');
      }

      return response.json();
    },
    enabled: isUserAuthenticated && !!userId,
    staleTime: 0,
    gcTime: 5 * 60 * 1000,
  });

  return {
    status: status || null,
    loading,
    refreshStatus,
  };
}

/**
 * Hook for fetching user's connections list
 */
export function useConnectionsList(userId?: number) {
  // Use auth context for immediate auth state updates
  const { isAuthenticated: authContextAuthenticated } = useAuth();
  // Also get from React Query for consistency with other data
  const { isAuthenticated } = useCurrentUser();

  // Use auth context state as primary, fallback to React Query state
  const isUserAuthenticated = authContextAuthenticated ?? isAuthenticated;

  const {
    data: connections,
    isLoading: loading,
    error,
    refetch: refreshConnections,
  } = useQuery({
    queryKey: queryKeys.connections.list(userId),
    queryFn: async (): Promise<Connection[]> => {
      const url = userId
        ? `/api/wp-proxy/connections?userId=${userId}`
        : '/api/wp-proxy/connections';

      const response = await fetch(url, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to get connections');
      }

      const data = await response.json();

      // Handle different response formats
      if (Array.isArray(data)) {
        return data;
      } else if (data && Array.isArray(data.connections)) {
        return data.connections;
      } else if (data && data.data && Array.isArray(data.data)) {
        return data.data;
      } else {
        console.warn('Unexpected connections response format:', data);
        return [];
      }
    },
    enabled: isUserAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000,
    // Disable window focus refetch to reduce API calls
    refetchOnWindowFocus: false,
  });

  return {
    connections: connections || [],
    loading,
    error: error?.message || null,
    refreshConnections,
  };
}

/**
 * Hook for fetching pending connection requests
 */
export function usePendingRequests() {
  // Use auth context for immediate auth state updates
  const { isAuthenticated: authContextAuthenticated } = useAuth();
  // Also get from React Query for consistency with other data
  const { isAuthenticated } = useCurrentUser();

  // Use auth context state as primary, fallback to React Query state
  const isUserAuthenticated = authContextAuthenticated ?? isAuthenticated;

  return useQuery({
    queryKey: queryKeys.connections.pending(),
    queryFn: async (): Promise<PendingRequests> => {
      const response = await fetch('/api/wp-proxy/connections/pending', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to get pending requests');
      }

      return response.json();
    },
    enabled: isUserAuthenticated,
    staleTime: 2 * 60 * 1000, // 2 minutes for pending requests
    gcTime: 5 * 60 * 1000,
  });
}

/**
 * Mutation for sending connection requests
 */
export function useSendConnectionRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: number): Promise<boolean> => {
      const response = await fetch('/api/wp-proxy/connections/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send connection request');
      }

      return true;
    },
    onSuccess: (_, userId) => {
      // Invalidate connection status for this user
      queryClient.invalidateQueries({ queryKey: queryKeys.connections.status(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.connections.pending() });
      // Invalidate bulk connections queries that might contain this user
      queryClient.invalidateQueries({
        queryKey: ['bulk-connections'],
        predicate: (query) => {
          const key = query.queryKey;
          if (Array.isArray(key) && key[0] === 'bulk-connections' && Array.isArray(key[1])) {
            return key[1].includes(userId);
          }
          return false;
        },
      });
    },
  });
}

/**
 * Mutation for accepting connection requests
 */
export function useAcceptConnectionRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: number): Promise<boolean> => {
      const response = await fetch('/api/wp-proxy/connections/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to accept connection request');
      }

      return true;
    },
    onSuccess: (_, userId) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.connections.status(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.connections.pending() });
      queryClient.invalidateQueries({ queryKey: queryKeys.connections.all });
      // Invalidate bulk connections queries that might contain this user
      queryClient.invalidateQueries({
        queryKey: ['bulk-connections'],
        predicate: (query) => {
          const key = query.queryKey;
          if (Array.isArray(key) && key[0] === 'bulk-connections' && Array.isArray(key[1])) {
            return key[1].includes(userId);
          }
          return false;
        },
      });
    },
  });
}

/**
 * Mutation for declining connection requests
 */
export function useDeclineConnectionRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: number): Promise<boolean> => {
      const response = await fetch('/api/wp-proxy/connections/decline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to decline connection request');
      }

      return true;
    },
    onSuccess: (_, userId) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.connections.status(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.connections.pending() });
      // Invalidate bulk connections queries that might contain this user
      queryClient.invalidateQueries({
        queryKey: ['bulk-connections'],
        predicate: (query) => {
          const key = query.queryKey;
          if (Array.isArray(key) && key[0] === 'bulk-connections' && Array.isArray(key[1])) {
            return key[1].includes(userId);
          }
          return false;
        },
      });
    },
  });
}

/**
 * Mutation for removing connections
 */
export function useRemoveConnection() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: number): Promise<boolean> => {
      const response = await fetch('/api/wp-proxy/connections/remove', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove connection');
      }

      return true;
    },
    onSuccess: (_, userId) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.connections.status(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.connections.all });
      // Invalidate bulk connections queries that might contain this user
      queryClient.invalidateQueries({
        queryKey: ['bulk-connections'],
        predicate: (query) => {
          const key = query.queryKey;
          if (Array.isArray(key) && key[0] === 'bulk-connections' && Array.isArray(key[1])) {
            return key[1].includes(userId);
          }
          return false;
        },
      });
    },
  });
}

/**
 * Main connections hook that provides all functionality
 * Maintains backward compatibility with the original interface
 */
export function useConnections() {
  const sendConnectionRequestMutation = useSendConnectionRequest();
  const acceptConnectionRequestMutation = useAcceptConnectionRequest();
  const declineConnectionRequestMutation = useDeclineConnectionRequest();
  const removeConnectionMutation = useRemoveConnection();

  // Helper functions that maintain the original async function signatures
  const sendConnectionRequest = async (userId: number): Promise<boolean> => {
    try {
      await sendConnectionRequestMutation.mutateAsync(userId);
      return true;
    } catch (error) {
      console.error('Failed to send connection request:', error);
      return false;
    }
  };

  const acceptConnectionRequest = async (userId: number): Promise<boolean> => {
    try {
      await acceptConnectionRequestMutation.mutateAsync(userId);
      return true;
    } catch (error) {
      console.error('Failed to accept connection request:', error);
      return false;
    }
  };

  const declineConnectionRequest = async (userId: number): Promise<boolean> => {
    try {
      await declineConnectionRequestMutation.mutateAsync(userId);
      return true;
    } catch (error) {
      console.error('Failed to decline connection request:', error);
      return false;
    }
  };

  const removeConnection = async (userId: number): Promise<boolean> => {
    try {
      await removeConnectionMutation.mutateAsync(userId);
      return true;
    } catch (error) {
      console.error('Failed to remove connection:', error);
      return false;
    }
  };

  const getConnectionStatus = async (userId: number): Promise<ConnectionStatus | null> => {
    try {
      const response = await fetch(`/api/wp-proxy/connections/status/${userId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to get connection status');
      }

      return await response.json();
    } catch (err) {
      console.error('Failed to get connection status:', err);
      return null;
    }
  };

  const getPendingRequests = async (): Promise<PendingRequests | null> => {
    try {
      const response = await fetch('/api/wp-proxy/connections/pending', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to get pending requests');
      }

      return await response.json();
    } catch (err) {
      console.error('Failed to get pending requests:', err);
      return null;
    }
  };

  const getConnections = async (userId?: number): Promise<Connection[] | null> => {
    try {
      const url = userId
        ? `/api/wp-proxy/connections?userId=${userId}`
        : '/api/wp-proxy/connections';

      const response = await fetch(url, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to get connections');
      }

      const data = await response.json();

      // Handle different response formats
      if (Array.isArray(data)) {
        return data;
      } else if (data && Array.isArray(data.connections)) {
        return data.connections;
      } else if (data && data.data && Array.isArray(data.data)) {
        return data.data;
      } else {
        console.warn('Unexpected connections response format:', data);
        return [];
      }
    } catch (err) {
      console.error('Failed to get connections:', err);
      return null;
    }
  };

  return {
    loading:
      sendConnectionRequestMutation.isPending ||
      acceptConnectionRequestMutation.isPending ||
      declineConnectionRequestMutation.isPending ||
      removeConnectionMutation.isPending,
    error:
      sendConnectionRequestMutation.error?.message ||
      acceptConnectionRequestMutation.error?.message ||
      declineConnectionRequestMutation.error?.message ||
      removeConnectionMutation.error?.message ||
      null,
    sendConnectionRequest,
    acceptConnectionRequest,
    declineConnectionRequest,
    removeConnection,
    getConnectionStatus,
    getPendingRequests,
    getConnections,
    clearError: () => {
      sendConnectionRequestMutation.reset();
      acceptConnectionRequestMutation.reset();
      declineConnectionRequestMutation.reset();
      removeConnectionMutation.reset();
    },
  };
}
