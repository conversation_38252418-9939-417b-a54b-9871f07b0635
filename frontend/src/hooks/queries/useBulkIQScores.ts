import { useQuery } from '@tanstack/react-query';

interface IQScoreData {
  score: number;
  rank: string;
  rank_data: {
    name: string;
    min_score: number;
    max_score?: number;
    color: string;
    icon: string;
  };
  total_activities: number;
  activities: Array<{
    activity_type: string;
    points: number;
    created_at: string;
  }>;
}

interface BulkIQScoresResponse {
  scores: Record<number, IQScoreData>;
}

async function fetchBulkIQScores(userIds: number[]): Promise<BulkIQScoresResponse> {
  const response = await fetch('/api/wp-proxy/iq-score/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({ userIds }),
  });

  if (!response.ok) {
    throw new Error('Failed to fetch bulk IQ scores');
  }

  return response.json();
}

export function useBulkIQScores(userIds: number[]) {
  return useQuery({
    queryKey: ['bulk-iq-scores', userIds.sort()],
    queryFn: () => fetchBulkIQScores(userIds),
    enabled: userIds.length > 0,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: false,
  });
}
