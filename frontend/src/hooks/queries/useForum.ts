import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';
import { Question } from '@/types/forum';
import { transformWPPostToQuestion } from '@/lib/api/forum';

interface UserForumQuestionsResponse {
  questions: unknown[];
  pagination: {
    page: number;
    per_page: number;
    total_pages: number;
    total: number;
  };
}

interface ForumQuestionsResponse {
  questions: unknown[];
  pagination: {
    page: number;
    per_page: number;
    total_pages: number;
    total: number;
  };
}

/**
 * Hook for fetching user's forum questions with pagination
 * Maintains backward compatibility with the original interface
 */
export function useUserForumQuestions(userId: number, page: number = 1, perPage: number = 10) {
  const {
    data,
    isLoading: loading,
    error,
    refetch: fetchQuestions,
  } = useQuery({
    queryKey: queryKeys.forum.userQuestions(userId),
    queryFn: async (): Promise<UserForumQuestionsResponse> => {
      const response = await fetch(
        `/api/wp-proxy/forum/user/${userId}?page=${page}&per_page=${perPage}`,
        {
          credentials: 'include',
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch forum questions');
      }

      return response.json();
    },
    enabled: !!userId,
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000,
  });

  // Transform WordPress questions to our Question format
  const transformedQuestions = (data?.questions || []).map(transformWPPostToQuestion);

  return {
    questions: transformedQuestions,
    loading,
    error: error?.message || null,
    pagination: data?.pagination || {
      page: 1,
      per_page: 10,
      total_pages: 1,
      total: 0,
    },
    refetch: fetchQuestions,
  };
}

/**
 * Hook for fetching all forum questions with pagination
 */
export function useForumQuestions(page: number = 1, perPage: number = 20) {
  const {
    data,
    isLoading: loading,
    error,
    refetch,
  } = useQuery({
    queryKey: queryKeys.forum.questions({ page, perPage }),
    queryFn: async (): Promise<ForumQuestionsResponse> => {
      const response = await fetch(`/api/wp-proxy/forum?page=${page}&per_page=${perPage}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch forum questions');
      }

      return response.json();
    },
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000,
  });

  // Transform WordPress questions to our Question format
  const transformedQuestions = (data?.questions || []).map(transformWPPostToQuestion);

  return {
    questions: transformedQuestions,
    loading,
    error: error?.message || null,
    pagination: data?.pagination || {
      page: 1,
      per_page: 20,
      total_pages: 1,
      total: 0,
    },
    refetch,
  };
}

/**
 * Hook for fetching a single forum question by ID
 */
export function useForumQuestion(questionId: number, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.forum.question(questionId),
    queryFn: async (): Promise<Question> => {
      const response = await fetch(`/api/wp-proxy/forum/${questionId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch forum question');
      }

      const data = await response.json();
      return transformWPPostToQuestion(data);
    },
    enabled: enabled && !!questionId,
    staleTime: 5 * 60 * 1000, // 5 minutes for individual questions
    gcTime: 15 * 60 * 1000,
  });
}

/**
 * Hook for fetching comments for a forum question
 */
export function useForumQuestionComments(questionId: number, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.forum.comments(questionId),
    queryFn: async () => {
      const response = await fetch(`/api/wp-proxy/forum/${questionId}/comments`, {
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch forum comments');
      }

      return response.json();
    },
    enabled: enabled && !!questionId,
    staleTime: 2 * 60 * 1000, // 2 minutes for comments
    gcTime: 10 * 60 * 1000,
  });
}

/**
 * Mutation for creating a new forum question
 */
export function useCreateForumQuestion() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (questionData: { title: string; content: string; category?: string }) => {
      const response = await fetch('/api/wp-proxy/forum/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(questionData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create forum question');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate forum questions list to show new question
      queryClient.invalidateQueries({ queryKey: queryKeys.forum.questions() });
    },
  });
}

/**
 * Mutation for voting on forum questions/comments
 */
export function useForumVote() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      questionId,
      voteType,
      isComment = false,
    }: {
      questionId: number;
      voteType: 'up' | 'down';
      isComment?: boolean;
    }) => {
      const endpoint = isComment
        ? `/api/wp-proxy/forum/${questionId}/comments/vote`
        : `/api/wp-proxy/forum/${questionId}/vote`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ voteType }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to vote');
      }

      return response.json();
    },
    onSuccess: (_, { questionId, isComment }) => {
      // Invalidate the specific question and comments
      queryClient.invalidateQueries({ queryKey: queryKeys.forum.question(questionId) });
      if (isComment) {
        queryClient.invalidateQueries({ queryKey: queryKeys.forum.comments(questionId) });
      }
      // Also invalidate the questions list to update vote counts
      queryClient.invalidateQueries({ queryKey: queryKeys.forum.questions() });
    },
  });
}

/**
 * Mutation for adding a comment to a forum question
 */
export function useAddForumComment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ questionId, content }: { questionId: number; content: string }) => {
      const response = await fetch(`/api/wp-proxy/forum/${questionId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ content }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add comment');
      }

      return response.json();
    },
    onSuccess: (_, { questionId }) => {
      // Invalidate comments for this question
      queryClient.invalidateQueries({ queryKey: queryKeys.forum.comments(questionId) });
      // Also invalidate the question to update comment count
      queryClient.invalidateQueries({ queryKey: queryKeys.forum.question(questionId) });
    },
  });
}
