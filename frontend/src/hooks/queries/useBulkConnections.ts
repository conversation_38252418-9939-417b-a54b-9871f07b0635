import { useQuery } from '@tanstack/react-query';

interface ConnectionStatus {
  status: 'none' | 'pending' | 'accepted';
  can_request?: boolean;
  pending_type?: 'sent' | 'received';
  message?: string;
  can_accept?: boolean;
  can_decline?: boolean;
  can_remove?: boolean;
}

interface BulkConnectionsResponse {
  statuses: Record<number, ConnectionStatus>;
}

async function fetchBulkConnectionStatuses(userIds: number[]): Promise<BulkConnectionsResponse> {
  const response = await fetch('/api/wp-proxy/connections/bulk-status', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({ userIds }),
  });

  if (!response.ok) {
    throw new Error('Failed to fetch bulk connection statuses');
  }

  return response.json();
}

export function useBulkConnectionStatuses(userIds: number[]) {
  return useQuery({
    queryKey: ['bulk-connections', userIds.sort()],
    queryFn: () => fetchBulkConnectionStatuses(userIds),
    enabled: userIds.length > 0,
    staleTime: 1000 * 60, // 1 minute
    gcTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
}
