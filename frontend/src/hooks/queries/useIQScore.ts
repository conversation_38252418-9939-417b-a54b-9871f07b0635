import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';
import { iqScoreAPI, type UserIQData, type LeaderboardEntry, type Rank } from '@/lib/api/iq-score';
import { useCurrentUser } from './useAuth';

/**
 * Hook for fetching current user's IQ score and rank
 * Maintains backward compatibility with the original interface
 */
export function useCurrentUserRank() {
  const { user, isAuthenticated } = useCurrentUser();

  const {
    data: userRank,
    isLoading: loading,
    error,
    refetch: fetchUserRank,
  } = useQuery({
    queryKey: queryKeys.iqScore.me(),
    queryFn: async (): Promise<UserIQData | null> => {
      const response = await iqScoreAPI.getCurrentUserIQ();

      if (response.success) {
        return response.data;
      } else {
        throw new Error('Failed to load rank data');
      }
    },
    enabled: isAuthenticated && !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000,
  });

  const getRankIcon = (rankName: string, isFounder: boolean = false) => {
    const rankNameLower = rankName.toLowerCase();
    const suffix = isFounder ? '-fc' : '';
    return `/images/icons/${rankNameLower}${suffix}.svg`;
  };

  return {
    userRank: userRank || null,
    loading,
    error: error?.message || null,
    getRankIcon,
    refetch: fetchUserRank,
  };
}

/**
 * Hook for fetching a specific user's IQ score and rank
 */
export function useUserIQScore(userId: number, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.iqScore.user(userId),
    queryFn: async (): Promise<UserIQData> => {
      const response = await iqScoreAPI.getUserIQ(userId);

      if (response.success) {
        return response.data;
      } else {
        throw new Error('Failed to load user IQ data');
      }
    },
    enabled: enabled && !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000,
  });
}

/**
 * Hook for fetching the leaderboard
 */
export function useLeaderboard(limit: number = 20, forceRefresh: boolean = false) {
  return useQuery({
    queryKey: queryKeys.iqScore.leaderboard(),
    queryFn: async (): Promise<LeaderboardEntry[]> => {
      const response = await iqScoreAPI.getLeaderboard(limit, forceRefresh);

      if (response.success) {
        return response.data;
      } else {
        throw new Error('Failed to load leaderboard data');
      }
    },
    staleTime: 3 * 60 * 1000, // 3 minutes for leaderboard
    gcTime: 10 * 60 * 1000,
  });
}

/**
 * Hook for fetching available ranks
 */
export function useRanks() {
  return useQuery({
    queryKey: queryKeys.iqScore.ranks(),
    queryFn: async (): Promise<Rank[]> => {
      const response = await fetch('/api/wp-proxy/iq-score/ranks', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch ranks: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        return data.data;
      } else {
        throw new Error('Failed to load ranks data');
      }
    },
    staleTime: 30 * 60 * 1000, // 30 minutes for ranks (rarely change)
    gcTime: 60 * 60 * 1000, // 1 hour
  });
}

/**
 * Mutation for awarding IQ points
 */
export function useAwardIQPoints() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      userId,
      points,
      reason,
    }: {
      userId: number;
      points: number;
      reason: string;
    }) => {
      const response = await fetch('/api/wp-proxy/iq-score/award', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ userId, points, reason }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to award IQ points');
      }

      return response.json();
    },
    onSuccess: (_, { userId }) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.iqScore.user(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.iqScore.me() });
      queryClient.invalidateQueries({ queryKey: queryKeys.iqScore.leaderboard() });
    },
  });
}

/**
 * Mutation for testing IQ award (development/testing)
 */
export function useTestIQAward() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ points, reason }: { points: number; reason: string }) => {
      const response = await fetch('/api/wp-proxy/iq-score/test-award', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ points, reason }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to test IQ award');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate current user's IQ data and leaderboard
      queryClient.invalidateQueries({ queryKey: queryKeys.iqScore.me() });
      queryClient.invalidateQueries({ queryKey: queryKeys.iqScore.leaderboard() });
    },
  });
}
