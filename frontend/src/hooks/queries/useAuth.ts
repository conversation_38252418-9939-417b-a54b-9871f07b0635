/*********************************************
# frontend/src/hooks/queries/useAuth.ts
# 01/27/2025 9:20pm Reduced auth polling from 30s to 5min, disabled window focus refetch
**********************************************/

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';

interface User {
  id: number;
  username: string;
  email: string;
  display_name: string;
  first_name: string;
  last_name: string;
  avatar_urls: {
    [key: string]: string;
  };
  roles: string[];
  acf?: Record<string, unknown>;
}

interface AuthStatus {
  isAuthenticated: boolean;
  user: User | null;
}

interface LoginCredentials {
  username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  user?: User;
  message?: string;
}

/**
 * React Query hook for authentication status
 * Maintains backward compatibility with the original interface: { isLoggedIn, isLoading, user }
 */
export function useAuthStatus() {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: queryKeys.auth.status(),
    queryFn: async (): Promise<AuthStatus> => {
      const response = await fetch('/api/wp-proxy/auth/status', {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        // If status check fails, user is not authenticated
        return { isAuthenticated: false, user: null };
      }

      const data = await response.json();

      return {
        isAuthenticated: data.isLoggedIn || data.isAuthenticated || false,
        user: data.wpUser || data.user || null, // Use wpUser which contains ACF fields
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes - less aggressive polling
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: false, // Don't retry auth failures
    refetchOnWindowFocus: false, // Disable window focus refetch to reduce API calls
    refetchOnReconnect: true, // Check when network reconnects
  });

  // Return interface matching the original hook for backward compatibility
  return {
    isLoggedIn: data?.isAuthenticated || false,
    isLoading,
    user: data?.user || null,
    error,
    refetch, // Add refetch function for manual refresh
  };
}

/**
 * Mutation hook for user login
 */
export function useLogin() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (credentials: LoginCredentials): Promise<LoginResponse> => {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      return data;
    },
    onSuccess: (data) => {
      if (data.success && data.user) {
        // Update the auth status cache with the new user data
        queryClient.setQueryData(queryKeys.auth.status(), {
          isAuthenticated: true,
          user: data.user,
        });

        // Invalidate other queries that depend on auth state
        queryClient.invalidateQueries({ queryKey: queryKeys.posts.all });
        queryClient.invalidateQueries({ queryKey: queryKeys.messages.all });
        queryClient.invalidateQueries({ queryKey: queryKeys.iqScore.all });

        // Delay connections invalidation slightly to ensure auth state has propagated
        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: queryKeys.connections.all });
        }, 100);
      }
    },
  });
}

/**
 * Mutation hook for user logout
 */
export function useLogout() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (): Promise<{ success: boolean }> => {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Logout failed');
      }

      return data;
    },
    onSuccess: () => {
      // Clear auth status
      queryClient.setQueryData(queryKeys.auth.status(), {
        isAuthenticated: false,
        user: null,
      });

      // Clear all cached data that requires authentication
      queryClient.removeQueries({ queryKey: queryKeys.posts.all });
      queryClient.removeQueries({ queryKey: queryKeys.connections.all });
      queryClient.removeQueries({ queryKey: queryKeys.messages.all });
      queryClient.removeQueries({ queryKey: queryKeys.iqScore.all });
      queryClient.removeQueries({ queryKey: queryKeys.notifications.all });
      queryClient.removeQueries({ queryKey: queryKeys.members.all });
    },
  });
}

/**
 * Hook to get the current authenticated user
 * Convenience hook that extracts the user from auth status
 */
export function useCurrentUser() {
  const { isLoggedIn, isLoading, user, error } = useAuthStatus();

  return {
    user: user || null,
    isAuthenticated: isLoggedIn || false,
    isLoading,
    error,
  };
}

/**
 * Hook for user profile data (separate from auth status for more specific caching)
 */
export function useUserProfile() {
  const { user, isAuthenticated } = useCurrentUser();

  return useQuery({
    queryKey: queryKeys.auth.profile(),
    queryFn: async () => {
      const response = await fetch('/api/user/me', {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user profile: ${response.status}`);
      }

      return response.json();
    },
    enabled: isAuthenticated && !!user,
    staleTime: 10 * 60 * 1000, // Profile data can be cached longer
    gcTime: 15 * 60 * 1000,
  });
}
