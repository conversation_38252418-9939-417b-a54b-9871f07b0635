/**
 * Utility to prevent layout shifts caused by modal/dropdown components
 * This overrides Radix UI's body scroll lock behavior
 */

let originalBodyStyle: string | null = null;

export function preventLayoutShift() {
  // Store original body style
  if (!originalBodyStyle) {
    originalBodyStyle = document.body.style.cssText;
  }

  // Override any attempts to modify body overflow
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
        const target = mutation.target as HTMLElement;
        if (target === document.body) {
          // Prevent overflow hidden and padding-right changes
          if (target.style.overflow === 'hidden' || target.style.paddingRight) {
            target.style.overflow = 'scroll';
            target.style.paddingRight = '';
            target.style.marginRight = '';
          }
        }
      }
    });
  });

  // Watch for changes to body element
  observer.observe(document.body, {
    attributes: true,
    attributeFilter: ['style', 'data-scroll-locked']
  });

  return () => {
    observer.disconnect();
    if (originalBodyStyle !== null) {
      document.body.style.cssText = originalBodyStyle;
    }
  };
}

// Initialize on DOM ready
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', preventLayoutShift);
  } else {
    preventLayoutShift();
  }
}
