/**
 * Authentication and API initialization
 *
 * This file handles the initialization of the WordPress REST API client
 * and provides utility functions for authentication.
 */

// Export the API settings for use in other modules
if (typeof window.wpApiSettings !== 'undefined') {
  window.tourismiq = window.tourismiq || {};
  window.tourismiq.api = {
    root: window.wpApiSettings.root,
    nonce: window.wpApiSettings.nonce,
    user: window.wpApiSettings.user,
  };
}

export default window.wpApiSettings;
