/**
 * Text formatting utilities
 */

/**
 * Converts plain text with line breaks to HTML paragraphs
 * @param text - Plain text with line breaks
 * @returns HTML string with proper paragraph tags
 */
export function textToParagraphs(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  // Split by double line breaks first (paragraph breaks)
  const paragraphs = text
    .trim()
    .split(/\n\s*\n/)
    .filter((paragraph) => paragraph.trim().length > 0);

  // Convert each paragraph, handling single line breaks within paragraphs
  const htmlParagraphs = paragraphs.map((paragraph) => {
    // Replace single line breaks with <br> tags within paragraphs
    const formattedParagraph = paragraph.trim().replace(/\n/g, '<br>');

    return `<p>${formattedParagraph}</p>`;
  });

  return htmlParagraphs.join('\n');
}

/**
 * Converts HTML paragraphs back to plain text with line breaks
 * @param html - HTML string with paragraph tags
 * @returns Plain text with line breaks
 */
export function paragraphsToText(html: string): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  return (
    html
      // Replace paragraph tags with double line breaks
      .replace(/<\/p>\s*<p>/gi, '\n\n')
      // Replace opening and closing p tags
      .replace(/<\/?p>/gi, '')
      // Replace br tags with single line breaks
      .replace(/<br\s*\/?>/gi, '\n')
      // Clean up any remaining HTML tags
      .replace(/<[^>]*>/g, '')
      // Decode HTML entities
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
      // Clean up extra whitespace
      .trim()
  );
}

/**
 * Decode HTML entities
 */
export function decodeHtmlEntities(text: string): string {
  if (typeof document !== 'undefined') {
    // Client-side: use DOM API
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
  } else {
    // Server-side: basic entity replacement
    return text
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ');
  }
}
