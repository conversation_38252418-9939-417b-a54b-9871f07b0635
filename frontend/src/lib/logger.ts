/*********************************************
# frontend/src/lib/logger.ts
# 01/27/2025 9:20pm Created development logging utility to reduce console noise
**********************************************/

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LoggerConfig {
  enabled: boolean;
  level: LogLevel;
  apiCalls: boolean;
  components: boolean;
  queries: boolean;
}

const config: LoggerConfig = {
  enabled: process.env.NODE_ENV === 'development',
  level: 'info',
  apiCalls:
    process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_LOG_API_CALLS === 'true',
  components: process.env.NODE_ENV === 'development',
  queries: process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_LOG_QUERIES === 'true',
};

const logLevels: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
};

const shouldLog = (level: LogLevel): boolean => {
  if (!config.enabled) return false;
  return logLevels[level] >= logLevels[config.level];
};

export const logger = {
  debug: (message: string, ...args: any[]) => {
    if (shouldLog('debug')) {
      console.debug(`🐛 [DEBUG] ${message}`, ...args);
    }
  },

  info: (message: string, ...args: any[]) => {
    if (shouldLog('info')) {
      console.info(`ℹ️ [INFO] ${message}`, ...args);
    }
  },

  warn: (message: string, ...args: any[]) => {
    if (shouldLog('warn')) {
      console.warn(`⚠️ [WARN] ${message}`, ...args);
    }
  },

  error: (message: string, ...args: any[]) => {
    if (shouldLog('error')) {
      console.error(`❌ [ERROR] ${message}`, ...args);
    }
  },

  // Specialized loggers
  api: (method: string, url: string, status?: number, duration?: number) => {
    if (config.apiCalls && shouldLog('debug')) {
      const statusEmoji = status ? (status >= 400 ? '❌' : '✅') : '⏳';
      const durationText = duration ? ` (${duration}ms)` : '';
      console.debug(`🌐 [API] ${method} ${url} ${statusEmoji}${durationText}`);
    }
  },

  query: (queryKey: string, type: 'fetch' | 'cache' | 'error', details?: any) => {
    if (config.queries && shouldLog('debug')) {
      const emoji = type === 'fetch' ? '🔄' : type === 'cache' ? '💾' : '❌';
      console.debug(`${emoji} [QUERY] ${queryKey}`, details);
    }
  },

  component: (name: string, action: string, details?: any) => {
    if (config.components && shouldLog('debug')) {
      console.debug(`🧩 [COMPONENT] ${name} - ${action}`, details);
    }
  },
};

// Export config for runtime changes
export const loggerConfig = config;
