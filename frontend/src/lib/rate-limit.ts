import { NextRequest } from 'next/server';

interface RateLimitOptions {
  max: number; // Maximum requests
  window: number; // Time window in milliseconds
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

// In-memory store for rate limiting (use Redis in production)
const store: RateLimitStore = {};

export function rateLimit(options: RateLimitOptions = { max: 100, window: 60000 }) {
  return {
    check: (request: NextRequest, identifier?: string) => {
      const key = identifier || getClientIdentifier(request);
      const now = Date.now();

      // Clean up expired entries
      if (store[key] && store[key].resetTime < now) {
        delete store[key];
      }

      // Initialize or get current count
      if (!store[key]) {
        store[key] = {
          count: 1,
          resetTime: now + options.window,
        };
        return { success: true, remaining: options.max - 1 };
      }

      // Check if limit exceeded
      if (store[key].count >= options.max) {
        return {
          success: false,
          remaining: 0,
          resetTime: store[key].resetTime,
        };
      }

      // Increment count
      store[key].count++;

      return {
        success: true,
        remaining: options.max - store[key].count,
      };
    },
  };
}

function getClientIdentifier(request: NextRequest): string {
  // Try to get IP from various headers (for different hosting environments)
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');

  const ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown';

  // Include user agent for additional uniqueness
  const userAgent = request.headers.get('user-agent') || 'unknown';

  return `${ip}-${userAgent.slice(0, 50)}`;
}

// Rate limit configurations for different endpoints
export const rateLimitConfigs = {
  auth: { max: 30, window: 60000 }, // 30 requests per minute for auth (increased for production)
  api: { max: 100, window: 60000 }, // 100 requests per minute for general API
  upload: { max: 10, window: 60000 }, // 10 uploads per minute
  search: { max: 50, window: 60000 }, // 50 searches per minute
};
