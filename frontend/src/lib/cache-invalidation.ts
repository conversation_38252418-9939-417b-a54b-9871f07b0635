/**
 * Cache Invalidation Manager
 * Polls WordPress for cache invalidation events and updates React Query cache
 */

import { queryClient } from '@/lib/react-query';
import { queryKeys } from '@/lib/react-query';

interface CacheInvalidation {
  type: 'post' | 'category' | 'all';
  id: number;
  timestamp: number;
}

interface InvalidationsResponse {
  invalidations: CacheInvalidation[];
  timestamp: number;
}

class CacheInvalidationManager {
  private static instance: CacheInvalidationManager;
  private pollInterval: NodeJS.Timeout | null = null;
  private lastCheck: number = 0;
  private isPolling: boolean = false;

  static getInstance(): CacheInvalidationManager {
    if (!CacheInvalidationManager.instance) {
      CacheInvalidationManager.instance = new CacheInvalidationManager();
    }
    return CacheInvalidationManager.instance;
  }

  /**
   * Start polling for cache invalidations
   */
  startPolling(intervalMs: number = 5000) {
    if (this.pollInterval) {
      return; // Already polling
    }

    // Set initial timestamp
    this.lastCheck = Math.floor(Date.now() / 1000);

    // Start polling
    this.pollInterval = setInterval(() => {
      this.checkInvalidations();
    }, intervalMs);

    // Do initial check
    this.checkInvalidations();
  }

  /**
   * Stop polling for cache invalidations
   */
  stopPolling() {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
      this.pollInterval = null;
    }
  }

  /**
   * Check for cache invalidations from WordPress
   */
  private async checkInvalidations() {
    if (this.isPolling) {
      return; // Prevent overlapping requests
    }

    this.isPolling = true;

    try {
      const response = await fetch(`/api/wp-proxy/cache/invalidations?since=${this.lastCheck}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        console.error('Failed to check cache invalidations:', response.status);
        return;
      }

      const data: InvalidationsResponse = await response.json();

      // Process invalidations
      if (data.invalidations && data.invalidations.length > 0) {
        console.log('Processing cache invalidations:', data.invalidations);
        this.processInvalidations(data.invalidations);
      }

      // Update last check timestamp
      this.lastCheck = data.timestamp;
    } catch (error) {
      console.error('Error checking cache invalidations:', error);
    } finally {
      this.isPolling = false;
    }
  }

  /**
   * Process cache invalidations and update React Query cache
   */
  private processInvalidations(invalidations: CacheInvalidation[]) {
    invalidations.forEach((invalidation) => {
      switch (invalidation.type) {
        case 'post':
          this.invalidatePostCache(invalidation.id);
          break;
        case 'category':
          this.invalidateCategoryCache(invalidation.id);
          break;
        case 'all':
          this.invalidateAllCache();
          break;
      }
    });
  }

  /**
   * Trigger server-side cache revalidation
   */
  private async triggerServerRevalidation(type: 'post' | 'category' | 'all', id?: number) {
    try {
      await fetch('/api/revalidate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          id: id || 0,
        }),
      });
      console.log(
        `[Cache] Server-side revalidation triggered for ${type} ${id ? `(ID: ${id})` : ''}`
      );
    } catch (error) {
      console.error('Failed to trigger server-side revalidation:', error);
    }
  }

  /**
   * Invalidate post-related caches
   */
  private invalidatePostCache(postId: number) {
    // Invalidate specific post
    queryClient.invalidateQueries({
      queryKey: queryKeys.posts.detail(postId),
    });

    // Invalidate all post lists (they might contain this post)
    queryClient.invalidateQueries({
      queryKey: queryKeys.posts.all,
    });

    // Invalidate user posts (in case this post belongs to a user)
    queryClient.invalidateQueries({
      predicate: (query) => {
        const key = query.queryKey;
        return Array.isArray(key) && key[0] === 'posts' && key[1] === 'user';
      },
    });
  }

  /**
   * Invalidate category-related caches
   */
  private invalidateCategoryCache(_categoryId: number) {
    // Invalidate all category-based queries
    queryClient.invalidateQueries({
      predicate: (query) => {
        const key = query.queryKey;
        return (
          Array.isArray(key) &&
          key[0] === 'posts' &&
          (key[1] === 'by-category' || (key[1] === 'list' && key[2]?.category))
        );
      },
    });

    // Invalidate categories list
    queryClient.invalidateQueries({
      queryKey: queryKeys.categories.all,
    });
  }

  /**
   * Invalidate all caches
   */
  private invalidateAllCache() {
    // Invalidate everything
    queryClient.invalidateQueries();
  }

  /**
   * Manually trigger cache invalidation
   */
  async invalidateCache(type: 'post' | 'category' | 'all', id?: number) {
    const invalidation: CacheInvalidation = {
      type,
      id: id || 0,
      timestamp: Math.floor(Date.now() / 1000),
    };

    // Process client-side invalidations
    this.processInvalidations([invalidation]);

    // Also trigger server-side revalidation
    await this.triggerServerRevalidation(type, id);
  }
}

export const cacheInvalidationManager = CacheInvalidationManager.getInstance();

// Auto-start in browser environment
if (typeof window !== 'undefined') {
  // Start polling when the page is visible
  if (document.visibilityState === 'visible') {
    cacheInvalidationManager.startPolling();
  }

  // Handle visibility changes
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      cacheInvalidationManager.startPolling();
    } else {
      cacheInvalidationManager.stopPolling();
    }
  });

  // Stop polling when the page is unloaded
  window.addEventListener('beforeunload', () => {
    cacheInvalidationManager.stopPolling();
  });
}
