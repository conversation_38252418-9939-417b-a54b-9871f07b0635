// Utility function to get proper display names for category slugs
export function getCategoryDisplayName(categorySlug: string): string {
  const categoryMap: Record<string, string> = {
    'thought-leadership': 'Thought Leadership',
    news: 'News',
    job: 'Jobs',
    podcast: 'Podcasts',
    book: 'Books',
    'case-study': 'Case Studies',
    course: 'Courses',
    presentation: 'Presentations',
    'press-release': 'Press Releases',
    template: 'Templates',
    video: 'Videos',
    webinar: 'Webinars',
    whitepaper: 'Whitepapers',
    event: 'Events',
  };

  // Return the mapped name if it exists, otherwise fall back to title case transformation
  return (
    categoryMap[categorySlug] ||
    categorySlug
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  );
}
