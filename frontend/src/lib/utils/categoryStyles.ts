// Category color constants
export const CATEGORY_COLORS = {
  NEWS: '#ff5ce0',
  NEWS_HOVER: '#e54cc7',
  RESOURCES: '#ffa15c',
  RESOURCES_HOVER: '#e68943',
  THOUGHT: '#5cc8ff',
  THOUGHT_HOVER: '#43b0e6',
  EVENTS: '#1dd05b',
  EVENTS_HOVER: '#17b749',
  JOBS: '#ff625c',
  JOBS_HOVER: '#e54943',
} as const;

// Resource category slugs - all should use orange color
export const RESOURCE_CATEGORIES = [
  'blog-post',
  'blog-posts',
  'book',
  'books',
  'case-study',
  'case-studies',
  'course',
  'courses',
  'presentation',
  'presentations',
  'template',
  'templates',
  'video',
  'videos',
  'webinar',
  'webinars',
  'whitepaper',
  'whitepapers',
  'resource',
  'resources',
  'tool',
  'tools',
  'guide',
  'guides',
  'tutorial',
  'tutorials',
  'download',
  'downloads',
] as const;

// Helper function to get category styles based on category name
export function getCategoryStyles(categoryName: string): string {
  const name = categoryName.toLowerCase().trim();

  // News category
  if (name === 'news' || name.includes('news')) {
    return `bg-[${CATEGORY_COLORS.NEWS}] hover:bg-[${CATEGORY_COLORS.NEWS_HOVER}]`;
  }

  // Resources category - check against comprehensive list
  if (
    RESOURCE_CATEGORIES.includes(name as any) ||
    name.includes('resource') ||
    name.includes('tool') ||
    name.includes('guide') ||
    name.includes('tutorial') ||
    name.includes('download')
  ) {
    return `bg-[${CATEGORY_COLORS.RESOURCES}] hover:bg-[${CATEGORY_COLORS.RESOURCES_HOVER}]`;
  }

  // Thought Leadership and Questions
  if (
    name === 'thought leadership' ||
    name === 'thought-leadership' ||
    name.includes('thought') ||
    name === 'questions' ||
    name.includes('question') ||
    name.includes('opinion') ||
    name.includes('insight')
  ) {
    return `bg-[${CATEGORY_COLORS.THOUGHT}] hover:bg-[${CATEGORY_COLORS.THOUGHT_HOVER}]`;
  }

  // Events
  if (
    name === 'events' ||
    name.includes('event') ||
    name.includes('conference') ||
    name.includes('meetup')
  ) {
    return `bg-[${CATEGORY_COLORS.EVENTS}] hover:bg-[${CATEGORY_COLORS.EVENTS_HOVER}]`;
  }

  // Jobs
  if (
    name === 'jobs' ||
    name.includes('job') ||
    name.includes('career') ||
    name.includes('hiring') ||
    name.includes('employment')
  ) {
    return `bg-[${CATEGORY_COLORS.JOBS}] hover:bg-[${CATEGORY_COLORS.JOBS_HOVER}]`;
  }

  // Default fallback color (thought leadership blue)
  return `bg-[${CATEGORY_COLORS.THOUGHT}] hover:bg-[${CATEGORY_COLORS.THOUGHT_HOVER}]`;
}
