interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

interface CacheStore {
  [key: string]: CacheEntry<unknown>;
}

// In-memory cache store (use Redis in production)
const cache: CacheStore = {};

export class Cache {
  private static instance: Cache;

  static getInstance(): Cache {
    if (!Cache.instance) {
      Cache.instance = new Cache();
    }
    return Cache.instance;
  }

  set<T>(key: string, data: T, ttlSeconds: number = 300): void {
    cache[key] = {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000,
    };
  }

  get<T>(key: string): T | null {
    const entry = cache[key];

    if (!entry) {
      return null;
    }

    // Check if cache entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      delete cache[key];
      return null;
    }

    return entry.data as T;
  }

  delete(key: string): void {
    delete cache[key];
  }

  clear(): void {
    Object.keys(cache).forEach((key) => delete cache[key]);
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now();
    Object.keys(cache).forEach((key) => {
      const entry = cache[key];
      if (entry && now - entry.timestamp > entry.ttl) {
        delete cache[key];
      }
    });
  }
}

// Cache key generators
export const cacheKeys = {
  posts: (params: Record<string, unknown>) => `posts:${JSON.stringify(params)}`,
  user: (userId: string) => `user:${userId}`,
  categories: () => 'categories',
  vendors: (slug?: string) => (slug ? `vendor:${slug}` : 'vendors'),
  forum: (id: string) => `forum:${id}`,
  iqScore: (userId: string) => `iq-score:${userId}`,
};

// Cache TTL configurations (in seconds)
export const cacheTTL = {
  posts: 300, // 5 minutes
  user: 600, // 10 minutes
  categories: 1800, // 30 minutes
  vendors: 900, // 15 minutes
  forum: 180, // 3 minutes
  iqScore: 300, // 5 minutes
};

// Utility function for cache-first data fetching
export async function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttlSeconds: number = 300
): Promise<T> {
  const cache = Cache.getInstance();

  // Try to get from cache first
  const cached = cache.get<T>(key);
  if (cached !== null) {
    return cached;
  }

  // Fetch fresh data
  const data = await fetcher();

  // Store in cache
  cache.set(key, data, ttlSeconds);

  return data;
}

// Start cleanup interval (run every 5 minutes)
if (typeof window === 'undefined') {
  // Server-side only
  setInterval(
    () => {
      Cache.getInstance().cleanup();
    },
    5 * 60 * 1000
  );
}

export function someCacheFunction() {
  // Implementation of someCacheFunction
}

export function anotherCacheFunction() {
  // Implementation of anotherCacheFunction
}
