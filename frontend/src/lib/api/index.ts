/**
 * API Module Index
 * Export all API utilities for easy importing
 *
 * This file serves as the main entry point for all API functionality.
 * It re-exports everything from the modular API files, allowing consumers
 * to import from a single location: '@/lib/api'
 */

// Core WordPress API utilities
export * from './wordpress';

// Modular API components
export * from './utils';
export * from './auth';
export * from './users';
export * from './posts';
export * from './members';
export * from './categories';

// Import types for specific functions
import type { AssignedVendor } from './categories';

// Get vendors assigned to current user
export async function getUserAssignedVendors(): Promise<AssignedVendor[]> {
  try {
    const response = await fetch('/api/wp-proxy/user/assigned-vendors', {
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch assigned vendors: ${response.status}`);
    }

    const vendors = await response.json();
    return vendors;
  } catch (error) {
    console.error('Error fetching assigned vendors:', error);
    throw error;
  }
}
