/**
 * User Roles Fix
 * Handle the user roles data format issue after migration
 */

/**
 * Safely extract user roles from WordPress user data
 */
export function safeGetUserRoles(wpUser: Record<string, unknown>): string[] {
  // Handle different possible formats
  if (Array.isArray(wpUser.roles)) {
    return wpUser.roles;
  }

  // Handle roles as object with numeric keys (WordPress format)
  if (wpUser.roles && typeof wpUser.roles === 'object' && !Array.isArray(wpUser.roles)) {
    const rolesArray = Object.values(wpUser.roles as Record<string, string>);
    return rolesArray;
  }

  if (Array.isArray(wpUser._user_roles)) {
    return wpUser._user_roles;
  }

  if (typeof wpUser.roles === 'string') {
    try {
      const parsed = JSON.parse(wpUser.roles);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [wpUser.roles];
    }
  }

  if (typeof wpUser._user_roles === 'string') {
    try {
      const parsed = JSON.parse(wpUser._user_roles);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [wpUser._user_roles];
    }
  }

  // Extract from capabilities if roles not available
  if (wpUser.capabilities) {
    const caps =
      typeof wpUser.capabilities === 'string'
        ? JSON.parse(wpUser.capabilities)
        : wpUser.capabilities;

    return Object.keys(caps).filter(
      (cap) =>
        cap.includes('um_') || cap === 'administrator' || cap === 'subscriber' || cap === 'editor'
    );
  }

  // Default fallback
  return ['subscriber'];
}

/**
 * Check if user has a specific role
 */
export function userHasRole(wpUser: Record<string, unknown>, role: string): boolean {
  const roles = safeGetUserRoles(wpUser);
  return roles.includes(role);
}

/**
 * Check if user has any of the specified roles
 */
export function userHasAnyRole(wpUser: Record<string, unknown>, rolesToCheck: string[]): boolean {
  const roles = safeGetUserRoles(wpUser);
  return rolesToCheck.some((role) => roles.includes(role));
}
