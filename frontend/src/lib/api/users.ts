/**
 * User API Functions
 * Handles user profile management, avatars, and user data transformation
 */

import { API_BASE, getCredentialsOption } from './utils';
import { safeGetUserRoles } from './user-roles-fix';

// Import directly from wordpress.ts to avoid circular dependencies
import { User, WPUser } from './wordpress';

/**
 * Get avatar URL for a user
 */
export function getAvatarUrl(user: WPUser | null, size: number = 96): string {
  if (!user) {
    return '';
  }

  // Try to get avatar from ACF profile_picture field
  if (user.acf?.profile_picture) {
    // Handle both string and object formats (WordPress can return either)
    if (typeof user.acf.profile_picture === 'string') {
      return user.acf.profile_picture;
    }

    // Handle object format with url property
    if (typeof user.acf.profile_picture === 'object' && user.acf.profile_picture.url) {
      return user.acf.profile_picture.url;
    }

    // Handle object format with sizes property
    if (typeof user.acf.profile_picture === 'object' && user.acf.profile_picture.sizes) {
      // Try to get the appropriate size
      const sizeKey = `${size}x${size}`;
      if (user.acf.profile_picture.sizes[sizeKey]) {
        return user.acf.profile_picture.sizes[sizeKey];
      }
      // Fallback to full size
      return user.acf.profile_picture.url || '';
    }
  }

  // Fallback to WordPress avatar_urls
  if (user.avatar_urls) {
    const sizeStr = size.toString();
    if (user.avatar_urls[sizeStr]) {
      return user.avatar_urls[sizeStr];
    }
    // Try common sizes as fallback
    if (user.avatar_urls['96']) return user.avatar_urls['96'];
    if (user.avatar_urls['48']) return user.avatar_urls['48'];
    if (user.avatar_urls['24']) return user.avatar_urls['24'];
  }

  // Final fallback to empty string (will use default avatar in UI)
  return '';
}

/**
 * Get user meta data
 */
export async function getUserMeta(userId: number): Promise<Record<string, unknown>> {
  try {
    const response = await fetch(`${API_BASE}/user/${userId}/meta`, {
      method: 'GET',
      credentials: getCredentialsOption(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user meta: ${response.status}`);
    }

    return await response.json();
  } catch (err: unknown) {
    console.error('Error fetching user meta:', err);
    return {};
  }
}

/**
 * Update user profile
 */
export async function updateUserProfile(data: Partial<User>): Promise<{
  success: boolean;
  user?: User;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_BASE}/user/profile`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      credentials: getCredentialsOption(),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || `Update failed: ${response.status}`,
      };
    }

    const responseData = await response.json();
    return {
      success: true,
      user: responseData.user,
    };
  } catch (error) {
    console.error('Error updating user profile:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An error occurred',
    };
  }
}

/**
 * Transform WordPress user to frontend User format
 */
export function transformWPUserToUser(wpUser: WPUser): User {
  // Get all our values with proper defaults
  // Use WordPress core fields for basic info
  const firstName = wpUser.first_name || null;
  const lastName = wpUser.last_name || null;
  const displayName = wpUser.display_name || wpUser.name || '';

  // Use ACF fields for everything else (no website from core WP)
  const bio = wpUser.acf?.bio || null;
  const jobTitle = wpUser.acf?.job_title || null;
  const company = wpUser.acf?.company || null;
  const location = wpUser.acf?.location || null;
  const phone = wpUser.acf?.phone || null;
  const website = wpUser.acf?.website || null;
  const socialTwitter = wpUser.acf?.twitter || null;
  const socialFacebook = wpUser.acf?.facebook || null;
  const socialLinkedin = wpUser.acf?.linkedin || null;
  const socialInstagram = wpUser.acf?.instagram || null;

  // Get cover image URL
  const getCoverImageUrl = (coverPhoto: any): string => {
    if (!coverPhoto) return '';

    if (typeof coverPhoto === 'string') {
      return coverPhoto;
    }

    if (typeof coverPhoto === 'object' && coverPhoto.url) {
      return coverPhoto.url;
    }

    return '';
  };

  // Now build the user object
  return {
    id: wpUser.id,
    username: wpUser.username || wpUser.user_login || wpUser.slug,
    name: displayName,
    email: wpUser.email || wpUser.user_email || '',
    firstName: firstName,
    lastName: lastName,
    avatarUrl: getAvatarUrl(wpUser),
    coverImageUrl: getCoverImageUrl(wpUser.coverImage),
    bio: typeof bio === 'string' ? bio : '',
    roles: safeGetUserRoles(wpUser as unknown as Record<string, unknown>),
    jobTitle: typeof jobTitle === 'string' ? jobTitle : '',
    company: typeof company === 'string' ? company : '',
    location: typeof location === 'string' ? location : '',
    phone: typeof phone === 'string' ? phone : '',
    website: typeof website === 'string' ? website : '',
    socialLinks: {
      twitter: socialTwitter ?? null,
      facebook: socialFacebook ?? null,
      linkedin: socialLinkedin ?? null,
      instagram: socialInstagram ?? null,
    },
    // Keep the acf property for backward compatibility
    acf: wpUser.acf
      ? { ...wpUser.acf }
      : {
          bio: typeof bio === 'string' ? bio : '',
          job_title: typeof jobTitle === 'string' ? jobTitle : '',
          company: typeof company === 'string' ? company : '',
          location: typeof location === 'string' ? location : '',
          phone: typeof phone === 'string' ? phone : '',
          website: typeof website === 'string' ? website : '',
          twitter: socialTwitter ?? '',
          facebook: socialFacebook ?? '',
          linkedin: socialLinkedin ?? '',
          instagram: socialInstagram ?? '',
        },
  };
}
