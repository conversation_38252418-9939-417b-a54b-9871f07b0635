/**
 * WordPress API Module
 *
 * This file now serves as a lightweight wrapper that re-exports functionality
 * from the modular API files. This approach makes the codebase more maintainable
 * by separating concerns into logical modules.
 */

// Re-export types
export * from './types';

// Re-export utility functions
export {
  getApiUrl,
  API_BASE,
  isClient,
  getCredentialsOption,
  formatDate,
  stripHtml,
} from './utils';

// Re-export auth functions
export { getCurrentUser, checkAuthStatus, loginUser, logoutUser } from './auth';

// Re-export user functions
export { getAvatarUrl, getUserMeta, updateUserProfile, transformWPUserToUser } from './users';

// Re-export post functions
export { getFeaturedPosts, getPostBySlug, createPost, type CreatePostData } from './posts';
