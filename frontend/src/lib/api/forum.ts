import {
  Question,
  QuestionDetail,
  QuestionFilters,
  QuestionsResponse,
  Comment,
} from '@/types/forum';

// Helper function to get the base URL for API calls
function getBaseUrl(): string {
  // For server-side rendering, use the full URL
  if (typeof window === 'undefined') {
    // In development, always use localhost
    if (process.env.NODE_ENV === 'development') {
      return 'http://localhost:3000';
    }
    return process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000';
  }
  // For client-side, use relative URLs
  return '';
}

// Get all forum questions with pagination and sorting
export async function getQuestions(filters: QuestionFilters = {}): Promise<QuestionsResponse> {
  try {
    const { page = 1, sort = 'new', search } = filters;

    const queryParams = new URLSearchParams({
      page: page.toString(),
      per_page: '10',
      sort,
    });

    if (search && search.trim()) {
      queryParams.append('search', search.trim());
    }

    const response = await fetch(`${getBaseUrl()}/api/wp-proxy/forum?${queryParams}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch questions: ${response.status}`);
    }

    const data = await response.json();

    return {
      questions: data.questions || [],
      totalPages: data.pagination?.total_pages || 1,
      currentPage: data.pagination?.page || 1,
    };
  } catch (error) {
    console.error('Error fetching questions:', error);
    return {
      questions: [],
      totalPages: 1,
      currentPage: 1,
    };
  }
}

// Get a specific forum question by ID
export async function getQuestionById(id: string): Promise<QuestionDetail | null> {
  try {
    const response = await fetch(`${getBaseUrl()}/api/wp-proxy/forum/${id}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch question: ${response.status}`);
    }

    const wpQuestion = await response.json();

    // Transform WordPress data to QuestionDetail format
    const question: QuestionDetail = {
      id: wpQuestion.id.toString(),
      title: wpQuestion.title || '',
      content: wpQuestion.content || '',
      createdAt: typeof wpQuestion.date === 'string' ? wpQuestion.date : new Date().toISOString(),
      updatedAt:
        typeof wpQuestion.modified === 'string'
          ? wpQuestion.modified
          : typeof wpQuestion.date === 'string'
            ? wpQuestion.date
            : new Date().toISOString(),
      author: wpQuestion.author_info
        ? {
            id: wpQuestion.author_info.id?.toString() || '',
            name: wpQuestion.author_info.name || 'Anonymous',
            avatar:
              wpQuestion.author_info.profile_picture ||
              wpQuestion.author_info.avatar_urls?.['96'] ||
              '',
            roles: Array.isArray(wpQuestion.author_info.roles) ? wpQuestion.author_info.roles : [],
          }
        : { id: '', name: 'Anonymous', avatar: '', roles: [] },
      commentCount: wpQuestion.total_comments || 0,
      isResolved: wpQuestion.forum_meta?.is_resolved || false,
      comments: wpQuestion.comments ? wpQuestion.comments.map(transformWPCommentToComment) : [],
    };

    return question;
  } catch (error) {
    console.error('Error fetching question:', error);
    return null;
  }
}

// Create a new forum question
export async function createQuestion(questionData: {
  title: string;
  content: string;
}): Promise<Question | null> {
  try {
    const response = await fetch(`${getBaseUrl()}/api/wp-proxy/forum/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(questionData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create question');
    }

    const data = await response.json();
    return data.question;
  } catch (error) {
    console.error('Error creating question:', error);
    throw error;
  }
}

// Get comments for a specific question
export async function getQuestionComments(questionId: string): Promise<Comment[]> {
  try {
    const response = await fetch(`${getBaseUrl()}/api/wp-proxy/forum/${questionId}/comments`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch comments: ${response.status}`);
    }

    const data = await response.json();
    const comments = data.comments || [];

    // Transform the comments using our transformation function
    return comments.map(transformWPCommentToComment);
  } catch (error) {
    console.error('Error fetching comments:', error);
    return [];
  }
}

// Add a comment to a forum question
export async function addComment(questionId: string, content: string): Promise<Comment | null> {
  try {
    const response = await fetch(`${getBaseUrl()}/api/wp-proxy/forum/${questionId}/comments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ content }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to add comment');
    }

    const data = await response.json();
    return data.comment;
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
}

// Transform WordPress post data to Question format
export function transformWPPostToQuestion(wpPost: unknown): Question {
  if (!wpPost || typeof wpPost !== 'object') {
    return {
      id: '',
      title: '',
      content: '',
      createdAt: '',
      updatedAt: '',
      author: { id: '', name: 'Anonymous', avatar: '' },
      commentCount: 0,
      isResolved: false,
    };
  }
  const post = wpPost as Record<string, unknown>;

  // Check for author_info first (our custom field), then fall back to _embedded.author
  let author: { id: string; name: string; avatar: string; roles?: string[] };

  if (post.author_info && typeof post.author_info === 'object') {
    const authorInfo = post.author_info as Record<string, unknown>;
    author = {
      id: authorInfo.id?.toString() || '',
      name: typeof authorInfo.name === 'string' ? authorInfo.name : 'Anonymous',
      avatar:
        typeof authorInfo.profile_picture === 'string'
          ? authorInfo.profile_picture
          : authorInfo.avatar_urls && typeof authorInfo.avatar_urls === 'object'
            ? (authorInfo.avatar_urls as Record<string, string>)['96'] || ''
            : '',
      roles: Array.isArray(authorInfo.roles) ? (authorInfo.roles as string[]) : [],
    };
  } else {
    // Fall back to _embedded.author
    const embedded = post._embedded as { author?: AuthorObj[] } | undefined;
    const authorObj = Array.isArray(embedded?.author) ? embedded.author[0] : undefined;
    author = authorObj
      ? {
          id: 'id' in authorObj && authorObj.id ? authorObj.id.toString() : '',
          name:
            'name' in authorObj && typeof authorObj.name === 'string'
              ? authorObj.name
              : 'Anonymous',
          avatar:
            'profile_picture' in authorObj && typeof authorObj.profile_picture === 'string'
              ? authorObj.profile_picture
              : (authorObj.avatar_urls?.['96'] ?? ''),
          roles: [],
        }
      : { id: '', name: 'Anonymous', avatar: '', roles: [] };
  }
  return {
    id: post.id?.toString() ?? '',
    title:
      typeof post.title === 'object' && post.title && 'rendered' in post.title
        ? (post.title as { rendered: string }).rendered
        : ((post.title as string) ?? ''),
    content:
      typeof post.content === 'object' && post.content && 'rendered' in post.content
        ? (post.content as { rendered: string }).rendered
        : ((post.content as string) ?? ''),
    createdAt: typeof post.date === 'string' ? post.date : new Date().toISOString(),
    updatedAt:
      typeof post.modified === 'string'
        ? post.modified
        : typeof post.date === 'string'
          ? post.date
          : new Date().toISOString(),
    author,
    commentCount:
      typeof post.total_comments === 'number'
        ? post.total_comments
        : typeof post.total_comments === 'string'
          ? parseInt(post.total_comments, 10) || 0
          : 0,
    isResolved:
      typeof post.forum_meta === 'object' && post.forum_meta && 'is_resolved' in post.forum_meta
        ? ((post.forum_meta as { is_resolved?: boolean }).is_resolved ?? false)
        : false,
  };
}

// Transform WordPress comment data to Comment format
export function transformWPCommentToComment(wpComment: unknown): Comment {
  if (!wpComment || typeof wpComment !== 'object') {
    return {
      id: '0',
      content: '',
      createdAt: '',
      updatedAt: '',
      author: { id: '0', name: 'Anonymous', avatar: '' },
    };
  }
  const comment = wpComment as Record<string, unknown>;
  const commentId =
    'id' in comment && comment.id ? comment.id : 'comment_ID' in comment ? comment.comment_ID : '0';
  let content = '';
  if (typeof comment.content === 'object' && comment.content && 'rendered' in comment.content) {
    content = (comment.content as { rendered: string }).rendered;
  } else if (typeof comment.content === 'string') {
    content = comment.content;
  } else if ('comment_content' in comment && typeof comment.comment_content === 'string') {
    content = comment.comment_content;
  }
  const createdAt =
    typeof comment.date === 'string'
      ? comment.date
      : typeof comment.comment_date === 'string'
        ? comment.comment_date
        : new Date().toISOString();
  let authorName = 'Anonymous';
  let authorId = '0';
  let authorAvatar = '/images/avatar-placeholder.svg';
  let authorRoles: string[] = [];
  if ('author_info' in comment && comment.author_info && typeof comment.author_info === 'object') {
    const info = comment.author_info as Record<string, unknown>;
    authorName = 'name' in info && typeof info.name === 'string' ? info.name : 'Anonymous';
    authorId = 'id' in info && info.id ? String(info.id) : '0';
    if ('profile_picture' in info && typeof info.profile_picture === 'string') {
      authorAvatar = info.profile_picture;
    } else if (
      'avatar_urls' in info &&
      info.avatar_urls &&
      typeof info.avatar_urls === 'object' &&
      '96' in info.avatar_urls
    ) {
      authorAvatar =
        (info.avatar_urls as Record<string, string>)['96'] ?? '/images/avatar-placeholder.svg';
    }
    if ('roles' in info && Array.isArray(info.roles)) {
      authorRoles = info.roles as string[];
    }
  } else if (
    '_embedded' in comment &&
    comment._embedded &&
    typeof comment._embedded === 'object' &&
    'author' in comment._embedded &&
    Array.isArray((comment._embedded as { author?: AuthorObj[] }).author)
  ) {
    const embeddedAuthor = (comment._embedded as { author?: AuthorObj[] }).author?.[0];
    if (embeddedAuthor && typeof embeddedAuthor === 'object') {
      authorName =
        'name' in embeddedAuthor && typeof embeddedAuthor.name === 'string'
          ? embeddedAuthor.name
          : 'Anonymous';
      authorId = 'id' in embeddedAuthor && embeddedAuthor.id ? String(embeddedAuthor.id) : '0';
      if (
        'acf' in embeddedAuthor &&
        embeddedAuthor.acf &&
        typeof embeddedAuthor.acf === 'object' &&
        'profile_picture' in embeddedAuthor.acf
      ) {
        const profilePic = embeddedAuthor.acf.profile_picture;
        if (typeof profilePic === 'object' && profilePic && 'url' in profilePic) {
          authorAvatar = (profilePic as { url?: string }).url ?? '/images/avatar-placeholder.svg';
        } else if (typeof profilePic === 'string') {
          authorAvatar = profilePic;
        }
      } else if (
        'avatar_urls' in embeddedAuthor &&
        embeddedAuthor.avatar_urls &&
        typeof embeddedAuthor.avatar_urls === 'object' &&
        '96' in embeddedAuthor.avatar_urls
      ) {
        authorAvatar =
          (embeddedAuthor.avatar_urls as Record<string, string>)['96'] ??
          '/images/avatar-placeholder.svg';
      }
    }
  } else if (
    'author_avatar_urls' in comment &&
    comment.author_avatar_urls &&
    typeof comment.author_avatar_urls === 'object' &&
    '96' in comment.author_avatar_urls
  ) {
    authorName =
      'author_name' in comment && typeof comment.author_name === 'string'
        ? comment.author_name
        : 'Anonymous';
    authorId =
      'author_id' in comment && comment.author_id
        ? String(comment.author_id)
        : 'author' in comment && comment.author
          ? String(comment.author)
          : '0';
    authorAvatar =
      (comment.author_avatar_urls as Record<string, string>)['96'] ??
      '/images/avatar-placeholder.svg';
  } else {
    authorName =
      'comment_author' in comment && typeof comment.comment_author === 'string'
        ? comment.comment_author
        : 'author_name' in comment && typeof comment.author_name === 'string'
          ? comment.author_name
          : 'Anonymous';
    authorId =
      'user_id' in comment && comment.user_id
        ? String(comment.user_id)
        : 'author_id' in comment && comment.author_id
          ? String(comment.author_id)
          : 'author' in comment && comment.author
            ? String(comment.author)
            : '0';
  }
  return {
    id: commentId ? String(commentId) : '0',
    content: content,
    createdAt: createdAt,
    updatedAt: createdAt,
    author: {
      id: authorId,
      name: authorName,
      avatar: authorAvatar,
      roles: authorRoles,
    },
  };
}

type AuthorObj = {
  id?: string | number;
  name?: string;
  avatar_urls?: Record<string, string>;
  profile_picture?: string;
  acf?: { profile_picture?: string | { url?: string } };
};
