/**
 * Posts API Functions
 * Handles fetching, creating, and managing WordPress posts
 */

import { API_BASE } from './utils';

// Import directly from wordpress.ts to avoid circular dependencies
import { WPPost } from './wordpress';

/**
 * Event post interface with additional event-specific fields
 */
export interface EventPost extends WPPost {
  // ACF fields will be in the acf property
  acf?: {
    event_start_date?: string;
    event_end_date?: string;
    event_host?: string;
    event_host_logo?: {
      url?: string;
      alt?: string;
      sizes?: {
        [key: string]: string;
      };
      ID?: number;
    };
    event_location?: string;
    event_details_link?: string;
    event_registration_link?: string;
    [key: string]: unknown;
  };
  // Computed fields for easier access
  formatted_date?: string;
}

/**
 * Interface for creating a new post
 */
export interface CreatePostData {
  title: string;
  content: string;
  imageFile?: File | null;
  categories?: number[];
  vendorId?: number; // Optional vendor ID for posting as vendor
  acfFields?: Record<string, any>; // ACF fields for category-specific data
}

/**
 * Simplified post interface for sitemap generation
 */
export interface SitemapPost {
  slug: string;
  date: string;
  modified?: string;
  title: {
    rendered: string;
  };
  featured_media_url?: string;
}

/**
 * Get featured posts
 * @param page - Page number for pagination (1-based)
 * @param perPage - Number of posts per page
 * @param orderby - How to order posts (date, meta_value_num)
 * @param metaKey - Meta key for ordering (when orderby is meta_value_num)
 * @param excludeVendorPosts - Whether to exclude vendor posts from results
 * @returns Object containing posts array and pagination info
 */
export async function getFeaturedPosts(
  page: number = 1,
  perPage: number = 20,
  orderby: string = 'date',
  metaKey?: string,
  excludeVendorPosts: boolean = false
): Promise<{ posts: WPPost[]; totalPages: number; totalPosts: number }> {
  try {
    // Use the Next.js API proxy instead of direct WordPress requests
    // This ensures authentication cookies are properly forwarded

    // Build query parameters
    const params = new URLSearchParams({
      per_page: perPage.toString(),
      page: page.toString(),
      _embed: '1',
      orderby,
      order: 'desc',
    });

    if (metaKey) {
      params.set('meta_key', metaKey);
    }

    if (excludeVendorPosts) {
      params.set('exclude_vendor_posts', 'true');
    }

    // First try to fetch sticky (featured) posts
    let response = await fetch(`${API_BASE}/wp-proxy/posts?sticky=true&${params.toString()}`, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Ensure authentication cookies are sent
    });

    if (!response.ok) {
      console.warn(`Failed to fetch sticky posts: ${response.status}. Trying regular posts...`);
    } else {
      const posts = await response.json();
      // If we have sticky posts, return them
      if (Array.isArray(posts) && posts.length > 0) {
        // Get pagination info from headers
        const totalPages = parseInt(response.headers.get('X-WP-TotalPages') || '1', 10);
        const totalPosts = parseInt(
          response.headers.get('X-WP-Total') || posts.length.toString(),
          10
        );
        return { posts, totalPages, totalPosts };
      }
    }

    // If no sticky posts or the request failed, fetch regular posts
    response = await fetch(`${API_BASE}/wp-proxy/posts?${params.toString()}`, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Ensure authentication cookies are sent
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch posts: ${response.status}`);
    }

    const posts = await response.json();
    // Get pagination info from headers
    const totalPages = parseInt(response.headers.get('X-WP-TotalPages') || '1', 10);
    const totalPosts = parseInt(response.headers.get('X-WP-Total') || posts.length.toString(), 10);

    return {
      posts: Array.isArray(posts) ? posts : [],
      totalPages,
      totalPosts,
    };
  } catch (error) {
    console.error('Error fetching posts:', error);
    return { posts: [], totalPages: 0, totalPosts: 0 };
  }
}

/**
 * Get post by slug
 */
export async function getPostBySlug(slug: string): Promise<WPPost | null> {
  try {
    // Use the Next.js API proxy instead of direct WordPress requests
    const response = await fetch(
      `${API_BASE}/wp-proxy/posts?slug=${encodeURIComponent(slug)}&_embed=1`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Ensure authentication cookies are sent
        next: {
          revalidate: 30, // Revalidate every 30 seconds
          tags: [`post-${slug}`, 'posts'], // Cache tags for targeted revalidation
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch post: ${response.status} ${response.statusText}`);
    }

    const posts = await response.json();
    return Array.isArray(posts) && posts.length > 0 ? posts[0] : null;
  } catch (error) {
    console.error('Error fetching WordPress post:', error);
    return null;
  }
}

/**
 * Create a new post
 */
export async function createPost(data: CreatePostData): Promise<WPPost | null> {
  try {
    // DEBUG LOG
    console.log('[createPost API] Received data:', {
      title: data.title,
      content: data.content.substring(0, 50) + '...',
      vendorId: data.vendorId,
      categories: data.categories,
      hasImageFile: !!data.imageFile,
      acfFields: data.acfFields,
    });

    // Use FormData to handle file uploads properly
    const formData = new FormData();
    formData.append('title', data.title);
    formData.append('content', data.content);
    formData.append('status', 'publish');

    // Add categories if provided
    if (data.categories && data.categories.length > 0) {
      data.categories.forEach((categoryId, index) => {
        formData.append(`categories[${index}]`, categoryId.toString());
      });
    }

    // Add vendor ID if posting as vendor
    if (data.vendorId) {
      console.log('[createPost API] Adding vendor_id to FormData:', data.vendorId);
      formData.append('vendor_id', data.vendorId.toString());
    }

    // Add featured media if provided
    if (data.imageFile) {
      formData.append('featured_media', data.imageFile);
    }

    // Add ACF fields if provided
    if (data.acfFields) {
      // Separate file fields from regular fields
      const regularFields: Record<string, any> = {};
      const fileFields: Record<string, File> = {};

      Object.entries(data.acfFields).forEach(([key, value]) => {
        if (value instanceof File) {
          fileFields[key] = value;
          // Append file to formData with ACF field name
          formData.append(`acf_file_${key}`, value);
        } else {
          regularFields[key] = value;
        }
      });

      // Only append regular fields as JSON
      if (Object.keys(regularFields).length > 0) {
        formData.append('acf_fields', JSON.stringify(regularFields));
      }
    }

    // Make sure we're using the correct endpoint and credentials
    const response = await fetch(`${API_BASE}/wp-proxy/posts/create`, {
      method: 'POST',
      // Let the browser set the Content-Type header with proper boundary
      // Don't set "Content-Type": "application/json" for FormData
      body: formData,
      credentials: 'include', // Always include credentials for authentication
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Create post error:', response.status, errorData);
      throw new Error(`Failed to create post: ${response.status} ${errorData.message || ''}`);
    }

    const responseData = await response.json();
    return responseData;
  } catch (error) {
    console.error('Error creating post:', error);
    return null;
  }
}

// Cache for category IDs to avoid repeated API calls
// const categoryCache = new Map<string, number>();

/**
 * Get posts by category slug or type
 * @param categorySlug - The slug of the category to filter by (e.g., 'news', 'leadership')
 * @param limit - Maximum number of posts to return
 * @param page - Page number for pagination (1-based)
 * @param orderby - How to order posts (date, meta_value_num)
 * @param metaKey - Meta key for ordering (when orderby is meta_value_num)
 * @returns Object containing posts array and pagination info
 */
export async function getPostsByCategory(
  categorySlug: string,
  limit: number = 20,
  page: number = 1,
  orderby: string = 'date',
  metaKey?: string
): Promise<{ posts: WPPost[]; totalPages: number; totalPosts: number }> {
  try {
    // Build query parameters
    const params = new URLSearchParams({
      per_page: limit.toString(),
      page: page.toString(),
      _embed: '1',
      orderby,
      order: 'desc',
    });

    if (metaKey) {
      params.set('meta_key', metaKey);
    }

    // Use the optimized endpoint that handles category lookup and caching server-side
    const response = await fetch(
      `${API_BASE}/wp-proxy/posts/by-category/${encodeURIComponent(categorySlug)}?${params.toString()}`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );

    if (!response.ok) {
      console.warn(
        `[getPostsByCategory] Failed to fetch posts for category '${categorySlug}': ${response.status}`
      );
      return { posts: [], totalPages: 0, totalPosts: 0 };
    }

    const posts = await response.json();

    // Get total pages and posts from headers
    const totalPages = parseInt(response.headers.get('X-WP-TotalPages') || '0', 10);
    const totalPosts = parseInt(response.headers.get('X-WP-Total') || '0', 10);

    return {
      posts: Array.isArray(posts) ? posts : [],
      totalPages,
      totalPosts,
    };
  } catch (error) {
    console.error(`Error fetching posts for category '${categorySlug}':`, error);
    return { posts: [], totalPages: 0, totalPosts: 0 };
  }
}

export async function getEventPosts(limit: number = 3): Promise<EventPost[]> {
  try {
    // Use the Next.js API proxy instead of direct WordPress requests
    const categoryResponse = await fetch(`${API_BASE}/wp-proxy/categories?slug=event`, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!categoryResponse.ok) {
      console.warn(`Failed to fetch event category: ${categoryResponse.status}`);
      return [];
    }

    const categories = await categoryResponse.json();
    if (!Array.isArray(categories) || categories.length === 0) {
      console.warn('Event category not found');
      return [];
    }

    const eventCategoryId = categories[0].id;

    // Fetch posts with the event category using the proxy
    const response = await fetch(
      `${API_BASE}/wp-proxy/posts?categories=${eventCategoryId}&per_page=${limit}&_embed&orderby=date&order=desc`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      }
    );

    if (!response.ok) {
      console.warn(`Failed to fetch event posts: ${response.status}`);
      return [];
    }

    const posts = await response.json();
    if (!Array.isArray(posts)) {
      return [];
    }

    // Process event posts to extract event-specific data
    return posts.map((post) => {
      const eventPost: EventPost = { ...post };

      // Format the date based on start and end dates if available
      if (post.acf) {
        // Handle ACF fields - knowing that they're returned as arrays with url property
        // based on the project's ACF configuration (from the memory)

        // Format the date string
        if (post.acf.event_start_date) {
          if (post.acf.event_end_date) {
            // If both start and end dates are available
            eventPost.formatted_date = `${post.acf.event_start_date} - ${post.acf.event_end_date}`;
          } else {
            // If only start date is available
            eventPost.formatted_date = post.acf.event_start_date;
          }
        } else {
          // Fallback to post date if no event date is specified
          eventPost.formatted_date = new Date(post.date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          });
        }

        // Handle host logo if it's an ACF image field (which returns an array with url)
        if (post.acf.event_host_logo && typeof post.acf.event_host_logo === 'object') {
          // Make sure the host logo URL is properly extracted based on ACF's return_format
          if (!post.acf.event_host_logo.url && post.acf.event_host_logo.ID) {
            // If it's just the ID, we might need to get the URL separately
            // This shouldn't happen with our ACF setup, but just in case
            console.warn('Host logo is missing URL property:', post.acf.event_host_logo);
          }
        }
      }

      return eventPost;
    });
  } catch (error) {
    console.error('Error fetching event posts:', error);
    return [];
  }
}

/**
 * Get all posts for sitemap generation (server-side only)
 * Fetches all posts efficiently for XML sitemap
 */
export async function getPostsForSitemap(): Promise<SitemapPost[]> {
  try {
    // Determine WordPress API URL with proper fallbacks
    let wpApiUrl =
      process.env.WORDPRESS_API_URL ||
      process.env.NEXT_PUBLIC_WORDPRESS_URL ||
      'https://mytourismiq.wpenginepowered.com';

    // Ensure HTTPS for production
    if (wpApiUrl.includes('wpenginepowered.com') && !wpApiUrl.startsWith('https://')) {
      wpApiUrl = wpApiUrl.replace('http://', 'https://');
    }

    // Clean up URL format
    wpApiUrl = wpApiUrl.endsWith('/') ? wpApiUrl.slice(0, -1) : wpApiUrl;

    console.log('Fetching posts for sitemap from:', wpApiUrl);

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add authentication if credentials are available
    if (process.env.WORDPRESS_USERNAME && process.env.WORDPRESS_APPLICATION_PASSWORD) {
      headers['Authorization'] = `Basic ${Buffer.from(
        `${process.env.WORDPRESS_USERNAME}:${process.env.WORDPRESS_APPLICATION_PASSWORD}`
      ).toString('base64')}`;
    }

    const response = await fetch(
      `${wpApiUrl}/wp-json/wp/v2/posts?per_page=100&_fields=slug,date,modified,title,featured_media&_embed=wp:featuredmedia`,
      {
        headers,
        next: { revalidate: 3600 }, // Cache for 1 hour
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch posts for sitemap: ${response.status}`);
    }

    const posts = await response.json();

    return posts.map(
      (post: any): SitemapPost => ({
        slug: post.slug,
        date: post.date,
        modified: post.modified,
        title: post.title,
        featured_media_url: post._embedded?.['wp:featuredmedia']?.[0]?.source_url,
      })
    );
  } catch (error) {
    console.error('Error fetching posts for sitemap:', error);
    return [];
  }
}
