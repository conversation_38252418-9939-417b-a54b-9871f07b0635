import { getApiUrl, API_BASE } from './utils';

export interface RfpMeta {
  rfp_status: string;
  rfp_organization: string;
  rfp_source_url: string;
  rfp_deadline: string;
}

export interface Rfp {
  id: number;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  excerpt: {
    rendered: string;
  };
  slug: string;
  status: string;
  date: string;
  modified: string;
  author: number;
  featured_media: number;
  featured_media_url?: string;
  rfp_meta: RfpMeta;
  rfp_categories?: string[];
  rfp_states?: string[];
  rfp_countries?: string[];
  _embedded?: {
    author?: Array<{
      id: number;
      name: string;
      avatar_urls: {
        '96': string;
      };
    }>;
    'wp:featuredmedia'?: Array<{
      id: number;
      source_url: string;
      alt_text: string;
    }>;
  };
}

export interface RfpsSearchParams {
  search?: string;
  per_page?: number;
  page?: number;
  category?: string;
  status?: string;
  organization?: string;
  state?: string;
  country?: string;
  orderby?: 'date' | 'title' | 'modified' | 'status_then_date' | 'id';
  order?: 'asc' | 'desc';
}

export interface RfpsResponse {
  rfps: Rfp[];
  total: number;
  pages: number;
  page: number;
  per_page: number;
}

/**
 * Search RFPs with filters
 */
export async function searchRfps(params: RfpsSearchParams = {}): Promise<RfpsResponse> {
  const searchParams = new URLSearchParams();

  // Add search parameters
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });

  const response = await fetch(`${API_BASE}/wp-proxy/rfps?${searchParams.toString()}`, {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    cache: 'no-store',
  });

  if (!response.ok) {
    throw new Error(`Failed to search RFPs: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get all RFPs with basic parameters
 */
export async function getRfps(
  params: {
    per_page?: number;
    page?: number;
    _embed?: boolean;
    status?: string;
  } = {}
): Promise<Rfp[]> {
  const searchParams = new URLSearchParams();

  // Default parameters
  searchParams.append('per_page', (params.per_page || 20).toString());
  searchParams.append('page', (params.page || 1).toString());

  if (params._embed) {
    searchParams.append('_embed', 'true');
  }

  if (params.status) {
    searchParams.append('status', params.status);
  }

  // Use proxy route for client-side requests
  const apiUrl =
    typeof window !== 'undefined'
      ? `${API_BASE}/wp-proxy/rfps?${searchParams.toString()}`
      : `${getApiUrl()}/wp/v2/rfps?${searchParams.toString()}`;

  const response = await fetch(apiUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch RFPs: ${response.status}`);
  }

  return response.json();
}

/**
 * Get a single RFP by slug
 */
export async function getRfpBySlug(slug: string): Promise<Rfp> {
  // Use proxy route for client-side requests
  const apiUrl =
    typeof window !== 'undefined'
      ? `${API_BASE}/wp-proxy/rfps/${slug}`
      : `${getApiUrl()}/tourismiq/v1/rfps/slug/${slug}`;

  const response = await fetch(apiUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch RFP: ${response.status}`);
  }

  return response.json();
}

/**
 * Get a single RFP by ID
 */
export async function getRfpById(id: number, embed = true): Promise<Rfp> {
  const searchParams = new URLSearchParams();
  if (embed) {
    searchParams.append('_embed', 'true');
  }

  // Use proxy route for client-side requests
  const apiUrl =
    typeof window !== 'undefined'
      ? `${API_BASE}/wp-proxy/rfps/by-id/${id}?${searchParams.toString()}`
      : `${getApiUrl()}/wp/v2/rfps/${id}?${searchParams.toString()}`;

  const response = await fetch(apiUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch RFP: ${response.status}`);
  }

  return response.json();
}

/**
 * Helper function to get unique values for filter options
 */
export async function getRfpFilterOptions(): Promise<{
  categories: Array<{ value: string; label: string }>;
  statuses: Array<{ value: string; label: string }>;
  countries: string[];
  states: string[];
  organizations: string[];
}> {
  // Get a large sample of RFPs to extract filter options
  const rfps = await getRfps({ per_page: 100 });

  // Get RFP categories from taxonomy
  let categories: Array<{ value: string; label: string }> = [];
  try {
    const categoriesResponse = await fetch('/api/wp-proxy/rfps/categories?per_page=100', {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (categoriesResponse.ok) {
      const categoriesData = await categoriesResponse.json();
      categories = categoriesData.map((cat: any) => ({
        value: cat.slug,
        label: cat.name,
      }));
    }
  } catch (error) {
    console.error('Failed to fetch RFP categories:', error);
    categories = [];
  }

  // Get RFP states from taxonomy
  let states: string[] = [];
  try {
    const statesResponse = await fetch('/api/wp-proxy/rfps/states?per_page=100', {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (statesResponse.ok) {
      const statesData = await statesResponse.json();
      states = statesData.map((state: any) => state.name).sort();
    }
  } catch (error) {
    console.error('Failed to fetch RFP states:', error);
    states = [];
  }

  // Get RFP countries from taxonomy
  let countries: string[] = [];
  try {
    const countriesResponse = await fetch('/api/wp-proxy/rfps/countries?per_page=100', {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (countriesResponse.ok) {
      const countriesData = await countriesResponse.json();
      countries = countriesData.map((country: any) => country.name).sort();
    }
  } catch (error) {
    console.error('Failed to fetch RFP countries:', error);
    countries = [];
  }

  const statuses = [
    { value: 'NEW', label: 'New' },
    { value: 'EXPIRED', label: 'Expired' },
  ];

  // Extract unique organizations from RFP data
  const organizations = Array.from(
    new Set(rfps.map((rfp) => rfp.rfp_meta?.rfp_organization).filter(Boolean))
  ).sort();

  return {
    categories,
    statuses,
    countries,
    states,
    organizations,
  };
}

/**
 * Format RFP deadline for display
 */
export function formatRfpDeadline(deadline: string): string {
  if (!deadline) return '';

  const date = new Date(deadline);
  const now = new Date();
  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Tomorrow';
  } else if (diffDays > 0 && diffDays <= 7) {
    return `${diffDays} days left`;
  } else {
    return date.toLocaleDateString();
  }
}

/**
 * Get category label from value
 */
export function getCategoryLabel(value: string): string {
  return value;
}

/**
 * State abbreviation to full name mapping
 */
const STATE_MAPPING: Record<string, string> = {
  AL: 'Alabama',
  AK: 'Alaska',
  AZ: 'Arizona',
  AR: 'Arkansas',
  CA: 'California',
  CO: 'Colorado',
  CT: 'Connecticut',
  DE: 'Delaware',
  FL: 'Florida',
  GA: 'Georgia',
  HI: 'Hawaii',
  ID: 'Idaho',
  IL: 'Illinois',
  IN: 'Indiana',
  IA: 'Iowa',
  KS: 'Kansas',
  KY: 'Kentucky',
  LA: 'Louisiana',
  ME: 'Maine',
  MD: 'Maryland',
  MA: 'Massachusetts',
  MI: 'Michigan',
  MN: 'Minnesota',
  MS: 'Mississippi',
  MO: 'Missouri',
  MT: 'Montana',
  NE: 'Nebraska',
  NV: 'Nevada',
  NH: 'New Hampshire',
  NJ: 'New Jersey',
  NM: 'New Mexico',
  NY: 'New York',
  NC: 'North Carolina',
  ND: 'North Dakota',
  OH: 'Ohio',
  OK: 'Oklahoma',
  OR: 'Oregon',
  PA: 'Pennsylvania',
  RI: 'Rhode Island',
  SC: 'South Carolina',
  SD: 'South Dakota',
  TN: 'Tennessee',
  TX: 'Texas',
  UT: 'Utah',
  VT: 'Vermont',
  VA: 'Virginia',
  WA: 'Washington',
  WV: 'West Virginia',
  WI: 'Wisconsin',
  WY: 'Wyoming',
  DC: 'District of Columbia',
};

/**
 * Get full state name from abbreviation or return original value
 */
export function getFullStateName(stateInput: string): string {
  const upperInput = stateInput.toUpperCase();
  return STATE_MAPPING[upperInput] || stateInput;
}

/**
 * Get state abbreviation from full name or return original value
 */
export function getStateAbbreviation(stateInput: string): string {
  const normalized = stateInput.toLowerCase();
  for (const [abbrev, fullName] of Object.entries(STATE_MAPPING)) {
    if (fullName.toLowerCase() === normalized) {
      return abbrev;
    }
  }
  return stateInput;
}

/**
 * Check if search term matches a state (abbreviation or full name)
 */
export function matchesState(searchTerm: string, stateName: string): boolean {
  const searchLower = searchTerm.toLowerCase();
  const stateLower = stateName.toLowerCase();

  // Direct match
  if (stateLower.includes(searchLower)) {
    return true;
  }

  // Check if search term is an abbreviation and state matches full name
  const fullName = getFullStateName(searchTerm);
  if (fullName !== searchTerm && stateLower.includes(fullName.toLowerCase())) {
    return true;
  }

  // Check if search term is a full name and state matches abbreviation
  const abbreviation = getStateAbbreviation(searchTerm);
  if (abbreviation !== searchTerm && stateLower.includes(abbreviation.toLowerCase())) {
    return true;
  }

  return false;
}

/**
 * Get status label from value
 */
export function getStatusLabel(value: string): string {
  const statusMap: Record<string, string> = {
    NEW: 'New',
    EXPIRED: 'Expired',
    new: 'New', // fallback for lowercase
    expired: 'Expired', // fallback for lowercase
  };

  return statusMap[value] || value;
}
