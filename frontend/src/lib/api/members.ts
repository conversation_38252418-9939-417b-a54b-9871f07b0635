import { getCredentialsOption } from './wordpress';
import { API_BASE } from './utils';

export interface FetchMembersParams {
  search?: string;
  category?: string;
  location?: string;
  page?: number;
  perPage?: number;
}

export interface Member {
  id: number;
  name: string;
  username: string;
  firstName: string;
  lastName: string;
  slug: string;
  roles: string[];
  role: string;
  avatar: string;
  bio: string;
  location: string;
  featured: boolean;
  categories: string[];
  coverImage: string;
  job_title: string;
  company: string;
  socialLinks: {
    website: string;
    twitter: string;
    facebook: string;
    linkedin: string;
    instagram: string;
  };
  contactInfo: {
    email: string;
    phone: string;
    website: string;
  };
  user_registered: string;
}

export interface FetchMembersResponse {
  members: Member[];
  totalPages: number;
  totalMembers: number;
}

/**
 * Fetches members from the WordPress REST API
 */
export async function fetchMembers(params: FetchMembersParams = {}): Promise<FetchMembersResponse> {
  return await fetchMembersFromStandardEndpoint(params);
}

/**
 * Fetch members from the standard WordPress users endpoint
 */
async function fetchMembersFromStandardEndpoint(
  params: FetchMembersParams = {}
): Promise<FetchMembersResponse> {
  try {
    const { search = '', category, location, page = 1, perPage = 12 } = params;

    // Build the query parameters for the proxy endpoint
    const queryParams = new URLSearchParams({
      per_page: perPage.toString(),
      page: page.toString(),
      orderby: 'registered',
      order: 'desc', // Newest members first
      context: 'edit',
      _embed: '1',
    });

    if (search) {
      queryParams.append('search', search);
    }

    // Add fields as separate parameters
    const fields = [
      'id',
      'name',
      'slug',
      'email',
      'url',
      'description',
      'registered',
      'roles',
      'capabilities',
      'first_name',
      'last_name',
      'username',
      'user_nicename',
      'user_login',
      'avatar_urls',
      'acf',
    ];

    fields.forEach((field) => {
      queryParams.append('_fields[]', field);
    });

    // Use the Next.js API proxy instead of direct WordPress requests
    const apiFullUrl = `${API_BASE}/wp-proxy/members?${queryParams.toString()}`;

    const response = await fetch(apiFullUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: getCredentialsOption(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch users: ${response.status}`);
    }

    // Get total counts from headers
    const totalPages = parseInt(response.headers.get('X-WP-TotalPages') || '0', 10);

    // Get WordPress users
    const wpUsers = await response.json();

    // Transform all users first
    const members = wpUsers
      .map((user: unknown) => {
        // Transform to consistent member format
        return transformWPUserToMember(user);
      })
      .filter((member: Member | null): member is Member => member !== null);

    // Apply client-side filtering
    let filteredMembers = [...members];

    if (category && category !== 'All Categories') {
      filteredMembers = filteredMembers.filter((member) => {
        if (!member.categories) return false;
        return member.categories.some(
          (cat: string) => cat.toLowerCase() === category.toLowerCase()
        );
      });
    }

    if (location && location !== 'All Locations') {
      filteredMembers = filteredMembers.filter((member) => {
        if (!member.location) return false;
        return member.location.toLowerCase().includes(location.toLowerCase());
      });
    }

    return {
      members: filteredMembers,
      totalPages,
      totalMembers: filteredMembers.length,
    };
  } catch (err: unknown) {
    console.error('Error fetching from standard endpoint:', err);

    return {
      members: [],
      totalPages: 1,
      totalMembers: 0,
    };
  }
}

/**
 * Transforms a WordPress user object into our Member format
 */
export function transformWPUserToMember(wpUser: unknown): Member | null {
  try {
    const user = wpUser as import('./wordpress').WPUser;
    // Safely get user roles
    let roles: string[] = [];
    if (Array.isArray(user.roles)) {
      roles = user.roles;
    } else if ((user as { role?: string }).role) {
      roles = [(user as { role?: string }).role!];
    } else {
      console.warn('No roles found for user, defaulting to member role', user.id);
      roles = ['member'];
    }

    // Determine if user is a founder based on WordPress role
    const isFounder = roles.includes('founder');

    // Extract first and last name from various possible sources
    const nameParts = typeof user.name === 'string' ? user.name.split(' ') : [];
    const firstName = String(
      user.first_name || user.acf?.first_name || (nameParts.length > 0 ? nameParts[0] : '')
    );
    const lastName = String(
      user.last_name ||
        user.acf?.last_name ||
        (nameParts.length > 1 ? nameParts.slice(1).join(' ') : '')
    );

    // Get username from various possible sources
    const username =
      (user as { username?: string }).username ??
      (user as { user_nicename?: string }).user_nicename ??
      user.slug ??
      `user-${user.id}`;

    // Get avatar from various possible sources
    let avatar = '/images/avatar-placeholder.svg';
    if (typeof user.acf?.profile_picture === 'string') {
      avatar = user.acf.profile_picture;
    } else if (typeof user.acf?.profile_picture === 'object' && user.acf?.profile_picture?.url) {
      avatar = user.acf.profile_picture.url;
    } else if (user.avatar_urls?.['96']) {
      avatar = user.avatar_urls['96'];
    }

    // Safely get ACF fields with defaults
    const acf = user.acf || {};
    const socialLinks = {
      website: String(user.url || acf.website || ''),
      twitter: String(acf.social_twitter || ''),
      facebook: String(acf.social_facebook || ''),
      linkedin: String(acf.social_linkedin || ''),
      instagram: String(acf.social_instagram || ''),
    };

    const contactInfo = {
      email: String(user.email || acf.email || ''),
      phone: String(acf.phone || ''),
      website: String(user.url || acf.website || ''),
    };

    const member = {
      id: user.id,
      name: user.name ?? username,
      username,
      firstName,
      lastName,
      slug: user.slug ?? `member-${user.id}`,
      roles,
      role: isFounder ? 'Founder' : 'Member',
      avatar,
      bio: user.description ?? acf.bio ?? '',
      location: acf.location ?? 'Unknown',
      featured: Boolean(isFounder || acf.featured),
      categories: Array.isArray(acf.categories) ? acf.categories : ['Tourism Professional'],
      coverImage:
        acf.cover_image && typeof acf.cover_image === 'object' && 'url' in acf.cover_image
          ? ((acf.cover_image as { url?: string }).url ?? '')
          : typeof acf.cover_image === 'string'
            ? acf.cover_image
            : '',
      job_title: String(acf.job_title ?? ''),
      company: String(acf.company ?? ''),
      socialLinks,
      contactInfo,
      user_registered:
        'registered' in user && typeof (user as { registered?: string }).registered === 'string'
          ? (user as { registered?: string }).registered!
          : '',
    };

    return member;
  } catch (err: unknown) {
    console.error('Error transforming user:', err);
    return null;
  }
}
