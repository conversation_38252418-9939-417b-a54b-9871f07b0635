export interface Bookmark {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  url: string;
  category: string;
  featured_image?: string | undefined;
  bookmarked_at: string;
  author: string;
}

// Simple localStorage-based bookmark management
export const bookmarksAPI = {
  // Get storage key for current user
  getStorageKey: (userId?: number): string => {
    return userId ? `bookmarks_user_${userId}` : 'bookmarks_guest';
  },

  // Get all bookmarked post IDs for a user
  getBookmarkedPostIds: (userId?: number): number[] => {
    if (typeof window === 'undefined') return [];

    try {
      const storageKey = bookmarksAPI.getStorageKey(userId);
      const stored = localStorage.getItem(storageKey);
      const bookmarks = stored ? JSON.parse(stored) : {};

      return Object.keys(bookmarks)
        .filter((postId) => bookmarks[parseInt(postId)] === true)
        .map((postId) => parseInt(postId));
    } catch (error) {
      console.error('Error getting bookmarked post IDs:', error);
      return [];
    }
  },

  // Check if a post is bookmarked
  isBookmarked: (postId: number, userId?: number): boolean => {
    if (typeof window === 'undefined') return false;

    try {
      const storageKey = bookmarksAPI.getStorageKey(userId);
      const stored = localStorage.getItem(storageKey);
      const bookmarks = stored ? JSON.parse(stored) : {};

      return bookmarks[postId] === true;
    } catch (error) {
      console.error('Error checking bookmark status:', error);
      return false;
    }
  },

  // Toggle bookmark for a post
  toggleBookmark: (postId: number, userId?: number): boolean => {
    if (typeof window === 'undefined') return false;

    try {
      const storageKey = bookmarksAPI.getStorageKey(userId);
      const stored = localStorage.getItem(storageKey);
      const bookmarks = stored ? JSON.parse(stored) : {};

      const currentState = bookmarks[postId] === true;
      const newState = !currentState;

      if (newState) {
        bookmarks[postId] = true;
      } else {
        delete bookmarks[postId];
      }

      localStorage.setItem(storageKey, JSON.stringify(bookmarks));

      // Dispatch event for other components
      window.dispatchEvent(
        new CustomEvent('bookmarkChanged', {
          detail: { postId, bookmarked: newState },
        })
      );

      return newState;
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      return false;
    }
  },

  // Clear all bookmarks for a user
  clearBookmarks: (userId?: number): void => {
    if (typeof window === 'undefined') return;

    try {
      const storageKey = bookmarksAPI.getStorageKey(userId);
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.error('Error clearing bookmarks:', error);
    }
  },
};
