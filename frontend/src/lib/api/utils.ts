/**
 * API Utility Functions
 * Common utilities used across the API modules
 */

// Helper to get the WordPress API URL
export function getApiUrl(): string {
  let apiUrl = process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://tourismiq.local';
  apiUrl = apiUrl.replace(/\/+$/, ''); // Remove trailing slashes
  if (!apiUrl.endsWith('/wp-json')) {
    apiUrl += '/wp-json';
  }
  return apiUrl;
}

// Helper to get API base URL for Next.js API routes
function getApiBase(): string {
  // Client-side: use relative URL
  if (typeof window !== 'undefined') {
    return '/api';
  }

  // Server-side: construct absolute URL for WPEngine
  const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';

  // Try to get the host from environment variables
  let host = 'localhost:3000'; // fallback for development

  if (process.env.NEXT_PUBLIC_APP_URL) {
    // Use the explicitly set app URL if available
    host = process.env.NEXT_PUBLIC_APP_URL.replace(/^https?:\/\//, '');
  } else if (process.env.NODE_ENV === 'production') {
    // For WPEngine production, use the frontend domain
    host = 'hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com';
  }

  return `${protocol}://${host}/api`;
}

export const API_BASE = getApiBase();

// Helper to safely access window object
export const isClient = typeof window !== 'undefined';

// Helper to get credentials option based on environment
export function getCredentialsOption(): RequestCredentials {
  return 'include';
}

// Format a date string
// Use a consistent format to avoid hydration errors between server and client
export function formatDate(dateString: string): string {
  const date = new Date(dateString);

  // Use a manual format to ensure consistency between server and client
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const month = months[date.getUTCMonth()];
  const day = date.getUTCDate();
  const year = date.getUTCFullYear();

  return `${month} ${day}, ${year}`;
}

// Strip HTML tags from a string and decode HTML entities
export function stripHtml(html: string): string {
  // Use a consistent approach for both server and client to avoid hydration errors
  return html
    .replace(/<[^>]*>?/gm, '')
    .replace(/\[.*?\]/g, '')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#0?39;/g, "'") // Handles both &#39; and &#039;
    .replace(/&#0?38;/g, '&') // Handles both &#38; and &#038;
    .replace(/&#8217;/g, "'")
    .replace(/&#8211;/g, '–')
    .replace(/&#8212;/g, '—')
    .replace(/&#8216;/g, "'")
    .replace(/&#8220;/g, '"')
    .replace(/&#8221;/g, '"')
    .replace(/&#8230;/g, '…')
    .trim();
}

// Decode HTML entities without stripping HTML tags
export function decodeHtml(html: string): string {
  // Use a consistent approach for both server and client to avoid hydration errors
  return html
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#0?39;/g, "'") // Handles both &#39; and &#039;
    .replace(/&#0?38;/g, '&') // Handles both &#38; and &#038;
    .replace(/&#8217;/g, "'")
    .replace(/&#8211;/g, '–')
    .replace(/&#8212;/g, '—')
    .replace(/&#8216;/g, "'")
    .replace(/&#8220;/g, '"')
    .replace(/&#8221;/g, '"')
    .replace(/&#8230;/g, '…');
}
