/**
 * Authentication API Functions
 * Handles user authentication, login, logout, and session management
 */

import { API_BASE, isClient } from './utils';

// Import directly from wordpress.ts to avoid circular dependencies
import { User, WPUser } from './wordpress';
import { transformWPUserToUser } from './wordpress';

/**
 * Get current authenticated user
 */
export async function getCurrentUser(): Promise<WPUser | null> {
  if (!isClient) {
    return null;
  }

  try {
    // First try the Next.js API route
    const response = await fetch(`${API_BASE}/user/me`, {
      method: 'GET',
      credentials: 'include', // Always include credentials for authentication
    });

    if (!response.ok) {
      if (response.status === 401) {
        console.warn('User not authenticated (401 from /user/me)');
        return null;
      }

      // If the Next.js API route fails, try the WordPress API directly
      console.warn(
        `Failed to fetch user data from Next.js API: ${response.status}, trying WordPress API directly...`
      );

      const wpResponse = await fetch(`${API_BASE}/wp-proxy/auth/status`, {
        method: 'GET',
        credentials: 'include',
      });

      if (!wpResponse.ok) {
        console.warn('Failed to fetch user data from WordPress API:', wpResponse.status);
        return null;
      }

      const wpData = await wpResponse.json();
      if (wpData && wpData.user) {
        return wpData.user;
      }

      return null;
    }

    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Error fetching current user:', error);
    return null;
  }
}

/**
 * Check authentication status
 */
export async function checkAuthStatus(): Promise<{
  isAuthenticated: boolean;
  user: User | null;
}> {
  try {
    const response = await fetch(`${API_BASE}/wp-proxy/auth/status`, {
      method: 'GET',
      credentials: 'include', // Always include credentials for authentication
    });

    if (!response.ok) {
      console.warn('Auth status check failed with status:', response.status);
      return { isAuthenticated: false, user: null };
    }

    const data = await response.json();

    // Check for any of the authentication flags that might be present
    const isUserAuthenticated = data.isAuthenticated || data.isLoggedIn || data.loggedIn;

    if (!isUserAuthenticated || !data.user) {
      return { isAuthenticated: false, user: null };
    }

    // Transform the WordPress user to our User format
    const user = transformWPUserToUser(data.user);

    return {
      isAuthenticated: true,
      user,
    };
  } catch (error) {
    console.error('Error checking auth status:', error);
    return { isAuthenticated: false, user: null };
  }
}

/**
 * Login user
 */
export async function loginUser(
  username: string,
  password: string
): Promise<{
  success: boolean;
  user?: User;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
      credentials: 'include', // Always include credentials for authentication
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Login failed with status:', response.status, errorData);
      return {
        success: false,
        error: errorData.message || `Login failed: ${response.status}`,
      };
    }

    const data = await response.json();

    // If the login was successful but no user data was returned
    if (!data.user) {
      // Try to fetch the user data separately
      const userResult = await getCurrentUser();
      if (userResult) {
        const user = transformWPUserToUser(userResult);
        return {
          success: true,
          user,
        };
      }

      return {
        success: true,
        error: 'Login successful but no user data returned',
      };
    }

    // Transform the WordPress user to our User format
    const user = transformWPUserToUser(data.user);

    return {
      success: true,
      user,
    };
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      error: 'An error occurred',
    };
  }
}

/**
 * Logout user
 */
export async function logoutUser(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE}/auth/logout`, {
      method: 'POST',
      credentials: 'include', // Always include credentials for authentication
    });

    if (!response.ok) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Request password reset
 */
export async function requestPasswordReset(email: string): Promise<{
  success: boolean;
  message?: string;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_BASE}/wp-proxy/auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || 'Failed to process password reset request',
      };
    }

    return {
      success: true,
      message:
        data.message ||
        'If an account exists with this email address, you will receive a password reset link shortly.',
    };
  } catch (error) {
    console.error('Password reset error:', error);
    return {
      success: false,
      error: 'An error occurred while processing your request',
    };
  }
}
