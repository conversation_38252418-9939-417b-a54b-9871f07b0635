/**
 * IQ Score System API Functions
 * Handles all communication with the IQ score and leaderboard endpoints
 */

export interface Rank {
  name: string;
  min_points: number;
  max_points: number;
  member_badge: string;
  founder_badge: string;
}

export interface UserIQData {
  user_id: number;
  username: string;
  display_name: string;
  iq_score: number;
  rank: Rank;
  badge: string;
  position: number;
}

export interface LeaderboardEntry {
  id: number;
  username: string;
  display_name: string;
  iq_score: number;
  rank: Rank;
  profile_picture?: string;
  is_founder: boolean;
  badge: string;
  position: number;
}

export interface LeaderboardResponse {
  success: boolean;
  data: LeaderboardEntry[];
  total: number;
}

export interface UserIQResponse {
  success: boolean;
  data: UserIQData;
}

export interface RanksResponse {
  success: boolean;
  data: Rank[];
}

class IQScoreAPI {
  private baseUrl: string;

  constructor() {
    this.baseUrl =
      process.env.NEXT_PUBLIC_WORDPRESS_URL || process.env.NEXT_PUBLIC_WORDPRESS_API_URL || '';
  }

  /**
   * Get leaderboard data
   */
  async getLeaderboard(
    limit: number = 20,
    forceRefresh: boolean = false
  ): Promise<LeaderboardResponse> {
    try {
      // Use Next.js API proxy to avoid CORS issues
      const cacheBuster = forceRefresh ? `&force_refresh=1&_=${Date.now()}` : `&_=${Date.now()}`;
      const response = await fetch(
        `/api/wp-proxy/iq-score/leaderboard?limit=${limit}${cacheBuster}`,
        {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      throw error;
    }
  }

  /**
   * Get current user's IQ score and rank
   */
  async getCurrentUserIQ(): Promise<UserIQResponse> {
    try {
      const response = await fetch(`/api/wp-proxy/iq-score/me`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching current user IQ score:', error);
      throw error;
    }
  }

  /**
   * Get specific user's IQ score and rank
   */
  async getUserIQ(userId: number): Promise<UserIQResponse> {
    try {
      // Validate userId
      if (!userId || isNaN(userId) || userId <= 0) {
        throw new Error(`Invalid user ID: ${userId}`);
      }

      const response = await fetch(`/api/wp-proxy/iq-score/${userId}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add abort signal for client-side timeout
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });

      if (!response.ok) {
        // Try to get error details from response
        let errorDetails = '';
        try {
          const errorData = await response.json();
          errorDetails = errorData.error || errorData.message || '';
        } catch {
          // Ignore JSON parsing errors
        }

        throw new Error(
          `HTTP error! status: ${response.status}${errorDetails ? ` - ${errorDetails}` : ''}`
        );
      }

      const data = await response.json();

      // Validate response structure
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format');
      }

      return data;
    } catch (error: unknown) {
      // Check if it's a timeout error
      if (error instanceof Error && error.name === 'AbortError') {
        console.error(`Timeout fetching IQ score for user ${userId}`);
        throw new Error('Request timeout - please try again');
      }

      console.error(`Error fetching IQ score for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get all available ranks and their requirements
   */
  async getRanks(): Promise<RanksResponse> {
    try {
      const response = await fetch(`/api/wp-proxy/iq-score/ranks`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching ranks:', error);
      throw error;
    }
  }

  /**
   * Award points to a user (admin only)
   */
  async awardPoints(
    userId: number,
    points: number,
    activityType: string = 'manual'
  ): Promise<unknown> {
    try {
      const response = await fetch(`${this.baseUrl}/wp-json/tourismiq/v1/iq-score/award`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          points: points,
          activity_type: activityType,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (err: unknown) {
      console.error('Error awarding points:', err);
      throw err;
    }
  }

  /**
   * Helper function to get the appropriate badge for a user
   */
  getBadgeForUser(rank: Rank, isFounder: boolean): string {
    return isFounder ? rank.founder_badge : rank.member_badge;
  }

  /**
   * Helper function to format IQ score display
   */
  formatScore(score: number): string {
    return score.toLocaleString();
  }

  /**
   * Helper function to get rank color for UI
   */
  getRankColor(rankName: string): string {
    const colors: Record<string, string> = {
      Novice: '#94a3b8', // slate-400
      Contributor: '#06b6d4', // cyan-500
      Engager: '#10b981', // emerald-500
      Influencer: '#f59e0b', // amber-500
      Expert: '#f97316', // orange-500
      Master: '#dc2626', // red-600
    };

    return colors[rankName] || colors.Novice || '#94a3b8';
  }

  /**
   * Helper function to get next rank requirements
   */
  getNextRankInfo(
    currentScore: number,
    ranks: Rank[]
  ): {
    nextRank: Rank | null;
    pointsNeeded: number;
    progress: number;
  } {
    // Find current rank
    const currentRank = ranks.find(
      (rank) => currentScore >= rank.min_points && currentScore <= rank.max_points
    );

    if (!currentRank) {
      return { nextRank: null, pointsNeeded: 0, progress: 0 };
    }

    // Find next rank
    const nextRank = ranks.find((rank) => rank.min_points > currentRank.max_points);

    if (!nextRank) {
      // Already at highest rank
      return { nextRank: null, pointsNeeded: 0, progress: 100 };
    }

    const pointsNeeded = nextRank.min_points - currentScore;
    const rangeSize = currentRank.max_points - currentRank.min_points + 1;
    const currentInRange = currentScore - currentRank.min_points;
    const progress = Math.min((currentInRange / rangeSize) * 100, 100);

    return { nextRank, pointsNeeded, progress };
  }
}

// Export singleton instance
export const iqScoreAPI = new IQScoreAPI();
export default iqScoreAPI;
