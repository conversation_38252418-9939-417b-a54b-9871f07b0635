/**
 * API Type Definitions
 * Centralized type definitions for the API modules
 */

import { YoastSEOData } from '@/lib/seo/yoast-parser';

/**
 * WordPress Post interface
 */
export interface WPPost {
  id: number;
  title: {
    rendered: string;
  };
  excerpt: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  date: string;
  slug: string;
  // Additional properties needed by feed components
  total_comments?: number;
  upvotes?: {
    count: number;
    upvoted: boolean;
  };
  meta?: {
    likes?: number;
    total_comments?: number;
    _upvotes?: number | string;
    post_vendor_id?: number;
    [key: string]: unknown;
  };
  _embedded?: {
    author?: Array<{
      name: string;
      avatar_urls?: {
        [key: string]: string;
      };
      first_name?: string;
      last_name?: string;
      acf?: {
        profile_picture?: {
          url?: string;
          [key: string]: unknown;
        };
        [key: string]: unknown;
      };
      [key: string]: unknown;
    }>;
    'wp:term'?: Array<
      Array<{
        id: number;
        name: string;
        slug: string;
        taxonomy: string;
        link?: string;
        [key: string]: unknown;
      }>
    >;
    'wp:featuredmedia'?: Array<{
      id: number;
      source_url: string;
      alt_text: string;
      media_details: {
        width: number;
        height: number;
        file: string;
        sizes: {
          [key: string]: {
            file: string;
            width: number;
            height: number;
            mime_type: string;
            source_url: string;
          };
        };
        image_meta: Record<string, unknown>;
      };
    }>;
  };
  // Vendor-related fields
  vendor_info?: {
    id: number;
    name: string;
    slug: string;
    logo_url?: string;
  };
  vendor_data?: {
    id: number;
    name: string;
    slug: string;
    logo_url?: string;
    [key: string]: unknown;
  };
  // Yoast SEO fields
  yoast_head?: string;
  yoast_head_json?: YoastSEOData;
  // Add a catch-all for any other properties that might be needed
  [key: string]: unknown;
}

/**
 * WordPress User interface
 */
export interface WPUser {
  id: number;
  name: string;
  slug: string;
  username?: string;
  user_login?: string;
  display_name?: string;
  user_email?: string;
  description?: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  url?: string;
  roles?: string[];
  avatar_urls?: {
    [key: string]: string;
  };
  acf?: {
    profile_picture?:
      | string
      | {
          ID?: number;
          id?: number;
          url?: string;
          sizes?: {
            [key: string]: string;
          };
        };
    cover_photo?:
      | string
      | {
          ID?: number;
          id?: number;
          url?: string;
          sizes?: {
            [key: string]: string;
          };
        };
    bio?: string;
    job_title?: string;
    company?: string;
    location?: string;
    phone?: string;
    website?: string;
    twitter?: string;
    facebook?: string;
    linkedin?: string;
    instagram?: string;
    [key: string]: unknown;
  };
  coverImage?: string; // Direct cover image URL from REST API
}

/**
 * Frontend User interface
 */
export interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  avatarUrl?: string;
  coverImageUrl?: string;
  coverImage?: string | null; // For API updates
  roles?: string[];
  bio?: string;
  jobTitle?: string;
  company?: string;
  location?: string;
  phone?: string;
  website?: string;
  socialLinks?: {
    twitter?: string | null;
    linkedin?: string | null;
    facebook?: string | null;
    instagram?: string | null;
  };
  // Keep the acf property for backward compatibility
  acf?: {
    profile_picture?:
      | string
      | {
          ID?: number;
          id?: number;
          url?: string;
          sizes?: {
            [key: string]: string;
          };
        };
    cover_photo?:
      | string
      | {
          ID?: number;
          id?: number;
          url?: string;
          sizes?: {
            [key: string]: string;
          };
        };
    bio?: string;
    job_title?: string;
    company?: string;
    location?: string;
    phone?: string;
    website?: string;
    twitter?: string;
    facebook?: string;
    linkedin?: string;
    instagram?: string;
    [key: string]: unknown;
  };
}
