export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  timestamp: string;
  isRead: boolean;
}

export interface Conversation {
  id: string;
  participants: {
    id: string;
    username: string;
    firstName?: string | null;
    lastName?: string | null;
    avatar?: {
      url: string;
    } | null;
    isOnline?: boolean;
  }[];
  lastMessage?: Message;
  unreadCount: number;
}

export interface SendMessageData {
  recipient_id: number;
  content: string;
}

export interface SendMessageResponse {
  success: boolean;
  message_id: number;
  message: Message;
  conversation_id: string;
}

export interface UnreadCountResponse {
  unreadCount: number;
}

export interface CanMessageResponse {
  canMessage: boolean;
  reason: string;
}

/**
 * Messaging API functions
 */
export const messagingApi = {
  /**
   * Send a message to another user
   */
  async sendMessage(data: SendMessageData): Promise<SendMessageResponse> {
    const response = await fetch('/api/wp-proxy/messages/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to send message');
    }

    return response.json();
  },

  /**
   * Get all conversations for the current user
   */
  async getConversations(): Promise<Conversation[]> {
    const response = await fetch('/api/wp-proxy/messages/conversations', {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch conversations');
    }

    return response.json();
  },

  /**
   * Get messages for a specific conversation
   */
  async getConversationMessages(
    userId: number,
    page: number = 1,
    perPage: number = 50
  ): Promise<Message[]> {
    const response = await fetch(
      `/api/wp-proxy/messages/conversation/${userId}?page=${page}&per_page=${perPage}`,
      {
        method: 'GET',
        credentials: 'include',
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch messages');
    }

    return response.json();
  },

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(conversationWith: number): Promise<void> {
    const response = await fetch('/api/wp-proxy/messages/mark-read', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ conversation_with: conversationWith }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to mark messages as read');
    }
  },

  /**
   * Get unread message count
   */
  async getUnreadCount(): Promise<UnreadCountResponse> {
    const response = await fetch('/api/wp-proxy/messages/unread-count', {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch unread count');
    }

    return response.json();
  },

  /**
   * Check if current user can message another user
   */
  async canMessageUser(userId: number): Promise<CanMessageResponse> {
    const response = await fetch(`/api/wp-proxy/messages/can-message/${userId}`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to check messaging permissions');
    }

    return response.json();
  },
};
