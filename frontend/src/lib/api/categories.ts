import { API_BASE } from './utils';

export interface Category {
  id: number;
  name: string;
  slug: string;
  link?: string;
  taxonomy?: string;
}

export interface AssignedVendor {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
  tier?: 'bronze' | 'silver' | 'gold';
  is_paid: boolean;
}

/**
 * Fetches categories from the WordPress REST API via Next.js API route
 * This matches the structure used in feed-item.tsx
 */
export async function getCategories(): Promise<Category[]> {
  try {
    // Fetch categories through our Next.js API route proxy
    const response = await fetch(`${API_BASE}/wp-proxy/categories?per_page=100`, {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Failed to fetch categories:', response.status);
      return [];
    }

    const categories = await response.json();

    if (!Array.isArray(categories) || categories.length === 0) {
      return [];
    }

    // Transform to our Category interface
    return categories.map((cat) => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      link: cat.link || `/category/${cat.slug || cat.id}`,
      taxonomy: 'category',
    }));
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}
