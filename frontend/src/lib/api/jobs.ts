import { getApiUrl } from './utils';

export interface JobMeta {
  job_status: string;
  job_organization: string;
  job_source_url: string;
  job_deadline: string;
}

export interface Job {
  id: number;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  excerpt: {
    rendered: string;
  };
  slug: string;
  status: string;
  date: string;
  modified: string;
  author: number;
  featured_media: number;
  featured_media_url?: string;
  job_meta: JobMeta;
  job_categories?: string[];
  job_states?: string[];
  job_countries?: string[];
  _embedded?: {
    author?: Array<{
      id: number;
      name: string;
      avatar_urls: {
        '96': string;
      };
    }>;
    'wp:featuredmedia'?: Array<{
      id: number;
      source_url: string;
      alt_text: string;
    }>;
  };
}

export interface JobsSearchParams {
  search?: string;
  per_page?: number;
  page?: number;
  category?: string;
  status?: string;
  organization?: string;
  state?: string;
  country?: string;
  orderby?: 'date' | 'title' | 'modified' | 'status_then_date';
  order?: 'asc' | 'desc';
}

export interface JobsResponse {
  jobs: Job[];
  total: number;
  pages: number;
  page: number;
  per_page: number;
}

/**
 * Search jobs with filters
 */
export async function searchJobs(params: JobsSearchParams = {}): Promise<JobsResponse> {
  const searchParams = new URLSearchParams();

  // Add search parameters
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });

  // Use proxy route for client-side requests
  const apiUrl =
    typeof window !== 'undefined'
      ? `/api/wp-proxy/jobs/search?${searchParams.toString()}`
      : `${getApiUrl()}/tourismiq/v1/jobs/search?${searchParams.toString()}`;

  try {
    const response = await fetch(apiUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      // If custom endpoint fails, try the standard WordPress endpoint as fallback
      if (response.status === 404) {
        return await getJobsAsFallback(params);
      }

      throw new Error(`Failed to search jobs: ${response.status} ${response.statusText}`);
    }

    return response.json();
  } catch (error) {
    // Try fallback to standard WordPress API
    try {
      return await getJobsAsFallback(params);
    } catch (fallbackError) {
      throw error; // Throw original error
    }
  }
}

/**
 * Fallback function to get jobs using standard WordPress API
 */
async function getJobsAsFallback(params: JobsSearchParams): Promise<JobsResponse> {
  const searchParams = new URLSearchParams();

  // Convert our custom parameters to WordPress parameters
  searchParams.append('per_page', (params.per_page || 20).toString());
  searchParams.append('page', (params.page || 1).toString());
  searchParams.append('orderby', params.orderby || 'date');
  searchParams.append('order', params.order || 'desc');
  searchParams.append('_embed', 'true');

  if (params.search) {
    searchParams.append('search', params.search);
  }

  // Use proxy route for client-side requests
  const apiUrl =
    typeof window !== 'undefined'
      ? `/api/wp-proxy/jobs?${searchParams.toString()}`
      : `${getApiUrl()}/wp/v2/jobs?${searchParams.toString()}`;

  const response = await fetch(apiUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(
      `Fallback failed - Failed to fetch jobs: ${response.status} ${response.statusText}`
    );
  }

  const jobs = await response.json();

  // Convert to our expected format
  return {
    jobs: Array.isArray(jobs) ? jobs : [],
    total: jobs.length || 0,
    pages: 1,
    page: params.page || 1,
    per_page: params.per_page || 20,
  };
}

/**
 * Get all jobs with basic parameters
 */
export async function getJobs(
  params: {
    per_page?: number;
    page?: number;
    _embed?: boolean;
    status?: string;
  } = {}
): Promise<Job[]> {
  const searchParams = new URLSearchParams();

  // Default parameters
  searchParams.append('per_page', (params.per_page || 20).toString());
  searchParams.append('page', (params.page || 1).toString());

  if (params._embed) {
    searchParams.append('_embed', 'true');
  }

  if (params.status) {
    searchParams.append('status', params.status);
  }

  // Use proxy route for client-side requests
  const apiUrl =
    typeof window !== 'undefined'
      ? `/api/wp-proxy/jobs?${searchParams.toString()}`
      : `${getApiUrl()}/wp/v2/jobs?${searchParams.toString()}`;

  const response = await fetch(apiUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch jobs: ${response.status}`);
  }

  return response.json();
}

/**
 * Get a single job by slug
 */
export async function getJobBySlug(slug: string): Promise<Job> {
  const response = await fetch(`${getApiUrl()}/tourismiq/v1/jobs/slug/${slug}`, {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch job: ${response.status}`);
  }

  return response.json();
}

/**
 * Get a single job by ID
 */
export async function getJobById(id: number, embed = true): Promise<Job> {
  const searchParams = new URLSearchParams();
  if (embed) {
    searchParams.append('_embed', 'true');
  }

  // Use proxy route for client-side requests
  const apiUrl =
    typeof window !== 'undefined'
      ? `/api/wp-proxy/jobs/${id}?${searchParams.toString()}`
      : `${getApiUrl()}/wp/v2/jobs/${id}?${searchParams.toString()}`;

  const response = await fetch(apiUrl, {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch job: ${response.status}`);
  }

  return response.json();
}

/**
 * Helper function to get unique values for filter options
 */
export async function getJobFilterOptions(): Promise<{
  categories: Array<{ value: string; label: string }>;
  statuses: Array<{ value: string; label: string }>;
  countries: string[];
  states: string[];
  organizations: string[];
}> {
  // Get a large sample of jobs to extract filter options
  const jobs = await getJobs({ per_page: 100 });

  // Get job categories from taxonomy
  let categories: Array<{ value: string; label: string }> = [];
  try {
    // Use proxy route for client-side requests
    const categoriesUrl =
      typeof window !== 'undefined'
        ? `/api/wp-proxy/jobs/categories?per_page=100`
        : `${getApiUrl()}/wp/v2/job-categories?per_page=100`;

    const categoriesResponse = await fetch(categoriesUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (categoriesResponse.ok) {
      const categoriesData = await categoriesResponse.json();
      categories = categoriesData.map((cat: any) => ({
        value: cat.slug,
        label: cat.name,
      }));
    }
  } catch (error) {
    console.error('Failed to fetch job categories:', error);
    // Fallback to empty array
    categories = [];
  }

  // Get job states from taxonomy
  let states: string[] = [];
  try {
    // Use proxy route for client-side requests
    const statesUrl =
      typeof window !== 'undefined'
        ? `/api/wp-proxy/jobs/states?per_page=100`
        : `${getApiUrl()}/wp/v2/job-states?per_page=100`;

    const statesResponse = await fetch(statesUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (statesResponse.ok) {
      const statesData = await statesResponse.json();
      states = statesData.map((state: any) => state.name).sort();
    }
  } catch (error) {
    console.error('Failed to fetch job states:', error);
    states = [];
  }

  // Get job countries from taxonomy
  let countries: string[] = [];
  try {
    // Use proxy route for client-side requests
    const countriesUrl =
      typeof window !== 'undefined'
        ? `/api/wp-proxy/jobs/countries?per_page=100`
        : `${getApiUrl()}/wp/v2/job-countries?per_page=100`;

    const countriesResponse = await fetch(countriesUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (countriesResponse.ok) {
      const countriesData = await countriesResponse.json();
      countries = countriesData.map((country: any) => country.name).sort();
    }
  } catch (error) {
    console.error('Failed to fetch job countries:', error);
    countries = [];
  }

  const statuses = [
    { value: 'NEW', label: 'New' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'EXPIRED', label: 'Expired' },
    { value: 'FILLED', label: 'Filled' },
    { value: 'DRAFT', label: 'Draft' },
  ];

  // Extract unique organizations from job data (still an ACF field)
  const organizations = Array.from(
    new Set(jobs.map((job) => job.job_meta?.job_organization).filter(Boolean))
  ).sort();

  return {
    categories,
    statuses,
    countries,
    states,
    organizations,
  };
}

/**
 * Format job deadline for display
 */
export function formatJobDeadline(deadline: string): string {
  if (!deadline) return '';

  const date = new Date(deadline);
  const now = new Date();
  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Tomorrow';
  } else if (diffDays > 0 && diffDays <= 7) {
    return `${diffDays} days left`;
  } else {
    return date.toLocaleDateString();
  }
}

/**
 * Get category label from value (now uses taxonomy data)
 */
export function getCategoryLabel(value: string): string {
  // Since categories are now dynamic from taxonomy, we return the value as-is
  // The frontend should use the category names from the API response
  return value;
}

/**
 * State abbreviation to full name mapping
 */
const STATE_MAPPING: Record<string, string> = {
  AL: 'Alabama',
  AK: 'Alaska',
  AZ: 'Arizona',
  AR: 'Arkansas',
  CA: 'California',
  CO: 'Colorado',
  CT: 'Connecticut',
  DE: 'Delaware',
  FL: 'Florida',
  GA: 'Georgia',
  HI: 'Hawaii',
  ID: 'Idaho',
  IL: 'Illinois',
  IN: 'Indiana',
  IA: 'Iowa',
  KS: 'Kansas',
  KY: 'Kentucky',
  LA: 'Louisiana',
  ME: 'Maine',
  MD: 'Maryland',
  MA: 'Massachusetts',
  MI: 'Michigan',
  MN: 'Minnesota',
  MS: 'Mississippi',
  MO: 'Missouri',
  MT: 'Montana',
  NE: 'Nebraska',
  NV: 'Nevada',
  NH: 'New Hampshire',
  NJ: 'New Jersey',
  NM: 'New Mexico',
  NY: 'New York',
  NC: 'North Carolina',
  ND: 'North Dakota',
  OH: 'Ohio',
  OK: 'Oklahoma',
  OR: 'Oregon',
  PA: 'Pennsylvania',
  RI: 'Rhode Island',
  SC: 'South Carolina',
  SD: 'South Dakota',
  TN: 'Tennessee',
  TX: 'Texas',
  UT: 'Utah',
  VT: 'Vermont',
  VA: 'Virginia',
  WA: 'Washington',
  WV: 'West Virginia',
  WI: 'Wisconsin',
  WY: 'Wyoming',
  DC: 'District of Columbia',
};

/**
 * Get full state name from abbreviation or return original value
 */
export function getFullStateName(stateInput: string): string {
  const upperInput = stateInput.toUpperCase();
  return STATE_MAPPING[upperInput] || stateInput;
}

/**
 * Get state abbreviation from full name or return original value
 */
export function getStateAbbreviation(stateInput: string): string {
  const normalized = stateInput.toLowerCase();
  for (const [abbrev, fullName] of Object.entries(STATE_MAPPING)) {
    if (fullName.toLowerCase() === normalized) {
      return abbrev;
    }
  }
  return stateInput;
}

/**
 * Check if search term matches a state (abbreviation or full name)
 */
export function matchesState(searchTerm: string, stateName: string): boolean {
  const searchLower = searchTerm.toLowerCase();
  const stateLower = stateName.toLowerCase();

  // Direct match
  if (stateLower.includes(searchLower)) {
    return true;
  }

  // Check if search term is an abbreviation and state matches full name
  const fullName = getFullStateName(searchTerm);
  if (fullName !== searchTerm && stateLower.includes(fullName.toLowerCase())) {
    return true;
  }

  // Check if search term is a full name and state matches abbreviation
  const abbreviation = getStateAbbreviation(searchTerm);
  if (abbreviation !== searchTerm && stateLower.includes(abbreviation.toLowerCase())) {
    return true;
  }

  return false;
}

/**
 * Get status label from value
 */
export function getStatusLabel(value: string): string {
  const statusMap: Record<string, string> = {
    NEW: 'New',
    ACTIVE: 'Active',
    EXPIRED: 'Expired',
    FILLED: 'Filled',
    DRAFT: 'Draft',
  };

  return statusMap[value] || value;
}
