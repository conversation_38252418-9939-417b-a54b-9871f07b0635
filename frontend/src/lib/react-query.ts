import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Data considered fresh for 5 minutes
      staleTime: 5 * 60 * 1000,
      // Keep unused data in cache for 10 minutes
      gcTime: 10 * 60 * 1000,
      // Retry failed requests 3 times
      retry: 3,
      // Don't refetch on window focus by default
      refetchOnWindowFocus: false,
      // Don't refetch on reconnect by default (we handle this via socket.io)
      refetchOnReconnect: false,
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
    },
  },
});

// Query key factory for consistent key generation
export const queryKeys = {
  // Posts queries
  posts: {
    all: ['posts'] as const,
    lists: () => [...queryKeys.posts.all, 'list'] as const,
    list: (filters?: {
      page?: number;
      perPage?: number;
      category?: string | undefined;
      author?: number;
    }) => [...queryKeys.posts.lists(), filters] as const,
    details: () => [...queryKeys.posts.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.posts.details(), id] as const,
    userPosts: (userId: number, page?: number, perPage?: number) =>
      [...queryKeys.posts.all, 'user', userId, { page, perPage }] as const,
    byCategory: (categorySlug: string, page?: number) =>
      [...queryKeys.posts.all, 'category', categorySlug, page] as const,
  },

  // User/Auth queries
  auth: {
    all: ['auth'] as const,
    status: () => [...queryKeys.auth.all, 'status'] as const,
    profile: () => [...queryKeys.auth.all, 'profile'] as const,
  },

  // Members queries
  members: {
    all: ['members'] as const,
    lists: () => [...queryKeys.members.all, 'list'] as const,
    list: (filters?: { search?: string; role?: string }) =>
      [...queryKeys.members.lists(), filters] as const,
    details: () => [...queryKeys.members.all, 'detail'] as const,
    detail: (userId: number) => [...queryKeys.members.details(), userId] as const,
    profile: (username: string) => [...queryKeys.members.all, 'profile', username] as const,
  },

  // Connections queries
  connections: {
    all: ['connections'] as const,
    status: (userId: number) => [...queryKeys.connections.all, 'status', userId] as const,
    list: (userId?: number) => [...queryKeys.connections.all, 'list', userId] as const,
    pending: () => [...queryKeys.connections.all, 'pending'] as const,
  },

  // IQ Score queries
  iqScore: {
    all: ['iqScore'] as const,
    user: (userId: number) => [...queryKeys.iqScore.all, 'user', userId] as const,
    me: () => [...queryKeys.iqScore.all, 'me'] as const,
    leaderboard: () => [...queryKeys.iqScore.all, 'leaderboard'] as const,
    ranks: () => [...queryKeys.iqScore.all, 'ranks'] as const,
  },

  // Forum queries
  forum: {
    all: ['forum'] as const,
    questions: (filters?: { page?: number; perPage?: number }) =>
      [...queryKeys.forum.all, 'questions', filters] as const,
    question: (id: number) => [...queryKeys.forum.all, 'question', id] as const,
    userQuestions: (userId: number) => [...queryKeys.forum.all, 'user', userId] as const,
    comments: (questionId: number) => [...queryKeys.forum.all, 'comments', questionId] as const,
  },

  // Messages queries
  messages: {
    all: ['messages'] as const,
    conversations: () => [...queryKeys.messages.all, 'conversations'] as const,
    conversation: (userId: number) => [...queryKeys.messages.all, 'conversation', userId] as const,
    unreadCount: () => [...queryKeys.messages.all, 'unreadCount'] as const,
  },

  // Vendors queries
  vendors: {
    all: ['vendors'] as const,
    lists: () => [...queryKeys.vendors.all, 'list'] as const,
    list: (filters?: { category?: string }) => [...queryKeys.vendors.lists(), filters] as const,
    details: () => [...queryKeys.vendors.all, 'detail'] as const,
    detail: (slug: string) => [...queryKeys.vendors.details(), slug] as const,
    posts: (slug: string) => [...queryKeys.vendors.all, 'posts', slug] as const,
    categories: () => [...queryKeys.vendors.all, 'categories'] as const,
  },

  // Categories queries
  categories: {
    all: ['categories'] as const,
    list: () => [...queryKeys.categories.all, 'list'] as const,
  },

  // Bookmarks queries
  bookmarks: {
    all: ['bookmarks'] as const,
    user: (userId: number) => [...queryKeys.bookmarks.all, 'user', userId] as const,
  },

  // Notifications queries
  notifications: {
    all: ['notifications'] as const,
    list: () => [...queryKeys.notifications.all, 'list'] as const,
  },
} as const;

// Helper function for invalidating related queries
export const invalidateQueries = {
  // Invalidate all post-related queries
  allPosts: () => queryClient.invalidateQueries({ queryKey: queryKeys.posts.all }),

  // Invalidate specific post
  post: (postId: number) =>
    queryClient.invalidateQueries({
      queryKey: queryKeys.posts.detail(postId),
    }),

  // Invalidate user's posts (all pages)
  userPosts: (userId: number) =>
    queryClient.invalidateQueries({
      queryKey: [...queryKeys.posts.all, 'user', userId],
    }),

  // Invalidate auth status
  auth: () => queryClient.invalidateQueries({ queryKey: queryKeys.auth.all }),

  // Invalidate connections
  connections: () => queryClient.invalidateQueries({ queryKey: queryKeys.connections.all }),

  // Invalidate IQ scores
  iqScores: () => queryClient.invalidateQueries({ queryKey: queryKeys.iqScore.all }),

  // Invalidate forum data
  forum: () => queryClient.invalidateQueries({ queryKey: queryKeys.forum.all }),

  // Invalidate messages
  messages: () => queryClient.invalidateQueries({ queryKey: queryKeys.messages.all }),

  // Invalidate notifications
  notifications: () => queryClient.invalidateQueries({ queryKey: queryKeys.notifications.all }),
};
