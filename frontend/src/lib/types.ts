export interface Post {
  id: number;
  slug: string;
  title: {
    rendered: string;
  };
  excerpt: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  date: string;
  upvotes?: {
    count: number;
    upvoted: boolean;
  };
  _embedded?: {
    author?: Array<{
      id?: number;
      name: string;
      avatar_urls?: {
        [key: string]: string;
      };
    }>;
    'wp:term'?: Array<Array<{ id: number; name: string }>>;
    'wp:featuredmedia'?: Array<{
      source_url: string;
      alt_text?: string;
      media_details?: {
        width: number;
        height: number;
        sizes?: {
          [key: string]: {
            source_url: string;
            width: number;
            height: number;
          };
        };
      };
    }>;
  };
  meta?: {
    _upvotes?: number;
    total_comments?: number;
    [key: string]: unknown;
  };
  total_comments?: number;
}

export interface Member {
  // Core user data
  id: number;
  name?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  slug?: string;
  role?: string;
  bio?: string;
  location?: string;

  // Profile images
  avatar?: string;
  profilePicture?: string;
  profile_picture?:
    | string
    | {
        ID: number;
        id: number;
        title: string;
        url: string;
        [key: string]: unknown;
      };
  coverImage?: string;

  // Profile details
  categories?: string[];
  featured?: boolean;
  roles?: string[];
  job_title?: string;
  company?: string;

  // Contact information
  contactInfo?: {
    email?: string;
    phone?: string;
    website?: string;
  };

  // Social media links
  socialLinks?: {
    website?: string;
    twitter?: string;
    facebook?: string;
    linkedin?: string;
    instagram?: string;
  };

  // Additional metadata
  user_registered?: string;
  iqScore?: number;

  // Allow any other properties but be more specific about common ones
  [key: string]: unknown;
}
