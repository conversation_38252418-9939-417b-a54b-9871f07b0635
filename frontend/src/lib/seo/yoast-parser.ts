/**
 * Yoast SEO Parser for Next.js
 *
 * Utilities to parse and use Yoast SEO data from WordPress REST API
 */

import { Metadata } from 'next';
import { decodeHtml } from '@/lib/api/utils';

export interface YoastSEOData {
  title?: string;
  description?: string;
  robots?: {
    index?: string;
    follow?: string;
  };
  canonical?: string;
  og_locale?: string;
  og_type?: string;
  og_title?: string;
  og_description?: string;
  og_url?: string;
  og_site_name?: string;
  article_publisher?: string;
  article_published_time?: string;
  article_modified_time?: string;
  og_image?: Array<{
    url: string;
    width?: number;
    height?: number;
    type?: string;
  }>;
  twitter_card?: string;
  twitter_site?: string;
  schema?: any;
}

/**
 * Convert Yoast SEO data to Next.js Metadata format
 */
export function yoastToNextMetadata(
  yoastData: YoastSEOData | undefined,
  fallbackTitle?: string,
  fallbackDescription?: string
): Metadata {
  if (!yoastData) {
    return {
      title: fallbackTitle ? decodeHtml(fallbackTitle) : 'TourismIQ',
      description: fallbackDescription ? decodeHtml(fallbackDescription) : 'Your premier destination for tourism industry insights',
    };
  }

  const metadata: Metadata = {
    title: yoastData.title ? decodeHtml(yoastData.title) : (fallbackTitle ? decodeHtml(fallbackTitle) : 'TourismIQ'),
    description: yoastData.description ? decodeHtml(yoastData.description) : (fallbackDescription ? decodeHtml(fallbackDescription) : undefined),
    robots: yoastData.robots
      ? {
          index: yoastData.robots.index === 'index',
          follow: yoastData.robots.follow === 'follow',
        }
      : undefined,
    alternates: {
      canonical: yoastData.canonical,
    },
    openGraph: {
      title: yoastData.og_title ? decodeHtml(yoastData.og_title) : (yoastData.title ? decodeHtml(yoastData.title) : undefined),
      description: yoastData.og_description ? decodeHtml(yoastData.og_description) : (yoastData.description ? decodeHtml(yoastData.description) : undefined),
      url: yoastData.og_url,
      siteName: yoastData.og_site_name,
      locale: yoastData.og_locale,
      type: yoastData.og_type as any,
      publishedTime: yoastData.article_published_time,
      modifiedTime: yoastData.article_modified_time,
      images: yoastData.og_image?.map((img) => ({
        url: img.url,
        width: img.width,
        height: img.height,
        type: img.type,
      })),
    },
    twitter: {
      card: yoastData.twitter_card as any,
      site: yoastData.twitter_site,
      title: yoastData.og_title ? decodeHtml(yoastData.og_title) : (yoastData.title ? decodeHtml(yoastData.title) : undefined),
      description: yoastData.og_description ? decodeHtml(yoastData.og_description) : (yoastData.description ? decodeHtml(yoastData.description) : undefined),
      images: yoastData.og_image?.map((img) => img.url),
    },
  };

  // Remove undefined values
  return JSON.parse(JSON.stringify(metadata));
}

/**
 * Parse Yoast head HTML string (if using yoast_head instead of yoast_head_json)
 * This is a fallback method if JSON data is not available
 */
export function parseYoastHeadHTML(yoastHead: string): Partial<YoastSEOData> {
  if (!yoastHead) return {};

  const data: Partial<YoastSEOData> = {};

  // Extract title
  const titleMatch = yoastHead.match(/<title>([^<]*)<\/title>/);
  if (titleMatch) data.title = titleMatch[1];

  // Extract meta description
  const descMatch = yoastHead.match(/<meta\s+name="description"\s+content="([^"]*)"/);
  if (descMatch) data.description = descMatch[1];

  // Extract canonical
  const canonicalMatch = yoastHead.match(/<link\s+rel="canonical"\s+href="([^"]*)"/);
  if (canonicalMatch) data.canonical = canonicalMatch[1];

  // Extract Open Graph data
  const ogTitleMatch = yoastHead.match(/<meta\s+property="og:title"\s+content="([^"]*)"/);
  if (ogTitleMatch) data.og_title = ogTitleMatch[1];

  const ogDescMatch = yoastHead.match(/<meta\s+property="og:description"\s+content="([^"]*)"/);
  if (ogDescMatch) data.og_description = ogDescMatch[1];

  const ogUrlMatch = yoastHead.match(/<meta\s+property="og:url"\s+content="([^"]*)"/);
  if (ogUrlMatch) data.og_url = ogUrlMatch[1];

  const ogImageMatch = yoastHead.match(/<meta\s+property="og:image"\s+content="([^"]*)"/);
  if (ogImageMatch) {
    data.og_image = [{ url: ogImageMatch[1] }];
  }

  // Extract Twitter card
  const twitterCardMatch = yoastHead.match(/<meta\s+name="twitter:card"\s+content="([^"]*)"/);
  if (twitterCardMatch) data.twitter_card = twitterCardMatch[1];

  return data;
}

/**
 * Generate structured data (JSON-LD) for SEO
 */
export function generateStructuredData(
  type: 'Article' | 'Person' | 'Organization' | 'WebPage',
  data: any
): string {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': type,
    ...data,
  };

  return JSON.stringify(structuredData);
}
