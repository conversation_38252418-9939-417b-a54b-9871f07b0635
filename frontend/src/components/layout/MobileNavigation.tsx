'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import {
  BookOpenText,
  Users,
  Calendar,
  BriefcaseBusiness,
  Megaphone,
  BookOpen,
  Book,
  Film,
  Headphones,
  Presentation,
  FileCheck,
  MonitorPlay,
  FileSearch,
  MessageSquare,
  GraduationCap,
  Home,
  FileText,
} from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface NavItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: NavItem[];
  isFeedFilter?: boolean;
}

interface MobileNavigationProps {
  onNavigate?: () => void;
  onResourcesClick?: () => void;
  currentMenu?: 'main' | 'resources';
}

export function MobileNavigation({
  onNavigate = () => {},
  onResourcesClick,
  currentMenu = 'main',
}: MobileNavigationProps = {}) {
  const pathname = usePathname();
  const router = useRouter();
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    resources: pathname.startsWith('/resources'),
  });

  // State to track active category from URL parameters (client-side only)
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  // State to track loading when filtering feed
  const [loadingCategory, setLoadingCategory] = useState<string | null>(null);

  // Update active category from URL parameters (client-side only)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const newCategory = urlParams.get('category');
    setActiveCategory(newCategory);
    // Clear loading state when URL changes (indicates feed has updated)
    setLoadingCategory(null);
  }, [pathname]);

  const toggleItem = (key: string) => {
    setExpandedItems((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const isActive = (path: string, exact = true) => {
    if (path.startsWith('/category/')) {
      // For category filters, check if the current URL has the matching category parameter
      const categorySlug = path.split('/category/')[1];

      // Check if we're on the home page with the matching category parameter
      if (pathname === '/') {
        return activeCategory === categorySlug;
      }
      return false;
    }

    // Special handling for Home - only active when no category filter is applied
    if (path === '/') {
      return pathname === '/' && !activeCategory;
    }

    return exact ? pathname === path : pathname.startsWith(path);
  };

  const isLoading = (path: string) => {
    if (path.startsWith('/category/')) {
      const categorySlug = path.split('/category/')[1];
      return loadingCategory === categorySlug;
    }
    return false;
  };

  // Handle navigation to feed filters
  const handleNavigation = (e: React.MouseEvent<HTMLAnchorElement>, item: NavItem) => {
    // Don't handle navigation for items with children (they're handled by toggleItem)
    // Exception: Resources should not navigate and only show popover
    if (item.children && item.children.length > 0 && item.label !== 'Resources') {
      e.preventDefault();
      toggleItem(item.href);
      return;
    }

    // Prevent navigation for Resources - it should only show popover
    if (item.label === 'Resources') {
      e.preventDefault();
      return;
    }

    // Don't handle navigation for disabled items
    if (item.href === '#') {
      e.preventDefault();
      return;
    }

    // Special handling for Home button - clear any active filters
    if (item.href === '/' && item.label === 'Home') {
      e.preventDefault();

      // Clear the active category filter
      setActiveCategory(null);

      // Use the global function to clear feed filter
      if ((window as any).setFeedCategory) {
        (window as any).setFeedCategory(null);
      }

      // Navigate to home without parameters
      router.push('/');
      onNavigate?.();
      return;
    }

    // For feed filter items, use dynamic filtering
    if (item.isFeedFilter) {
      e.preventDefault();

      // Extract the category slug from the href
      const parts = item.href.split('/category/');
      const categorySlug = parts[1];

      if (!categorySlug) return; // Guard against undefined

      // If we're not on the home page, navigate to home with the category parameter
      if (pathname !== '/') {
        router.push(`/?category=${categorySlug}`);
        onNavigate?.();
      } else {
        // We're on the home page - use instant filtering
        // Check if clicking the same category (do nothing)
        if (activeCategory === categorySlug) {
          // Do nothing - already active
          return;
        } else {
          // Set loading state for sidebar UI feedback
          setLoadingCategory(categorySlug);

          // Update the active category immediately
          setActiveCategory(categorySlug);

          // Use the global function we exposed from Feed for instant filtering
          if ((window as any).setFeedCategory) {
            (window as any).setFeedCategory(categorySlug);

            // Scroll to top immediately
            window.scrollTo({ top: 0, behavior: 'smooth' });

            // Clear loading state quickly since filtering is instant
            setTimeout(() => {
              setLoadingCategory(null);
            }, 800);
          } else {
            // Fallback if the function isn't available
            router.push(`/?category=${categorySlug}`);
            onNavigate?.();
          }
        }

        // Close mobile menu for instant filtering too
        onNavigate?.();
      }
    }
    // For normal links, let the Link component handle navigation
  };

  const navItems: NavItem[] = [
    {
      href: '/',
      label: 'Home',
      icon: Home,
    },
    {
      href: '/jobs',
      label: 'Jobs Hub',
      icon: BriefcaseBusiness,
    },
    {
      href: '/people',
      label: 'People on the Move',
      icon: Users,
    },
    {
      href: '/rfps',
      label: 'RFPs',
      icon: FileSearch,
    },
    {
      href: '/vendors',
      label: 'Vendor Directory',
      icon: BriefcaseBusiness,
    },
    {
      href: '/member-directory',
      label: 'Member Directory',
      icon: Users,
    },
    {
      href: '/category/news',
      label: 'News',
      icon: Megaphone,
      isFeedFilter: true,
    },
    {
      href: '/category/thought-leadership',
      label: 'Thought Leadership',
      icon: BookOpenText,
      isFeedFilter: true,
    },
    {
      href: '/category/podcast',
      label: 'Podcasts',
      icon: Headphones,
      isFeedFilter: true,
    },
    {
      href: '/resources',
      label: 'Resources',
      icon: BookOpen,
      children: [
        {
          href: '/category/blog-post',
          label: 'Blog Posts',
          icon: FileText,
          isFeedFilter: true,
        },
        {
          href: '/category/book',
          label: 'Books',
          icon: Book,
          isFeedFilter: true,
        },
        {
          href: '/category/case-study',
          label: 'Case Studies',
          icon: FileSearch,
          isFeedFilter: true,
        },
        {
          href: '/category/course',
          label: 'Courses',
          icon: GraduationCap,
          isFeedFilter: true,
        },
        {
          href: '/category/presentation',
          label: 'Presentations',
          icon: Presentation,
          isFeedFilter: true,
        },
        {
          href: '/category/press-release',
          label: 'Press Releases',
          icon: Megaphone,
          isFeedFilter: true,
        },
        {
          href: '/category/template',
          label: 'Templates',
          icon: FileCheck,
          isFeedFilter: true,
        },
        {
          href: '/category/video',
          label: 'Videos',
          icon: Film,
          isFeedFilter: true,
        },
        {
          href: '/category/webinar',
          label: 'Webinars',
          icon: MonitorPlay,
          isFeedFilter: true,
        },
        {
          href: '/category/whitepaper',
          label: 'Whitepapers',
          icon: FileSearch,
          isFeedFilter: true,
        },
      ],
    },
    {
      href: '/forum',
      label: 'Community Q&A',
      icon: MessageSquare,
    },
    {
      href: '/category/event',
      label: 'Events',
      icon: Calendar,
      isFeedFilter: true,
    },
  ];

  const renderNavItem = (item: NavItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isItemActive = isActive(item.href, !hasChildren);
    const isItemLoading = isLoading(item.href);
    const isResourcesItem = item.label === 'Resources';
    const isExpandable = hasChildren && item.children!.length > 0 && !isResourcesItem;
    const isExpanded = expandedItems[item.href] ?? isItemActive;

    // Special handling for Resources - tiered menu on mobile
    if (isResourcesItem && hasChildren) {
      // If we're in mobile menu and on resources submenu, show the children
      if (currentMenu === 'resources' && onResourcesClick) {
        return (
          <div key="resources-children" className="space-y-1">
            {item.children!.map((child) => (
              <div key={child.href} className="space-y-1">
                <Link
                  href={child.href}
                  onClick={(e) => {
                    handleNavigation(e, child);
                    onNavigate?.();
                  }}
                  className={cn(
                    'flex items-center gap-3 rounded-full px-4 text-base transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 cursor-pointer',
                    'h-10',
                    isActive(child.href)
                      ? 'text-gray-600 font-bold bg-[rgba(92,200,255,0.2)]'
                      : 'text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.2)]'
                  )}
                >
                  <child.icon className="h-5 w-5 flex-shrink-0" />
                  <span className="truncate">{child.label}</span>
                  {isLoading(child.href) && (
                    <span className="ml-auto">
                      <svg
                        className="h-5 w-5 animate-spin"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                    </span>
                  )}
                </Link>
              </div>
            ))}
          </div>
        );
      }

      // If we're in main menu, show Resources button that navigates to submenu
      if (currentMenu === 'main') {
        return (
          <div key={item.href} className="space-y-1">
            <button
              onClick={onResourcesClick}
              className={cn(
                'flex items-center gap-3 rounded-full px-4 text-base transition-all duration-200 ease-in-out w-full focus:outline-none focus:ring-0 border-0 cursor-pointer',
                level === 0 ? 'h-10' : 'h-10 pl-6 text-sm',
                isItemActive
                  ? 'text-gray-600 font-extrabold'
                  : 'text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.15)]'
              )}
            >
              <item.icon className="h-5 w-5 flex-shrink-0" />
              <span className="truncate">{item.label}</span>
              <span className="ml-auto">
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </span>
            </button>
          </div>
        );
      }

      return null;
    }

    // Regular nav item rendering
    return (
      <div key={item.href} className="space-y-1">
        <Link
          href={item.href}
          onClick={(e) => {
            handleNavigation(e, item);
            // Close mobile menu for normal navigation (non-filter, non-expandable)
            if (!item.isFeedFilter && !item.children?.length && item.href !== '#') {
              onNavigate?.();
            }
          }}
          className={cn(
            'flex items-center gap-3 rounded-full px-4 text-base transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 cursor-pointer w-full',
            level === 0 ? 'h-10' : 'h-10 pl-6 text-sm',
            isItemActive
              ? 'text-gray-600 font-bold bg-[rgba(92,200,255,0.2)]'
              : 'text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.2)]',
            item.href === '#' && 'text-gray-400 cursor-not-allowed'
          )}
        >
          <item.icon className="h-5 w-5 flex-shrink-0" />
          <span className="truncate">{item.label}</span>
          {isItemLoading && (
            <span className="ml-auto">
              <svg
                className="h-5 w-5 animate-spin"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </span>
          )}
          {isExpandable && !isItemLoading && (
            <span className="ml-auto">
              <svg
                className={`h-5 w-5 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </span>
          )}
        </Link>
        {isExpanded && hasChildren && (
          <div className="mt-1 space-y-1">
            {item.children!.map((child) => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col w-full h-full">
      <div className="flex-1 p-4">
        {/* Main Menu */}
        <nav className={`flex flex-col gap-1 ${currentMenu === 'main' ? 'block' : 'hidden'}`}>
          {navItems.map((item) => renderNavItem(item))}
        </nav>

        {/* Resources Submenu */}
        <nav className={`flex flex-col gap-1 ${currentMenu === 'resources' ? 'block' : 'hidden'}`}>
          {navItems
            .find((item) => item.label === 'Resources')
            ?.children?.map((child) => renderNavItem(child))}
        </nav>
      </div>

      {/* Newsletter Subscription - Always visible on mobile */}
      <div className="p-4 pt-2 border-t border-[#3d405b]/20 mt-4">
        {/* Newsletter content - Always visible on mobile */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm font-bold text-gray-700">Subscribe to our Newsletter</span>
          </div>
          <p className="text-xs text-gray-500 mb-3">
            Stay updated with the latest news and insights
          </p>
          <div className="space-y-3">
            <input
              type="email"
              placeholder="Email Address*"
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-full focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
            />
            <button className="w-full px-3 py-2 text-sm font-semibold bg-primary text-foreground rounded-full hover:bg-primary/90 transition-colors">
              Sign Up
            </button>
          </div>
        </div>

        {/* Always visible footer */}
        <div className="text-[10px] text-gray-500 space-y-3">
          <div className="flex items-center gap-1.5">
            <a
              href="/sponsorships"
              className="hover:text-[#3d405b] hover:underline whitespace-nowrap transition-all duration-200"
            >
              Sponsorships
            </a>
            <span>|</span>
            <a
              href="/terms-of-use"
              className="hover:text-[#3d405b] hover:underline whitespace-nowrap transition-all duration-200"
            >
              Terms & Use
            </a>
            <span>|</span>
            <a
              href="/privacy-policy"
              className="hover:text-[#3d405b] hover:underline whitespace-nowrap transition-all duration-200"
            >
              Privacy Policy
            </a>
          </div>
          <div>&copy; TourismIQ 2025. All rights reserved.</div>
        </div>
      </div>
    </div>
  );
}
