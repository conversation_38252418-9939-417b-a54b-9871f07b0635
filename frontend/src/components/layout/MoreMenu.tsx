/*********************************************
# frontend/src/components/layout/MoreMenu.tsx
# 01/27/2025 6:35pm Created file - More menu component for collapsed navigation items
# 01/27/2025 7:05pm Increased dropdown width from w-56 to w-72 for longer nav items
# 02/06/2025 12:15am Fixed hover states appearing on active links by explicitly setting transparent backgrounds
# 02/07/2025 2:10pm Fixed inconsistent spacing in More menu dropdown links to use consistent 3px spacing
# 02/07/2025 2:15pm Fixed top two items spacing in More menu to ensure all items have 3px spacing
**********************************************/

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { MoreHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu';
import { NavItem } from '@/hooks/useAdaptiveNavigation';

interface MoreMenuProps {
  collapsedItems: NavItem[];
  onNavigate?: () => void;
  isActive: (path: string, exact?: boolean) => boolean;
  isLoading: (path: string) => boolean;
  handleNavigation: (e: React.MouseEvent<HTMLAnchorElement>, item: NavItem) => void;
}

export function MoreMenu({
  collapsedItems,
  onNavigate = () => {},
  isActive,
  isLoading,
  handleNavigation,
}: MoreMenuProps) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const router = useRouter();

  const renderMenuItem = (item: NavItem, index: number, totalItems: number) => {
    const hasChildren = item.children && item.children.length > 0;
    const isItemActive = isActive(item.href, !hasChildren);
    const isItemLoading = isLoading(item.href);

    // Special handling for Resources with nested dropdown
    if (item.label === 'Resources' && hasChildren) {
      return (
        <DropdownMenuSub key={item.href}>
          <DropdownMenuSubTrigger
            className={cn(
              'flex items-center gap-3 rounded-full px-6 text-xl transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 h-[52px] cursor-pointer',
              index === 0 && 'mt-0', // Remove top margin from first item
              index === totalItems - 1 && 'mb-0', // Remove bottom margin from last item
              index !== totalItems - 1 && 'mb-[3px]', // Apply margin to all items except the last
              isItemActive
                ? 'text-gray-600 font-semibold bg-[rgba(92,200,255,0.1)]'
                : 'text-gray-600 font-medium',
              isItemActive
                ? 'hover:bg-transparent focus:bg-transparent'
                : 'hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]'
            )}
          >
            <item.icon className="h-5 w-5 flex-shrink-0" />
            <span className="truncate">{item.label}</span>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent className="w-72 rounded-[30px]">
            {item.children.map((child, childIndex) => {
              const isChildActive = isActive(child.href, true);
              return (
                <DropdownMenuItem
                  key={child.href}
                  onSelect={(e) => {
                    e.preventDefault();
                    setDropdownOpen(false);
                    const syntheticEvent = {
                      preventDefault: () => {},
                      currentTarget: {} as HTMLAnchorElement,
                    } as React.MouseEvent<HTMLAnchorElement>;

                    handleNavigation(syntheticEvent, child);
                    onNavigate?.();
                  }}
                  className={cn(
                    'flex items-center gap-3 rounded-full px-6 text-xl transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 h-[52px] cursor-pointer',
                    childIndex === 0 && 'mt-0', // Remove top margin from first item
                    childIndex === item.children.length - 1 && 'mb-0', // Remove bottom margin from last item
                    childIndex !== item.children.length - 1 && 'mb-[3px]', // Apply margin to all items except the last
                    isChildActive
                      ? 'text-gray-600 font-semibold bg-[rgba(92,200,255,0.1)]'
                      : 'text-gray-600 font-medium',
                    isChildActive
                      ? 'hover:bg-transparent focus:bg-transparent'
                      : 'hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]'
                  )}
                >
                  <child.icon className="h-5 w-5 flex-shrink-0" />
                  <span className="truncate">{child.label}</span>
                  {isLoading(child.href) && (
                    <span className="ml-auto">
                      <svg
                        className="h-5 w-5 animate-spin"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                    </span>
                  )}
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuSubContent>
        </DropdownMenuSub>
      );
    }

    // Regular menu item
    return (
      <DropdownMenuItem
        key={item.href}
        onSelect={(e) => {
          e.preventDefault();
          setDropdownOpen(false);

          // Handle navigation properly
          if (item.isFeedFilter || item.children?.length || item.href === '#') {
            // Use the handleNavigation function for special cases
            const syntheticEvent = {
              preventDefault: () => {},
              currentTarget: {} as HTMLAnchorElement,
            } as React.MouseEvent<HTMLAnchorElement>;

            handleNavigation(syntheticEvent, item);
          } else {
            // For normal links (like Community Q&A), use router navigation
            router.push(item.href);
          }

          onNavigate?.();
        }}
        className={cn(
          'flex items-center gap-3 rounded-full px-6 text-xl transition-colors duration-150 ease-in-out focus:outline-none focus:ring-0 border-0 h-[52px] cursor-pointer',
          index === 0 && 'mt-0', // Remove top margin from first item
          index === totalItems - 1 && 'mb-0', // Remove bottom margin from last item
          index !== totalItems - 1 && 'mb-[3px]', // Apply margin to all items except the last
          isItemActive
            ? 'text-gray-600 font-semibold bg-[rgba(92,200,255,0.1)]'
            : 'text-gray-600 font-medium',
          isItemActive
            ? 'hover:bg-transparent focus:bg-transparent'
            : 'hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]'
        )}
      >
        <item.icon className="h-5 w-5 flex-shrink-0" />
        <span className="truncate">{item.label}</span>
        {isItemLoading && (
          <span className="ml-auto">
            <svg
              className="h-5 w-5 animate-spin"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9M4.582 9H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </span>
        )}
      </DropdownMenuItem>
    );
  };

  return (
    <div className="space-y-3">
      <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen} modal={false}>
        <DropdownMenuTrigger asChild>
          <button
            className={cn(
              'flex items-center gap-3 rounded-full px-6 text-xl transition-all duration-200 ease-in-out w-full focus:outline-none focus:ring-0 border-0 cursor-pointer',
              'h-[52px]',
              'text-gray-600 font-medium hover:bg-[rgba(92,200,255,0.15)]'
            )}
          >
            <MoreHorizontal className="h-5 w-5 flex-shrink-0" />
            <span className="truncate">More</span>
            <span className="ml-auto">
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </span>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" side="right" className="w-72 rounded-[30px]">
          {collapsedItems.map((item, index) => renderMenuItem(item, index, collapsedItems.length))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
