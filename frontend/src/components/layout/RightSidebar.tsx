/*********************************************
# frontend/src/components/layout/RightSidebar.tsx
# 01/27/2025 10:40pm Updated to use responsive accordion for better vertical space management
# 01/27/2025 10:50pm Fixed all cards to defaultOpen=true and moved CTA buttons outside collapsible content
# 01/27/2025 10:55pm Added data-sidebar attribute for overflow detection trigger
# 01/27/2025 11:00pm Simplified logic - removed dynamic content cropping to fix glitching
# 01/27/2025 11:10pm Fixed 'View All Events' button to match left sidebar Events link route
# 02/07/2025 3:00pm Optimized to prevent unnecessary reloading using React Query caching
**********************************************/

'use client';

import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Calendar, MapPin, Check, User<PERSON>lus, User<PERSON>inus, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import React, { useEffect, useState } from 'react';
import { getEventPosts } from '@/lib/api';
import {
  useConnectionStatus,
  useConnections,
  useConnectionsList,
} from '@/hooks/queries/useConnections';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import { useQueryClient, useQuery } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { SidebarAccordion } from '@/components/ui/SidebarAccordion';
import { useWindowSize } from '@/hooks/useWindowSize';
import { cn } from '@/lib/utils';

type SuggestedConnectionType = {
  id: string;
  name: string;
  username?: string;
  role: string;
  job_title?: string;
  image?: string;
};

interface MemberData {
  id: number | string;
  name: string;
  username?: string;
  slug?: string;
  role?: string;
  job_title?: string;
  avatar?: string;
}

interface MembersResponse {
  founders?: MemberData[];
  members?: MemberData[];
}

// Component for individual member connection button
function MemberConnectionButton({
  memberId,
  memberName,
}: {
  memberId: number;
  memberName: string;
}) {
  const { status, refreshStatus, loading: statusLoading } = useConnectionStatus(memberId);
  const { sendConnectionRequest, removeConnection } = useConnections();
  const [isConnecting, setIsConnecting] = useState(false);
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  const queryClient = useQueryClient();

  const handleConnectionAction = async (action: string, targetUserId: number) => {
    setIsConnecting(true);

    try {
      if (action === 'request') {
        await sendConnectionRequest(targetUserId);

        // Immediately update the cache with the pending status
        queryClient.setQueryData(queryKeys.connections.status(targetUserId), {
          status: 'pending',
          pending_type: 'sent',
          can_request: false,
          can_accept: false,
          can_decline: false,
          can_remove: false,
        });

        // Then refetch to get the actual status from the server
        await refreshStatus();
      }
    } catch (error) {
      console.error('Connection action error:', error);
      // If there's an error, refetch to ensure we have the correct status
      await refreshStatus();
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    setIsConnecting(true);

    try {
      await removeConnection(memberId);

      // Immediately update the cache to show no connection
      queryClient.setQueryData(queryKeys.connections.status(memberId), {
        status: 'none',
        can_request: true,
        can_accept: false,
        can_decline: false,
        can_remove: false,
      });

      // Then refetch to get the actual status from the server
      await refreshStatus();
    } catch (error) {
      console.error('Disconnect error:', error);
      // If there's an error, refetch to ensure we have the correct status
      await refreshStatus();
    } finally {
      setIsConnecting(false);
      setShowDisconnectDialog(false);
    }
  };

  const getConnectionButton = () => {
    const isLoading = Boolean(isConnecting || statusLoading);

    if (statusLoading && !status) {
      return (
        <Button variant="outline" size="sm" className="h-8 text-xs" disabled>
          <Loader2 className="h-3 w-3 animate-spin" />
        </Button>
      );
    }

    if (!status || status.status === 'none') {
      return (
        <Button
          variant="outline"
          size="sm"
          className="h-8 text-xs text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
          onClick={() => handleConnectionAction('request', memberId)}
          disabled={isLoading}
        >
          {isLoading ? '...' : 'Connect'}
        </Button>
      );
    }

    if (status.status === 'pending') {
      if (status.pending_type === 'sent') {
        return (
          <Button
            variant="outline"
            size="sm"
            className="rounded-full w-8 h-8 p-0 text-[#5cc8ff] border-[#5cc8ff] bg-white hover:bg-[#5cc8ff]/10"
            disabled
          >
            <Check className="h-4 w-4" />
          </Button>
        );
      } else if (status.pending_type === 'received') {
        return (
          <Button
            variant="outline"
            size="sm"
            className="rounded-full w-8 h-8 p-0 text-orange-600 border-orange-200 bg-white"
            disabled
          >
            <UserPlus className="h-4 w-4" />
          </Button>
        );
      }
    }

    if (status.status === 'accepted') {
      return (
        <>
          <Button
            variant="outline"
            size="sm"
            className="rounded-full w-8 h-8 p-0 bg-[#5cc8ff]/10 text-[#5cc8ff] border-[#5cc8ff] hover:bg-[#5cc8ff]/20"
            onClick={() => setShowDisconnectDialog(true)}
            disabled={isLoading}
          >
            {isLoading ? (
              <UserMinus className="h-4 w-4 animate-spin" />
            ) : (
              <Check className="h-4 w-4" />
            )}
          </Button>

          {/* Disconnect Confirmation Dialog */}
          <Dialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Disconnect from {memberName}?</DialogTitle>
                <DialogDescription>
                  Are you sure you want to disconnect from {memberName}? This will remove your
                  connection and you&apos;ll need to send a new connection request to reconnect.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowDisconnectDialog(false)}
                  disabled={isConnecting}
                >
                  Cancel
                </Button>
                <Button variant="destructive" onClick={handleDisconnect} disabled={isConnecting}>
                  {isConnecting ? (
                    <>
                      <UserMinus className="h-4 w-4 mr-2 animate-spin" />
                      Disconnecting...
                    </>
                  ) : (
                    <>
                      <UserMinus className="h-4 w-4 mr-2" />
                      Disconnect
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      );
    }

    return null;
  };

  return getConnectionButton();
}

// Custom hook for caching newest members data
const useNewestMembersCache = (user: any) => {
  return useQuery({
    queryKey: ['newest-members', user?.id],
    queryFn: async () => {
      const response = await fetch(`/api/wp-proxy/members/custom?per_page=20&page=1`, {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.status}`);
      }

      const membersData: MembersResponse = await response.json();
      const allMembers = [...(membersData.founders || []), ...(membersData.members || [])];

      // Filter out the current logged-in user and take top 4 for suggested connections
      const formattedMembers = allMembers
        .filter((member: MemberData) => {
          // Check various ways the current user might be identified
          const isCurrentUser =
            member.id === user.id ||
            member.id === parseInt(user.id?.toString() || '0') ||
            member.username === user.username ||
            member.slug === user.username ||
            member.name === user.name ||
            (member.username &&
              user.username &&
              member.username.toLowerCase() === user.username.toLowerCase()) ||
            (member.slug &&
              user.username &&
              member.slug.toLowerCase() === user.username.toLowerCase());

          return !isCurrentUser;
        })
        .slice(0, 4) // Take top 4 members after filtering
        .map((member: MemberData): SuggestedConnectionType => {
          return {
            id: member.id.toString(),
            name: member.name,
            username: member.slug || member.username || member.id.toString(),
            role: member.role || 'Member',
            job_title: (member as any).job_title || (member as any).jobTitle || undefined,
            image: member.avatar || '/images/avatar-placeholder.svg',
          };
        });

      return formattedMembers;
    },
    enabled: !!user?.id, // Only fetch when user ID is available
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes
  });
};

// Custom hook for caching events data
const useEventsCache = (isWordPressAdmin: boolean) => {
  return useQuery({
    queryKey: ['upcoming-events'],
    queryFn: async () => {
      const events = await getEventPosts(3);
      return events || [];
    },
    enabled: !isWordPressAdmin, // Don't fetch in WordPress admin
    staleTime: 10 * 60 * 1000, // Cache for 10 minutes
    gcTime: 60 * 60 * 1000, // Keep in cache for 1 hour
  });
};

export function RightSidebar() {
  // Use the auth context for authentication status and user info
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  // Check if we're in WordPress admin context
  const [isWordPressAdmin, setIsWordPressAdmin] = useState(false);

  useEffect(() => {
    // Simple check to see if we're in WordPress admin
    const isInWordPressAdmin =
      typeof window !== 'undefined' &&
      (window.location.pathname.includes('/wp-admin') ||
        document.body.classList.contains('wp-admin') ||
        document.body.classList.contains('post-php'));

    setIsWordPressAdmin(isInWordPressAdmin);
  }, []);

  // Use React Query hooks for cached data
  const { data: newestMembers = [], isLoading } = useNewestMembersCache(user);

  const { data: upcomingEvents = [], isLoading: isLoadingEvents } =
    useEventsCache(isWordPressAdmin);

  // Get current user's connections
  const {
    connections,
    loading: connectionsLoading,
    error: connectionsError,
  } = useConnectionsList();

  // Use window size hook for responsive behavior
  const { shouldUseAccordion } = useWindowSize();

  // Build accordion items based on authentication state
  const accordionItems = [];

  // My Connections (only if authenticated)
  if (isAuthenticated) {
    accordionItems.push({
      id: 'my-connections',
      title: 'My Connections',
      defaultOpen: true,
      priority: 1, // Highest priority - opens first
      content: (
        <div className="space-y-4">
          {connectionsLoading ? (
            // Show loading state
            <div className="py-4 flex flex-col items-center justify-center text-sm text-muted-foreground">
              <Loader2 className="h-6 w-6 animate-spin mb-2" />
              Loading connections...
            </div>
          ) : connectionsError ? (
            // Show error state
            <div className="py-2 text-center text-sm text-red-500">Error loading connections</div>
          ) : connections.length > 0 ? (
            // Show connections (limit to first 3 for space)
            connections.slice(0, 3).map((connection: any) => {
              // Get display name
              const displayName =
                connection.name || connection.display_name || connection.username || 'Unknown';

              // Get avatar image
              const avatarUrl =
                (typeof connection.profile_picture === 'string'
                  ? connection.profile_picture
                  : connection.profile_picture?.url) ||
                connection.avatar ||
                '/images/avatar-placeholder.svg';

              return (
                <div key={connection.user_id} className="flex items-center justify-between">
                  <Link
                    href={`/profile/${connection.slug || connection.username}`}
                    className="flex items-center space-x-3 hover:opacity-80 transition-opacity"
                  >
                    <UserAvatarWithRank
                      userId={
                        typeof connection.user_id === 'string'
                          ? parseInt(connection.user_id)
                          : connection.user_id
                      }
                      avatarUrl={avatarUrl}
                      displayName={displayName}
                      size="h-10 w-10"
                      containerSize="w-12 h-12"
                      userRoles={(connection as any).roles || []}
                    />
                    <div>
                      <p className="text-sm font-medium">{displayName}</p>
                      <p className="text-xs text-muted-foreground">
                        {connection.job_title || 'Member'}
                      </p>
                    </div>
                  </Link>
                </div>
              );
            })
          ) : (
            // Show empty state
            <div className="py-2 text-center text-sm text-muted-foreground">
              <p className="mb-3">No connections yet</p>
              <Link href="/member-directory" passHref>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
                >
                  Find Connections
                </Button>
              </Link>
            </div>
          )}
        </div>
      ),
      cta: isAuthenticated && !connectionsLoading && !connectionsError && (
        <Button
          variant="outline"
          size="sm"
          className="w-full text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
          onClick={() => {
            const userProfile = `/profile/${user?.username || user?.id}`;
            const profileUrl = `${userProfile}?tab=connections`;

            // Check if we're already on the user's profile page
            if (pathname === userProfile) {
              // Update the URL and trigger a custom event that ProfileTabs can listen to
              window.history.pushState({}, '', profileUrl);
              // Dispatch a custom event to notify about the tab change
              window.dispatchEvent(
                new CustomEvent('tabChange', { detail: { tab: 'connections' } })
              );
            } else {
              // Navigate to the profile page with the tab parameter
              router.push(profileUrl);
            }
          }}
        >
          My Connections
        </Button>
      ),
    });
  }

  // Suggested Connections
  accordionItems.push({
    id: 'suggested-connections',
    title: 'Newest Users',
    defaultOpen: true, // Always open by default
    priority: 2, // Second priority
    content: (
      <div className="space-y-4">
        {!isAuthenticated ? (
          // Show login prompt for non-authenticated users
          <div className="py-4 flex flex-col items-center justify-center text-sm text-muted-foreground">
            <p className="mb-3">Log in to find your connections</p>
            <Link href="/login" passHref>
              <Button
                variant="outline"
                size="sm"
                className="text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
              >
                Log In
              </Button>
            </Link>
          </div>
        ) : isLoading || !user ? (
          // Show loading state while API is loading OR user is not loaded yet
          <div className="py-4 flex flex-col items-center justify-center text-sm text-muted-foreground">
            <Loader2 className="h-6 w-6 animate-spin mb-2" />
            Loading members...
          </div>
        ) : newestMembers.length > 0 ? (
          // Show newest members
          newestMembers.slice(0, 4).map((member) => (
            <div key={member.id} className="flex items-start justify-between gap-2">
              <Link
                href={`/profile/${member.username || member.id}`}
                className="flex items-start space-x-3 hover:opacity-80 transition-opacity flex-1 min-w-0"
              >
                <div className="flex-shrink-0">
                  <UserAvatarWithRank
                    userId={parseInt(member.id)}
                    avatarUrl={member.image || '/images/avatar-placeholder.svg'}
                    displayName={member.name}
                    size="h-10 w-10"
                    containerSize="w-12 h-12"
                    userRoles={member.role ? [member.role.toLowerCase()] : []}
                  />
                </div>
                <div className="min-w-0">
                  <p className="text-sm font-medium truncate">{member.name}</p>
                  <p className="text-xs text-muted-foreground line-clamp-1">
                    {member.job_title || member.role || 'Member'}
                  </p>
                </div>
              </Link>
              <div className="flex-shrink-0">
                <MemberConnectionButton memberId={parseInt(member.id)} memberName={member.name} />
              </div>
            </div>
          ))
        ) : (
          // Show empty state
          <div className="py-2 text-center text-sm text-muted-foreground">No members found</div>
        )}
      </div>
    ),
    cta: (
      <Link href="/member-directory" passHref>
        <Button
          variant="outline"
          size="sm"
          className="w-full text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
        >
          View More
        </Button>
      </Link>
    ),
  });

  // Upcoming Events
  accordionItems.push({
    id: 'upcoming-events',
    title: 'Upcoming Events',
    defaultOpen: true,
    priority: 3, // Third priority
    content: (
      <div className="space-y-3">
        {isLoadingEvents ? (
          // Show loading state
          <div className="py-4 flex flex-col items-center justify-center text-sm text-muted-foreground">
            <Loader2 className="h-6 w-6 animate-spin mb-2" />
            Loading events...
          </div>
        ) : upcomingEvents.length > 0 ? (
          // Show events
          upcomingEvents.slice(0, 3).map((event) => (
            <Link
              href={`/posts/${event.slug}`}
              key={event.id.toString()}
              className="block space-y-1 p-2 rounded-md transition-colors hover:bg-gray-50 cursor-pointer"
            >
              <p className="text-sm font-medium text-brand-text">{event.title.rendered}</p>
              <div className="flex items-center text-xs text-muted-foreground">
                <Calendar className="h-3 w-3 mr-1" />
                <span>
                  {event.acf?.event_start_date ||
                    event.formatted_date ||
                    new Date(event.date).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <MapPin className="h-3 w-3 mr-1" />
                <span>{event.acf?.event_location || 'Location not specified'}</span>
              </div>
            </Link>
          ))
        ) : (
          // Show empty state
          <div className="py-2 text-center text-sm text-muted-foreground">No upcoming events</div>
        )}
      </div>
    ),
    cta: !isLoadingEvents && upcomingEvents.length > 0 && (
      <Link href="/?category=event" passHref>
        <Button
          variant="outline"
          size="sm"
          className="w-full text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
        >
          View All Events
        </Button>
      </Link>
    ),
  });

  return (
    <aside className="flex w-full h-full" data-sidebar="right">
      <div
        className={cn(
          'w-full p-4',
          // Prevent scrollbars from ever appearing
          'overflow-hidden',
          // Always hide scrollbars even if content temporarily overflows
          '[&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]'
        )}
      >
        <SidebarAccordion items={accordionItems} useAccordionMode={shouldUseAccordion} />
      </div>
    </aside>
  );
}

export default RightSidebar;
