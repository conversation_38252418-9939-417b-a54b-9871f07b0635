/*********************************************
# frontend/src/components/layout/MainLayout.tsx
# 01/27/2025 8:45pm Increased feed width from 600px to 650px for better content presentation
# 02/06/2025 11:15am Increased main container width from 1200px to 1360px for better content utilization
# 02/06/2025 11:25am Added 30px left padding to main content area for better visual spacing
# 02/07/2025 12:00pm Fixed unnecessary scrollbar issue on short pages by updating height and overflow strategy
# 02/07/2025 12:15pm Reverted sidebar heights to preserve navigation truncation functionality
# 02/07/2025 2:00pm Fixed scroll behavior to keep header and sidebars fixed while main content scrolls
# 02/07/2025 2:30pm Reverted to original working layout structure with main browser scrollbar controlling content
# 02/07/2025 4:45pm Added 20px right padding to main content area for better visual spacing
**********************************************/

import { ReactNode } from 'react';

interface MainLayoutProps {
  children: ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="flex justify-center">
      <div className="flex w-full max-w-[1360px]">
        {/* Left Sidebar - hidden below 1280px (adjusted from 1200px) */}
        <div className="hidden min-[1280px]:block flex-shrink-0">
          <div className="sticky top-[80px] md:top-[90px] h-[calc(100vh-80px)] md:h-[calc(100vh-90px)] overflow-y-auto">
            {Array.isArray(children) ? children[0] : null}
          </div>
        </div>

        {/* Main Content - responsive sizing and centering with increased widths */}
        <div className="flex-1 max-w-[900px] min-[1280px]:max-w-[750px] max-[1023px]:mx-auto px-3 sm:px-4 md:pl-[30px] md:pr-[20px] relative">
          {Array.isArray(children) ? children[1] : children}
        </div>

        {/* Right Sidebar - hidden below 1024px, increased width for larger container */}
        <div className="hidden min-[1024px]:block w-[300px] min-[1280px]:w-[350px] flex-shrink-0">
          <div className="sticky top-[80px] md:top-[90px] h-[calc(100vh-80px)] md:h-[calc(100vh-90px)] overflow-y-auto">
            {Array.isArray(children) ? children[2] : null}
          </div>
        </div>
      </div>
    </div>
  );
}
