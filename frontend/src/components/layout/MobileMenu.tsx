'use client';

import { useState, useEffect } from 'react';
import { Menu, <PERSON>Left } from 'lucide-react';
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
  SheetDescription,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { MobileNavigation } from './MobileNavigation';

export function MobileMenu() {
  const [open, setOpen] = useState(false);
  const [currentMenu, setCurrentMenu] = useState<'main' | 'resources'>('main');

  // Close menu on route change and reset to main menu
  useEffect(() => {
    const handleRouteChange = () => {
      setOpen(false);
      setCurrentMenu('main');
    };
    window.addEventListener('popstate', handleRouteChange);
    return () => window.removeEventListener('popstate', handleRouteChange);
  }, []);

  // Reset to main menu when closing
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setCurrentMenu('main');
    }
  };

  const handleResourcesClick = () => {
    setCurrentMenu('resources');
  };

  const handleBackClick = () => {
    setCurrentMenu('main');
  };

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="min-[1280px]:hidden">
          <Menu className="size-[1.4rem]" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[280px] p-0 z-50">
        <SheetHeader className="sr-only">
          <SheetTitle>
            {currentMenu === 'resources' ? 'Resources Menu' : 'Navigation Menu'}
          </SheetTitle>
          <SheetDescription>
            {currentMenu === 'resources'
              ? 'Browse resources and content categories'
              : 'Navigate to different sections of the site'}
          </SheetDescription>
        </SheetHeader>

        {currentMenu === 'resources' && (
          <div className="px-4 py-3 border-b">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleBackClick}
                className="h-8 w-8 transition-transform duration-200 hover:scale-110 animate-in fade-in-0 slide-in-from-left-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-center flex-1 text-lg font-semibold">Resources</h2>
            </div>
          </div>
        )}
        <div className="h-[calc(100vh-60px)] overflow-y-auto">
          <MobileNavigation
            onNavigate={() => setOpen(false)}
            onResourcesClick={handleResourcesClick}
            currentMenu={currentMenu}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
}
