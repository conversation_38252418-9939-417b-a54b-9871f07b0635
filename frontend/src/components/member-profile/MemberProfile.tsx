/*********************************************
# frontend/src/components/member-profile/MemberProfile.tsx
# 02/07/2025 11:45pm Added edit button for profile image to match cover image edit functionality and improve UX consistency
**********************************************/

'use client';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  MessageSquare,
  UserPlus,
  UserMinus,
  Check,
  MapPin,
  Globe,
  Calendar,
  Edit,
  Trash2,
  Upload,
  Loader2,
} from 'lucide-react';
import { ProfileTabs } from './ProfileTabs';
import { useAuth } from '@/contexts/auth-context';
import { useConnectionStatus } from '@/hooks/queries/useConnections';
import { useConnections } from '@/hooks/useConnections';
import React, { useState, useRef } from 'react';
import { useMessaging } from '@/components/messaging';
import { messagingApi } from '@/lib/api/messaging';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import {
  FacebookSolid,
  TwitterSolid,
  LinkedinSolid,
  InstagramSolid,
} from '@/components/ui/SocialIcons';
import { toast } from 'sonner';
import { Label } from '@/components/ui/label';

export interface Member {
  // Core user data
  id: number;
  name: string;
  username: string;
  firstName: string;
  lastName: string;
  slug: string;
  role: string;
  bio: string;
  location: string;

  // Profile images
  avatar: string;
  profile_picture?:
    | string
    | {
        ID: number;
        id: number;
        title: string;
        url: string;
        [key: string]: unknown;
      };
  coverImage: string;

  // Profile details
  categories: string[];
  featured: boolean;
  roles: string[];
  job_title?: string;
  company?: string;

  // Contact information
  contactInfo: {
    email: string;
    phone: string;
    website: string;
  };

  // Social media links
  socialLinks: {
    website: string;
    twitter: string;
    facebook: string;
    linkedin: string;
    instagram: string;
  };

  // Additional metadata
  user_registered: string;
  iqScore?: number;

  // Allow any other properties
  [key: string]: unknown;
}

interface MemberProfileProps {
  member: Member;
  initialTab?: string;
}

export function MemberProfile({ member, initialTab }: MemberProfileProps) {
  const { user, isAuthenticated, updateProfile } = useAuth();
  const { status, refreshStatus, loading: statusLoading } = useConnectionStatus(member.id);
  const { sendConnectionRequest, removeConnection } = useConnections();
  const [isConnecting, setIsConnecting] = useState(false);
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  const [isStartingChat, setIsStartingChat] = useState(false);
  const [localConnectionStatus, setLocalConnectionStatus] = useState(status);
  const { openChat } = useMessaging();

  // Cover image editing state
  const [showCoverImageDialog, setShowCoverImageDialog] = useState(false);
  const [coverImageFile, setCoverImageFile] = useState<File | null>(null);
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null);
  const [isUpdatingCoverImage, setIsUpdatingCoverImage] = useState(false);
  const coverImageInputRef = useRef<HTMLInputElement>(null);

  // Profile image editing state
  const [showProfileImageDialog, setShowProfileImageDialog] = useState(false);
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(null);
  const [isUpdatingProfileImage, setIsUpdatingProfileImage] = useState(false);
  const profileImageInputRef = useRef<HTMLInputElement>(null);
  const profileImageFileReaderRef = useRef<FileReader | null>(null);

  // Update local status when prop changes
  React.useEffect(() => {
    setLocalConnectionStatus(status);
  }, [status]);

  // Use local status with fallback to fetched status
  const currentStatus = localConnectionStatus || status;

  // Get avatar URL with fallbacks
  const getAvatarUrl = (): string | undefined => {
    // For own profile, use auth user data for real-time updates
    if (isOwnProfile && user?.avatarUrl) {
      return user.avatarUrl;
    }

    // Try profile_picture first (ACF field)
    if (member.profile_picture) {
      if (typeof member.profile_picture === 'string') {
        return member.profile_picture;
      } else if (member.profile_picture?.url) {
        return member.profile_picture.url;
      }
    }
    return undefined;
  };

  // Determine if this is the current user's own profile
  const isOwnProfile = Boolean(
    isAuthenticated && user && (user.username === member.username || user.id === member.id)
  );

  // Get the current cover image - use auth user data for own profile to get real-time updates
  const getCurrentCoverImage = () => {
    if (isOwnProfile && user?.coverImageUrl) {
      return user.coverImageUrl;
    }
    return member.coverImage;
  };

  const handleConnectionAction = async (action: string, targetUserId: number) => {
    setIsConnecting(true);
    let success = false;

    if (action === 'request') {
      // Optimistically update the local state
      setLocalConnectionStatus({
        status: 'pending',
        pending_type: 'sent',
        can_request: false,
        message: 'Request sent',
      });

      success = await sendConnectionRequest(targetUserId);

      if (!success) {
        // Revert on failure
        setLocalConnectionStatus(status);
      } else {
        // Refresh the actual status after successful action
        await refreshStatus();
      }
    }

    setIsConnecting(false);
  };

  const getConnectionButton = () => {
    // Show loading state while status is being fetched
    if (statusLoading) {
      return (
        <Button
          variant="outline"
          className="flex-1 min-w-[120px] text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold"
          disabled
        >
          <UserPlus className="h-4 w-4 mr-2 animate-pulse" />
          Loading...
        </Button>
      );
    }

    const isLoading = Boolean(isConnecting);

    if (!currentStatus || currentStatus.status === 'none') {
      return (
        <Button
          variant="outline"
          className="flex-1 min-w-[120px] text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold"
          onClick={() => handleConnectionAction('request', member.id)}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <UserPlus className="h-4 w-4 mr-2 animate-spin" />
              Connecting...
            </>
          ) : (
            <>
              <UserPlus className="h-4 w-4 mr-2" />
              Connect
            </>
          )}
        </Button>
      );
    }

    if (currentStatus.status === 'pending') {
      if (currentStatus.pending_type === 'sent') {
        return (
          <Button
            variant="outline"
            className="flex-1 min-w-[120px] text-[#5cc8ff] border-2 border-[#5cc8ff] bg-white hover:bg-[#5cc8ff]/10 font-extrabold"
            disabled
          >
            <Check className="h-4 w-4 mr-2" />
            Request Sent
          </Button>
        );
      } else if (currentStatus.pending_type === 'received') {
        // Don't show accept/decline buttons here - they're in the notification menu
        return (
          <Button
            variant="outline"
            className="flex-1 min-w-[120px] text-orange-600 border-2 border-orange-200 bg-white font-extrabold"
            disabled
          >
            <Check className="h-4 w-4 mr-2" />
            Request Received
          </Button>
        );
      }
    }

    if (currentStatus.status === 'accepted') {
      return (
        <Button
          variant="outline"
          className="flex-1 min-w-[120px] bg-[#5cc8ff]/10 text-[#5cc8ff] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff]/20 font-extrabold"
          onClick={() => setShowDisconnectDialog(true)}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <UserMinus className="h-4 w-4 mr-2 animate-spin" />
              Disconnecting...
            </>
          ) : (
            <>
              <Check className="h-4 w-4 mr-2" />
              Connected
            </>
          )}
        </Button>
      );
    }

    return null;
  };

  const handleDisconnect = async () => {
    setIsConnecting(true);

    // Store the current status in case we need to revert
    const previousStatus = localConnectionStatus;

    // Optimistically update to disconnected state
    setLocalConnectionStatus({
      status: 'none',
      can_request: true,
    });

    const success = await removeConnection(member.id);

    if (!success) {
      // Revert on failure
      setLocalConnectionStatus(previousStatus);
    } else {
      // Refresh the actual status after successful disconnection
      await refreshStatus();
    }

    setIsConnecting(false);
    setShowDisconnectDialog(false);
  };

  const handleStartConversation = async () => {
    if (!isAuthenticated) {
      // Redirect to login or show login modal
      return;
    }

    setIsStartingChat(true);
    try {
      // Get all conversations and check if there's one with this user
      const conversations = await messagingApi.getConversations();
      const existingConversation = conversations.find((conv) =>
        conv.participants.some((p) => p.id === String(member.id))
      );

      if (existingConversation) {
        // Open existing conversation
        openChat(existingConversation);
      } else {
        // Start a new conversation by opening a chat window
        // The messaging provider will handle creating the conversation
        const newConversation = {
          id: `new-${member.id}`,
          participants: [
            {
              id: String(member.id),
              username: member.username,
              firstName: member.firstName,
              lastName: member.lastName,
              avatar:
                member.profile_picture &&
                typeof member.profile_picture === 'object' &&
                member.profile_picture.url
                  ? { url: String(member.profile_picture.url) }
                  : member.profile_picture && typeof member.profile_picture === 'string'
                    ? { url: member.profile_picture }
                    : null,
            },
          ],
          unreadCount: 0,
        };
        openChat(newConversation);
      }
    } catch (error) {
      console.error('Error starting conversation:', error);
    } finally {
      setIsStartingChat(false);
    }
  };

  // Cover image editing functions
  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Reset the input value to allow selecting the same file again
    if (e.target) {
      e.target.value = '';
    }

    // Validate file type
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type. Please select PNG, JPG, JPEG, WEBP, or GIF images only.');
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size is too large. Please select an image under 5MB.');
      return;
    }

    setCoverImageFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (event) => {
      const result = event.target?.result;
      if (typeof result === 'string') {
        setCoverImagePreview(result);
      }
    };
    reader.readAsDataURL(file);
  };

  const handleCoverImageClick = () => {
    coverImageInputRef.current?.click();
  };

  const handleDeleteCoverImage = async () => {
    setIsUpdatingCoverImage(true);
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          coverImage: null, // Set to null to remove the cover image
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete cover image');
      }

      // Refresh the page to show the updated cover image
      window.location.reload();
    } catch (error) {
      console.error('Error deleting cover image:', error);
      toast.error('Failed to delete cover image. Please try again.');
    } finally {
      setIsUpdatingCoverImage(false);
    }
  };

  const handleUpdateCoverImage = async () => {
    if (!coverImageFile) return;

    // Validate file size before uploading (5MB limit)
    if (coverImageFile.size > 5 * 1024 * 1024) {
      toast.error('Cover image is too large. Please select an image smaller than 5MB.');
      return;
    }

    setIsUpdatingCoverImage(true);
    try {
      const formData = new FormData();
      formData.append('cover_image', coverImageFile);

      const response = await fetch('/api/user/cover/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to upload cover image');
      }

      const { url } = await response.json();

      // Update the user's profile with the new cover image
      if (user) {
        await updateProfile({ ...user, coverImageUrl: url });
      }

      setShowCoverImageDialog(false);
      setCoverImageFile(null);
      setCoverImagePreview(null);
      toast.success('Cover image updated successfully!');
    } catch (error) {
      console.error('Error updating cover image:', error);
      toast.error('Failed to update cover image. Please try again.');
    } finally {
      setIsUpdatingCoverImage(false);
    }
  };

  // Profile image handling functions
  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    // Reset the input value to allow selecting the same file again
    if (e.target) {
      e.target.value = '';
    }

    if (!file) {
      return;
    }

    try {
      // Check if file size is too large (limit to 8MB)
      if (file.size > 8 * 1024 * 1024) {
        toast.error('Image size is too large. Please select an image under 8MB.');
        return;
      }

      // Check if file type is allowed
      const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/webp', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Invalid file type. Please select PNG, JPG, JPEG, WEBP, or GIF images only.');
        return;
      }

      // Store the file object for upload
      setProfileImageFile(file);

      // Cancel any existing FileReader
      if (profileImageFileReaderRef.current) {
        profileImageFileReaderRef.current.abort();
      }

      // Use FileReader with proper error handling
      const reader = new FileReader();
      profileImageFileReaderRef.current = reader;

      reader.onload = (event) => {
        try {
          const result = event.target?.result;
          if (typeof result === 'string') {
            setProfileImagePreview(result);
          }
        } catch (error) {
          console.error('Error processing file reader result:', error);
          toast.error('Error processing image. Please try again.');
        } finally {
          profileImageFileReaderRef.current = null;
        }
      };

      reader.onerror = (error) => {
        console.error('FileReader error:', error);
        toast.error('Error reading file. Please try again.');
        setProfileImageFile(null);
        profileImageFileReaderRef.current = null;
      };

      reader.onabort = () => {
        setProfileImageFile(null);
        profileImageFileReaderRef.current = null;
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error in handleProfileImageChange:', error);
      toast.error('Error handling file. Please try again.');
      setProfileImageFile(null);
    }
  };

  const handleProfileImageClick = () => {
    profileImageInputRef.current?.click();
  };

  const handleUpdateProfileImage = async () => {
    if (!profileImageFile) return;

    setIsUpdatingProfileImage(true);
    try {
      const formData = new FormData();
      formData.append('avatar', profileImageFile);

      const response = await fetch('/api/user/avatar/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload profile image');
      }

      const { url } = await response.json();

      // Update the user's profile with the new avatar
      if (user) {
        await updateProfile({ ...user, avatarUrl: url });
      }

      setShowProfileImageDialog(false);
      setProfileImageFile(null);
      setProfileImagePreview(null);
      toast.success('Profile image updated successfully!');
    } catch (error) {
      console.error('Error updating profile image:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to update profile image. Please try again.'
      );
    } finally {
      setIsUpdatingProfileImage(false);
    }
  };

  // Helper function to get user's display name
  const getDisplayName = () => {
    if (member.firstName && member.lastName) {
      return `${member.firstName} ${member.lastName}`;
    }
    return member.name || member.username;
  };

  // Format the joined date
  const formatJoinedDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <>
      <div className="max-w-4xl mx-auto py-4">
        {/* Profile Header */}
        <div className="bg-white rounded-lg shadow overflow-hidden mb-8">
          {/* Cover Image */}
          <div className="h-48 relative">
            {getCurrentCoverImage() ? (
              <img
                src={getCurrentCoverImage()}
                alt={`${getDisplayName()}'s cover photo`}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="h-full w-full bg-gradient-to-br from-[#fef7cd] via-[#e0e7ff] via-[#fce7f3] to-[#dbeafe] opacity-90"></div>
            )}
            {isOwnProfile && (
              <Button
                variant="ghost"
                size="icon"
                className={`absolute top-4 right-4 ${
                  getCurrentCoverImage()
                    ? 'bg-white/20 hover:bg-white/40 text-white border border-white/30'
                    : 'bg-black/20 hover:bg-black/40 text-black border border-black/30'
                }`}
                onClick={() => setShowCoverImageDialog(true)}
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Profile Info */}
          <div className="relative px-6 pb-6">
            {/* Avatar */}
            <div className="absolute -top-16 left-6">
              <div className="relative">
                <UserAvatarWithRank
                  userId={member.id}
                  avatarUrl={getAvatarUrl() || '/images/avatar-placeholder.svg'}
                  displayName={getDisplayName()}
                  size="h-32 w-32"
                  containerSize="w-40 h-40"
                  showRankBadge={true}
                  userRoles={member.roles || []}
                />
                {isOwnProfile && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute -bottom-2 -right-2 bg-[#5cc8ff]/10 hover:bg-[#5cc8ff]/20 text-[#5cc8ff] h-8 w-8 rounded-full"
                    onClick={() => setShowProfileImageDialog(true)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>

            {/* User Info */}
            <div className="pt-28">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                <div>
                  <h1 className="text-2xl font-bold text-[#3d405b]">{getDisplayName()}</h1>
                  <p className="text-gray-500">@{member.username}</p>
                  {(member.job_title || member.company) && (
                    <div className="mt-1 text-gray-600">
                      {member.job_title && <div className="font-medium">{member.job_title}</div>}
                      {member.company && <div>{member.company}</div>}
                    </div>
                  )}
                </div>

                {!isOwnProfile && (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold"
                      onClick={handleStartConversation}
                      disabled={isStartingChat}
                    >
                      {isStartingChat ? (
                        <>
                          <MessageSquare className="h-4 w-4 mr-2 animate-pulse" />
                          Starting...
                        </>
                      ) : (
                        <>
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Message
                        </>
                      )}
                    </Button>
                    {getConnectionButton()}
                  </div>
                )}
              </div>

              {/* Bio */}
              {member.bio && <p className="mt-4 text-[#3d405b]">{member.bio}</p>}

              {/* User Details */}
              <div className="mt-4 flex flex-wrap gap-4">
                {member.location && (
                  <div className="flex items-center text-gray-500">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{member.location}</span>
                  </div>
                )}
                {member.contactInfo?.website && (
                  <div className="flex items-center text-gray-500">
                    <Globe className="h-4 w-4 mr-1" />
                    <a
                      href={member.contactInfo.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-[#5cc8ff]"
                    >
                      {member.contactInfo.website.replace(/^https?:\/\//, '')}
                    </a>
                  </div>
                )}
                <div className="flex items-center text-gray-500">
                  <Calendar className="h-4 w-4 mr-1" />
                  <span>Joined {formatJoinedDate(member.user_registered)}</span>
                </div>
              </div>

              {/* Social Links */}
              {(member.socialLinks?.twitter ||
                member.socialLinks?.linkedin ||
                member.socialLinks?.facebook ||
                member.socialLinks?.instagram) && (
                <div className="mt-4 flex gap-3">
                  {member.socialLinks.twitter && (
                    <a
                      href={member.socialLinks.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-500 hover:text-[#5cc8ff] transition-colors"
                    >
                      <TwitterSolid className="h-5 w-5" />
                    </a>
                  )}
                  {member.socialLinks.linkedin && (
                    <a
                      href={member.socialLinks.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-500 hover:text-[#5cc8ff] transition-colors"
                    >
                      <LinkedinSolid className="h-5 w-5" />
                    </a>
                  )}
                  {member.socialLinks.facebook && (
                    <a
                      href={member.socialLinks.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-500 hover:text-[#5cc8ff] transition-colors"
                    >
                      <FacebookSolid className="h-5 w-5" />
                    </a>
                  )}
                  {member.socialLinks.instagram && (
                    <a
                      href={member.socialLinks.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-500 hover:text-[#5cc8ff] transition-colors"
                    >
                      <InstagramSolid className="h-5 w-5" />
                    </a>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Profile Tabs */}
        <ProfileTabs member={member} initialTab={initialTab || 'about'} />
      </div>

      {/* Disconnect Confirmation Dialog */}
      <Dialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Disconnect from{' '}
              {member.firstName && member.lastName
                ? `${member.firstName} ${member.lastName}`
                : member.name}
              ?
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to disconnect from{' '}
              {member.firstName && member.lastName
                ? `${member.firstName} ${member.lastName}`
                : member.name}
              ? This will remove your connection and you&apos;ll need to send a new connection
              request to reconnect.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDisconnectDialog(false)}
              disabled={isConnecting}
              className="text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDisconnect}
              disabled={isConnecting}
              className="bg-red-600 text-white hover:bg-red-700 font-extrabold border-2 border-red-600"
            >
              {isConnecting ? (
                <>
                  <UserMinus className="h-4 w-4 mr-2 animate-spin" />
                  Disconnecting...
                </>
              ) : (
                <>
                  <UserMinus className="h-4 w-4 mr-2" />
                  Disconnect
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cover Image Editing Dialog */}
      <Dialog open={showCoverImageDialog} onOpenChange={setShowCoverImageDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Cover Image</DialogTitle>
            <DialogDescription>
              Upload a new cover image or remove the current one to use the default gradient.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Current Cover Image Preview */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Current Cover Image</Label>
              <div className="h-32 border-2 border-dashed border-gray-300 rounded-lg relative overflow-hidden">
                {member.coverImage ? (
                  <img
                    src={member.coverImage}
                    alt="Current cover"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="w-full h-full bg-gradient-to-br from-[#fef7cd] via-[#e0e7ff] via-[#fce7f3] to-[#dbeafe] opacity-90"></div>
                  </div>
                )}
              </div>
            </div>

            {/* Upload New Image */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Upload New Image</Label>
              <div className="h-32 border-2 border-dashed border-gray-300 rounded-lg relative overflow-hidden">
                {coverImagePreview ? (
                  <img
                    src={coverImagePreview}
                    alt="New cover preview"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <Upload className="h-8 w-8 mr-2" />
                    Click to upload
                  </div>
                )}
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                  <Button
                    type="button"
                    variant="secondary"
                    size="sm"
                    onClick={handleCoverImageClick}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Choose Image
                  </Button>
                </div>
              </div>
              <input
                ref={coverImageInputRef}
                type="file"
                accept="image/*"
                onChange={handleCoverImageChange}
                className="hidden"
              />
              <p className="text-xs text-gray-500">
                Recommended: 1200x600px, max 5MB. Supports PNG, JPG, JPEG, WEBP, GIF.
              </p>
            </div>
          </div>

          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            {member.coverImage && (
              <Button
                variant="destructive"
                onClick={handleDeleteCoverImage}
                disabled={isUpdatingCoverImage}
                className="w-full sm:w-auto"
              >
                {isUpdatingCoverImage ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Removing...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove Cover Image
                  </>
                )}
              </Button>
            )}
            <div className="flex gap-2 w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={() => setShowCoverImageDialog(false)}
                disabled={isUpdatingCoverImage}
                className="flex-1 sm:flex-none"
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpdateCoverImage}
                disabled={!coverImageFile || isUpdatingCoverImage}
                className="flex-1 sm:flex-none"
              >
                {isUpdatingCoverImage ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Update Cover
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Profile Image Editing Dialog */}
      <Dialog open={showProfileImageDialog} onOpenChange={setShowProfileImageDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Profile Image</DialogTitle>
            <DialogDescription>Upload a new profile image to update your avatar.</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Current Profile Image Preview */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Current Profile Image</Label>
              <div className="flex justify-center">
                <div className="h-24 w-24 rounded-full border-2 border-gray-300 relative overflow-hidden">
                  <img
                    src={getAvatarUrl() || ''}
                    alt="Current profile"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const currentSrc = e.currentTarget.src;
                      if (getAvatarUrl() && currentSrc !== getAvatarUrl()) {
                        e.currentTarget.src = getAvatarUrl() || '';
                      } else {
                        e.currentTarget.src = '';
                      }
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Upload New Image */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Upload New Image</Label>
              <div className="flex justify-center">
                <div
                  className="h-24 w-24 border-2 border-dashed border-gray-300 rounded-full relative overflow-hidden cursor-pointer"
                  onClick={handleProfileImageClick}
                >
                  {profileImagePreview ? (
                    <img
                      src={profileImagePreview}
                      alt="New profile preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full text-gray-500">
                      <Upload className="h-6 w-6" />
                    </div>
                  )}
                </div>
              </div>
              <input
                ref={profileImageInputRef}
                type="file"
                accept=".png,.jpg,.jpeg,.webp,.gif"
                onChange={handleProfileImageChange}
                className="hidden"
              />
              <p className="text-xs text-gray-500 text-center">
                Recommended: Square image, max 8MB. Supports PNG, JPG, JPEG, WEBP, GIF.
              </p>
            </div>
          </div>

          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowProfileImageDialog(false)}
              disabled={isUpdatingProfileImage}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateProfileImage}
              disabled={!profileImageFile || isUpdatingProfileImage}
            >
              {isUpdatingProfileImage ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Update Profile Image
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
