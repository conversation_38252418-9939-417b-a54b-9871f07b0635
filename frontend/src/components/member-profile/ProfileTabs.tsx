'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Member } from './MemberProfile';
import {
  User,
  MessageSquare,
  Users,
  Bookmark,
  HelpCircle,
  Award,
  ChevronDown,
  Archive,
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Import tab components
import { AboutTab } from './tabs/AboutTab';
import { PostsTab } from './tabs/PostsTab';
import { ConnectionsTab } from './tabs/ConnectionsTab';
import { BookmarksTab } from './tabs/BookmarksTab';
import { QATab } from './tabs/QATab';
import { IQScoreTab } from './tabs/IQScoreTab';
import { LegacyMessagesTab } from './tabs/LegacyMessagesTab';

// Base navigation options with icons
const baseTabOptions = [
  { id: 'about', label: 'About', icon: <User className="h-4 w-4" /> },
  { id: 'posts', label: 'Posts', icon: <MessageSquare className="h-4 w-4" /> },
  { id: 'connections', label: 'Connections', icon: <Users className="h-4 w-4" /> },
  { id: 'bookmarks', label: 'Bookmarks', icon: <Bookmark className="h-4 w-4" /> },
  { id: 'qa', label: 'Q&A', icon: <HelpCircle className="h-4 w-4" /> },
  { id: 'iqscore', label: 'IQ Score', icon: <Award className="h-4 w-4" /> },
];

// Legacy messages tab - only for own profile
const legacyMessagesTab = {
  id: 'legacy-messages',
  label: 'Legacy Messages (v1)',
  icon: <Archive className="h-4 w-4" />,
};

interface ProfileTabsProps {
  member: Member;
  initialTab?: string;
}

export function ProfileTabs({ member, initialTab = 'about' }: ProfileTabsProps) {
  const [activeTab, setActiveTab] = useState(initialTab);
  const [isOpen, setIsOpen] = useState(false);
  const searchParams = useSearchParams();
  const { user } = useAuth();

  // Check if current user is viewing their own profile
  const isOwnProfile = user && String(user.id) === String(member.id);

  // Create tab options based on whether user is viewing own profile
  const tabOptions = isOwnProfile ? [...baseTabOptions, legacyMessagesTab] : baseTabOptions;

  // Update active tab when search params change
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam) {
      // Validate that it's a valid tab
      const validTab = tabOptions.find((tab) => tab.id === tabParam);
      if (validTab) {
        setActiveTab(tabParam);
      }
    } else {
      setActiveTab(initialTab);
    }
  }, [searchParams, initialTab]);

  // Also listen to popstate events for browser navigation
  useEffect(() => {
    const handleRouteChange = () => {
      const params = new URLSearchParams(window.location.search);
      const tabParam = params.get('tab');
      if (tabParam) {
        const validTab = tabOptions.find((tab) => tab.id === tabParam);
        if (validTab) {
          setActiveTab(tabParam);
        }
      }
    };

    // Listen for custom tab change events from RightSidebar
    const handleTabChange = (event: CustomEvent) => {
      const { tab } = event.detail;
      const validTab = tabOptions.find((t) => t.id === tab);
      if (validTab) {
        setActiveTab(tab);
      }
    };

    window.addEventListener('popstate', handleRouteChange);
    window.addEventListener('tabChange', handleTabChange as EventListener);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
      window.removeEventListener('tabChange', handleTabChange as EventListener);
    };
  }, []);

  // Get the active tab information
  const currentTab = tabOptions.find((tab) => tab.id === activeTab) ?? tabOptions[0]!;

  // Render the active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'about':
        return <AboutTab member={member} />;
      case 'posts':
        return <PostsTab member={member} />;
      case 'connections':
        return <ConnectionsTab member={member} />;
      case 'bookmarks':
        return <BookmarksTab member={member} />;
      case 'qa':
        return <QATab member={member} />;
      case 'iqscore':
        return <IQScoreTab member={member} />;
      case 'legacy-messages':
        return isOwnProfile ? <LegacyMessagesTab member={member} /> : <AboutTab member={member} />;
      default:
        return <AboutTab member={member} />;
    }
  };

  return (
    <div className="w-full mt-8">
      {/* Dropdown Navigation */}
      <div className="mb-6">
        <DropdownMenu onOpenChange={setIsOpen} modal={false}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="w-full md:w-64 flex items-center justify-between gap-2 py-6 text-base text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[rgba(92,200,255,0.2)] hover:text-[#3d405b] font-extrabold"
            >
              <div className="flex items-center gap-3">
                <div className="text-[#5cc8ff]">{currentTab.icon}</div>
                <span>{currentTab.label}</span>
              </div>
              <ChevronDown
                className={`h-4 w-4 text-[#3d405b] transition-transform ${isOpen ? 'rotate-180' : ''}`}
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-64 p-2" style={{ borderRadius: '27px' }}>
            {tabOptions.map((tab) => (
              <DropdownMenuItem
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-3 px-4 py-3 rounded-full transition-all duration-200 cursor-pointer ${
                  activeTab === tab.id
                    ? '!bg-[rgba(92,200,255,0.2)] !text-[#3d405b]'
                    : 'text-gray-700 hover:!bg-[rgba(92,200,255,0.2)] hover:!text-[#3d405b] focus:!bg-[rgba(92,200,255,0.2)] focus:!text-[#3d405b]'
                }`}
              >
                <div className="h-5 w-5">{tab.icon}</div>
                <span className="font-medium">{tab.label}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Tab Content */}
      <div className="w-full">{renderTabContent()}</div>
    </div>
  );
}
