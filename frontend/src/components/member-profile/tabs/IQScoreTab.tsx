'use client';

import { useState, useEffect } from 'react';
import { Trophy, Award, Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { iqScoreAPI, type UserIQData, type Rank } from '@/lib/api/iq-score';
import { useAuthStatus } from '@/hooks/queries/useAuth';
import Image from 'next/image';

interface IQScoreTabProps {
  member: {
    id: number;
    username: string;
    iqScore?: number;
  };
}

interface UserStats {
  posts: number;
  comments: number;
  upvotes: number;
  connections: number;
}

export function IQScoreTab({ member }: IQScoreTabProps) {
  const [userIQ, setUserIQ] = useState<UserIQData | null>(null);
  const [ranks, setRanks] = useState<Rank[]>([]);
  const [userStats, setUserStats] = useState<UserStats>({
    posts: 0,
    comments: 0,
    upvotes: 0,
    connections: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuthStatus();

  // Check if we're viewing the current user's profile
  // const isCurrentUser = isLoggedIn && user?.id === member.id;

  useEffect(() => {
    fetchUserData();
  }, [member.id]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchUserData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch user IQ data and ranks in parallel
      const [iqResponse, ranksResponse] = await Promise.all([
        iqScoreAPI.getUserIQ(member.id),
        iqScoreAPI.getRanks(),
      ]);

      if (iqResponse.success) {
        setUserIQ(iqResponse.data);
      }

      if (ranksResponse.success) {
        setRanks(ranksResponse.data);
      }

      // Fetch user statistics
      await fetchUserStats();
    } catch (err) {
      console.error('Error fetching user IQ data:', err);
      setError('Failed to load IQ score data');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserStats = async () => {
    try {
      // Fetch user posts using the correct API structure
      const postsResponse = await fetch(`/api/wp-proxy/posts/user/${member.id}?per_page=100`, {
        credentials: 'include',
      });

      let posts = 0;
      if (postsResponse.ok) {
        const postsData = await postsResponse.json();
        posts = postsData.pagination?.total || 0;
      }

      // Fetch user connections count
      let connections = 0;
      try {
        const connectionsResponse = await fetch(`/api/wp-proxy/connections?userId=${member.id}`, {
          credentials: 'include',
        });
        if (connectionsResponse.ok) {
          const connectionsData = await connectionsResponse.json();
          connections = connectionsData.total || connectionsData.connections?.length || 0;
        }
      } catch (err) {
        console.error('Error fetching connections:', err);
      }

      // Fetch user comments count using the WordPress API directly
      let comments = 0;
      try {
        const wpApiUrl = process.env.NEXT_PUBLIC_WORDPRESS_URL || 'http://tourismiq.local';
        const commentsResponse = await fetch(
          `${wpApiUrl}/wp-json/tourismiq/v1/users/${member.id}/comments-count`,
          {
            credentials: 'include',
          }
        );
        if (commentsResponse.ok) {
          const commentsData = await commentsResponse.json();
          comments = commentsData.comments_count || 0;
        }
      } catch (err) {
        console.error('Error fetching comments count:', err);
      }

      setUserStats({
        posts,
        comments,
        upvotes: 0, // Could be calculated from post upvotes if needed
        connections,
      });
    } catch (err) {
      console.error('Error fetching user stats:', err);
    }
  };

  const getNextRankInfo = () => {
    if (!userIQ || ranks.length === 0) return null;
    return iqScoreAPI.getNextRankInfo(userIQ.iq_score, ranks);
  };

  const isRankAchieved = (rank: Rank) => {
    if (!userIQ) return false;
    return userIQ.iq_score >= rank.min_points;
  };

  const getRankIcon = (rankName: string, isFounder: boolean = false) => {
    const rankNameLower = rankName.toLowerCase();
    const suffix = isFounder ? '-fc' : '';
    return `/images/icons/${rankNameLower}${suffix}.svg`;
  };

  const isFounder = Array.isArray(user?.roles) && user.roles.includes('administrator');

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="animate-pulse">
            <div className="h-40 w-40 rounded-full bg-gray-200 mx-auto mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-48 mx-auto mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-32 mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !userIQ) {
    return (
      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
          <p className="text-red-600">{error || 'Failed to load IQ score data'}</p>
        </div>
      </div>
    );
  }

  const nextRankInfo = getNextRankInfo();

  return (
    <div className="space-y-6">
      {/* IQ Score Summary */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex flex-col items-center text-center mb-6">
          <div className="relative">
            <div
              className="h-40 w-40 rounded-full border-8 flex items-center justify-center"
              style={{ borderColor: 'rgba(92, 200, 255, 0.2)' }}
            >
              <div className="text-center">
                <div className="text-4xl font-bold" style={{ color: '#3d405b' }}>
                  {iqScoreAPI.formatScore(userIQ.iq_score)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">IQ Points</div>
              </div>
            </div>
            <div className="absolute -top-2 -right-2 bg-yellow-400 text-yellow-900 rounded-full p-2">
              <Trophy className="h-6 w-6" />
            </div>
          </div>
          <h2 className="mt-4 text-2xl font-bold">{userIQ.rank.name}</h2>
          {nextRankInfo && nextRankInfo.nextRank ? (
            <p className="text-gray-600 dark:text-gray-300">
              {nextRankInfo.pointsNeeded} points to reach {nextRankInfo.nextRank.name}
            </p>
          ) : (
            <p className="text-amber-600 dark:text-amber-400 font-medium">
              Maximum rank achieved! 🎉
            </p>
          )}
          {userIQ.position && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              #{userIQ.position} on leaderboard
            </p>
          )}
        </div>

        {/* Progress to next rank */}
        {nextRankInfo && nextRankInfo.nextRank && (
          <div className="w-full mb-6">
            <div className="flex items-center justify-between text-sm mb-2">
              <span className="text-gray-500 dark:text-gray-400">
                Progress to {nextRankInfo.nextRank.name}
              </span>
              <span className="font-medium">{Math.round(nextRankInfo.progress)}% complete</span>
            </div>
            <Progress value={nextRankInfo.progress} className="h-4" />
          </div>
        )}

        {/* User Statistics */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-3 rounded-lg" style={{ backgroundColor: 'rgba(92, 200, 255, 0.2)' }}>
            <div className="text-2xl font-bold" style={{ color: '#3d405b' }}>
              {userStats.posts}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Posts</div>
          </div>
          <div className="p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {userStats.comments}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Comments</div>
          </div>
          <div className="p-3 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {userStats.connections}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Connections</div>
          </div>
        </div>
      </div>

      {/* Rank System (replacing Achievements) */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Award className="h-5 w-5 mr-2 text-yellow-500" />
          Rank Progression
        </h2>
        <div className="space-y-6">
          {ranks.map((rank) => {
            const achieved = isRankAchieved(rank);
            const isCurrent = userIQ.rank.name === rank.name;

            // Calculate progress for current rank
            let progress = 0;
            if (achieved) {
              progress = 100;
            } else if (isCurrent && nextRankInfo) {
              progress = nextRankInfo.progress;
            }

            return (
              <div
                key={rank.name}
                className={`flex items-center p-4 rounded-lg border transition-all duration-300 ${
                  isCurrent
                    ? 'shadow-md'
                    : achieved
                      ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                      : 'bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-600 opacity-60'
                }`}
                style={
                  isCurrent
                    ? {
                        backgroundColor: 'rgba(92, 200, 255, 0.2)',
                        borderColor: '#5cc8ff',
                      }
                    : {}
                }
              >
                {/* Circular Progress Indicator */}
                <div className="relative w-20 h-20 mr-4">
                  {/* Center content */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="relative w-20 h-20">
                      <Image
                        src={getRankIcon(rank.name, isFounder)}
                        alt={`${rank.name} rank badge`}
                        width={80}
                        height={80}
                        className="object-contain"
                        onError={(e) => {
                          // Fallback to founder version if regular version doesn't exist
                          const target = e.target as HTMLImageElement;
                          if (!target.src.includes('-fc')) {
                            target.src = getRankIcon(rank.name, true);
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-1">
                    <h3 className="font-bold text-lg">{rank.name}</h3>
                    {isCurrent && (
                      <Badge variant="default" className="text-xs">
                        Current Rank
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-1">
                    {rank.min_points === 0 ? '0' : iqScoreAPI.formatScore(rank.min_points)} -{' '}
                    {rank.max_points === 99999999 ? '∞' : iqScoreAPI.formatScore(rank.max_points)}{' '}
                    points
                  </p>
                  {isCurrent && progress < 100 && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {Math.round(progress)}% complete
                    </p>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  {achieved ? (
                    <Star className="h-6 w-6 text-yellow-400" fill="currentColor" />
                  ) : (
                    <span className="text-xs text-gray-400 px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded">
                      Locked
                    </span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
