'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Calendar, MessageSquare, CheckCircle, HelpCircle } from 'lucide-react';
import { useUserForumQuestions } from '@/hooks/queries/useForum';
import { Question } from '@/types/forum';

interface QATabProps {
  member: {
    id: number;
    username: string;
    name?: string;
    display_name?: string;
    firstName?: string;
    lastName?: string;
    avatar: string;
  };
}

function QuestionCard({ question, author }: { question: Question; author: QATabProps['member'] }) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const stripHtml = (html: string) => {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  };

  const getTruncatedContent = (content: string, maxLength: number = 200) => {
    const plainText = stripHtml(content);
    if (plainText.length <= maxLength) return plainText;
    return plainText.substr(0, maxLength).trim() + '...';
  };

  // Get author avatar
  const authorAvatar = author?.avatar || '';

  // Get author name
  const authorName =
    author?.name ||
    author?.display_name ||
    (author?.firstName && author?.lastName ? `${author.firstName} ${author.lastName}` : '') ||
    author?.username ||
    'Unknown Author';

  return (
    <Link href={`/forum/${question.id}`} className="block">
      <Card className="hover:shadow-lg hover:scale-[1.02] transition-all duration-200 cursor-pointer w-full">
        <CardContent className="px-8 py-6">
          {/* Question Status */}
          {question.isResolved && (
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Badge
                  variant="default"
                  className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                >
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Resolved
                </Badge>
              </div>
            </div>
          )}

          {/* Title */}
          <h3 className="text-lg font-semibold mb-3 line-clamp-2 group-hover:text-[#5cc8ff] transition-colors">
            {stripHtml(question.title)}
          </h3>

          {/* Content Preview */}
          <p className="text-gray-600 dark:text-gray-300 text-base mb-6 line-clamp-3">
            {getTruncatedContent(question.content)}
          </p>

          {/* Author and Date */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={authorAvatar} alt={authorName} />
                <AvatarFallback>
                  {authorName
                    .split(' ')
                    .map((n: string) => n[0])
                    .join('')
                    .toUpperCase()
                    .substr(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">{authorName}</p>
                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatDate(question.createdAt)}
                </div>
              </div>
            </div>
          </div>

          {/* Question Stats */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center">
                <MessageSquare className="h-4 w-4 mr-1" />
                <span>
                  {question.commentCount || 0} {question.commentCount === 1 ? 'answer' : 'answers'}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

export function QATab({ member }: QATabProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [allQuestions, setAllQuestions] = useState<Question[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const questionsPerPage = 6;
  const observer = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  const { questions, loading, error, pagination } = useUserForumQuestions(
    member.id,
    currentPage,
    questionsPerPage
  );

  // Load more questions for infinite scroll
  const loadMoreQuestions = useCallback(async () => {
    if (isLoadingMore || !hasMore) return;

    setIsLoadingMore(true);
    try {
      const nextPage = currentPage + 1;
      const response = await fetch(
        `/api/wp-proxy/forum/user/${member.id}?page=${nextPage}&per_page=${questionsPerPage}`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );

      if (response.ok) {
        const data = await response.json();
        const newQuestions = (data.questions || []).map((q: any) => ({
          id: q.id,
          title: q.title?.rendered || q.title || '',
          content: q.content?.rendered || q.content || '',
          createdAt: q.date || q.created_at || new Date().toISOString(),
          isResolved: q.meta?._is_resolved || false,
          commentCount: q.comment_count || 0,
        }));

        setAllQuestions((prev) => [...prev, ...newQuestions]);
        setCurrentPage(nextPage);
        setHasMore(nextPage < (data.pagination?.total_pages || 1));
      }
    } catch (error) {
      console.error('Error loading more questions:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, hasMore, currentPage, member.id, questionsPerPage]);

  // Setup intersection observer for infinite scroll
  useEffect(() => {
    if (loading) return () => {};

    if (observer.current) {
      observer.current.disconnect();
    }

    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting && hasMore) {
          loadMoreQuestions();
        }
      },
      { threshold: 0.5 }
    );

    if (loadingRef.current) {
      observer.current.observe(loadingRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [loadMoreQuestions, loading, hasMore]);

  // Initialize questions when first load completes
  useEffect(() => {
    if (!loading && questions.length > 0 && allQuestions.length === 0) {
      setAllQuestions(questions);
      setHasMore(currentPage < (pagination?.total_pages || 1));
    }
  }, [loading, questions, allQuestions.length, currentPage, pagination?.total_pages]);

  const memberName =
    member.name ||
    member.display_name ||
    (member.firstName && member.lastName ? `${member.firstName} ${member.lastName}` : '') ||
    member.username;

  if (loading && allQuestions.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Forum Questions by {memberName}</h2>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-500">Loading questions...</span>
        </div>
      </div>
    );
  }

  if (error && allQuestions.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Forum Questions by {memberName}</h2>
        <div className="text-center py-8">
          <p className="text-red-500 dark:text-red-400">Error: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">
          Q&A Questions by {memberName} {pagination.total > 0 && `(${pagination.total})`}
        </h2>
      </div>

      {allQuestions.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <HelpCircle className="h-12 w-12 mx-auto" />
          </div>
          <p className="text-gray-500 dark:text-gray-400 text-lg">No questions yet.</p>
          <p className="text-gray-400 dark:text-gray-500 text-sm mt-2">
            {memberName} hasn&apos;t asked any questions in the Q&A yet.
          </p>
        </div>
      ) : (
        <>
          <div className="flex flex-col gap-6">
            {allQuestions.map((question) => (
              <QuestionCard key={question.id} question={question} author={member} />
            ))}
          </div>

          {/* Loading indicator for infinite scroll */}
          {isLoadingMore && (
            <div className="flex justify-center py-6">
              <div className="flex items-center gap-3">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                <span className="text-[#3d405b] font-medium">Loading more questions...</span>
              </div>
            </div>
          )}

          {/* End of results indicator */}
          {!hasMore && allQuestions.length > 0 && (
            <div className="text-center py-4 text-gray-500">
              <p>You've reached the end. {allQuestions.length} questions loaded.</p>
            </div>
          )}

          {/* Invisible element for intersection observer */}
          {hasMore && !isLoadingMore && <div ref={loadingRef} className="h-10" />}
        </>
      )}
    </div>
  );
}
