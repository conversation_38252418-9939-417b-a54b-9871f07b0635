'use client';

import { useState, useEffect } from 'react';
import { bookmarksAPI, Bookmark } from '@/lib/api/bookmarks';
import { useAuthStatus } from '@/hooks/queries/useAuth';
import { useBookmarks } from '@/hooks/useBookmarks';
import Link from 'next/link';
import { format } from 'date-fns';
import { X } from 'lucide-react';

interface BookmarksTabProps {
  member: {
    id: number;
    username: string;
  };
}

interface PostData {
  id: number;
  title: { rendered: string };
  slug: string;
  excerpt: { rendered: string };
  link: string;
  date: string;
  _embedded?: {
    author?: Array<{ name: string }>;
    'wp:term'?: Array<Array<{ name: string }>>;
    'wp:featuredmedia'?: Array<{ source_url: string }>;
  };
}

export function BookmarksTab({ member }: BookmarksTabProps) {
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuthStatus();
  const { toggleBookmark } = useBookmarks();

  // Check if viewing own profile
  const isOwnProfile = user?.id === member.id;

  useEffect(() => {
    const fetchBookmarks = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get bookmarked post IDs from localStorage
        const bookmarkedPostIds = bookmarksAPI.getBookmarkedPostIds(
          isOwnProfile ? user?.id : member.id
        );

        if (bookmarkedPostIds.length === 0) {
          setBookmarks([]);
          return;
        }

        // Fetch post data from WordPress API via Next.js proxy
        const postPromises = bookmarkedPostIds.map(async (postId) => {
          const response = await fetch(`/api/wp-proxy/posts/${postId}?_embed=true`, {
            credentials: 'include',
          });
          if (!response.ok) {
            console.warn(`Failed to fetch post ${postId}`);
            return null;
          }
          return response.json();
        });

        const posts = await Promise.all(postPromises);
        const validPosts = posts.filter((post): post is PostData => post !== null);

        // Convert to bookmark format
        const bookmarkData: Bookmark[] = validPosts.map((post) => {
          const category = post._embedded?.['wp:term']?.[0]?.[0]?.name || 'Uncategorized';
          const author = post._embedded?.author?.[0]?.name || 'Unknown Author';
          const featuredImage = post._embedded?.['wp:featuredmedia']?.[0]?.source_url;

          // Extract plain text from excerpt
          const excerpt = post.excerpt.rendered.replace(/<[^>]*>/g, '').trim();

          return {
            id: post.id,
            title: post.title.rendered,
            slug: post.slug,
            excerpt: excerpt,
            url: post.link,
            category: category,
            featured_image: featuredImage || undefined,
            bookmarked_at: post.date, // Use post date as fallback
            author: author,
          };
        });

        setBookmarks(bookmarkData);
      } catch (err) {
        console.error('Error fetching bookmarks:', err);
        setError(err instanceof Error ? err.message : 'Failed to load bookmarks');
      } finally {
        setLoading(false);
      }
    };

    fetchBookmarks();
  }, [member.id, isOwnProfile, user?.id]);

  // Listen for bookmark changes from other components
  useEffect(() => {
    if (!isOwnProfile) return () => {}; // Only listen for changes on own profile

    const handleBookmarkChange = (event: CustomEvent) => {
      const { postId, bookmarked } = event.detail;

      if (!bookmarked) {
        // Bookmark was removed, remove from local state
        setBookmarks((prev) => prev.filter((bookmark) => bookmark.id !== postId));
      } else {
        // Bookmark was added, we could either refresh or add the new bookmark
        // For simplicity, let's refresh the entire list
        window.location.reload();
      }
    };

    window.addEventListener('bookmarkChanged', handleBookmarkChange as EventListener);

    return () => {
      window.removeEventListener('bookmarkChanged', handleBookmarkChange as EventListener);
    };
  }, [isOwnProfile]);

  const handleDeleteBookmark = async (postId: number, event: React.MouseEvent) => {
    event.preventDefault(); // Prevent navigation when clicking delete
    event.stopPropagation(); // Stop event bubbling

    if (!isOwnProfile) return; // Only allow deleting own bookmarks

    try {
      // Use the shared bookmark hook to toggle bookmark
      toggleBookmark(postId);

      // Remove from local state immediately for instant UI feedback
      setBookmarks((prev) => prev.filter((bookmark) => bookmark.id !== postId));
    } catch (error) {
      console.error('Error deleting bookmark:', error);
    }
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Bookmarks</h2>
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">Loading bookmarks...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Bookmarks</h2>
        <div className="text-center py-8">
          <p className="text-red-500">Error: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-6">Bookmarks</h2>

      {bookmarks.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            {isOwnProfile ? "You haven't" : `${member.username} hasn't`} saved any bookmarks yet.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {bookmarks.map((bookmark) => (
            <div key={bookmark.id} className="relative group">
              <Link
                href={`/posts/${bookmark.slug}`}
                className="block border-b border-gray-200 dark:border-gray-700 pb-4 mb-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 p-3 rounded-lg transition-colors cursor-pointer"
              >
                <div className="flex gap-4">
                  {bookmark.featured_image && (
                    <div className="flex-shrink-0">
                      <img
                        src={bookmark.featured_image}
                        alt={bookmark.title}
                        className="w-20 h-20 object-cover rounded-lg"
                      />
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-dark-text dark:text-white mb-2 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                      {bookmark.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-2">
                      {bookmark.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center gap-4">
                        <span>By {bookmark.author}</span>
                        <span className="px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded-full text-xs">
                          {bookmark.category}
                        </span>
                      </div>
                      <span>
                        Saved on {format(new Date(bookmark.bookmarked_at), 'MMM d, yyyy')}
                      </span>
                    </div>
                  </div>
                </div>
              </Link>

              {/* Delete button - only show for own profile */}
              {isOwnProfile && (
                <button
                  onClick={(e) => handleDeleteBookmark(bookmark.id, e)}
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg z-10"
                  title="Remove bookmark"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
