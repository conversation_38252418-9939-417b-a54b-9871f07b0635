import { JSX } from 'react';
import { Mail, Phone } from 'lucide-react';
import { Member } from '../MemberProfile';

interface AboutTabProps {
  member: Member;
}

export function AboutTab({ member }: AboutTabProps) {
  // Define the type for detail items
  type DetailItem = {
    icon: JSX.Element;
    label: string;
    value: string | string[] | undefined;
    isLink?: boolean;
    href?: string;
    isArray?: boolean;
  };

  // Combine all the details we want to show

  const details: DetailItem[] = [
    // Only show contact info that's NOT already in the profile header
    // Email and Phone are unique to the About section
    ...(member.contactInfo?.email
      ? [
          {
            icon: <Mail className="h-4 w-4 text-gray-400" />,
            label: 'Email',
            value: member.contactInfo.email,
            isLink: true,
            href: `mailto:${member.contactInfo.email}`,
          },
        ]
      : []),
    ...(member.contactInfo?.phone
      ? [
          {
            icon: <Phone className="h-4 w-4 text-gray-400" />,
            label: 'Phone',
            value: member.contactInfo.phone,
            isLink: true,
            href: `tel:${member.contactInfo.phone}`,
          },
        ]
      : []),
  ];

  return (
    <div className="space-y-6">
      {/* Details Section - Only show if there are details */}
      {details.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Contact Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 gap-x-8">
            {details.map((detail, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 h-5 w-5 mr-3 mt-0.5">{detail.icon}</div>
                <div className="min-w-0 flex-1">
                  <div className="text-sm text-gray-500">{detail.label}</div>
                  {detail.isLink ? (
                    <a
                      href={detail.href}
                      className="text-dark-text hover:text-blue-600 transition-colors break-words"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {detail.value}
                    </a>
                  ) : (
                    <div className="text-dark-text break-words">{detail.value}</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Categories */}
      {member.categories && member.categories.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Categories</h2>
          <div className="flex flex-wrap gap-2">
            {member.categories.map((category, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                {category}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
