'use client';

import { useState, useEffect } from 'react';
import { Member } from '../MemberProfile';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Archive, MessageSquare, Clock, ChevronRight, ArrowLeft, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface LegacyConversation {
  conversation_id: number;
  other_user: {
    id: number;
    name: string;
    avatar: string;
  };
  last_message: string;
  last_message_time: string;
  unread_count: number;
}

interface LegacyMessage {
  id: number;
  content: string;
  time: string;
  is_from_me: boolean;
  author: {
    id: number;
    name: string;
    avatar: string;
  };
  status: number;
}

interface LegacyConversationData {
  conversation_id: number;
  other_user: {
    id: number;
    name: string;
    avatar: string;
  };
  messages: LegacyMessage[];
}

interface LegacyMessagesTabProps {
  member: Member;
}

export function LegacyMessagesTab({ member: _ }: LegacyMessagesTabProps) {
  const [conversations, setConversations] = useState<LegacyConversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<LegacyConversationData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [loadingConversation, setLoadingConversation] = useState(false);
  const [view, setView] = useState<'list' | 'conversation'>('list');

  // Fetch conversations on component mount
  useEffect(() => {
    fetchConversations();
  }, []);

  const fetchConversations = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/wp-proxy/legacy-messages/conversations', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch conversations: ${response.status}`);
      }

      const data = await response.json();
      setConversations(data);
    } catch (error) {
      console.error('Error fetching legacy conversations:', error);
      toast.error('Failed to load legacy messages');
    } finally {
      setLoading(false);
    }
  };

  const fetchConversationMessages = async (conversationId: number) => {
    try {
      setLoadingConversation(true);
      const response = await fetch(`/api/wp-proxy/legacy-messages/conversation/${conversationId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch conversation messages');
      }

      const data = await response.json();
      setSelectedConversation(data);
      setView('conversation');
    } catch (error) {
      console.error('Error fetching conversation messages:', error);
      toast.error('Failed to load conversation');
    } finally {
      setLoadingConversation(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return dateString;
    }
  };

  const truncateMessage = (message: string, maxLength: number = 100) => {
    if (!message) return '';
    return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
  };

  const backToList = () => {
    setView('list');
    setSelectedConversation(null);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Archive className="h-5 w-5" />
            Legacy Messages (v1)
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading legacy messages...</span>
        </CardContent>
      </Card>
    );
  }

  // Conversation view
  if (view === 'conversation' && selectedConversation) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={backToList} className="p-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-2">
              <div>
                <CardTitle className="text-lg">
                  Conversation with {selectedConversation.other_user.name}
                </CardTitle>
                <CardDescription className="text-sm">
                  Legacy messages from the old system
                </CardDescription>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loadingConversation ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading conversation...</span>
            </div>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {selectedConversation.messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.is_from_me ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.is_from_me ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    <p
                      className={`text-xs mt-1 ${message.is_from_me ? 'text-blue-100' : 'text-gray-500'}`}
                    >
                      {formatDate(message.time)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Conversations list view
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Archive className="h-5 w-5" />
          Legacy Messages (v1)
        </CardTitle>
        <CardDescription>
          Your archived messages from the old messaging system. These are read-only.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {conversations.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Legacy Messages</h3>
            <p className="text-gray-500">
              You don't have any archived messages from the old system.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">
                {conversations.length} conversation{conversations.length !== 1 ? 's' : ''} found
              </p>
            </div>

            <div className="space-y-3">
              {conversations.map((conversation) => (
                <div
                  key={conversation.conversation_id}
                  className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => fetchConversationMessages(conversation.conversation_id)}
                >
                  <div className="flex items-center gap-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900 truncate">
                          {conversation.other_user.name}
                        </h4>
                        <div className="flex items-center gap-2">
                          {conversation.unread_count > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {conversation.unread_count} unread
                            </Badge>
                          )}
                          <ChevronRight className="h-4 w-4 text-gray-400" />
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 truncate mt-1">
                        {truncateMessage(conversation.last_message)}
                      </p>
                      <div className="flex items-center gap-1 mt-2 text-xs text-gray-400">
                        <Clock className="h-3 w-3" />
                        {formatDate(conversation.last_message_time)}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
