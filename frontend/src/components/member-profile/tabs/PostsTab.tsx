/*********************************************
# frontend/src/components/member-profile/tabs/PostsTab.tsx
# 02/07/2025 12:40pm Reduced 3-dot menu container corner radius from 25px to 22px for better visual balance
# 02/07/2025 12:45pm Updated placeholder images to use the same beautiful gradient as member directory listings
**********************************************/

import React, { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Loader2,
  Calendar,
  MessageSquare,
  Heart,
  Edit,
  Trash2,
  MoreHorizontal,
  FileText,
  Newspaper,
  Users,
  Briefcase,
  Presentation,
  Video,
  Mic,
  Play,
  GraduationCap,
  Download,
  Lightbulb,
  BookOpen,
} from 'lucide-react';
import { Post } from '@/lib/types';
import { useAuth } from '@/contexts/auth-context';
import { EditPostDialog } from '@/components/post-edit/EditPostDialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
// Helper function to get category styles based on category name
function getCategoryStyles(categoryName: string): string {
  const name = categoryName.toLowerCase().trim();

  // News category
  if (name === 'news' || name.includes('news')) {
    return 'bg-[#ff5ce0] hover:bg-[#e54cc7]';
  }

  // Resource categories - comprehensive list using orange color
  if (
    name === 'blog post' ||
    name === 'blog-post' ||
    name === 'blog_post' ||
    name === 'book' ||
    name === 'books' ||
    name === 'case study' ||
    name === 'case-study' ||
    name === 'case_study' ||
    name === 'course' ||
    name === 'courses' ||
    name === 'presentation' ||
    name === 'presentations' ||
    name === 'template' ||
    name === 'templates' ||
    name === 'video' ||
    name === 'videos' ||
    name === 'webinar' ||
    name === 'webinars' ||
    name === 'whitepaper' ||
    name === 'whitepapers' ||
    name === 'press release' ||
    name === 'press-release' ||
    name === 'press_release' ||
    name.includes('resource') ||
    name.includes('tool') ||
    name.includes('guide') ||
    name.includes('tutorial') ||
    name.includes('download')
  ) {
    return 'bg-[#ffa15c] hover:bg-[#e68943]';
  }

  // Thought Leadership and Questions
  if (
    name === 'thought leadership' ||
    name === 'thought-leadership' ||
    name.includes('thought') ||
    name === 'questions' ||
    name.includes('question') ||
    name.includes('opinion') ||
    name.includes('insight')
  ) {
    return 'bg-[#5cc8ff] hover:bg-[#43b0e6]';
  }

  // Events
  if (
    name === 'events' ||
    name.includes('event') ||
    name.includes('conference') ||
    name.includes('meetup')
  ) {
    return 'bg-[#1dd05b] hover:bg-[#17b749]';
  }

  // Jobs
  if (
    name === 'jobs' ||
    name.includes('job') ||
    name.includes('career') ||
    name.includes('hiring') ||
    name.includes('employment')
  ) {
    return 'bg-[#ff625c] hover:bg-[#e54943]';
  }

  // Default fallback color (thought leadership blue)
  return 'bg-[#5cc8ff] hover:bg-[#43b0e6]';
}

// Helper function to get category icon based on category name
function getCategoryIcon(categoryName: string) {
  const name = categoryName.toLowerCase().trim();

  if (name === 'news' || name.includes('news')) {
    return Newspaper;
  }

  if (name === 'thought leadership' || name.includes('thought')) {
    return Lightbulb;
  }

  if (name === 'people-on-the-move' || name.includes('people')) {
    return Users;
  }

  if (name === 'jobs' || name.includes('job')) {
    return Briefcase;
  }

  if (name === 'events' || name.includes('event')) {
    return Users;
  }

  if (name === 'courses' || name === 'course') {
    return GraduationCap;
  }

  if (name === 'podcasts' || name === 'podcast') {
    return Mic;
  }

  if (name === 'videos' || name === 'video') {
    return Play;
  }

  if (name === 'books' || name === 'book') {
    return BookOpen;
  }

  if (name === 'webinars' || name === 'webinar') {
    return Video;
  }

  if (name === 'presentations' || name === 'presentation') {
    return Presentation;
  }

  if (name === 'press-releases' || name === 'press-release') {
    return Newspaper;
  }

  if (name === 'blog-posts' || name === 'blog-post') {
    return FileText;
  }

  if (
    name === 'whitepapers' ||
    name === 'whitepaper' ||
    name === 'case-studies' ||
    name === 'case-study' ||
    name === 'templates' ||
    name === 'template'
  ) {
    return FileText;
  }

  if (
    name.includes('resource') ||
    name.includes('tool') ||
    name.includes('guide') ||
    name.includes('download')
  ) {
    return Download;
  }

  return FileText;
}

interface PostsTabProps {
  member: {
    id: number;
    username: string;
    name?: string;
    display_name?: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    profile_picture?:
      | string
      | {
          url?: string;
          [key: string]: unknown;
        };
  };
}

function PostCard({
  post,
  author,
  isOwner,
  onEdit,
  onDelete,
}: {
  post: Post;
  author: PostsTabProps['member'];
  isOwner: boolean;
  onEdit: (post: Post) => void;
  onDelete: (post: Post) => void;
}) {
  const { user } = useAuth();
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const stripHtml = (html: string) => {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  };

  const getTruncatedContent = (content: string, maxLength: number = 200) => {
    const plainText = stripHtml(content);
    if (plainText.length <= maxLength) return plainText;
    return plainText.substr(0, maxLength).trim() + '...';
  };

  // Get featured image
  const featuredImage = post._embedded?.['wp:featuredmedia']?.[0];
  const categories = post._embedded?.['wp:term']?.[0] || [];

  // Get author avatar - use auth user data for current user's posts for real-time updates
  const authorAvatar = (() => {
    // If this is the current user's post and we have updated auth data, use it
    if (isOwner && user?.avatarUrl) {
      return user.avatarUrl;
    }

    // Otherwise use the static author data
    return (
      (typeof author?.profile_picture === 'string'
        ? author.profile_picture
        : author?.profile_picture?.url) ||
      author?.avatar ||
      post._embedded?.author?.[0]?.avatar_urls?.['96'] ||
      ''
    );
  })();

  // Get author name - use auth user data for current user's posts for real-time updates
  const authorName = (() => {
    // If this is the current user's post and we have updated auth data, use it
    if (isOwner && user?.name) {
      return user.name;
    }

    // Otherwise use the static author data
    return (
      author?.name ||
      author?.display_name ||
      (author?.firstName && author?.lastName ? `${author.firstName} ${author.lastName}` : '') ||
      post._embedded?.author?.[0]?.name ||
      author?.username ||
      'Unknown Author'
    );
  })();

  return (
    <div className="relative">
      <Card className="p-0 hover:shadow-lg transition-all duration-200">
        <CardContent className="p-0">
          {/* Edit/Delete Menu - Only show for post owner */}
          {isOwner && (
            <div className="absolute top-2 right-2 z-10">
              <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 bg-white/80 hover:bg-white"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" style={{ borderRadius: '22px' }}>
                  <DropdownMenuItem
                    onClick={() => onEdit(post)}
                    className="rounded-full hover:!bg-[rgba(92,200,255,0.2)] hover:!text-[#3d405b] focus:!bg-[rgba(92,200,255,0.2)] focus:!text-[#3d405b] transition-all duration-200"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => onDelete(post)}
                    className="text-red-600 focus:text-red-600 rounded-full hover:!bg-[rgba(239,68,68,0.2)] hover:!text-red-600 focus:!bg-[rgba(239,68,68,0.2)] focus:!text-red-600 transition-all duration-200"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
          <Link href={`/posts/${post.slug}`} className="block cursor-pointer">
            {/* Featured Image or Placeholder */}
            <div className="relative h-48 w-full">
              {featuredImage ? (
                <img
                  src={featuredImage.source_url}
                  alt={featuredImage.alt_text || post.title.rendered}
                  className="w-full h-full object-cover rounded-t-lg"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-[#fef7cd] via-[#e0e7ff] via-[#fce7f3] to-[#dbeafe] opacity-90 rounded-t-lg">
                  <img src="/images/logo.svg" alt="TourismIQ" className="w-24 h-auto opacity-30" />
                </div>
              )}
            </div>

            <div className="p-6">
              {/* Categories */}
              {categories.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-3">
                  {categories.slice(0, 3).map((category: { id: number; name: string }) => {
                    const CategoryIcon = getCategoryIcon(category.name);
                    const categoryStyles = getCategoryStyles(category.name);
                    return (
                      <span
                        key={category.id}
                        className={`inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full text-white border-0 transition-all duration-200 cursor-pointer ${categoryStyles}`}
                      >
                        <CategoryIcon className="h-4 w-4 text-white" />
                        {category.name}
                      </span>
                    );
                  })}
                </div>
              )}

              {/* Title */}
              <h3 className="text-lg font-semibold mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                {stripHtml(post.title.rendered)}
              </h3>

              {/* Excerpt */}
              <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                {getTruncatedContent(post.excerpt.rendered || post.content.rendered)}
              </p>

              {/* Author and Date */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={authorAvatar} alt={authorName} />
                    <AvatarFallback>
                      {authorName
                        .split(' ')
                        .map((n: string) => n[0])
                        .join('')
                        .toUpperCase()
                        .substr(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{authorName}</p>
                    <div className="flex items-center text-xs text-gray-500">
                      <Calendar className="h-3 w-3 mr-1" />
                      {formatDate(post.date)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Post Stats */}
              <div className="flex items-center gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-1 text-gray-400">
                  <Heart className="h-4 w-4" />
                  <span className="text-sm">{post.upvotes?.count || post.meta?._upvotes || 0}</span>
                </div>
                <div className="flex items-center gap-1 text-gray-400">
                  <MessageSquare className="h-4 w-4" />
                  <span className="text-sm">
                    {post.total_comments || post.meta?.total_comments || 0}
                  </span>
                </div>
              </div>
            </div>
          </Link>
        </CardContent>
      </Card>
    </div>
  );
}

export function PostsTab({ member }: PostsTabProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [allPosts, setAllPosts] = useState<Post[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<any>(null);

  const postsPerPage = 6;
  const [editingPost, setEditingPost] = useState<Post | null>(null);
  const [deletingPost, setDeletingPost] = useState<Post | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const { user } = useAuth();
  const observer = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  // Load initial posts
  const loadInitialPosts = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(
        `/api/wp-proxy/posts/user/${member.id}?page=1&per_page=${postsPerPage}`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );

      if (response.ok) {
        const data = await response.json();
        const posts = data.posts || [];

        setAllPosts(posts);
        setPagination(data.pagination);
        setCurrentPage(1);
        setHasMore(1 < (data.pagination?.total_pages || 1));
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to fetch posts');
      }
    } catch (err) {
      console.error('Error loading initial posts:', err);
      setError('Failed to fetch posts');
    } finally {
      setLoading(false);
    }
  }, [member.id, postsPerPage]);

  // Load more posts for infinite scroll
  const loadMorePosts = useCallback(async () => {
    if (isLoadingMore || !hasMore || loading) return;

    setIsLoadingMore(true);
    try {
      const nextPage = currentPage + 1;
      const response = await fetch(
        `/api/wp-proxy/posts/user/${member.id}?page=${nextPage}&per_page=${postsPerPage}`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );

      if (response.ok) {
        const data = await response.json();
        const newPosts = data.posts || [];

        if (newPosts.length > 0) {
          setAllPosts((prev) => [...prev, ...newPosts]);
          setCurrentPage(nextPage);
          setHasMore(nextPage < (data.pagination?.total_pages || 1));
        } else {
          // No more posts available
          setHasMore(false);
        }
      }
    } catch (error) {
      console.error('Error loading more posts:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, hasMore, currentPage, member.id, postsPerPage, loading]);

  // Load initial posts on mount or member change
  useEffect(() => {
    loadInitialPosts();
  }, [loadInitialPosts]);

  // Setup intersection observer for infinite scroll
  useEffect(() => {
    if (loading) return () => {};

    if (observer.current) {
      observer.current.disconnect();
    }

    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting && hasMore) {
          loadMorePosts();
        }
      },
      { threshold: 0.5 }
    );

    if (loadingRef.current) {
      observer.current.observe(loadingRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [loadMorePosts, loading, hasMore]);

  // Clean up body pointer-events when dialog closes
  useEffect(() => {
    if (!deletingPost) {
      // Force remove pointer-events: none from body when dialog closes
      document.body.style.pointerEvents = '';
    }
  }, [deletingPost]);

  const handleEditPost = (post: Post) => {
    setEditingPost(post);
  };

  const handleDeletePost = (post: Post) => {
    setDeletingPost(post);
  };

  const confirmDeletePost = async () => {
    if (!deletingPost) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/wp-proxy/posts/${deletingPost.id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        // Remove the deleted post from the list
        setAllPosts((prev) => prev.filter((post) => post.id !== deletingPost.id));
      } else {
        const errorData = await response.json();
        console.error('Failed to delete post:', errorData);
        alert('Failed to delete post. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting post:', error);
      alert('Error deleting post. Please try again.');
    } finally {
      // Always clear the loading state and close the dialog
      setIsDeleting(false);
      setDeletingPost(null);
      // Force cleanup of body pointer-events
      setTimeout(() => {
        document.body.style.pointerEvents = '';
      }, 100);
    }
  };

  const handleEditSuccess = () => {
    setEditingPost(null);
    // Refresh the posts list
    loadInitialPosts();
  };

  // Check if the current user is the owner of the profile
  const isOwner = user?.id === member.id;

  const memberName =
    member.name ||
    member.display_name ||
    (member.firstName && member.lastName ? `${member.firstName} ${member.lastName}` : '') ||
    member.username;

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Posts by {memberName}</h2>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-500">Loading posts...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Posts by {memberName}</h2>
        <div className="text-center py-8">
          <p className="text-red-500 dark:text-red-400">Error: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Posts by {memberName} {pagination?.total > 0 && `(${pagination.total})`}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {allPosts.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <MessageSquare className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-gray-500 dark:text-gray-400 text-lg">No posts yet.</p>
              <p className="text-gray-400 dark:text-gray-500 text-sm mt-2">
                {memberName} hasn&apos;t shared any posts yet.
              </p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {allPosts.map((post: Post) => (
                  <PostCard
                    key={post.id}
                    post={post}
                    author={member}
                    isOwner={isOwner}
                    onEdit={handleEditPost}
                    onDelete={handleDeletePost}
                  />
                ))}
              </div>

              {/* Loading indicator for infinite scroll */}
              {isLoadingMore && (
                <div className="flex justify-center py-6">
                  <div className="flex items-center gap-3">
                    <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                    <span className="text-[#3d405b] font-medium">Loading more posts...</span>
                  </div>
                </div>
              )}

              {/* End of results indicator */}
              {!hasMore && allPosts.length > 0 && (
                <div className="text-center py-4 text-gray-500">
                  <p>You've reached the end. {allPosts.length} posts loaded.</p>
                </div>
              )}

              {/* Invisible element for intersection observer */}
              <div ref={loadingRef} className="h-10" />
            </>
          )}
        </CardContent>
      </Card>

      {/* Edit Post Dialog */}
      <EditPostDialog
        post={editingPost}
        open={!!editingPost}
        onOpenChange={(open) => !open && setEditingPost(null)}
        onSuccess={handleEditSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingPost} onOpenChange={(open) => !open && setDeletingPost(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Post</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{deletingPost?.title?.rendered}&quot;? This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isDeleting}
              className="border-2 border-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b]"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeletePost}
              disabled={isDeleting}
              className="bg-[#5cc8ff] hover:bg-[#5cc8ff]/80 text-white font-extrabold"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
