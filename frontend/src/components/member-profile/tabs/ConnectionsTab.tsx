/*********************************************
# frontend/src/components/member-profile/tabs/ConnectionsTab.tsx
# 02/06/2025 11:45am Applied Jobs Hub search bar styling for consistency
**********************************************/

import { Button } from '@/components/ui/button';
import { useConnectionsList } from '@/hooks/queries/useConnections';
import { useAuth } from '@/contexts/auth-context';
import Link from 'next/link';
import { Loader2, MapPin, Briefcase } from 'lucide-react';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import SearchBar from '@/components/forum/search-bar';
import { useState, useCallback, useMemo } from 'react';

interface ConnectionsTabProps {
  member: {
    id: number;
    username: string;
  };
}

export function ConnectionsTab({ member }: ConnectionsTabProps) {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');

  // Determine if this is the current user's own profile (for messaging purposes)
  const isOwnProfile = user?.id === member.id || user?.username === member.username;

  // Always fetch connections for the profile being viewed (member.id)
  const { connections, loading, error } = useConnectionsList(member.id);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Ensure connections is always an array and sort alphabetically
  const safeConnections = Array.isArray(connections)
    ? connections.sort((a, b) => {
        const nameA = (a.display_name || a.name || a.username || '').toLowerCase();
        const nameB = (b.display_name || b.name || b.username || '').toLowerCase();
        return nameA.localeCompare(nameB);
      })
    : [];

  // Filter connections based on search query
  const filteredConnections = useMemo(() => {
    if (!searchQuery.trim()) {
      return safeConnections;
    }

    const query = searchQuery.toLowerCase();
    return safeConnections.filter((connection) => {
      const name = (connection.display_name || connection.name || '').toLowerCase();
      const username = (connection.username || '').toLowerCase();
      const jobTitle = (connection.job_title || '').toLowerCase();
      const company = (connection.company || '').toLowerCase();
      const location = (connection.location || '').toLowerCase();
      const bio = (connection.bio || '').toLowerCase();

      return (
        name.includes(query) ||
        username.includes(query) ||
        jobTitle.includes(query) ||
        company.includes(query) ||
        location.includes(query) ||
        bio.includes(query)
      );
    });
  }, [safeConnections, searchQuery]);

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Connections</h2>
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-500">Loading connections...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Connections</h2>
        <div className="text-center py-8">
          <p className="text-red-500 dark:text-red-400">Error: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">
          Connections {safeConnections.length > 0 && `(${safeConnections.length})`}
        </h2>
      </div>

      {safeConnections.length > 0 && (
        <div className="mb-6">
          <SearchBar
            onSearch={handleSearch}
            placeholder="Search connections by name, company, location..."
            inputClassName="bg-white rounded-full h-12 border border-gray-200 px-6 text-base"
          />
        </div>
      )}

      {/* Search Results Indicator */}
      {searchQuery && safeConnections.length > 0 && (
        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            {filteredConnections.length === 0
              ? `No connections found for "${searchQuery}"`
              : `Showing ${filteredConnections.length} connection${filteredConnections.length !== 1 ? 's' : ''} for "${searchQuery}"`}
          </p>
        </div>
      )}

      {safeConnections.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            {isOwnProfile
              ? "You haven't made any connections yet."
              : `${member.username} hasn't made any connections yet.`}
          </p>
        </div>
      ) : filteredConnections.length === 0 && searchQuery ? (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            No connections match your search criteria.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredConnections.map((connection) => (
            <div
              key={connection.user_id || connection.id}
              className="flex flex-col p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center mb-3">
                <div className="mr-3">
                  <UserAvatarWithRank
                    userId={
                      typeof connection.user_id === 'string'
                        ? parseInt(connection.user_id)
                        : connection.user_id || connection.id || 0
                    }
                    avatarUrl={
                      (typeof connection.profile_picture === 'string'
                        ? connection.profile_picture
                        : connection.profile_picture?.url) ||
                      connection.avatar ||
                      '/images/avatar-placeholder.svg'
                    }
                    displayName={
                      connection.display_name || connection.name || connection.username || 'User'
                    }
                    size="h-12 w-12"
                    containerSize="w-14 h-14"
                    userRoles={(connection as any).roles || []}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium truncate">
                    {connection.display_name || connection.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                    @{connection.username}
                  </p>
                </div>
              </div>

              {/* Additional info */}
              {(connection.job_title || connection.company) && (
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300 mb-2">
                  <Briefcase className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span className="truncate">
                    {connection.job_title && connection.company
                      ? `${connection.job_title} at ${connection.company}`
                      : connection.job_title || connection.company}
                  </span>
                </div>
              )}

              {connection.location && (
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300 mb-2">
                  <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span className="truncate">{connection.location}</span>
                </div>
              )}

              {connection.bio && (
                <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-3">
                  {connection.bio}
                </p>
              )}

              <Button asChild variant="outline" size="sm" className="mt-auto gap-1">
                <Link href={`/profile/${connection.slug || connection.username}`}>
                  View Profile
                </Link>
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
