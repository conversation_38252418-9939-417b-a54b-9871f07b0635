/*********************************************
# frontend/src/components/rfps/RfpsHub.tsx
# 02/06/2025 11:50am Removed extra left padding (pl-[30px]) to match content width of other pages
# 02/07/2025 2:45pm Added back to top button functionality matching feed component styling
# 02/07/2025 3:30pm Implemented true grid view: updated card layout to use 2-column grid (md:grid-cols-2) for better responsive design
**********************************************/

'use client';

import { useState, useEffect, useCallback } from 'react';
import { Filter, Calendar, SortAsc, SortDesc, Grid3X3, List } from 'lucide-react';
import { Rfp, RfpsSearchParams, searchRfps, getRfpFilterOptions } from '@/lib/api/rfps';
import { RfpCard } from './RfpCard';
import { RfpCompactCard } from './RfpCompactCard';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import SearchBar from '@/components/forum/search-bar';
import { cn } from '@/lib/utils';

interface RfpsHubProps {
  initialRfps?: Rfp[];
  initialTotal?: number;
}

export function RfpsHub({ initialRfps = [], initialTotal = 0 }: RfpsHubProps) {
  const [rfps, setRfps] = useState<Rfp[]>(initialRfps);
  const [total, setTotal] = useState(initialTotal);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<RfpsSearchParams>({
    per_page: 2000, // Large number to get all results
    orderby: 'date',
    order: 'desc',
  });

  // Filter options
  const [filterOptions, setFilterOptions] = useState<{
    categories: Array<{ value: string; label: string }>;
    statuses: Array<{ value: string; label: string }>;
    countries: string[];
    states: string[];
    organizations: string[];
  }>({
    categories: [],
    statuses: [],
    countries: [],
    states: [],
    organizations: [],
  });

  // UI states
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);
  const [viewMode, setViewMode] = useState<'card' | 'compact'>('compact');
  const [showFilters, setShowFilters] = useState(false);

  // Handle search from SearchBar component
  const handleSearch = useCallback((query: string) => {
    setSearchTerm(query);
  }, []);

  // Load filter options on mount
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const options = await getRfpFilterOptions();
        setFilterOptions(options);
      } catch (err) {
        console.error('Failed to load filter options:', err);
      }
    };

    loadFilterOptions();
  }, []);

  // Count active filters
  useEffect(() => {
    const count = Object.entries(filters).filter(([key, value]) => {
      if (key === 'per_page' || key === 'orderby' || key === 'order' || key === 'page')
        return false;
      return value !== undefined && value !== null && value !== '';
    }).length;

    if (searchTerm) {
      setActiveFiltersCount(count + 1);
    } else {
      setActiveFiltersCount(count);
    }
  }, [filters, searchTerm]);

  // Search and filter RFPs
  const searchRfpsWithFilters = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const searchParams: RfpsSearchParams = {
        ...filters,
        page: 1,
        ...(searchTerm && { search: searchTerm }),
      };

      const response = await searchRfps(searchParams);

      setRfps(response.rfps);
      setTotal(response.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load RFPs');
      console.error('Error searching RFPs:', err);
    } finally {
      setLoading(false);
    }
  }, [filters, searchTerm]);

  // Trigger search when filters or search term change
  useEffect(() => {
    searchRfpsWithFilters();
  }, [searchRfpsWithFilters]);

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setFilters({
      per_page: 2000,
      orderby: 'status_then_date',
      order: 'desc',
    });
  };

  // Update filter
  const updateFilter = (key: keyof RfpsSearchParams, value: string | undefined) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  // Update sort
  const updateSort = (orderby: string, order: string) => {
    setFilters((prev) => ({
      ...prev,
      orderby: orderby as 'date' | 'title' | 'modified' | 'status_then_date',
      order: order as 'asc' | 'desc',
    }));
  };

  return (
    <div className="min-h-screen bg-[#eff1f4]">
      {/* Header */}
      <div className="border-b border-gray-200">
        <div className="max-w-[1360px] mx-auto py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">RFPs Hub</h1>
              <p className="text-gray-600 mt-1">
                Discover Request for Proposal opportunities in tourism and travel
              </p>
            </div>

            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500">
                {total} RFP{total !== 1 ? 's' : ''} found
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-[1300px] mx-auto py-6">
        {/* Standalone Search with Advanced Search Button */}
        <div className="mb-6">
          <div className="flex items-center gap-3">
            <div className="flex-1">
              <SearchBar
                onSearch={handleSearch}
                placeholder="Search by title, organization..."
                defaultValue={searchTerm}
                inputClassName="bg-white rounded-full h-12 border border-gray-200 px-6 text-base"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="rounded-full bg-white border-2 border-[#5cc8ff] text-[#3d405b] h-12 px-6 font-semibold flex items-center gap-2 shadow-none"
            >
              <Filter className="h-4 w-4 text-[#3d405b]" />
              Advanced Search
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="text-xs ml-1">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </div>
        </div>

        {/* Collapsible Filters */}
        {showFilters && (
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Advanced Filters
                </CardTitle>
                {activeFiltersCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="text-xs h-auto p-1"
                  >
                    Clear All
                  </Button>
                )}
              </div>
            </CardHeader>

            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Status Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Status</label>
                  <Select
                    value={filters.status || ''}
                    onValueChange={(value) =>
                      updateFilter('status', value === 'all' ? undefined : value)
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      {filterOptions.statuses.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          {status.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Category Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Category</label>
                  <Select
                    value={filters.category || ''}
                    onValueChange={(value) =>
                      updateFilter('category', value === 'all' ? undefined : value)
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {filterOptions.categories.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Secondary Row of Filters */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                {/* Country Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Country</label>
                  <Select
                    value={filters.country || ''}
                    onValueChange={(value) =>
                      updateFilter('country', value === 'all' ? undefined : value)
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="All countries" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Countries</SelectItem>
                      {filterOptions.countries.map((country) => (
                        <SelectItem key={country} value={country}>
                          {country}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* State Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    State/Province
                  </label>
                  <Select
                    value={filters.state || ''}
                    onValueChange={(value) =>
                      updateFilter('state', value === 'all' ? undefined : value)
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="All states" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[200px] overflow-y-auto">
                      <SelectItem value="all">All States</SelectItem>
                      {filterOptions.states.map((state) => (
                        <SelectItem key={state} value={state}>
                          {state}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Organization Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Organization
                  </label>
                  <Select
                    value={filters.organization || ''}
                    onValueChange={(value) =>
                      updateFilter('organization', value === 'all' ? undefined : value)
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="All organizations" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Organizations</SelectItem>
                      {filterOptions.organizations.map((organization) => (
                        <SelectItem key={organization} value={organization}>
                          {organization}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Sort Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">Sort by:</label>
            <Select
              value={`${filters.orderby}-${filters.order}`}
              onValueChange={(value) => {
                const [orderby, order] = value.split('-');
                updateSort(orderby || 'date', order || 'desc');
              }}
            >
              <SelectTrigger className="w-[180px] bg-white rounded-full border h-12 px-6 font-semibold">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date-desc">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>Newest First</span>
                  </div>
                </SelectItem>
                <SelectItem value="date-asc">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>Oldest First</span>
                  </div>
                </SelectItem>
                <SelectItem value="status_then_date-desc">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>Status</span>
                  </div>
                </SelectItem>
                <SelectItem value="title-asc">
                  <div className="flex items-center gap-2">
                    <SortAsc className="h-4 w-4" />
                    <span>Title A-Z</span>
                  </div>
                </SelectItem>
                <SelectItem value="title-desc">
                  <div className="flex items-center gap-2">
                    <SortDesc className="h-4 w-4" />
                    <span>Title Z-A</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-lg overflow-hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode('card')}
              className={cn(
                'rounded-none h-8 px-3 bg-white transition-colors',
                viewMode === 'card' && 'bg-[#5cc8ff] !shadow-none',
                viewMode === 'card' ? 'hover:bg-[#5cc8ff]' : 'hover:bg-[rgba(92,200,255,0.15)]'
              )}
            >
              <Grid3X3 className="h-4 w-4 text-[#3d405b]" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode('compact')}
              className={cn(
                'rounded-none h-8 px-3 bg-white transition-colors',
                viewMode === 'compact' && 'bg-[#5cc8ff] !shadow-none',
                viewMode === 'compact' ? 'hover:bg-[#5cc8ff]' : 'hover:bg-[rgba(92,200,255,0.15)]'
              )}
            >
              <List className="h-4 w-4 text-[#3d405b]" />
            </Button>
          </div>
        </div>

        {/* RFPs List */}
        <div className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              {error}
            </div>
          )}

          {loading ? (
            // Loading state
            <div className="space-y-4">
              <div className="text-center py-8">
                <div className="text-lg text-gray-600 mb-4">Loading RFPs...</div>
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
              </div>
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="opacity-50">
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : rfps.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg mb-2">No RFPs found</div>
              <p className="text-gray-400">Try adjusting your search criteria or filters</p>
              {activeFiltersCount > 0 && (
                <Button variant="outline" onClick={clearFilters} className="mt-4">
                  Clear all filters
                </Button>
              )}
            </div>
          ) : (
            <div
              className={
                viewMode === 'card' ? 'grid grid-cols-1 md:grid-cols-2 gap-6' : 'space-y-2'
              }
            >
              {rfps.map((rfp) =>
                viewMode === 'card' ? (
                  <RfpCard key={rfp.id} rfp={rfp} />
                ) : (
                  <RfpCompactCard key={rfp.id} rfp={rfp} />
                )
              )}

              {rfps.length > 0 && (
                <div className="text-center py-6 text-gray-500 text-sm">
                  Showing all {rfps.length} RFP{rfps.length !== 1 ? 's' : ''}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
