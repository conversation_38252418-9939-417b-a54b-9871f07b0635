'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  Search,
  X,
  Clock,
  ArrowRight,
  MessageSquare,
  User,
  Building,
  FileText,
  Briefcase,
  Clipboard,
  MapPin,
  Calendar,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CustomSearchDialog } from '@/components/ui/CustomSearchDialog';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';
import Link from 'next/link';

interface SearchResult {
  id: number;
  title?: string;
  name?: string;
  question?: string;
  excerpt?: string;
  content?: string;
  description?: string;
  link: string;
  type: 'post' | 'user' | 'vendor' | 'forum' | 'rfp' | 'job';
  author?: string;
  avatar_url?: string;
  featured_media?: string;
  categories?: string[];
  job_title?: string;
  company?: string;
  votes?: number;
  comments_count?: number;
  location?: string;
  budget?: string;
  is_paid?: boolean;
  date?: string;
  job_type?: string;
  salary_range?: string;
  deadline?: string;
  status?: string;
  category?: string;
}

interface SearchResults {
  posts: SearchResult[];
  users: SearchResult[];
  vendors: SearchResult[];
  forum: SearchResult[];
  rfps: SearchResult[];
  jobs: SearchResult[];
  total: number;
  totalPosts?: number;
}

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const TABS = [
  { id: 'posts', label: 'Posts', icon: FileText },
  { id: 'users', label: 'People', icon: User },
  { id: 'vendors', label: 'Vendors', icon: Building },
  { id: 'forum', label: 'Forum', icon: MessageSquare },
  { id: 'rfps', label: 'RFPs', icon: Briefcase },
  { id: 'jobs', label: 'Jobs', icon: Clipboard },
];

export function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResults>({
    posts: [],
    users: [],
    vendors: [],
    forum: [],
    rfps: [],
    jobs: [],
    total: 0,
  });
  const [activeTab, setActiveTab] = useState('posts');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('search-recent');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch {
        setRecentSearches([]);
      }
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback(
    (searchQuery: string) => {
      const updated = [searchQuery, ...recentSearches.filter((s) => s !== searchQuery)].slice(0, 5);
      setRecentSearches(updated);
      localStorage.setItem('search-recent', JSON.stringify(updated));
    },
    [recentSearches]
  );

  // Search function - only searches once, no per-tab filtering
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      setResults({ posts: [], users: [], vendors: [], forum: [], rfps: [], jobs: [], total: 0 });
      setIsLoading(false);
      return;
    }

    // Cancel any previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    try {
      // Create new abort controller for this request
      abortControllerRef.current = new AbortController();

      // Always search all types - no filtering by tab
      const response = await fetch(`/api/search?q=${encodeURIComponent(searchQuery)}&limit=25`, {
        signal: abortControllerRef.current.signal,
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setResults(data);
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }
      console.error('Search error:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Handle search input change with debouncing
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (query.trim()) {
      setIsLoading(true);
      searchTimeoutRef.current = setTimeout(() => {
        performSearch(query);
      }, 300);
    } else {
      setResults({ posts: [], users: [], vendors: [], forum: [], rfps: [], jobs: [], total: 0 });
      setIsLoading(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query, performSearch]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setQuery('');
      setActiveTab('posts');
      setSelectedIndex(-1);
      setResults({ posts: [], users: [], vendors: [], forum: [], rfps: [], jobs: [], total: 0 });
    }
  }, [isOpen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Memoized current results based on active tab
  const currentResults = useMemo((): SearchResult[] => {
    return results[activeTab as keyof Omit<SearchResults, 'total' | 'totalPosts'>] || [];
  }, [results, activeTab]);

  // Keyboard navigation with auto-scroll
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex((prev) => {
            const newIndex = prev < currentResults.length - 1 ? prev + 1 : prev;
            // Scroll to the selected item
            setTimeout(() => {
              const selectedElement = resultsRef.current?.querySelector(
                `[data-result-index="${newIndex}"]`
              );
              if (selectedElement) {
                selectedElement.scrollIntoView({
                  behavior: 'smooth',
                  block: 'nearest',
                });
              }
            }, 0);
            return newIndex;
          });
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex((prev) => {
            const newIndex = prev > -1 ? prev - 1 : -1;
            // Scroll to the selected item
            setTimeout(() => {
              if (newIndex >= 0) {
                const selectedElement = resultsRef.current?.querySelector(
                  `[data-result-index="${newIndex}"]`
                );
                if (selectedElement) {
                  selectedElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                  });
                }
              } else {
                // Scroll to top when no item is selected
                resultsRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
              }
            }, 0);
            return newIndex;
          });
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && currentResults[selectedIndex]) {
            const result = currentResults[selectedIndex];
            saveRecentSearch(query);
            onClose();
            // Navigate to result
            window.location.href = result.link;
          }
          break;
        case 'Escape':
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, currentResults, query, saveRecentSearch, onClose]);

  // Get result icon
  const getResultIcon = (type: string) => {
    switch (type) {
      case 'post':
        return FileText;
      case 'user':
        return User;
      case 'vendor':
        return Building;
      case 'forum':
        return MessageSquare;
      case 'rfp':
        return Briefcase;
      case 'job':
        return Clipboard;
      default:
        return FileText;
    }
  };

  // Handle result click
  const handleResultClick = () => {
    saveRecentSearch(query);
    onClose();
  };

  // Handle recent search click
  const handleRecentSearchClick = (recentQuery: string) => {
    setQuery(recentQuery);
    setSelectedIndex(-1);
  };

  return (
    <CustomSearchDialog isOpen={isOpen} onClose={onClose}>
      <div className="overflow-hidden">
        {/* Search Input */}
        <div className="flex items-center px-6 py-4 border-b border-gray-100">
          <Search className="h-5 w-5 text-gray-400 mr-3 flex-shrink-0" />
          <Input
            ref={inputRef}
            type="text"
            placeholder="Search posts, people, vendors, forum, and RFPs..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="flex-1 border-0 bg-transparent text-lg placeholder:text-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none"
            style={{ fontSize: '18px' }}
          />
          {isLoading && (
            <div className="animate-spin rounded-full h-5 w-5 border-2 border-gray-300 border-t-blue-500 mr-3 flex-shrink-0"></div>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full flex-shrink-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Tabs */}
        <div className="flex bg-gray-50/50 overflow-x-auto scrollbar-hide">
          {TABS.map((tab) => {
            const Icon = tab.icon;
            const count =
              tab.id === 'posts'
                ? results.totalPosts || results.posts.length
                : results[tab.id as keyof Omit<SearchResults, 'total' | 'totalPosts'>]?.length || 0;

            return (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  setSelectedIndex(-1);
                }}
                className={`flex items-center space-x-2 px-3 lg:px-4 py-3 text-sm font-medium transition-all flex-shrink-0 justify-center min-w-fit ${
                  activeTab === tab.id
                    ? 'text-blue-600 bg-white border-b-2 border-blue-500 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }`}
              >
                <Icon className="h-4 w-4 flex-shrink-0" />
                <span className="whitespace-nowrap">{tab.label}</span>
                {count > 0 && (
                  <Badge
                    variant="secondary"
                    className="ml-1 text-xs bg-gray-100 text-gray-600 flex-shrink-0"
                  >
                    {count}
                  </Badge>
                )}
              </button>
            );
          })}
        </div>

        {/* Results */}
        <div ref={resultsRef} className="max-h-96 overflow-y-auto bg-white">
          {!query.trim() && recentSearches.length > 0 && (
            <div className="p-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                <Clock className="h-4 w-4 mr-2 text-gray-500" />
                Recent Searches
              </h3>
              <div className="space-y-1">
                {recentSearches.map((recentQuery, index) => (
                  <button
                    key={index}
                    onClick={() => handleRecentSearchClick(recentQuery)}
                    className="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-50 text-sm text-gray-700 transition-colors"
                  >
                    {recentQuery}
                  </button>
                ))}
              </div>
            </div>
          )}

          {query.trim() && currentResults.length === 0 && !isLoading && (
            <div className="p-12 text-center">
              <Search className="h-16 w-16 text-gray-300 mx-auto mb-6" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No results found</h3>
              <p className="text-gray-500 text-sm">
                Try adjusting your search terms or check a different category.
              </p>
            </div>
          )}

          {currentResults.length > 0 && (
            <div className="p-2">
              {currentResults.map((result, index) => {
                const Icon = getResultIcon(result.type);
                const isSelected = index === selectedIndex;

                return (
                  <Link
                    key={`${result.type}-${result.id}`}
                    href={result.link}
                    onClick={handleResultClick}
                    data-result-index={index}
                    className={`block p-4 mx-2 rounded-xl transition-all duration-150 ${
                      isSelected
                        ? 'bg-blue-50 border border-blue-200 shadow-sm scale-[1.02]'
                        : 'hover:bg-gray-50 border border-transparent'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      {/* Icon or Avatar */}
                      <div className="flex-shrink-0">
                        {result.type === 'user' && result.avatar_url ? (
                          <Image
                            src={result.avatar_url}
                            alt={result.name || ''}
                            width={40}
                            height={40}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : result.type === 'vendor' && result.featured_media ? (
                          <Image
                            src={result.featured_media}
                            alt={result.title || ''}
                            width={40}
                            height={40}
                            className="w-10 h-10 rounded-lg object-contain bg-white p-1"
                          />
                        ) : result.type === 'post' && result.featured_media ? (
                          <Image
                            src={result.featured_media}
                            alt={result.title || ''}
                            width={40}
                            height={40}
                            className="w-10 h-10 rounded-lg object-cover"
                          />
                        ) : (
                          <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                            <Icon className="h-5 w-5 text-gray-600" />
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        {/* Job specific layout */}
                        {result.type === 'job' && (
                          <>
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
                                {result.title}
                              </h4>
                              <div className="flex gap-1 ml-2 flex-shrink-0">
                                {result.status === 'NEW' && (
                                  <Badge className="bg-[#22c55e] text-white text-xs">New</Badge>
                                )}
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-1">
                              <div className="space-y-1">
                                {result.company && (
                                  <div className="flex items-center gap-1">
                                    <Building className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">{result.company}</span>
                                  </div>
                                )}
                                {result.location && (
                                  <div className="flex items-center gap-1">
                                    <MapPin className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">{result.location}</span>
                                  </div>
                                )}
                              </div>
                              <div className="space-y-1 text-right">
                                {result.date && (
                                  <div className="flex items-center justify-end gap-1">
                                    <Calendar className="h-3 w-3 flex-shrink-0" />
                                    <span>Posted {new Date(result.date).toLocaleDateString()}</span>
                                  </div>
                                )}
                                {result.deadline && (
                                  <div className="flex items-center justify-end gap-1">
                                    <Clock className="h-3 w-3 flex-shrink-0" />
                                    <span>{result.deadline}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </>
                        )}

                        {/* RFP specific layout */}
                        {result.type === 'rfp' && (
                          <>
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
                                {result.title}
                              </h4>
                              <div className="flex gap-1 ml-2 flex-shrink-0">
                                {result.status === 'NEW' && (
                                  <Badge className="bg-[#22c55e] text-white text-xs">New</Badge>
                                )}
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 mb-1">
                              <div className="space-y-1">
                                {result.company && (
                                  <div className="flex items-center gap-1">
                                    <Building className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">{result.company}</span>
                                  </div>
                                )}
                                {result.location && (
                                  <div className="flex items-center gap-1">
                                    <MapPin className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">{result.location}</span>
                                  </div>
                                )}
                                {result.category && (
                                  <div className="flex items-center gap-1">
                                    <FileText className="h-3 w-3 flex-shrink-0" />
                                    <span className="truncate">{result.category}</span>
                                  </div>
                                )}
                              </div>
                              <div className="space-y-1 text-right">
                                {result.date && (
                                  <div className="flex items-center justify-end gap-1">
                                    <Calendar className="h-3 w-3 flex-shrink-0" />
                                    <span>Posted {new Date(result.date).toLocaleDateString()}</span>
                                  </div>
                                )}
                                {result.deadline && (
                                  <div className="flex items-center justify-end gap-1">
                                    <Clock className="h-3 w-3 flex-shrink-0" />
                                    <span>{result.deadline}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </>
                        )}

                        {/* Default layout for other content types */}
                        {result.type !== 'job' && result.type !== 'rfp' && (
                          <>
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className="text-sm font-medium text-gray-900 truncate">
                                {result.title || result.name || result.question}
                              </h4>
                              {result.is_paid && (
                                <Badge
                                  variant="default"
                                  className="text-xs bg-yellow-100 text-yellow-800"
                                >
                                  Paid
                                </Badge>
                              )}
                            </div>

                            {(result.excerpt || result.content || result.description) && (
                              <p
                                className="text-sm text-gray-600 mb-1 overflow-hidden"
                                style={{
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                }}
                              >
                                {result.excerpt || result.content || result.description}
                              </p>
                            )}

                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              {result.author && <span>by {result.author}</span>}
                              {result.job_title && <span>{result.job_title}</span>}
                              {result.company && <span>at {result.company}</span>}
                              {result.location && <span>{result.location}</span>}
                              {result.job_type && <span>{result.job_type}</span>}
                              {result.salary_range && <span>{result.salary_range}</span>}
                              {result.votes !== undefined && <span>{result.votes} votes</span>}
                              {result.comments_count !== undefined && (
                                <span>{result.comments_count} comments</span>
                              )}
                              {result.categories && result.categories.length > 0 && (
                                <span>{result.categories.slice(0, 2).join(', ')}</span>
                              )}
                            </div>
                          </>
                        )}
                      </div>

                      <ArrowRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
                    </div>
                  </Link>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-100 px-6 py-3 bg-gray-50/50">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="hidden md:flex items-center space-x-6">
              <span className="flex items-center">
                <kbd className="px-1.5 py-0.5 text-xs font-mono bg-gray-200 rounded mr-1">↑↓</kbd>
                Navigate
              </span>
              <span className="flex items-center">
                <kbd className="px-1.5 py-0.5 text-xs font-mono bg-gray-200 rounded mr-1">↵</kbd>
                Open
              </span>
              <span className="flex items-center">
                <kbd className="px-1.5 py-0.5 text-xs font-mono bg-gray-200 rounded mr-1">Esc</kbd>
                Close
              </span>
            </div>
            <div className="flex items-center gap-4 md:ml-auto">
              {currentResults.length > 0 && currentResults.length >= 25 && (
                <button
                  onClick={() => {
                    saveRecentSearch(query);
                    onClose();
                    // Navigate to search page with query
                    window.location.href = `/search?q=${encodeURIComponent(query)}`;
                  }}
                  className="text-blue-600 hover:text-blue-700 font-medium transition-colors"
                >
                  View all results
                </button>
              )}
              {results.total > 0 && (
                <span className="font-medium text-gray-600">
                  {currentResults.length}
                  {currentResults.length >= 25 ? '+' : ''} of {results.total} result
                  {results.total !== 1 ? 's' : ''}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </CustomSearchDialog>
  );
}
