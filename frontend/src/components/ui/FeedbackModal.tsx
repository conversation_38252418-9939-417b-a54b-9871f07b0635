/*********************************************
# frontend/src/components/ui/FeedbackModal.tsx
# 02/05/2025 12:00am Created file
# 02/06/2025 3:45pm Added feedback modal with form
# 02/06/2025 4:30pm Updated with modern 2025 UI styling
# 02/06/2025 5:15pm Updated colors to use tourismiq light blue (#5cc8ff) and dark blue (#3d405b)
# 02/06/2025 5:30pm Removed duplicate close button, made icon boxes white, updated buttons to pill style
**********************************************/

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogHeader } from './dialog';
import { Button } from './button';
import { Input } from './input';
import { Textarea } from './textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { Label } from './label';
import { MessageSquare } from 'lucide-react';

interface FeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function FeedbackModal({ isOpen, onClose }: FeedbackModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    category: '',
    rating: '',
    feedback: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        alert('Feedback submitted successfully! Thank you for your input.');
        setFormData({
          name: '',
          email: '',
          category: '',
          rating: '',
          feedback: '',
        });
        onClose();
      } else {
        alert('Failed to submit feedback. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] p-0 bg-white rounded-2xl shadow-2xl border-0 flex flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle className="sr-only">Share Feedback</DialogTitle>
        </DialogHeader>

        {/* Header with gradient background */}
        <div className="relative bg-gradient-to-r from-[#5cc8ff]/30 to-[#5cc8ff]/20 p-6 rounded-t-2xl">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white rounded-xl shadow-sm">
              <MessageSquare className="h-6 w-6 text-[#5cc8ff]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-[#3d405b] leading-tight">Share Feedback</h2>
              <p className="text-sm text-gray-600 mt-1">
                Help us improve by sharing your thoughts and suggestions
              </p>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name and Email Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-semibold text-gray-700">
                  Full Name *
                </Label>
                <Input
                  id="name"
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                  className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200"
                  placeholder="Enter your full name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-semibold text-gray-700">
                  Email Address *
                </Label>
                <Input
                  id="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                  className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            {/* Category and Rating Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category" className="text-sm font-semibold text-gray-700">
                  Feedback Category *
                </Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, category: value }))}
                  required
                >
                  <SelectTrigger className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General Feedback</SelectItem>
                    <SelectItem value="feature">Feature Request</SelectItem>
                    <SelectItem value="bug">Bug Report</SelectItem>
                    <SelectItem value="ui">User Interface</SelectItem>
                    <SelectItem value="performance">Performance</SelectItem>
                    <SelectItem value="content">Content Quality</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="rating" className="text-sm font-semibold text-gray-700">
                  Overall Rating *
                </Label>
                <Select
                  value={formData.rating}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, rating: value }))}
                  required
                >
                  <SelectTrigger className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200">
                    <SelectValue placeholder="Select rating" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">⭐⭐⭐⭐⭐ Excellent</SelectItem>
                    <SelectItem value="4">⭐⭐⭐⭐ Very Good</SelectItem>
                    <SelectItem value="3">⭐⭐⭐ Good</SelectItem>
                    <SelectItem value="2">⭐⭐ Fair</SelectItem>
                    <SelectItem value="1">⭐ Poor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Feedback Text */}
            <div className="space-y-2">
              <Label htmlFor="feedback" className="text-sm font-semibold text-gray-700">
                Your Feedback *
              </Label>
              <Textarea
                id="feedback"
                required
                value={formData.feedback}
                onChange={(e) => setFormData((prev) => ({ ...prev, feedback: e.target.value }))}
                className="min-h-[140px] px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200 resize-none"
                placeholder="Please share your detailed feedback, suggestions, or concerns. What did you like? What could be improved? Any specific features you'd like to see?"
              />
              <p className="text-xs text-gray-500">
                Your feedback helps us make TourismIQ better for everyone. We appreciate your input!
              </p>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="px-8 py-3 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 font-semibold rounded-full transition-all duration-200"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-3 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-bold rounded-full transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
