'use client';

import { useEffect, useState, useCallback } from 'react';
import { <PERSON>ap, Crown, Star, Trophy } from 'lucide-react';
import { useNotifications } from '@/contexts/NotificationContext';

interface IQPointsToastProps {
  notification: {
    id: string;
    type: string;
    content: string;
    pointsEarned?: number;
    newTotal?: number;
    rankedUp?: boolean;
    newRank?: string;
    activityType?: string;
  };
  onDismiss: (id: string) => void;
}

export function IQPointsToast({ notification, onDismiss }: IQPointsToastProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  const handleDismiss = useCallback(() => {
    setIsExiting(true);
    setTimeout(() => {
      onDismiss(notification.id);
    }, 300);
  }, [onDismiss, notification.id]);

  useEffect(() => {
    // Show animation
    setIsVisible(true);

    // Auto dismiss after 3 seconds (longer for rank ups)
    const timeout = notification.rankedUp ? 5000 : 3000;
    const timer = setTimeout(() => {
      handleDismiss();
    }, timeout);

    return () => clearTimeout(timer);
  }, [handleDismiss, notification.rankedUp]);

  const getActivityIcon = (activityType?: string) => {
    switch (activityType) {
      case 'create_account':
      case 'profile_image':
      case 'social_media_link':
        return <Star className="h-4 w-4 text-amber-500" />;
      case 'post_publish':
      case 'comment':
        return <Trophy className="h-4 w-4 text-amber-500" />;
      case 'connection':
      case 'upvote':
        return <Zap className="h-4 w-4 text-amber-500" />;
      default:
        return <Zap className="h-4 w-4 text-amber-500" />;
    }
  };

  // Create concise content
  const getConciseContent = () => {
    if (notification.rankedUp) {
      return `Rank up! You're now ${notification.newRank}`;
    }

    // Extract action from content for brevity
    if (notification.content.includes('upvoting')) {
      return 'Great participation!';
    } else if (notification.content.includes('post')) {
      return 'Post published!';
    } else if (notification.content.includes('comment')) {
      return 'Comment added!';
    } else if (notification.content.includes('connection')) {
      return 'New connection made!';
    } else {
      return 'Well done!';
    }
  };

  return (
    <div
      className={`
        fixed right-4 z-50 max-w-sm w-full
        transform transition-all duration-300 ease-in-out
        ${
          isVisible && !isExiting
            ? 'translate-x-0 opacity-100 scale-100'
            : 'translate-x-full opacity-0 scale-95'
        }
      `}
      style={{
        top: '118px', // Moved down 28px from original 90px (header is 90px + 28px = 118px)
        zIndex: 9999,
      }}
    >
      <div
        className={`
          rounded-lg border-l-4 border-[#5cc8ff] p-4 cursor-pointer
          max-h-[80px] overflow-hidden
          ${notification.rankedUp ? 'bg-gradient-to-r from-amber-50/95 to-yellow-50/95' : ''}
        `}
        style={{
          // Frosted glass effect with good contrast
          background: notification.rankedUp
            ? 'linear-gradient(to right, rgba(251, 191, 36, 0.15), rgba(254, 240, 138, 0.15))'
            : 'rgba(255, 255, 255, 0.85)',
          backdropFilter: 'blur(12px)',
          WebkitBackdropFilter: 'blur(12px)',
          boxShadow:
            '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(255, 255, 255, 0.1)',
          border: 'none',
          borderLeft: '4px solid #5cc8ff',
        }}
        onClick={handleDismiss}
      >
        {/* Single line content with icon and points */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Single consolidated icon */}
            <div
              className={`p-1.5 rounded-full flex-shrink-0 ${
                notification.rankedUp
                  ? 'bg-gradient-to-r from-amber-100 to-yellow-100'
                  : 'bg-amber-100'
              }`}
            >
              {notification.rankedUp ? (
                <Crown className="h-4 w-4 text-amber-500" />
              ) : (
                getActivityIcon(notification.activityType)
              )}
            </div>

            {/* Points and description in one line */}
            <div className="flex items-center gap-2 min-w-0">
              <div className="flex items-center gap-1">
                <span className="font-bold text-amber-600 text-sm">
                  +{notification.pointsEarned}
                </span>
                <span className="text-xs text-gray-600">IQ Points</span>
              </div>
              <span className="text-xs text-gray-500">•</span>
              <span className="text-xs text-gray-700 truncate">{getConciseContent()}</span>
            </div>
          </div>

          {/* Total points */}
          <div className="text-xs text-gray-500 flex-shrink-0 ml-2">
            Total: {notification.newTotal}
          </div>
        </div>

        {/* Rank up animation overlay */}
        {notification.rankedUp && (
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-1 right-1 animate-bounce text-xs">✨</div>
            <div className="absolute bottom-1 left-8 animate-pulse text-xs">🎊</div>
          </div>
        )}
      </div>
    </div>
  );
}

// Container component to manage multiple IQ points toasts
export function IQPointsToastContainer() {
  const { notifications } = useNotifications();
  const [activeToast, setActiveToast] = useState<IQPointsToastProps['notification'] | null>(null);
  const [shownNotificationIds, setShownNotificationIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Get only the latest IQ points notification that hasn't been shown yet and isn't read
    const iqNotifications = notifications.filter(
      (n) => n.type === 'iq_points_earned' && !shownNotificationIds.has(n.id) && !n.read // Don't show toasts for notifications that are already read
    );

    if (iqNotifications.length > 0) {
      const latestNotification = iqNotifications[0]; // Get the most recent unshown one

      if (latestNotification) {
        // Set it as active and mark as shown
        setActiveToast(latestNotification);
        setShownNotificationIds((prev) => new Set([...prev, latestNotification.id]));
      }
    }
  }, [notifications, shownNotificationIds]); // Added shownNotificationIds dependency

  const handleDismiss = () => {
    setActiveToast(null);
  };

  // Only render if we have an active toast
  if (!activeToast) {
    return null;
  }

  return (
    <div
      style={{
        top: '118px', // Consistent with individual toast positioning
        zIndex: 9999,
      }}
      className="fixed right-4"
    >
      <IQPointsToast notification={activeToast} onDismiss={handleDismiss} />
    </div>
  );
}
