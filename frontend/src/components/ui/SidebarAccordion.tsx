/*********************************************
# frontend/src/components/ui/SidebarAccordion.tsx
# 01/27/2025 10:40pm Created file - Accordion component for responsive right sidebar cards
# 01/27/2025 10:45pm Added CTA support and improved collapsed state styling
# 02/07/2025 4:05pm Enhanced with priority-based opening for taller browser windows
# 02/07/2025 4:30pm Updated corner radius to 20px for right sidebar boxes
# 02/07/2025 8:30pm Fixed header hover state corner radius to match container corners
**********************************************/

'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface AccordionItem {
  id: string;
  title: string;
  content: React.ReactNode;
  defaultOpen?: boolean;
  cta?: React.ReactNode;
  priority?: number; // Lower number = higher priority (opens first)
}

interface SidebarAccordionProps {
  items: AccordionItem[];
  useAccordionMode: boolean;
  className?: string;
}

export function SidebarAccordion({ items, useAccordionMode, className }: SidebarAccordionProps) {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());
  const containerRef = useRef<HTMLDivElement>(null);

  // Smart height calculation for priority-based opening
  const calculateOptimalOpenItems = useCallback(() => {
    if (!useAccordionMode) {
      // In non-accordion mode, open all items marked as defaultOpen
      const defaultOpenItems = items.filter((item) => item.defaultOpen).map((item) => item.id);
      return defaultOpenItems.length === 0 ? items.map((item) => item.id) : defaultOpenItems;
    }

    if (!containerRef.current) {
      // Fallback: open first item only
      return items.length > 0 ? [items[0].id] : [];
    }

    const containerHeight = containerRef.current.clientHeight;

    // Estimate heights
    const CARD_HEADER_HEIGHT = 60;
    const CARD_SPACING = 12;
    const CTA_HEIGHT = 50;

    // Content heights based on item type (estimated)
    const getEstimatedContentHeight = (item: AccordionItem): number => {
      if (item.id === 'my-connections') return 240; // 3 connections at ~60px each + padding
      if (item.id === 'suggested-connections') return 240; // 3 suggestions at ~60px each + padding
      if (item.id === 'upcoming-events') return 240; // 3 events at ~60px each + padding
      return 180; // default fallback
    };

    // Sort items by priority (lower number = higher priority)
    const prioritizedItems = [...items].sort((a, b) => {
      const aPriority = a.priority ?? items.indexOf(a);
      const bPriority = b.priority ?? items.indexOf(b);
      return aPriority - bPriority;
    });

    let usedHeight = 0;
    const openItemIds: string[] = [];

    for (const item of prioritizedItems) {
      const itemHeight = CARD_HEADER_HEIGHT + (item.cta ? CTA_HEIGHT : 0) + CARD_SPACING;

      const contentHeight = getEstimatedContentHeight(item);
      const totalItemHeight = itemHeight + contentHeight;

      if (usedHeight + totalItemHeight <= containerHeight - 100) {
        // Increased buffer from 50px to 100px
        openItemIds.push(item.id);
        usedHeight += totalItemHeight;
      } else {
        // If we can't fit content but haven't opened any items yet, open at least one
        if (openItemIds.length === 0) {
          openItemIds.push(item.id);
        }
        break;
      }
    }

    // Always open at least one item in accordion mode
    if (openItemIds.length === 0 && prioritizedItems.length > 0) {
      openItemIds.push(prioritizedItems[0].id);
    }

    return openItemIds;
  }, [useAccordionMode, items]);

  // Initialize open items based on mode and available space
  useEffect(() => {
    const optimalOpenItems = calculateOptimalOpenItems();
    setOpenItems(new Set(optimalOpenItems));
  }, [calculateOptimalOpenItems]);

  // Recalculate when container size changes
  useEffect(() => {
    if (!containerRef.current) return undefined;

    const resizeObserver = new ResizeObserver(() => {
      // Respond immediately without debouncing for faster accordion adjustments
      const optimalOpenItems = calculateOptimalOpenItems();
      setOpenItems(new Set(optimalOpenItems));
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [calculateOptimalOpenItems]);

  const toggleItem = (itemId: string) => {
    if (useAccordionMode) {
      // In accordion mode, manual clicks toggle single items
      setOpenItems(openItems.has(itemId) ? new Set() : new Set([itemId]));
    } else {
      // In normal mode, multiple items can be open
      const newOpenItems = new Set(openItems);
      if (newOpenItems.has(itemId)) {
        newOpenItems.delete(itemId);
      } else {
        newOpenItems.add(itemId);
      }
      setOpenItems(newOpenItems);
    }
  };

  return (
    <div ref={containerRef} className={cn('space-y-3 h-full overflow-hidden', className)}>
      {items.map((item) => {
        const isOpen = openItems.has(item.id);

        return (
          <Card key={item.id} className="border-gray-200 rounded-[26px]">
            <CardHeader
              className={cn(
                'pb-2 transition-colors rounded-t-[26px]',
                useAccordionMode && 'cursor-pointer select-none hover:bg-gray-50',
                !isOpen && useAccordionMode && item.cta && 'pb-4'
              )}
              onClick={() => useAccordionMode && toggleItem(item.id)}
            >
              <div className={cn('flex items-center justify-between', isOpen && 'pb-2.5')}>
                <h3 className="text-lg font-extrabold text-brand-text">{item.title}</h3>
                {useAccordionMode && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleItem(item.id);
                    }}
                    className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                    aria-label={isOpen ? `Collapse ${item.title}` : `Expand ${item.title}`}
                  >
                    {isOpen ? (
                      <ChevronUp className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </button>
                )}
              </div>
            </CardHeader>

            <div
              className={cn(
                'transition-all duration-300 ease-in-out overflow-hidden',
                isOpen ? 'max-h-[800px] opacity-100' : 'max-h-0 opacity-0'
              )}
            >
              <CardContent className="pt-2.5">{item.content}</CardContent>
            </div>

            {/* Always visible CTA section */}
            {item.cta && <div className="px-6 pb-6 pt-2.5">{item.cta}</div>}
          </Card>
        );
      })}
    </div>
  );
}
