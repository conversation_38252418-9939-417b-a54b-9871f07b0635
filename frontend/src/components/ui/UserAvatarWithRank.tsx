'use client';

import { useState, useEffect, useCallback } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useIQScore } from '@/contexts/iq-score-context';
// Local type that matches what this component actually uses
interface UserRankData {
  score: number;
  rank?: { name: string } | undefined;
  rank_data?:
    | {
        name: string;
        min_score: number;
        max_score?: number;
        color: string;
        icon: string;
      }
    | undefined;
  total_activities?: number;
  activities?: any[];
}
import Image from 'next/image';

interface UserAvatarWithRankProps {
  userId: number;
  avatarUrl: string;
  displayName: string;
  size?: string;
  containerSize?: string;
  showRankBadge?: boolean;
  userRoles?: string[] | { [key: string]: string } | undefined | null;
  iqScoreData?: {
    score: number;
    rank: string;
    rank_data: {
      name: string;
      min_score: number;
      max_score?: number;
      color: string;
      icon: string;
    };
  };
}

export function UserAvatarWithRank({
  userId,
  avatarUrl,
  displayName,
  size = 'h-10 w-10',
  containerSize = 'w-12 h-12',
  showRankBadge = true,
  userRoles = [],
  iqScoreData,
}: UserAvatarWithRankProps) {
  const { getUserIQ, getCachedUserIQ } = useIQScore();
  const [userRank, setUserRank] = useState<UserRankData | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchUserRank = useCallback(async () => {
    // Use prop data if available
    if (iqScoreData) {
      setUserRank({
        score: iqScoreData.score,
        rank: { name: iqScoreData.rank },
        rank_data: iqScoreData.rank_data,
        total_activities: 0,
        activities: [],
      });
      return;
    }

    if (loading) return; // Prevent duplicate requests

    try {
      setLoading(true);
      const data = await getUserIQ(userId);
      if (data) {
        setUserRank({
          score: data.iq_score || 0,
          rank: data.rank
            ? { name: typeof data.rank === 'string' ? data.rank : data.rank.name }
            : undefined,
          rank_data: undefined,
          total_activities: 0,
          activities: [],
        });
      }
    } catch (error) {
      console.error('Error fetching user rank:', error);
    } finally {
      setLoading(false);
    }
  }, [loading, getUserIQ, userId, iqScoreData]);

  // NOTE: For now, we require roles to be passed as props
  // TODO: Implement proper user data fetching if needed
  const fetchUserRoles = useCallback(async () => {
    // Disabled for now to prevent 404 errors
    // Components should pass userRoles as props
    return;
  }, []);

  // Check for cached data first
  useEffect(() => {
    // Use prop data if available
    if (iqScoreData) {
      setUserRank({
        score: iqScoreData.score,
        rank: { name: iqScoreData.rank },
        rank_data: iqScoreData.rank_data,
        total_activities: 0,
        activities: [],
      });
      return;
    }

    const cachedData = getCachedUserIQ(userId);
    if (cachedData) {
      setUserRank({
        score: cachedData.iq_score || 0,
        rank: cachedData.rank
          ? { name: typeof cachedData.rank === 'string' ? cachedData.rank : cachedData.rank.name }
          : undefined,
        rank_data: undefined,
        total_activities: 0,
        activities: [],
      });
      return;
    }

    // Only fetch if we don't have cached data and showRankBadge is true
    if (showRankBadge && userId) {
      fetchUserRank();
    }

    // Fetch user roles if not provided as props
    fetchUserRoles();
  }, [userId, showRankBadge, getCachedUserIQ, fetchUserRank, iqScoreData, fetchUserRoles]);

  // Helper function to determine user type
  const getUserType = () => {
    // Use props roles if provided, otherwise use fetched roles
    let rolesArray: string[] = [];

    // Check if userRoles prop has data
    if (
      userRoles &&
      (Array.isArray(userRoles) ? userRoles.length > 0 : Object.keys(userRoles).length > 0)
    ) {
      if (Array.isArray(userRoles)) {
        rolesArray = userRoles;
      } else if (typeof userRoles === 'object') {
        // If it's an object like {7: "administrator"}, extract the values
        rolesArray = Object.values(userRoles);
      }
    } else {
      // No roles provided
      rolesArray = [];
    }

    // Admin role always takes priority
    if (rolesArray.includes('administrator') || rolesArray.includes('admin')) return 'admin';
    if (rolesArray.includes('founder')) return 'founder';
    return 'member';
  };

  // Helper function to get role-based badge icon
  const getRoleBadgeIcon = (userType: string) => {
    return `/images/icons/${userType}.svg`;
  };

  // Helper function to get rank icon
  const getRankIcon = (rankName: string | undefined, isFounder: boolean = false) => {
    if (!rankName) return `/images/icons/novice${isFounder ? '-fc' : ''}.svg`;
    const rankNameLower = rankName.toLowerCase();
    const suffix = isFounder ? '-fc' : '';
    return `/images/icons/${rankNameLower}${suffix}.svg`;
  };

  // Calculate badge dimensions based on container size
  const getBadgeDimensions = () => {
    // Extract numeric values from containerSize (e.g., "w-12 h-12" or "w-[160px] h-[160px]")
    const sizeMatch = containerSize.match(/w-(?:\[(\d+)px\]|(\d+))/);
    let containerWidth = 48; // Default fallback (w-12 = 48px)

    if (sizeMatch) {
      // If it's a bracket notation like w-[160px], use the first capture group
      // If it's a standard notation like w-12, use the second capture group and multiply by 4 (Tailwind scale)
      if (sizeMatch[1]) {
        containerWidth = parseInt(sizeMatch[1]);
      } else if (sizeMatch[2]) {
        containerWidth = parseInt(sizeMatch[2]) * 4;
      }
    }

    // Badge should be slightly larger than container to create the ring effect
    const badgeSize = Math.max(containerWidth * 1.1, 56); // Minimum 56px for small avatars

    return {
      width: badgeSize,
      height: badgeSize,
    };
  };

  const badgeDimensions = getBadgeDimensions();
  const userType = getUserType();

  return (
    <div className={`relative flex items-center justify-center ${containerSize}`}>
      {/* Role Badge (Admin only) - takes priority over rank badges */}
      {showRankBadge && userType === 'admin' && (
        <Image
          src={getRoleBadgeIcon(userType)}
          alt={`${userType} badge`}
          width={badgeDimensions.width}
          height={badgeDimensions.height}
          className="absolute inset-0 object-contain"
        />
      )}

      {/* Rank Badge Ring - show for members/founders (not admin) */}
      {showRankBadge && (userType === 'member' || userType === 'founder') && userRank && (
        <Image
          src={getRankIcon(userRank.rank?.name || userRank.rank_data?.name, userType === 'founder')}
          alt={`${userRank.rank?.name || userRank.rank_data?.name || 'Member'} rank badge`}
          width={badgeDimensions.width}
          height={badgeDimensions.height}
          className="absolute inset-0 object-contain"
        />
      )}

      {/* Avatar */}
      <Avatar
        className={`${size} relative z-10 border ${size.includes('150px') ? 'border-4 border-white bg-white' : ''}`}
      >
        <AvatarImage
          src={avatarUrl}
          alt={displayName}
          className={size.includes('150px') ? 'object-cover' : ''}
        />
        <AvatarFallback className={`bg-gray-100 ${size.includes('150px') ? 'text-xl' : ''}`}>
          {size.includes('150px')
            ? displayName
                .split(' ')
                .map((n: string) => n[0])
                .join('')
                .toUpperCase()
            : `${displayName.charAt(0)}${displayName.split(' ')[1]?.charAt(0) || ''}`}
        </AvatarFallback>
      </Avatar>
    </div>
  );
}
