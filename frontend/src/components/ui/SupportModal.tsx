/*********************************************
# frontend/src/components/ui/SupportModal.tsx
# 02/05/2025 12:00am Created file
# 02/06/2025 3:45pm Added support modal with form
# 02/06/2025 4:30pm Updated with modern 2025 UI styling
# 02/06/2025 5:15pm Updated colors to use tourismiq light blue (#5cc8ff) and dark blue (#3d405b)
# 02/06/2025 5:30pm Removed duplicate close button, made icon boxes white, updated buttons to pill style
**********************************************/

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogHeader } from './dialog';
import { Button } from './button';
import { Input } from './input';
import { Textarea } from './textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { Label } from './label';
import { X, Upload, HelpCircle } from 'lucide-react';

interface SupportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function SupportModal({ isOpen, onClose }: SupportModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    priority: '',
    description: '',
    attachments: [] as File[],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const formDataToSend = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'attachments') {
          (value as File[]).forEach((file, index) => {
            formDataToSend.append(`attachment_${index}`, file);
          });
        } else {
          formDataToSend.append(key, value as string);
        }
      });

      const response = await fetch('/api/support', {
        method: 'POST',
        body: formDataToSend,
      });

      if (response.ok) {
        alert('Support request submitted successfully!');
        setFormData({
          name: '',
          email: '',
          subject: '',
          priority: '',
          description: '',
          attachments: [],
        });
        onClose();
      } else {
        alert('Failed to submit support request. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting support request:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...files],
    }));
  };

  const removeFile = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index),
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] p-0 bg-white rounded-2xl shadow-2xl border-0 flex flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle className="sr-only">Get Support</DialogTitle>
        </DialogHeader>

        {/* Header with gradient background */}
        <div className="relative bg-gradient-to-r from-[#5cc8ff]/30 to-[#5cc8ff]/20 p-6 rounded-t-2xl">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-white rounded-xl shadow-sm">
              <HelpCircle className="h-6 w-6 text-[#5cc8ff]" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-[#3d405b] leading-tight">Get Support</h2>
              <p className="text-sm text-gray-600 mt-1">
                We're here to help you with any questions or issues
              </p>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name and Email Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-semibold text-gray-700">
                  Full Name *
                </Label>
                <Input
                  id="name"
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                  className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200"
                  placeholder="Enter your full name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-semibold text-gray-700">
                  Email Address *
                </Label>
                <Input
                  id="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                  className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            {/* Subject and Priority Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="subject" className="text-sm font-semibold text-gray-700">
                  Subject *
                </Label>
                <Input
                  id="subject"
                  type="text"
                  required
                  value={formData.subject}
                  onChange={(e) => setFormData((prev) => ({ ...prev, subject: e.target.value }))}
                  className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200"
                  placeholder="Brief description of your issue"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority" className="text-sm font-semibold text-gray-700">
                  Priority Level *
                </Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, priority: value }))}
                  required
                >
                  <SelectTrigger className="h-12 px-4 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low - General question</SelectItem>
                    <SelectItem value="medium">Medium - Feature request</SelectItem>
                    <SelectItem value="high">High - Bug report</SelectItem>
                    <SelectItem value="urgent">Urgent - System issue</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-semibold text-gray-700">
                Description *
              </Label>
              <Textarea
                id="description"
                required
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                className="min-h-[120px] px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-[#5cc8ff] focus:ring-2 focus:ring-[#5cc8ff]/20 transition-all duration-200 resize-none"
                placeholder="Please provide detailed information about your issue or question..."
              />
            </div>

            {/* File Upload */}
            <div className="space-y-2">
              <Label className="text-sm font-semibold text-gray-700">Attachments (Optional)</Label>
              <div className="border-2 border-dashed border-gray-200 rounded-xl p-6 text-center hover:border-[#5cc8ff] transition-colors duration-200">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-2">Drop files here or click to upload</p>
                <p className="text-xs text-gray-500 mb-4">
                  Max 5 files, 10MB each (PDF, JPG, PNG, DOC)
                </p>
                <Input
                  type="file"
                  multiple
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                  onChange={handleFileChange}
                  className="hidden"
                  id="file-upload"
                />
                <Label
                  htmlFor="file-upload"
                  className="inline-flex items-center px-6 py-3 bg-[#5cc8ff] text-[#3d405b] rounded-full hover:bg-[#5cc8ff]/80 font-semibold cursor-pointer transition-colors duration-200"
                >
                  Choose Files
                </Label>
              </div>

              {/* File List */}
              {formData.attachments.length > 0 && (
                <div className="space-y-2">
                  {formData.attachments.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <span className="text-sm text-gray-700 truncate">{file.name}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                        className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="px-8 py-3 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 font-semibold rounded-full transition-all duration-200"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-3 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-bold rounded-full transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Request'}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
