/*********************************************
# frontend/src/components/ui/NotificationMenu.tsx
# 02/07/2025 4:15pm Removed overlay backdrop and enhanced with sophisticated shadow for non-modal slideout behavior
# 02/07/2025 8:45pm Updated notification styling with new color scheme: dark blue hover states, light blue backgrounds, and updated connection confirmation colors
# 02/07/2025 8:00pm Updated to use custom confirmation dialog instead of window.confirm
# 02/07/2025 9:30pm Updated icon colors and circles: messages icon to light blue, 10% opacity icon color circles, unread backgrounds to 10% light blue with 100% light blue left edge
**********************************************/

'use client';

import React, { useState, useMemo } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetClose } from '@/components/ui/sheet';
import { useNotifications } from '@/contexts/NotificationContext';
import { useConnections } from '@/hooks/useConnections';
import { useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/react-query';
import {
  Bell,
  Check,
  X,
  UserPlus,
  MessageCircle,
  Heart,
  Share2,
  Zap,
  Trophy,
  HelpCircle,
  Calendar,
  Crown,
  Loader2,
  Trash2,
  Trash,
} from 'lucide-react';
import Image from 'next/image';
import { useConfirmation } from '@/components/ui/ConfirmationDialog';

interface NotificationMenuProps {
  isOpen: boolean;
  onClose: () => void;
  onNotificationsRead?: () => void;
}

export function NotificationMenu({ isOpen, onClose, onNotificationsRead }: NotificationMenuProps) {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    removeNotification,
    updateNotification,
  } = useNotifications();
  const { acceptConnectionRequest, declineConnectionRequest } = useConnections();
  const queryClient = useQueryClient();
  const [connectionActionLoading, setConnectionActionLoading] = useState<string | null>(null);
  const { confirm, ConfirmationDialogComponent } = useConfirmation();

  // State for active tab (all or unread)
  const activeTab = 'all';

  // Group notifications by date like in the reference
  const groupedNotifications = useMemo(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
    const yesterday = today - 86400000; // 24 hours in milliseconds
    const oneWeekAgo = today - 86400000 * 7;

    // Filter notifications based on active tab and exclude message notifications
    const filteredNotifications = notifications.filter((n) => n.type !== 'message');

    return {
      today: filteredNotifications.filter((n) => new Date(n.timestamp).getTime() >= today),
      yesterday: filteredNotifications.filter((n) => {
        const time = new Date(n.timestamp).getTime();
        return time >= yesterday && time < today;
      }),
      thisWeek: filteredNotifications.filter((n) => {
        const time = new Date(n.timestamp).getTime();
        return time >= oneWeekAgo && time < yesterday;
      }),
      earlier: filteredNotifications.filter((n) => new Date(n.timestamp).getTime() < oneWeekAgo),
    };
  }, [notifications, activeTab]);

  // Close menu when clicking outside

  // Format timestamp
  const formatTime = (timestamp: number) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch {
      return 'some time ago';
    }
  };

  const handleDeleteAll = async () => {
    const isConfirmed = await confirm(
      'Are you sure you want to delete all notifications? This action cannot be undone.',
      'Delete All Notifications'
    );

    if (isConfirmed) {
      // Get all notification IDs to delete
      const notificationIds = notifications.map((n) => n.id);

      // Delete all notifications
      for (const id of notificationIds) {
        await removeNotification(id);
      }
    }
  };

  const handleMarkAllAsRead = async () => {
    markAllAsRead();
    // Also call the onNotificationsRead callback if provided
    onNotificationsRead?.();
  };

  const handleAcceptConnection = async (notificationId: string, senderId: number) => {
    setConnectionActionLoading(`accept-${notificationId}`);

    try {
      const success = await acceptConnectionRequest(senderId);

      if (success) {
        // Update the notification to reflect the accepted status
        updateNotification(notificationId, {
          type: 'connection_accepted',
          content: 'Connection request accepted',
          read: true,
        });

        // Invalidate connection-related queries
        queryClient.invalidateQueries({ queryKey: queryKeys.connections.all });
        queryClient.invalidateQueries({ queryKey: queryKeys.connections.status(senderId) });
      } else {
        console.error('Failed to accept connection request');
      }
    } catch (error) {
      console.error('Error accepting connection:', error);
    } finally {
      setConnectionActionLoading(null);
    }
  };

  const handleDeclineConnection = async (notificationId: string, senderId: number) => {
    setConnectionActionLoading(`decline-${notificationId}`);

    try {
      const success = await declineConnectionRequest(senderId);

      if (success) {
        // Remove the notification after declining
        removeNotification(notificationId);

        // Invalidate connection-related queries
        queryClient.invalidateQueries({ queryKey: queryKeys.connections.all });
        queryClient.invalidateQueries({ queryKey: queryKeys.connections.status(senderId) });
      } else {
        console.error('Failed to decline connection request');
      }
    } catch (error) {
      console.error('Error declining connection:', error);
    } finally {
      setConnectionActionLoading(null);
    }
  };

  // Helper function to get icon background color with 10% opacity
  const getIconBackgroundColor = (type: string, notification?: { rankedUp?: boolean }) => {
    switch (type) {
      case 'connection_request':
        return 'rgba(236, 72, 153, 0.1)'; // Pink with 10% opacity
      case 'message':
        return 'rgba(92, 200, 255, 0.1)'; // Light blue with 10% opacity
      case 'upvote':
        return 'rgba(239, 68, 68, 0.1)'; // Red with 10% opacity
      case 'comment':
        return 'rgba(249, 115, 22, 0.1)'; // Orange with 10% opacity
      case 'share':
        return 'rgba(132, 204, 22, 0.1)'; // Lime with 10% opacity
      case 'iq_points_earned':
        if (notification?.rankedUp) {
          return 'rgba(245, 158, 11, 0.1)'; // Amber with 10% opacity for rank up
        }
        return 'rgba(245, 158, 11, 0.1)'; // Amber with 10% opacity
      case 'iq_update':
        return 'rgba(59, 130, 246, 0.1)'; // Blue with 10% opacity
      case 'qa_answer':
      case 'qa_comment':
        return 'rgba(59, 130, 246, 0.1)'; // Blue with 10% opacity
      case 'event':
        return 'rgba(249, 115, 22, 0.1)'; // Orange with 10% opacity
      default:
        return 'rgba(92, 200, 255, 0.1)'; // Light blue with 10% opacity
    }
  };

  // Map our notification type to the reference's notification icon
  const getNotificationIcon = (type: string, notification?: { rankedUp?: boolean }) => {
    switch (type) {
      case 'connection_request':
        return <UserPlus className="h-5 w-5 text-pink-500" />;
      case 'message':
        return (
          <Image
            src="/images/icons/icon-messages.svg"
            alt="Messages"
            width={20}
            height={20}
            className="h-5 w-5"
            style={{ filter: 'hue-rotate(180deg) saturate(2) brightness(1.2)' }}
          />
        );
      case 'upvote':
        return <Heart className="h-5 w-5 text-red-500" />;
      case 'comment':
        return <MessageCircle className="h-5 w-5 text-orange-500" />;
      case 'share':
        return <Share2 className="h-5 w-5 text-lime-500" />;
      case 'iq_points_earned':
        // Special icon if user ranked up
        if (notification?.rankedUp) {
          return <Crown className="h-5 w-5 text-amber-500" />;
        }
        return <Zap className="h-5 w-5 text-amber-500" />;
      case 'iq_update':
        return <Trophy className="h-5 w-5 text-blue-500" />;
      case 'qa_answer':
      case 'qa_comment':
        return <HelpCircle className="h-5 w-5 text-blue-500" />;
      case 'event':
        return <Calendar className="h-5 w-5 text-orange-500" />;
      default:
        return <Bell className="h-5 w-5 text-[#5cc8ff]" />;
    }
  };

  // Render notification content based on type
  const renderNotificationContent = (notification: {
    id: string;
    type: string;
    content: string;
    senderName?: string;
    senderAvatar?: string;
    senderId?: number;
  }) => {
    if (notification.type === 'connection_request') {
      const isAcceptLoading = connectionActionLoading === `accept-${notification.id}`;
      const isDeclineLoading = connectionActionLoading === `decline-${notification.id}`;
      const isAnyLoading = isAcceptLoading || isDeclineLoading;

      const senderId = notification.senderId;
      const displayName = notification.senderName || 'Someone';
      const avatarUrl = notification.senderAvatar;

      return (
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={avatarUrl || ''} alt={displayName} />
              <AvatarFallback className="text-xs">
                {displayName ? displayName.charAt(0).toUpperCase() : 'U'}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">{displayName}</p>
              <p className="text-xs text-gray-500">wants to connect with you</p>
            </div>
          </div>

          <div className="flex gap-2 mt-3">
            <Button
              size="sm"
              className="flex-1"
              onClick={() => handleAcceptConnection(notification.id, senderId)}
              disabled={isAnyLoading}
            >
              {isAcceptLoading ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Accepting...
                </>
              ) : (
                <>
                  <Check className="h-3 w-3 mr-1" />
                  Accept
                </>
              )}
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="flex-1"
              onClick={() => handleDeclineConnection(notification.id, senderId)}
              disabled={isAnyLoading}
            >
              {isDeclineLoading ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Declining...
                </>
              ) : (
                <>
                  <X className="h-3 w-3 mr-1" />
                  Decline
                </>
              )}
            </Button>
          </div>
        </div>
      );
    }

    // For IQ points notifications, show enhanced content
    if (notification.type === 'iq_points_earned') {
      return (
        <div>
          <p className="text-sm text-gray-700">{notification.content}</p>
        </div>
      );
    }

    // Default notification content
    return (
      <div>
        <p className="text-sm text-gray-700">{notification.content}</p>
        {notification.senderName && (
          <p className="text-xs text-gray-500 mt-1">from {notification.senderName}</p>
        )}
      </div>
    );
  };

  // Render notification group
  const renderNotificationGroup = (
    title: string,
    notifications: Array<{
      id: string;
      type: string;
      timestamp: number;
      read: boolean;
      rankedUp?: boolean;
      content?: string;
    }>
  ) => {
    if (notifications.length === 0) return null;

    return (
      <div>
        <h3 className="text-sm font-medium text-gray-500 mb-2">{title}</h3>
        <div className="space-y-3">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className={`flex gap-3 p-3 rounded-lg transition-colors duration-200 ${!notification.read ? 'bg-[rgba(92,200,255,0.1)] border-l-2 border-[#5cc8ff]' : 'bg-white hover:bg-gray-50'}`}
              onClick={() => markAsRead(notification.id)}
            >
              <div
                className="h-10 w-10 rounded-full flex items-center justify-center flex-shrink-0"
                style={{ backgroundColor: getIconBackgroundColor(notification.type, notification) }}
              >
                {getNotificationIcon(notification.type, notification)}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-500">
                    {formatTime(notification.timestamp)}
                  </span>
                  <div className="flex items-center gap-2">
                    {!notification.read && (
                      <Badge className="bg-[#22c55e] text-white text-xs py-0 px-1.5 h-4">New</Badge>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-gray-400 hover:text-red-500 hover:bg-red-50"
                      onClick={async (e) => {
                        e.stopPropagation();
                        await removeNotification(notification.id);
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {renderNotificationContent({
                  ...notification,
                  content: notification.content ?? '',
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Check if there are any notifications to display
  const hasNotifications = Object.values(groupedNotifications).some((group) => group.length > 0);

  // Show loading state
  const showLoading = false;

  return (
    <>
      <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <SheetContent
          className="w-[400px] rounded-none p-0 overflow-y-auto"
          side="right"
          hideOverlay
        >
          {/* Add SheetHeader with SheetTitle for accessibility */}
          <SheetHeader className="sr-only">
            <SheetTitle>Notifications</SheetTitle>
          </SheetHeader>

          {/* Header with close button */}
          <div className="sticky top-0 bg-white z-10 px-6 py-4 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-[#5cc8ff]" />
                <h2 className="text-xl font-bold">Notifications</h2>
                {unreadCount > 0 && (
                  <Badge className="bg-[#22c55e] text-white ml-2">{unreadCount} new</Badge>
                )}
              </div>
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full hover:bg-red-50 hover:text-red-500"
                  onClick={handleDeleteAll}
                  disabled={notifications.length === 0}
                  title="Delete all notifications"
                >
                  <Trash className="h-4 w-4" />
                </Button>
                <SheetClose asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 rounded-full hover:bg-gray-100"
                    onClick={onClose}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </SheetClose>
              </div>
            </div>

            {/* Mark all as read button */}
            {unreadCount > 0 && (
              <div className="mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleMarkAllAsRead}
                  className="w-full"
                >
                  Mark all as read
                </Button>
              </div>
            )}
          </div>

          <div className="px-6 py-4">
            {showLoading ? (
              <div className="flex flex-col items-center justify-center py-16 text-center">
                <div className="mb-4">
                  <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
                </div>
                <h3 className="text-lg font-semibold mb-1">Loading notifications...</h3>
                <p className="text-gray-500 max-w-xs">
                  Please wait while we fetch your notifications.
                </p>
              </div>
            ) : hasNotifications ? (
              <div className="space-y-6">
                {renderNotificationGroup('Today', groupedNotifications.today)}
                {renderNotificationGroup('Yesterday', groupedNotifications.yesterday)}
                {renderNotificationGroup('This Week', groupedNotifications.thisWeek)}
                {renderNotificationGroup('Earlier', groupedNotifications.earlier)}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-16 text-center">
                <div className="bg-gray-100 rounded-full p-4 mb-4">
                  <Bell className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold mb-1">No notifications</h3>
                <p className="text-gray-500 max-w-xs">
                  {activeTab === 'all'
                    ? "You're all caught up! Check back later for new activity."
                    : "You don't have any unread notifications."}
                </p>
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Custom confirmation dialog */}
      <ConfirmationDialogComponent />
    </>
  );
}
