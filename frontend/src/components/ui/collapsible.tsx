'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

interface CollapsibleProps {
  children: React.ReactNode;
  className?: string;
}

interface CollapsibleContextType {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const CollapsibleContext = React.createContext<CollapsibleContextType | undefined>(undefined);

const Collapsible = React.forwardRef<HTMLDivElement, CollapsibleProps>(
  ({ children, className, ...props }, ref) => {
    const [open, setOpen] = React.useState(false);

    return (
      <CollapsibleContext.Provider value={{ open, onOpenChange: setOpen }}>
        <div ref={ref} className={cn(className)} {...props}>
          {children}
        </div>
      </CollapsibleContext.Provider>
    );
  }
);
Collapsible.displayName = 'Collapsible';

const CollapsibleTrigger = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ children, className, onClick, ...props }, ref) => {
  const context = React.useContext(CollapsibleContext);

  if (!context) {
    throw new Error('CollapsibleTrigger must be used within a Collapsible');
  }

  const { open, onOpenChange } = context;

  return (
    <button
      ref={ref}
      className={cn(className)}
      onClick={(e) => {
        onOpenChange(!open);
        onClick?.(e);
      }}
      {...props}
    >
      {children}
    </button>
  );
});
CollapsibleTrigger.displayName = 'CollapsibleTrigger';

const CollapsibleContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ children, className, ...props }, ref) => {
    const context = React.useContext(CollapsibleContext);

    if (!context) {
      throw new Error('CollapsibleContent must be used within a Collapsible');
    }

    const { open } = context;

    return (
      <div
        ref={ref}
        className={cn(
          'overflow-hidden transition-all duration-200',
          open ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0',
          className
        )}
        {...props}
      >
        <div className={open ? 'pb-3' : ''}>{children}</div>
      </div>
    );
  }
);
CollapsibleContent.displayName = 'CollapsibleContent';

export { Collapsible, CollapsibleTrigger, CollapsibleContent };
