/*********************************************
# frontend/src/components/ui/WelcomeModal.tsx
# Created for TourismQ v2 welcome modal with cookie persistence
**********************************************/

'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogHeader } from './dialog';
import { Button } from './button';
import { Sparkles, ArrowRight, Users, Zap, Globe, Shield } from 'lucide-react';
import { SupportModal } from './SupportModal';

interface WelcomeModalProps {
  onClose?: () => void;
}

export function WelcomeModal({ onClose }: WelcomeModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isSupportModalOpen, setIsSupportModalOpen] = useState(false);

  useEffect(() => {
    // Check if the welcome modal was already dismissed
    const hasSeenWelcome = document.cookie
      .split('; ')
      .find((row) => row.startsWith('tourismq_welcome_dismissed='));

    if (!hasSeenWelcome) {
      // Show modal after a brief delay for better UX
      const timer = setTimeout(() => {
        setIsOpen(true);
      }, 1000);

      return () => clearTimeout(timer);
    }

    return undefined;
  }, []);

  const handleClose = () => {
    // Set cookie to remember that user dismissed the modal
    // Cookie expires in 1 year
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 1);
    document.cookie = `tourismq_welcome_dismissed=true; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax`;

    setIsOpen(false);
    onClose?.();
  };

  const features = [
    {
      icon: <Zap className="h-5 w-5" />,
      title: 'Modern Tech Stack',
      description: 'Upgraded to Next.js with enhanced performance and reliability',
    },
    {
      icon: <Sparkles className="h-5 w-5" />,
      title: 'Fresh UI/UX',
      description: 'Complete visual overhaul with improved navigation and user experience',
    },
    {
      icon: <Users className="h-5 w-5" />,
      title: 'Enhanced Features',
      description: 'Better member directory, improved messaging, and streamlined vendor tools',
    },
    {
      icon: <Globe className="h-5 w-5" />,
      title: 'New Hosting',
      description: 'Faster, more reliable infrastructure for a smoother experience',
    },
  ];

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] p-0 bg-white rounded-2xl shadow-2xl border-0 flex flex-col overflow-hidden">
          <DialogHeader>
            <DialogTitle className="sr-only">Welcome to TourismIQ V2</DialogTitle>
          </DialogHeader>

          {/* Header with gradient background and celebration animation */}
          <div className="relative bg-gradient-to-br from-[#5cc8ff]/30 via-[#5cc8ff]/20 to-[#3d405b]/10 p-6 rounded-t-2xl overflow-hidden">
            {/* Decorative background elements */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#5cc8ff]/20 to-transparent rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-[#3d405b]/10 to-transparent rounded-full blur-xl"></div>

            <div className="relative flex items-start space-x-4">
              <div className="p-3 bg-white rounded-2xl shadow-lg border border-[#5cc8ff]/20">
                <Sparkles className="h-8 w-8 text-[#5cc8ff]" />
              </div>
              <div className="flex-1">
                <h2 className="text-3xl font-bold text-[#3d405b] leading-tight mb-2">
                  Welcome to TourismIQ V2! 🎉
                </h2>
                <p className="text-[#3d405b]/80 text-lg leading-relaxed">
                  We&apos;re excited to introduce our completely redesigned platform with major
                  improvements across the board
                </p>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-8">
            {/* What's New Section */}
            <div className="mb-8">
              <h3 className="text-xl font-bold text-[#3d405b] mb-6 flex items-center">
                <Sparkles className="h-5 w-5 text-[#5cc8ff] mr-2" />
                What&apos;s New:
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className="group p-4 rounded-xl border-2 border-gray-100 hover:border-[#5cc8ff]/30 hover:bg-[#5cc8ff]/5 transition-all duration-300"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="p-2 bg-[#5cc8ff]/10 text-[#5cc8ff] rounded-lg group-hover:bg-[#5cc8ff] group-hover:text-white transition-all duration-300">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-[#3d405b] text-sm mb-1">
                          {feature.title}
                        </h4>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* For Existing Users Section */}
            <div className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-[#5cc8ff]/10 rounded-xl border border-blue-100">
              <h3 className="text-lg font-bold text-[#3d405b] mb-3 flex items-center">
                <Shield className="h-5 w-5 text-blue-600 mr-2" />
                For Existing Users:
              </h3>
              <p className="text-gray-700 leading-relaxed mb-3">
                Your account and data have been preserved. If you experience any login issues,
                please contact our support team and we&apos;ll help you get back in quickly.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="text-blue-600 border-blue-200 hover:bg-blue-50 rounded-full"
                onClick={() => setIsSupportModalOpen(true)}
              >
                Contact Support
              </Button>
            </div>

            {/* What's Next Section */}
            <div className="mb-6">
              <h3 className="text-lg font-bold text-[#3d405b] mb-3 flex items-center">
                <ArrowRight className="h-5 w-5 text-[#5cc8ff] mr-2" />
                What&apos;s Next:
              </h3>
              <p className="text-gray-700 leading-relaxed mb-4">
                Explore the new interface, connect with fellow tourism professionals, and discover
                enhanced tools to grow your business. We&apos;re just getting started!
              </p>
            </div>

            {/* Footer Message */}
            <div className="text-center p-6 bg-gradient-to-r from-[#5cc8ff]/10 to-[#3d405b]/10 rounded-xl">
              <p className="text-[#3d405b] font-medium leading-relaxed">
                Thank you for being part of our community. Here&apos;s to building the future of
                tourism together! 🚀
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-3 mt-8">
              <Button
                onClick={handleClose}
                className="px-8 py-3 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/90 font-bold rounded-full transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center group"
              >
                <span>Let&apos;s Explore!</span>
                <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </Button>
              <Button
                variant="outline"
                onClick={handleClose}
                className="px-8 py-3 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 font-semibold rounded-full transition-all duration-200"
              >
                Maybe Later
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <SupportModal isOpen={isSupportModalOpen} onClose={() => setIsSupportModalOpen(false)} />
    </>
  );
}
