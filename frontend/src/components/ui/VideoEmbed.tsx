'use client';

import { useState, useEffect, useRef } from 'react';

interface VideoEmbedProps {
  embedUrl: string;
  videoSource: string;
  title: string;
  className?: string;
}

export function VideoEmbed({ embedUrl, videoSource, title, className = '' }: VideoEmbedProps) {
  const [hasError, setHasError] = useState(false);
  const [isTwitterLoaded, setIsTwitterLoaded] = useState(false);
  const twitterRef = useRef<HTMLDivElement>(null);

  // Load Twitter widgets script for Twitter embeds
  useEffect(() => {
    if (
      videoSource === 'twitter' ||
      embedUrl.includes('twitter.com') ||
      embedUrl.includes('x.com')
    ) {
      if (typeof window !== 'undefined' && !(window as any).twttr) {
        const script = document.createElement('script');
        script.src = 'https://platform.twitter.com/widgets.js';
        script.async = true;
        script.onload = () => {
          setIsTwitterLoaded(true);
          if ((window as any).twttr && twitterRef.current) {
            (window as any).twttr.widgets.load(twitterRef.current);
          }
        };
        document.head.appendChild(script);
      } else if ((window as any).twttr) {
        setIsTwitterLoaded(true);
        if (twitterRef.current) {
          (window as any).twttr.widgets.load(twitterRef.current);
        }
      }
    }
  }, [videoSource, embedUrl]);

  if (!embedUrl || hasError) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`}>
        <div className="text-center text-gray-500">
          <p>Video embed not available</p>
          {embedUrl && (
            <a
              href={embedUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline text-sm mt-2 inline-block"
            >
              View Video
            </a>
          )}
        </div>
      </div>
    );
  }

  // Handle different video platforms
  if (videoSource === 'youtube' || embedUrl.includes('youtube.com/embed/')) {
    return (
      <iframe
        src={embedUrl}
        title={title}
        className={`w-full ${className}`}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        onError={() => setHasError(true)}
      />
    );
  }

  if (videoSource === 'vimeo' || embedUrl.includes('vimeo.com')) {
    // Process Vimeo URL client-side if needed
    let vimeoEmbedUrl = embedUrl;

    if (!embedUrl.includes('player.vimeo.com/video/')) {
      // Try to extract video ID from various Vimeo URL formats
      const patterns = [
        /(?:vimeo\.com\/)([0-9]+)/,
        /(?:vimeo\.com\/(?:user[0-9]+\/|channels\/[^\/]+\/))([^\/\?]+)/,
        /(?:vimeo\.com\/)([0-9]+)\/[a-zA-Z0-9]+/,
        /([0-9]{6,})/, // Last resort: any long numeric ID
      ];

      for (const pattern of patterns) {
        const match = embedUrl.match(pattern);
        if (match && match[1]) {
          if (/^[0-9]+$/.test(match[1])) {
            vimeoEmbedUrl = `https://player.vimeo.com/video/${match[1]}`;
            break;
          }
        }
      }
    }

    return (
      <iframe
        src={vimeoEmbedUrl}
        title={title}
        className={`w-full bg-black ${className}`}
        allow="autoplay; fullscreen; picture-in-picture"
        allowFullScreen
        onError={() => setHasError(true)}
      />
    );
  }

  if (videoSource === 'twitter' || embedUrl.includes('twitter.com') || embedUrl.includes('x.com')) {
    return (
      <div ref={twitterRef} className={`w-full flex justify-center ${className}`}>
        <blockquote className="twitter-tweet" data-theme="light" data-align="center">
          <a href={embedUrl} target="_blank" rel="noopener noreferrer">
            {title}
          </a>
        </blockquote>
        {!isTwitterLoaded && (
          <div className="flex items-center justify-center bg-gray-50 border-2 border-dashed border-gray-300 rounded-xl p-8 min-h-[300px] w-full">
            <div className="text-center">
              <div className="mb-4">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                </svg>
              </div>
              <p className="text-gray-600 mb-4">Loading Twitter embed...</p>
              <a
                href={embedUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                View on X/Twitter
              </a>
            </div>
          </div>
        )}
      </div>
    );
  }

  if (videoSource === 'linkedin' || embedUrl.includes('linkedin.com')) {
    return (
      <iframe
        src={embedUrl}
        title={title}
        className={`w-full ${className}`}
        allowFullScreen
        onError={() => setHasError(true)}
      />
    );
  }

  // Generic iframe fallback
  return (
    <iframe
      src={embedUrl}
      title={title}
      className={`w-full ${className}`}
      allowFullScreen
      onError={() => setHasError(true)}
    />
  );
}
