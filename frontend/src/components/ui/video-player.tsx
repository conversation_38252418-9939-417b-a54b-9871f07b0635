'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { Play } from 'lucide-react';

interface VideoPlayerProps {
  src: string;
  className?: string;
}

export function VideoPlayer({ src, className = '' }: VideoPlayerProps) {
  const [showVideo, setShowVideo] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handlePlay = () => {
    setShowVideo(true);
    // Auto-play video when showing it
    setTimeout(() => {
      if (videoRef.current) {
        videoRef.current.play();
      }
    }, 100);
  };

  if (!showVideo) {
    return (
      <div className={`relative bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
        <div className="aspect-video flex items-center justify-center">
          <button
            onClick={handlePlay}
            className="absolute inset-0 z-10 flex items-center justify-center group"
            aria-label="Play video"
          >
            <div className="absolute inset-0 bg-black/5 group-hover:bg-black/10 transition-colors" />
            <div className="relative flex flex-col items-center gap-4">
              <Image
                src="/images/logo.svg"
                alt="TourismIQ"
                width={200}
                height={60}
                className="relative z-20"
              />
              <div className="relative z-20 flex items-center justify-center w-16 h-16 bg-gray-800 rounded-full group-hover:bg-gray-700 transition-colors">
                <Play className="w-8 h-8 text-white ml-1" fill="white" />
              </div>
            </div>
          </button>
        </div>
      </div>
    );
  }

  return (
    <video ref={videoRef} controls className={`rounded-lg shadow-lg ${className}`}>
      <source src={src} type="video/mp4" />
      Your browser does not support the video tag.
    </video>
  );
}
