'use client';

import * as React from 'react';
import * as AvatarPrimitive from '@radix-ui/react-avatar';

import { cn } from '@/lib/utils';

function Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {
  return (
    <AvatarPrimitive.Root
      data-slot="avatar"
      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}
      {...props}
    />
  );
}

interface AvatarImageProps extends React.ComponentProps<typeof AvatarPrimitive.Image> {
  src?: string;
  alt?: string;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
}

function AvatarImage({ className, src, alt, onError, ...props }: AvatarImageProps) {
  const [imageError, setImageError] = React.useState(false);
  const [imageSrc, setImageSrc] = React.useState<string | undefined>(
    typeof src === 'string' ? src : undefined
  );

  React.useEffect(() => {
    if (typeof src === 'string' && src.trim() !== '') {
      // Ensure the URL is absolute
      let url = src;
      if (!url.startsWith('http') && !url.startsWith('/')) {
        url = `/${url.replace(/^\/+/, '')}`; // Ensure single leading slash
      }
      setImageSrc(url);
      setImageError(false);
    } else {
      setImageSrc(undefined);
    }
  }, [src]);

  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.error('Failed to load avatar:', src);
    setImageError(true);
    if (onError) {
      onError(e);
    }
  };

  // If there's no valid source or there was an error, return null to trigger the fallback
  if (imageError || !imageSrc) {
    return null;
  }

  return (
    <AvatarPrimitive.Image
      data-slot="avatar-image"
      className={cn('aspect-square size-full object-cover', className)}
      src={imageSrc}
      alt={alt || 'User avatar'}
      onError={handleError}
      {...props}
    />
  );
}

function AvatarFallback({
  className,
  ...props
}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {
  return (
    <AvatarPrimitive.Fallback
      data-slot="avatar-fallback"
      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}
      {...props}
    />
  );
}

export { Avatar, AvatarImage, AvatarFallback };
