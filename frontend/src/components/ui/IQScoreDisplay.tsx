'use client';

import { useState, useEffect, useCallback } from 'react';
import { Zap, TrendingUp, Trophy, Info } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { iqScoreAPI, type UserIQData, type Rank } from '@/lib/api/iq-score';

interface IQScoreDisplayProps {
  userId?: number;
  variant?: 'full' | 'compact' | 'minimal';
  showProgress?: boolean;
  showBadge?: boolean;
  className?: string;
}

export function IQScoreDisplay({
  userId,
  variant = 'full',
  showProgress = true,
  showBadge = true,
  className = '',
}: IQScoreDisplayProps) {
  const [userIQ, setUserIQ] = useState<UserIQData | null>(null);
  const [ranks, setRanks] = useState<Rank[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserIQ = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      let response;
      if (userId) {
        response = await iqScoreAPI.getUserIQ(userId);
      } else {
        response = await iqScoreAPI.getCurrentUserIQ();
      }

      if (response.success) {
        setUserIQ(response.data);
      } else {
        setError('Failed to load IQ score');
      }
    } catch (err) {
      console.error('Error fetching user IQ:', err);
      setError('Failed to load IQ score');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchUserIQ();
    fetchRanks();
  }, [fetchUserIQ]);

  const fetchRanks = async () => {
    try {
      const response = await iqScoreAPI.getRanks();
      if (response.success) {
        setRanks(response.data);
      }
    } catch (err) {
      console.error('Error fetching ranks:', err);
    }
  };

  const getNextRankInfo = () => {
    if (!userIQ || ranks.length === 0) return null;
    return iqScoreAPI.getNextRankInfo(userIQ.iq_score, ranks);
  };

  const getRankColor = (rankName: string) => {
    return iqScoreAPI.getRankColor(rankName);
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-16"></div>
      </div>
    );
  }

  if (error || !userIQ) {
    return (
      <div className={`text-muted-foreground text-sm ${className}`}>
        {error || 'No IQ data available'}
      </div>
    );
  }

  const nextRankInfo = getNextRankInfo();

  // Minimal variant - just score and rank
  if (variant === 'minimal') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Zap className="h-4 w-4 text-amber-500" />
        <span className="font-semibold">{iqScoreAPI.formatScore(userIQ.iq_score)}</span>
        {showBadge && (
          <Badge
            variant="secondary"
            className="text-xs"
            style={{
              backgroundColor: `${getRankColor(userIQ.rank.name)}20`,
              color: getRankColor(userIQ.rank.name),
            }}
          >
            {userIQ.rank.name}
          </Badge>
        )}
      </div>
    );
  }

  // Compact variant - score, rank, and position
  if (variant === 'compact') {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-amber-500" />
            <span className="font-bold text-lg">{iqScoreAPI.formatScore(userIQ.iq_score)}</span>
            <span className="text-sm text-muted-foreground">points</span>
          </div>
          {userIQ.position && (
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Trophy className="h-4 w-4" />
              <span>#{userIQ.position}</span>
            </div>
          )}
        </div>
        {showBadge && (
          <Badge
            variant="secondary"
            style={{
              backgroundColor: `${getRankColor(userIQ.rank.name)}20`,
              color: getRankColor(userIQ.rank.name),
            }}
          >
            {userIQ.rank.name} Rank
          </Badge>
        )}
      </div>
    );
  }

  // Full variant - all information with progress
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with score and rank */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-amber-100 rounded-full">
            <Zap className="h-6 w-6 text-amber-600" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold">{iqScoreAPI.formatScore(userIQ.iq_score)}</span>
              <span className="text-muted-foreground">IQ Points</span>
            </div>
            <div className="flex items-center gap-2">
              {showBadge && (
                <Badge
                  variant="secondary"
                  style={{
                    backgroundColor: `${getRankColor(userIQ.rank.name)}20`,
                    color: getRankColor(userIQ.rank.name),
                  }}
                >
                  {userIQ.rank.name} Rank
                </Badge>
              )}
              {userIQ.position && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Trophy className="h-4 w-4" />
                  <span>#{userIQ.position} Overall</span>
                </div>
              )}
            </div>
          </div>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Info className="h-4 w-4 text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">
                IQ Points are earned through active participation: posting content, engaging with
                others, and contributing to the community.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Progress to next rank */}
      {showProgress && nextRankInfo && nextRankInfo.nextRank && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Progress to {nextRankInfo.nextRank.name}</span>
            <span className="font-medium">{nextRankInfo.pointsNeeded} points needed</span>
          </div>
          <Progress value={nextRankInfo.progress} className="h-2" />
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <TrendingUp className="h-3 w-3" />
            <span>{Math.round(nextRankInfo.progress)}% complete</span>
          </div>
        </div>
      )}

      {/* Already at max rank */}
      {showProgress && nextRankInfo && !nextRankInfo.nextRank && (
        <div className="flex items-center gap-2 text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
          <Trophy className="h-4 w-4" />
          <span className="font-medium">Maximum rank achieved! 🎉</span>
        </div>
      )}

      {/* Rank requirements tooltip */}
      <div className="text-xs text-muted-foreground">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger className="underline decoration-dotted">
              {userIQ.rank.name} ({userIQ.rank.min_points}-{userIQ.rank.max_points} points)
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                <p className="font-medium">Rank Requirements:</p>
                <p>
                  Current: {userIQ.rank.min_points}-{userIQ.rank.max_points} points
                </p>
                {nextRankInfo?.nextRank && <p>Next: {nextRankInfo.nextRank.min_points}+ points</p>}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}
