'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowUp } from 'lucide-react';

export function BackToTopButton() {
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Handle scroll to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowBackToTop(true);
      } else {
        setShowBackToTop(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Function to scroll back to top with smooth animation
  const scrollToTop = () => {
    const duration = 400; // 400ms duration
    const start = window.pageYOffset;
    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function for smooth animation
      const easeInOutCubic = (t: number) =>
        t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;

      const currentPosition = start * (1 - easeInOutCubic(progress));
      window.scrollTo(0, currentPosition);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };

    requestAnimationFrame(animateScroll);
  };

  return (
    <AnimatePresence>
      {showBackToTop && (
        <motion.button
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          onClick={scrollToTop}
          className="fixed bottom-4 left-[34%] transform -translate-x-1/2 md:absolute md:top-[110px] lg:top-[130px] lg:left-[31%] xl:left-[45%] md:bottom-auto bg-[#5cc8ff] text-[#3d405b] px-3 py-2 rounded-full shadow-lg flex items-center gap-1.5 transition-colors duration-200 text-sm font-bold hover:bg-[#5cc8ff]/90 z-40"
          aria-label="Back to top"
        >
          <ArrowUp className="h-4 w-4" />
          <span>Back to top</span>
        </motion.button>
      )}
    </AnimatePresence>
  );
}
