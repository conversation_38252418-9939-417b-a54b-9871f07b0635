/*********************************************
# frontend/src/components/ui/BackToSectionHeader.tsx
# 01/27/2025 9:45pm Created file - Sticky header component for back navigation with frosted glass effect
# 01/27/2025 10:15pm Increased header and button height by 20%
# 01/27/2025 10:25pm Reduced height increase to 10% and added button padding
# 01/27/2025 10:30pm Increased white background opacity from 70% to 85% for better contrast when content scrolls behind
# 01/27/2025 10:35pm Added shadow-lg to sticky header for visual consistency with feed container
# 02/07/2025 10:30pm Updated hover state opacity from 10% to 20% to match sidebar nav hover state
**********************************************/

'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface BackToSectionHeaderProps {
  href: string;
  text: string;
  className?: string;
}

export function BackToSectionHeader({ href, text, className = '' }: BackToSectionHeaderProps) {
  return (
    <div
      className={`sticky top-[79px] md:top-[90px] z-40 backdrop-blur-md bg-white/90 py-2.5 px-4 border-b shadow-lg ${className}`}
    >
      <Button
        variant="ghost"
        size="sm"
        asChild
        className="text-[#3d405b] hover:bg-[rgba(92,200,255,0.2)] h-10 px-6 min-w-[150px]"
      >
        <Link href={href} className="flex items-center gap-2">
          <ArrowLeft className="h-5 w-5" />
          {text}
        </Link>
      </Button>
    </div>
  );
}
