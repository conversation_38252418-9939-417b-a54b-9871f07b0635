'use client';

import { useEffect, useRef, forwardRef, useImperativeHandle, useState } from 'react';
import { cn } from '@/lib/utils';
import '@/styles/quill.css';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export interface RichTextEditorRef {
  focus: () => void;
}

export const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(
  ({ value, onChange, placeholder, className, disabled }, ref) => {
    const quillRef = useRef<any>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const [isMounted, setIsMounted] = useState(false);

    useImperativeHandle(ref, () => ({
      focus: () => {
        if (quillRef.current) {
          quillRef.current.focus();
        }
      },
    }));

    // Set mounted state to prevent hydration issues
    useEffect(() => {
      setIsMounted(true);
    }, []);

    useEffect(() => {
      let quill: any = null;

      const initQuill = async () => {
        try {
          // Dynamic import to avoid SSR issues
          const { default: Quill } = await import('quill');

          if (containerRef.current && !quillRef.current && isMounted) {
            // Custom toolbar configuration
            const toolbarOptions = [
              ['bold', 'italic', 'underline'],
              [{ list: 'ordered' }, { list: 'bullet' }],
              ['link'],
              ['clean'], // remove formatting button
            ];

            quill = new Quill(containerRef.current, {
              theme: 'snow',
              placeholder: placeholder || 'Start typing...',
              modules: {
                toolbar: toolbarOptions,
              },
              formats: ['bold', 'italic', 'underline', 'list', 'link'],
            });

            quillRef.current = quill;

            // Set initial content
            if (value) {
              quill.root.innerHTML = value;
            }

            // Handle text changes
            quill.on('text-change', () => {
              const content = quill.root.innerHTML;
              // Clean up empty paragraphs
              const cleanContent = content === '<p><br></p>' ? '' : content;
              onChange(cleanContent);
            });

            // Handle disabled state
            if (disabled) {
              quill.enable(false);
            }
          }
        } catch (error) {
          console.error('Failed to load Quill editor:', error);
        }
      };

      if (isMounted) {
        initQuill();
      }

      return () => {
        if (quillRef.current) {
          quillRef.current = null;
        }
      };
    }, [isMounted]);

    // Update content when value prop changes
    useEffect(() => {
      if (quillRef.current && value !== undefined) {
        const currentContent = quillRef.current.root.innerHTML;
        const cleanValue = value || '';

        if (currentContent !== cleanValue) {
          quillRef.current.root.innerHTML = cleanValue;
        }
      }
    }, [value]);

    // Update disabled state
    useEffect(() => {
      if (quillRef.current) {
        quillRef.current.enable(!disabled);
      }
    }, [disabled]);

    // Show loading state until mounted to prevent hydration issues
    if (!isMounted) {
      return (
        <div className={cn('rich-text-editor', className)}>
          <div className="border border-gray-300 rounded-md p-4 min-h-[120px] bg-gray-50">
            <div className="text-gray-500">Loading editor...</div>
          </div>
        </div>
      );
    }

    return (
      <div className={cn('rich-text-editor', className)}>
        <div ref={containerRef} />
        <style jsx global>{`
          .ql-editor {
            min-height: 120px;
            font-size: 14px;
            line-height: 1.5;
          }

          .ql-toolbar {
            border-top: 1px solid #e2e8f0;
            border-left: 1px solid #e2e8f0;
            border-right: 1px solid #e2e8f0;
            border-bottom: none;
            border-radius: 0.375rem 0.375rem 0 0;
          }

          .ql-container {
            border-bottom: 1px solid #e2e8f0;
            border-left: 1px solid #e2e8f0;
            border-right: 1px solid #e2e8f0;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            font-family: inherit;
          }

          .ql-editor.ql-blank::before {
            color: #9ca3af;
            font-style: normal;
          }

          /* Dark mode support */
          .dark .ql-toolbar {
            border-color: #374151;
            background-color: #374151;
          }

          .dark .ql-container {
            border-color: #374151;
            background-color: #1f2937;
          }

          .dark .ql-editor {
            color: #f9fafb;
          }

          .dark .ql-editor.ql-blank::before {
            color: #6b7280;
          }

          .dark .ql-stroke {
            stroke: #9ca3af;
          }

          .dark .ql-fill {
            fill: #9ca3af;
          }

          /* Focus styles */
          .ql-container.ql-focus,
          .ql-toolbar.ql-focus {
            border-color: #3b82f6;
            outline: 2px solid transparent;
            outline-offset: 2px;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
          }
        `}</style>
      </div>
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';
