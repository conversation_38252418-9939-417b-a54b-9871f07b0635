'use client';

import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { FileIcon, ImageIcon, XIcon, CheckIcon } from 'lucide-react';

interface FileUploadWithPreviewProps {
  label: string;
  accept?: string;
  required?: boolean;
  currentFileUrl?: string;
  currentFileName?: string;
  fieldName: string;
  onChange: (fieldName: string, file: File | null, keepExisting?: boolean) => void;
  className?: string;
}

export function FileUploadWithPreview({
  label,
  accept,
  required = false,
  currentFileUrl,
  currentFileName,
  fieldName,
  onChange,
  className = '',
}: FileUploadWithPreviewProps) {
  const [keepExisting, setKeepExisting] = useState(true);
  const [newFile, setNewFile] = useState<File | null>(null);
  const [newFilePreview, setNewFilePreview] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setNewFile(file);
    setKeepExisting(false);
    onChange(fieldName, file, false);

    // Create preview for image files
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewFilePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setNewFilePreview(null);
    }
  };

  const handleKeepExisting = () => {
    setKeepExisting(true);
    setNewFile(null);
    setNewFilePreview(null);
    onChange(fieldName, null, true);
    // Clear the file input
    const input = document.getElementById(`${fieldName}-input`) as HTMLInputElement;
    if (input) {
      input.value = '';
    }
  };

  const handleRemoveNew = () => {
    setNewFile(null);
    setNewFilePreview(null);
    if (currentFileUrl) {
      setKeepExisting(true);
      onChange(fieldName, null, true);
    } else {
      onChange(fieldName, null, false);
    }
    // Clear the file input
    const input = document.getElementById(`${fieldName}-input`) as HTMLInputElement;
    if (input) {
      input.value = '';
    }
  };

  const isImage =
    accept?.includes('image') || currentFileName?.match(/\.(jpg|jpeg|png|gif|webp)$/i);
  const FileIconComponent = isImage ? ImageIcon : FileIcon;

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={`${fieldName}-input`} className="text-sm text-gray-600">
        {label} {required && '*'}
      </Label>

      {/* Show current file if exists */}
      {currentFileUrl && (
        <div className="p-4 bg-gradient-to-r from-primary/10 to-primary/15 border border-primary/30 rounded-lg shadow-sm">
          {/* Image preview for image files */}
          {isImage && (
            <div className="mb-4">
              <img
                src={currentFileUrl}
                alt={currentFileName || 'Current image'}
                className="w-full h-auto max-h-48 rounded-lg object-cover shadow-md border border-gray-200"
              />
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary/20 rounded-full">
                <FileIconComponent className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="text-sm font-semibold text-foreground">Current file</p>
                <p className="text-xs text-primary truncate max-w-48">
                  {currentFileName || 'Uploaded file'}
                </p>
              </div>
            </div>
            {keepExisting && (
              <div className="flex items-center space-x-2 bg-green-100 px-3 py-1 rounded-full">
                <CheckIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs font-medium text-green-700">Active</span>
              </div>
            )}
          </div>

          {!keepExisting && (
            <Button
              type="button"
              size="sm"
              variant="outline"
              onClick={handleKeepExisting}
              className="mt-3 w-full bg-white hover:bg-gray-50 border-primary text-primary hover:text-primary"
            >
              ↶ Keep existing file
            </Button>
          )}
        </div>
      )}

      {/* Show new file if selected */}
      {newFile && (
        <div className="p-4 bg-gradient-to-r from-blue-50 to-sky-50 border border-blue-200 rounded-lg shadow-sm">
          {/* Image preview for new image files */}
          {newFilePreview && (
            <div className="mb-4">
              <img
                src={newFilePreview}
                alt={newFile.name}
                className="w-full h-auto max-h-48 rounded-lg object-cover shadow-md border border-gray-200"
              />
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <FileIconComponent className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-semibold text-blue-900">New file selected</p>
                <p className="text-xs text-blue-700 truncate max-w-48">{newFile.name}</p>
              </div>
            </div>
            <Button
              type="button"
              size="sm"
              variant="outline"
              onClick={handleRemoveNew}
              className="bg-white hover:bg-red-50 border-red-300 text-red-600 hover:text-red-700 hover:border-red-400"
            >
              <XIcon className="h-3 w-3 mr-1" />
              Remove
            </Button>
          </div>
        </div>
      )}

      {/* Custom File Input */}
      <div className="space-y-2">
        <div className="relative">
          <input
            id={`${fieldName}-input`}
            type="file"
            accept={accept}
            onChange={handleFileChange}
            required={required && !currentFileUrl}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          />
          <div className="w-full h-16 border-2 border-dashed border-gray-300 hover:border-primary bg-gray-50 hover:bg-primary/10 rounded-lg transition-all duration-200 cursor-pointer group">
            <div className="flex flex-col items-center justify-center h-full space-y-1">
              <div className="flex items-center space-x-2 text-gray-600 group-hover:text-primary">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  />
                </svg>
                <span className="text-sm font-medium">
                  {currentFileUrl ? 'Choose new file' : `Upload ${isImage ? 'image' : 'file'}`}
                </span>
              </div>
              <span className="text-xs text-gray-500 group-hover:text-primary">
                or drag and drop
              </span>
            </div>
          </div>
        </div>

        {currentFileUrl && (
          <p className="text-xs text-gray-500 text-center">
            Choose a new file to replace the current one, or leave empty to keep existing.
          </p>
        )}

        {!currentFileUrl && accept && (
          <p className="text-xs text-gray-500 text-center">
            {accept.includes('image')
              ? 'PNG, JPG, GIF up to 10MB'
              : accept.includes('pdf')
                ? 'PDF files up to 10MB'
                : 'Supported file formats'}
          </p>
        )}
      </div>
    </div>
  );
}
