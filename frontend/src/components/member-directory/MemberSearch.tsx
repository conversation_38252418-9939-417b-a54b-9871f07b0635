/*********************************************
# frontend/src/components/member-directory/MemberSearch.tsx
# 02/07/2025 3:45pm Updated to use SearchBar component matching JobsHub styling
**********************************************/

import SearchBar from '@/components/forum/search-bar';

interface MemberSearchProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}

export function MemberSearch({ searchQuery, setSearchQuery }: MemberSearchProps) {
  // Handle search from SearchBar component
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  return (
    <div className="w-full">
      <SearchBar
        onSearch={handleSearch}
        placeholder="Search by name, username, job title, company, location..."
        defaultValue={searchQuery}
        inputClassName="bg-white rounded-full h-12 border border-gray-200 px-6 text-base"
      />
    </div>
  );
}
