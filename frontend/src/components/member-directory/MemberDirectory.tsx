/*********************************************
# frontend/src/components/member-directory/MemberDirectory.tsx
# 02/07/2025 4:00pm Updated header styling to match other pages (text-3xl font-bold) and description text color
# 02/07/2025 4:05pm Added back to top button functionality matching other pages
# 02/07/2025 4:30pm Replaced numbered pagination with infinite scroll pagination
**********************************************/

'use client';

import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { MemberCard } from './MemberCard';
import { MemberSearch } from './MemberSearch';
import { Member } from '@/lib/types';
import { useBulkConnectionStatuses } from '@/hooks/queries/useBulkConnections';
import { useBulkIQScores } from '@/hooks/queries/useBulkIQScores';

const MEMBERS_PER_PAGE = 30;

export function MemberDirectory() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [allMembers, setAllMembers] = useState<Member[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const observer = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  // Fetch members from WordPress API via Next.js proxy
  const fetchMembers = useCallback(
    async (page: number = 1, append: boolean = false) => {
      try {
        if (append) {
          setIsLoadingMore(true);
        } else {
          setIsLoading(true);
        }

        // Build URL with search parameter if provided
        const searchParam = searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : '';
        const response = await fetch(
          `/api/wp-proxy/members?per_page=${MEMBERS_PER_PAGE}&page=${page}${searchParam}`,
          {
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
          }
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch users: ${response.status}`);
        }

        // Get pagination info from headers
        const totalPagesHeader = response.headers.get('X-WP-TotalPages');
        const totalPages = totalPagesHeader ? parseInt(totalPagesHeader, 10) : 1;

        // Get members from API
        const membersData = await response.json();

        // The custom endpoint returns { founders: [], members: [] } - combine them
        const newMembers = [...(membersData.founders || []), ...(membersData.members || [])];

        // Transform to ensure consistent structure for our Member type
        const members = newMembers.map((user: Record<string, unknown>) => {
          return {
            id: user.id as number,
            name: user.name as string,
            username: user.username as string,
            firstName: (user.firstName as string) || '',
            lastName: (user.lastName as string) || '',
            slug: user.slug as string,
            roles:
              (user.roles as string[]) ||
              (user.role === 'Founder'
                ? ['founder']
                : user.role === 'Admin'
                  ? ['admin']
                  : ['member']),
            role: user.role as string,
            avatar: user.avatar as string,
            ...(user.profile_picture &&
            typeof user.profile_picture === 'object' &&
            Object.keys(user.profile_picture).length > 0
              ? {
                  profile_picture: user.profile_picture as {
                    ID: number;
                    id: number;
                    title: string;
                    url: string;
                    [key: string]: unknown;
                  },
                }
              : typeof user.profile_picture === 'string' && user.profile_picture
                ? { profile_picture: user.profile_picture }
                : {}),
            bio: (user.bio as string) || '',
            location: (user.location as string) || 'Unknown',
            featured: (user.featured as boolean) || false,
            categories: (user.categories as string[]) || [],
            coverImage: (user.coverImage as string) || '',
            job_title: (user.job_title as string) || '',
            company: (user.company as string) || '',
            socialLinks: {
              website: (user.socialLinks as Record<string, string>)?.website || '',
              twitter: (user.socialLinks as Record<string, string>)?.twitter || '',
              facebook: (user.socialLinks as Record<string, string>)?.facebook || '',
              linkedin: (user.socialLinks as Record<string, string>)?.linkedin || '',
              instagram: (user.socialLinks as Record<string, string>)?.instagram || '',
            },
            contactInfo: {
              email: (user.contactInfo as Record<string, string>)?.email || '',
              phone: (user.contactInfo as Record<string, string>)?.phone || '',
              website: (user.contactInfo as Record<string, string>)?.website || '',
            },
            user_registered: (user.user_registered as string) || '',
            displayName:
              (user.name as string) ||
              `${user.firstName} ${user.lastName}`.trim() ||
              (user.username as string),
          };
        });

        if (append) {
          setAllMembers((prev) => [...prev, ...members]);
        } else {
          setAllMembers(members);
        }

        setHasMore(page < totalPages);
      } catch (error) {
        console.error('Error fetching members:', error);
        setError(error instanceof Error ? error : new Error('An error occurred'));
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
      }
    },
    [searchQuery]
  );

  // Load more members for infinite scroll
  const loadMoreMembers = useCallback(async () => {
    if (isLoadingMore || !hasMore) return;

    const nextPage = currentPage + 1;
    await fetchMembers(nextPage, true);
    setCurrentPage(nextPage);
  }, [isLoadingMore, hasMore, currentPage, fetchMembers]);

  // Setup intersection observer for infinite scroll
  useEffect(() => {
    if (isLoading) return () => {};

    if (observer.current) {
      observer.current.disconnect();
    }

    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting && hasMore) {
          loadMoreMembers();
        }
      },
      { threshold: 0.5 }
    );

    if (loadingRef.current) {
      observer.current.observe(loadingRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [loadMoreMembers, isLoading, hasMore]);

  // Fetch members when search query changes
  useEffect(() => {
    setCurrentPage(1);
    setAllMembers([]);
    fetchMembers(1, false);
  }, [fetchMembers]);

  // Get user IDs for bulk fetching (search is now handled server-side)
  const memberIds = useMemo(() => allMembers.map((member) => member.id), [allMembers]);

  // Bulk fetch connection statuses and IQ scores
  const { data: connectionsData } = useBulkConnectionStatuses(memberIds) as {
    data?: { statuses: Record<number, any> };
  };
  const { data: iqScoresData } = useBulkIQScores(memberIds) as {
    data?: { scores: Record<number, any> };
  };

  return (
    <div className="max-w-[1400px] mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Member Directory</h1>
        <p className="text-gray-600 mt-1">
          Connect with tourism industry professionals from around the world. Use the search to find
          members by name, username, job title, company, or location.
        </p>
      </div>

      {/* Search only */}
      <div className="mb-8">
        <MemberSearch searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
      </div>

      {isLoading ? (
        <div className="text-center py-12">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>Loading members...</p>
        </div>
      ) : error ? (
        <div className="text-center py-12 text-red-500">
          <p>Error loading members. Please try again later.</p>
        </div>
      ) : (
        <>
          {/* Members Section */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-medium">Members</h2>
              <p className="text-sm text-muted-foreground">{allMembers.length} members loaded</p>
            </div>
            {allMembers.length > 0 ? (
              <>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                  {allMembers.map((member) => (
                    <MemberCard
                      key={member.id}
                      member={member}
                      featured={
                        member.roles?.includes('founder') ||
                        member.roles?.includes('admin') ||
                        false
                      }
                      connectionStatus={connectionsData?.statuses?.[member.id]}
                      iqScoreData={iqScoresData?.scores?.[member.id]}
                    />
                  ))}
                </div>

                {/* Loading indicator for infinite scroll */}
                {isLoadingMore && (
                  <div className="flex justify-center py-6">
                    <div className="flex items-center gap-3">
                      <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                      <span className="text-[#3d405b] font-medium">Loading more members...</span>
                    </div>
                  </div>
                )}

                {/* End of results indicator */}
                {!hasMore && allMembers.length > 0 && (
                  <div className="text-center py-4 text-gray-500">
                    <p>You've reached the end. {allMembers.length} members loaded.</p>
                  </div>
                )}

                {/* Invisible element for intersection observer */}
                {hasMore && !isLoadingMore && <div ref={loadingRef} className="h-10" />}
              </>
            ) : (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <p>No members found matching your criteria.</p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
