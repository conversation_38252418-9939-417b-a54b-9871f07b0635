import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Filter, MapPin } from 'lucide-react';

interface MemberFilterProps {
  options: string[];
  selected: string;
  onChange: (value: string) => void;
  icon: 'filter' | 'map-pin';
}

export function MemberFilter({ options, selected, onChange, icon }: MemberFilterProps) {
  const Icon = icon === 'filter' ? Filter : MapPin;

  return (
    <div className="w-full sm:w-auto">
      <Select value={selected} onValueChange={onChange}>
        <SelectTrigger className="w-full sm:w-[220px]">
          <div className="flex items-center">
            <Icon className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Select option" />
          </div>
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
