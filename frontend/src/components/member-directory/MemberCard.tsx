/*********************************************
# frontend/src/components/member-directory/MemberCard.tsx
# 02/07/2025 3:45pm Updated cover image background to use gradient placeholder with light blue, dark blue, and cream colors
# 02/07/2025 3:55pm Removed animation and updated to static mesh gradient with cream, lavender, pink, and blue tones
# 02/07/2025 2:30pm Fixed inconsistent card heights by making cards consistent height with buttons stuck to bottom
# 02/07/2025 2:30pm Updated connect button to have 2px light blue border instead of default 1px border
# 02/07/2025 2:35pm Fixed button alignment for three-button variation to match two-button positioning
# 02/07/2025 2:35pm Updated connected button styling to be greyed out with dark blue text and 50% opacity
# 02/07/2025 2:35pm Fixed icon spacing in buttons by reducing margin from mr-2 to mr-1.5
# 02/07/2025 2:45pm Fixed icon spacing by overriding button gap-2 with gap-1 and removing individual icon margins
# 02/07/2025 2:50pm Fixed View Profile button icon spacing in member directory cards
# 02/07/2025 2:40pm Restructured connected state layout to have View Profile and Message buttons side-by-side, eliminating third row
**********************************************/

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import Link from 'next/link';
import { MapPin, Check, UserPlus, UserMinus, MessageSquare } from 'lucide-react';
import { Member } from '@/lib/types';
import { useConnections } from '@/hooks/queries/useConnections';
import { useAuth } from '@/contexts/auth-context';
import React, { useState } from 'react';
import { useMessaging } from '@/components/messaging';
import { messagingApi } from '@/lib/api/messaging';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';

interface MemberCardProps {
  member: Member;
  featured: boolean;
  connectionStatus?: {
    status: 'none' | 'pending' | 'accepted';
    can_request?: boolean;
    pending_type?: 'sent' | 'received';
    message?: string;
    can_accept?: boolean;
    can_decline?: boolean;
    can_remove?: boolean;
  };
  iqScoreData?: {
    score: number;
    rank: string;
    rank_data: {
      name: string;
      min_score: number;
      max_score?: number;
      color: string;
      icon: string;
    };
  };
}

export function MemberCard({ member, connectionStatus, iqScoreData }: MemberCardProps) {
  const { user } = useAuth();
  const { sendConnectionRequest, removeConnection } = useConnections();
  const [isConnecting, setIsConnecting] = useState(false);
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  const [isStartingChat, setIsStartingChat] = useState(false);
  const [localConnectionStatus, setLocalConnectionStatus] = useState(connectionStatus);
  const { openChat } = useMessaging();

  // Use prop data instead of individual API calls
  const status = localConnectionStatus || connectionStatus;

  // Update local status when prop changes
  React.useEffect(() => {
    setLocalConnectionStatus(connectionStatus);
  }, [connectionStatus]);

  // Don't show the current user's own card
  if (user?.id === member.id) {
    return null;
  }

  const handleConnectionAction = async (action: string, targetUserId: number) => {
    setIsConnecting(true);
    let success = false;

    if (action === 'request') {
      // Optimistically update the local state
      setLocalConnectionStatus({
        status: 'pending',
        pending_type: 'sent',
        can_request: false,
        message: 'Request sent',
      });

      success = await sendConnectionRequest(targetUserId);

      if (!success) {
        // Revert on failure
        setLocalConnectionStatus(connectionStatus);
      }
    }

    setIsConnecting(false);
  };

  const handleStartConversation = async () => {
    if (isStartingChat) return;

    setIsStartingChat(true);
    try {
      // Check if user can message this member
      const canMessage = await messagingApi.canMessageUser(member.id);

      if (!canMessage.canMessage) {
        // Show error message or handle the case where messaging is not allowed
        console.error('Cannot message user:', canMessage.reason);
        alert(`Cannot start conversation: ${canMessage.reason}`);
        return;
      }

      // Create a conversation object for the messaging system
      const conversation = {
        id: `conversation-${member.id}`, // Generate a conversation ID
        participants: [
          {
            id: member.id.toString(),
            username: member.username || `user-${member.id}`,
            firstName: member.firstName || null,
            lastName: member.lastName || null,
            avatar: member.profile_picture
              ? {
                  url:
                    typeof member.profile_picture === 'string'
                      ? member.profile_picture
                      : typeof member.profile_picture === 'object' &&
                          member.profile_picture !== null &&
                          'url' in member.profile_picture &&
                          typeof member.profile_picture.url === 'string'
                        ? member.profile_picture.url
                        : '',
                }
              : null,
            isOnline: false, // We don't have real-time status here
          },
        ],
        unreadCount: 0,
      };

      // Open the chat window
      openChat(conversation);
    } catch (error) {
      console.error('Failed to start conversation:', error);
      alert('Failed to start conversation. Please try again.');
    } finally {
      setIsStartingChat(false);
    }
  };

  const getConnectionButton = () => {
    const isLoading = Boolean(isConnecting && status?.status === 'pending');

    if (!status || status.status === 'none') {
      return (
        <Button
          variant="outline"
          className="w-full border-2 border-[#5cc8ff] gap-1"
          onClick={() => handleConnectionAction('request', member.id)}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <UserPlus className="h-4 w-4 animate-spin" />
              Connecting...
            </>
          ) : (
            <>
              <UserPlus className="h-4 w-4" />
              Connect
            </>
          )}
        </Button>
      );
    }

    if (status.status === 'pending') {
      if (status.pending_type === 'sent') {
        return (
          <Button
            variant="outline"
            className="w-full text-[#5cc8ff] border-2 border-[#5cc8ff] bg-white hover:bg-[#5cc8ff]/10 font-extrabold gap-1"
            disabled
          >
            <Check className="h-4 w-4" />
            Request Sent
          </Button>
        );
      } else if (status.pending_type === 'received') {
        // Don't show accept/decline buttons here - they're in the notification menu
        return (
          <Button
            variant="outline"
            className="w-full text-orange-600 border-2 border-orange-200 bg-white font-extrabold gap-1"
            disabled
          >
            <Check className="h-4 w-4" />
            Request Received
          </Button>
        );
      }
    }

    if (status.status === 'accepted') {
      return (
        <Button
          variant="outline"
          className="w-full bg-gray-100 text-[#3d405b] border-2 border-gray-300 opacity-50 font-extrabold cursor-not-allowed gap-1"
          disabled={true}
        >
          <Check className="h-4 w-4 text-[#3d405b]" />
          Connected
        </Button>
      );
    }

    return null;
  };

  const handleDisconnect = async () => {
    setIsConnecting(true);

    // Store the current status in case we need to revert
    const previousStatus = localConnectionStatus;

    // Optimistically update to disconnected state
    setLocalConnectionStatus({
      status: 'none',
      can_request: true,
    });

    const success = await removeConnection(member.id);

    if (!success) {
      // Revert on failure
      setLocalConnectionStatus(previousStatus);
    }

    setIsConnecting(false);
    setShowDisconnectDialog(false);
  };

  return (
    <>
      <Card className="overflow-hidden p-0 h-[500px] flex flex-col">
        <div className="relative">
          {/* Background Image */}
          <div className="w-full h-40 bg-gradient-to-br from-[#fef7cd] via-[#e0e7ff] via-[#fce7f3] to-[#dbeafe] opacity-90">
            {member.coverImage && (
              <img src={member.coverImage} alt="" className="w-full h-full object-cover" />
            )}
          </div>

          {/* Avatar with Rank Badge */}
          <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2">
            <UserAvatarWithRank
              userId={member.id}
              avatarUrl={
                (typeof member.profile_picture === 'string'
                  ? member.profile_picture
                  : typeof member.profile_picture === 'object' &&
                      member.profile_picture !== null &&
                      'url' in member.profile_picture &&
                      typeof member.profile_picture.url === 'string'
                    ? member.profile_picture.url
                    : '') ||
                member.avatar ||
                '/images/avatar-placeholder.svg'
              }
              displayName={member.displayName || member.username || 'U'}
              size="h-[150px] w-[150px]"
              containerSize="w-44 h-44"
              userRoles={member.roles || []}
              {...(iqScoreData && { iqScoreData })}
            />
          </div>
        </div>

        {/* Content - flex-1 to take remaining space */}
        <div className="pt-16 p-6 text-center flex-1 flex flex-col">
          <h3 className="font-semibold text-lg mb-1">@{member.username}</h3>
          <div className="flex flex-col items-center mb-2">
            <p className="text-sm text-muted-foreground">
              {member.displayName ||
                `${member.firstName} ${member.lastName}`.trim() ||
                member.username}
            </p>
            <p className="text-xs text-muted-foreground">{member.role}</p>
          </div>

          {member.location && (
            <div className="flex items-center justify-center text-xs text-muted-foreground mb-4">
              <MapPin className="h-3 w-3 mr-1" />
              <span>{member.location}</span>
            </div>
          )}

          <p className="text-sm mb-6 line-clamp-2 flex-1">{member.bio}</p>

          {/* Buttons stuck to bottom - ensure consistent positioning */}
          <div className="mt-auto">
            {status?.status === 'accepted' ? (
              // Connected state: View Profile and Message side-by-side, Connected below
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <Button asChild variant="default" className="w-full gap-1">
                    <Link href={`/profile/${member.slug || member.username}`}>View Profile</Link>
                  </Button>
                  <Button
                    variant="default"
                    className="w-full gap-1"
                    onClick={handleStartConversation}
                    disabled={isStartingChat}
                  >
                    {isStartingChat ? (
                      <>
                        <MessageSquare className="h-4 w-4 animate-pulse" />
                        Starting...
                      </>
                    ) : (
                      <>
                        <MessageSquare className="h-4 w-4" />
                        Message
                      </>
                    )}
                  </Button>
                </div>
                <Button
                  variant="outline"
                  className="w-full bg-gray-100 text-[#3d405b] border-2 border-gray-300 opacity-50 font-extrabold cursor-not-allowed gap-1"
                  disabled={true}
                >
                  <Check className="h-4 w-4 text-[#3d405b]" />
                  Connected
                </Button>
              </div>
            ) : (
              // Non-connected state: View Profile on top, connection button below
              <div className="space-y-2">
                <Button asChild variant="default" className="w-full gap-1">
                  <Link href={`/profile/${member.slug || member.username}`}>View Profile</Link>
                </Button>
                {getConnectionButton()}
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Disconnect Confirmation Dialog */}
      <Dialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Disconnect from {member.displayName || member.username}?</DialogTitle>
            <DialogDescription>
              Are you sure you want to disconnect from {member.displayName || member.username}? This
              will remove your connection and you&apos;ll need to send a new connection request to
              reconnect.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDisconnectDialog(false)}
              disabled={isConnecting}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDisconnect} disabled={isConnecting}>
              {isConnecting ? (
                <>
                  <UserMinus className="h-4 w-4 mr-2 animate-spin" />
                  Disconnecting...
                </>
              ) : (
                <>
                  <UserMinus className="h-4 w-4 mr-2" />
                  Disconnect
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
