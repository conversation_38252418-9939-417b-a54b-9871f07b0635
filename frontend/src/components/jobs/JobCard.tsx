/*********************************************
# frontend/src/components/jobs/JobCard.tsx
# 02/07/2025 3:30pm Updated NEW status styling to match notification green color (#22c55e) for consistency with list view
# 02/07/2025 3:35pm Improved card layout: added flex-col and h-full to push footer to bottom, changed footer background to light blue (10% opacity) for better visual consistency
# 02/07/2025 4:15pm Added Google search functionality to grid view: entire card clickable with sophisticated hover overlay (light blue border, diagonal gradient, white pill)
# 02/07/2025 4:20pm Fixed hover overlay positioning: added relative positioning to Card component to contain gradient overlay within individual cards
# 02/07/2025 4:25pm Lightened gradient opacity by 10% for more subtle hover effect: reduced from 13-65% to 12-59% opacity range
# 02/07/2025 4:30pm Fixed z-index layering: added z-20 to hover overlay to ensure gradient appears above all tags and content
# 02/07/2025 3:40pm Reduced footer background opacity from 10% to 5% for more subtle appearance
**********************************************/

'use client';

import { MapPin, Building2, Calendar, ExternalLink, Clock } from 'lucide-react';
import { Job, formatJobDeadline, getCategoryLabel, getStatusLabel } from '@/lib/api/jobs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';

interface JobCardProps {
  job: Job;
  showFullDescription?: boolean;
}

export function JobCard({ job, showFullDescription = false }: JobCardProps) {
  const { title, content, excerpt, date, job_meta } = job;

  const {
    job_status = '',
    job_organization = '',
    job_source_url = '',
    job_deadline = '',
  } = job_meta || {};

  // Get first category from job_categories array
  const job_category =
    job.job_categories && job.job_categories.length > 0 ? job.job_categories[0] : '';

  // Get location from taxonomy fields
  const job_state = job.job_states && job.job_states.length > 0 ? job.job_states[0] : '';
  const job_country = job.job_countries && job.job_countries.length > 0 ? job.job_countries[0] : '';

  // Create location string
  const location = [job_state, job_country].filter(Boolean).join(', ');

  // Format posting date
  const postingDate = new Date(date).toLocaleDateString();

  // Get deadline info
  const deadlineInfo = formatJobDeadline(job_deadline);
  const isExpired = job_status === 'EXPIRED' || deadlineInfo === 'Expired';
  const isUrgent = deadlineInfo.includes('day') && !isExpired;

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-[#22c55e] text-white border-[#22c55e]';
      case 'ACTIVE':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'FILLED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get category color
  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      hospitality: 'bg-purple-100 text-purple-800 border-purple-200',
      travel_agent: 'bg-blue-100 text-blue-800 border-blue-200',
      tour_operator: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      marketing: 'bg-pink-100 text-pink-800 border-pink-200',
      sales: 'bg-orange-100 text-orange-800 border-orange-200',
      technology: 'bg-cyan-100 text-cyan-800 border-cyan-200',
      management: 'bg-slate-100 text-slate-800 border-slate-200',
      finance: 'bg-emerald-100 text-emerald-800 border-emerald-200',
    };
    return colors[category] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  // Construct Google search query
  const constructGoogleSearch = () => {
    const searchTerms = [
      title.rendered.replace(/<[^>]*>/g, ''), // Remove HTML tags
      job_organization,
      location,
      'job',
    ]
      .filter(Boolean)
      .join(' ');

    const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(searchTerms)}`;
    window.open(searchUrl, '_blank');
  };

  return (
    <Card
      className={`group relative flex flex-col h-full transition-all duration-200 hover:shadow-md cursor-pointer ${isExpired ? 'opacity-75' : ''}`}
      onClick={constructGoogleSearch}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          constructGoogleSearch();
        }
      }}
      aria-label={`Search Google for job: ${title.rendered.replace(/<[^>]*>/g, '')}`}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <div
              className="text-xl font-semibold text-gray-900 line-clamp-2 block"
              dangerouslySetInnerHTML={{ __html: title.rendered }}
            />

            <div className="flex items-center gap-2 mt-2 text-sm text-gray-600">
              <Building2 className="h-4 w-4 flex-shrink-0" />
              <span className="font-medium">{job_organization}</span>
            </div>

            {location && (
              <div className="flex items-center gap-2 mt-1 text-sm text-gray-500">
                <MapPin className="h-4 w-4 flex-shrink-0" />
                <span>{location}</span>
              </div>
            )}

            {job_category && (
              <div className="mt-2">
                <Badge variant="outline" className={`${getCategoryColor(job_category)} text-sm`}>
                  {getCategoryLabel(job_category)}
                </Badge>
              </div>
            )}
          </div>

          <div className="flex flex-col gap-2 items-end">
            {job_status && (
              <Badge className={`${getStatusColor(job_status)} text-sm`}>
                {getStatusLabel(job_status)}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      {/* Hover overlay for Google search */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg pointer-events-none z-20">
        {/* Light blue border */}
        <div className="absolute inset-0 border-2 border-[#5cc8ff] rounded-lg"></div>

        {/* Diagonal gradient overlay */}
        <div
          className="absolute inset-0 rounded-lg"
          style={{
            background:
              'linear-gradient(135deg, transparent 0%, rgba(92, 200, 255, 0.12) 20%, rgba(92, 200, 255, 0.23) 40%, rgba(92, 200, 255, 0.35) 60%, rgba(92, 200, 255, 0.47) 80%, rgba(92, 200, 255, 0.59) 100%)',
          }}
        ></div>

        {/* White pill in bottom right */}
        <div className="absolute bottom-3 right-3 pointer-events-auto">
          <div className="bg-white rounded-full px-3 py-1.5 flex items-center gap-1.5">
            <svg className="w-4 h-4 text-[#5cc8ff]" fill="currentColor" viewBox="0 0 24 24">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
            </svg>
            <span className="text-xs text-gray-700 font-medium">Search for this job on Google</span>
          </div>
        </div>
      </div>

      <CardContent className="py-3 flex-1">
        <div
          className={`text-sm text-gray-700 ${!showFullDescription ? 'line-clamp-3' : ''}`}
          dangerouslySetInnerHTML={{
            __html: showFullDescription ? content.rendered : excerpt.rendered,
          }}
        />
      </CardContent>

      <CardFooter className="pt-3 border-t" style={{ backgroundColor: 'rgba(92, 200, 255, 0.05)' }}>
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-4 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Posted {postingDate}</span>
            </div>

            {job_deadline && (
              <div
                className={`flex items-center gap-1 ${isUrgent ? 'text-orange-600 font-medium' : ''}`}
              >
                <Clock className="h-3 w-3" />
                <span>{deadlineInfo}</span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            {job_source_url && !isExpired && (
              <Button size="sm" asChild>
                <a
                  href={job_source_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1"
                >
                  Apply Now
                  <ExternalLink className="h-3 w-3" />
                </a>
              </Button>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
