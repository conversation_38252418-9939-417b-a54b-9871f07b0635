/*********************************************
# frontend/src/components/jobs/JobCompactCard.tsx
# 02/07/2025 2:55pm Updated NEW status styling to match notification green color (#22c55e)
# 02/07/2025 2:55pm Added NEW tag positioning in top right corner of list view cards
# 02/07/2025 2:55pm Reduced title text container width by 30% for better layout balance
# 02/07/2025 3:00pm Removed duplicate NEW status badge from bottom left when NEW tag is shown in top right
# 02/07/2025 3:05pm Improved layout spacing: increased card padding, reduced title width to 55%, added better vertical spacing
# 02/07/2025 3:10pm Restructured layout to 2-column grid: organization/location on left, posted/deadline dates on right, eliminating text overlap with NEW tag
# 02/07/2025 3:15pm Reduced title container width by 30% (from 55% to 38.5%) to prevent title text from crashing into NEW tag
# 02/07/2025 3:20pm Fixed CSS override issue: removed flex-1 class and added maxWidth constraint to enforce title width limit
# 02/07/2025 3:25pm Adjusted title width to 66.67% (2/3 of container) for better text display and reduced excessive wrapping
# 02/07/2025 3:45pm Increased NEW tag size by 20% and implemented horizontal tag stack: moved all tags (NEW, status, category) to top right corner with consistent sizing and spacing
# 02/07/2025 3:50pm Reordered tags: NEW tag is now always the rightmost, with category tag positioned to its left for better visual hierarchy
# 02/07/2025 3:55pm Added Google search functionality: entire card is now clickable with hover overlay revealing "Search Google for this job" with smooth animations
# 02/07/2025 4:00pm Refined hover UI: replaced overlay with subtle light blue border, diagonal gradient, and white pill in bottom right corner for more sophisticated appearance
# 02/07/2025 4:10pm Enhanced gradient: increased opacity by 30% and extended coverage across entire card for more prominent hover effect
# 02/07/2025 4:25pm Lightened gradient opacity by 10% for more subtle hover effect: reduced from 13-65% to 12-59% opacity range
# 02/07/2025 4:30pm Fixed z-index layering: added z-20 to hover overlay to ensure gradient appears above all tags and content
**********************************************/

'use client';

import { MapPin, Building2, Calendar, ExternalLink, Clock } from 'lucide-react';
import { Job, formatJobDeadline, getCategoryLabel, getStatusLabel } from '@/lib/api/jobs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface JobCompactCardProps {
  job: Job;
}

export function JobCompactCard({ job }: JobCompactCardProps) {
  const { title, date, job_meta } = job;

  const {
    job_status = '',
    job_organization = '',
    job_source_url = '',
    job_deadline = '',
  } = job_meta || {};

  // Get first category from job_categories array
  const job_category =
    job.job_categories && job.job_categories.length > 0 ? job.job_categories[0] : '';

  // Get location from taxonomy fields
  const job_state = job.job_states && job.job_states.length > 0 ? job.job_states[0] : '';
  const job_country = job.job_countries && job.job_countries.length > 0 ? job.job_countries[0] : '';

  // Create location string
  const location = [job_state, job_country].filter(Boolean).join(', ');

  // Format posting date
  const postingDate = new Date(date).toLocaleDateString();

  // Get deadline info
  const deadlineInfo = formatJobDeadline(job_deadline);
  const isExpired = job_status === 'EXPIRED';
  const isUrgent = deadlineInfo.includes('day');

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-[#22c55e] text-white border-[#22c55e]';
      case 'ACTIVE':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'FILLED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get category color
  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      hospitality: 'bg-purple-100 text-purple-800 border-purple-200',
      travel_agent: 'bg-blue-100 text-blue-800 border-blue-200',
      tour_operator: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      marketing: 'bg-pink-100 text-pink-800 border-pink-200',
      sales: 'bg-orange-100 text-orange-800 border-orange-200',
      technology: 'bg-cyan-100 text-cyan-800 border-cyan-200',
      management: 'bg-slate-100 text-slate-800 border-slate-200',
      finance: 'bg-teal-100 text-teal-800 border-teal-200',
    };
    return colors[category] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  // Construct Google search query
  const constructGoogleSearch = () => {
    const searchTerms = [
      title.rendered.replace(/<[^>]*>/g, ''), // Remove HTML tags
      job_organization,
      location,
      'job',
    ]
      .filter(Boolean)
      .join(' ');

    const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(searchTerms)}`;
    window.open(searchUrl, '_blank');
  };

  return (
    <div
      className={`group relative flex flex-col w-full p-4 bg-white border rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer ${
        isExpired ? 'opacity-75' : ''
      }`}
      onClick={constructGoogleSearch}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          constructGoogleSearch();
        }
      }}
      aria-label={`Search Google for job: ${title.rendered.replace(/<[^>]*>/g, '')}`}
    >
      {/* Tags in top right corner */}
      <div className="absolute top-3 right-3 z-10 flex gap-1">
        {job_category && (
          <Badge
            variant="outline"
            className={`${getCategoryColor(job_category)} text-sm py-0.5 px-2 h-5`}
          >
            {getCategoryLabel(job_category)}
          </Badge>
        )}
        {job_status && job_status !== 'NEW' && (
          <Badge
            className={`${getStatusColor(job_status)} text-sm py-0.5 px-2 h-5`}
            variant="outline"
          >
            {getStatusLabel(job_status)}
          </Badge>
        )}
        {job_status === 'NEW' && (
          <Badge className="bg-[#22c55e] text-white text-sm py-0.5 px-2 h-5 border-[#22c55e]">
            New
          </Badge>
        )}
      </div>

      {/* Hover overlay for Google search */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg pointer-events-none z-20">
        {/* Light blue border */}
        <div className="absolute inset-0 border-2 border-[#5cc8ff] rounded-lg"></div>

        {/* Diagonal gradient overlay */}
        <div
          className="absolute inset-0 rounded-lg"
          style={{
            background:
              'linear-gradient(135deg, transparent 0%, rgba(92, 200, 255, 0.12) 20%, rgba(92, 200, 255, 0.23) 40%, rgba(92, 200, 255, 0.35) 60%, rgba(92, 200, 255, 0.47) 80%, rgba(92, 200, 255, 0.59) 100%)',
          }}
        ></div>

        {/* White pill in bottom right */}
        <div className="absolute bottom-3 right-3 pointer-events-auto">
          <div className="bg-white rounded-full px-3 py-1.5 flex items-center gap-1.5">
            <svg className="w-4 h-4 text-[#5cc8ff]" fill="currentColor" viewBox="0 0 24 24">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
            </svg>
            <span className="text-xs text-gray-700 font-medium">Search for this job on Google</span>
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="flex items-start gap-4 flex-1 min-w-0">
        {/* Title */}
        <div className="min-w-0 pl-1 flex-shrink-0" style={{ width: '66.67%', maxWidth: '66.67%' }}>
          <div
            className="text-base font-semibold text-gray-900 break-words mb-3"
            dangerouslySetInnerHTML={{ __html: title.rendered }}
          />
        </div>
      </div>

      {/* Bottom metadata grid */}
      <div className="flex items-start gap-4 flex-1 min-w-0 pl-1">
        {/* Left column - Organization and Location */}
        <div className="flex flex-col gap-2 text-xs text-gray-500 flex-1">
          {job_organization && (
            <div className="flex items-center gap-1">
              <Building2 className="h-3 w-3 flex-shrink-0" />
              <span className="break-words">{job_organization}</span>
            </div>
          )}
          {location && (
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3 flex-shrink-0" />
              <span className="break-words">{location}</span>
            </div>
          )}
        </div>

        {/* Right column - Deadline */}
        <div className="flex flex-col gap-2 text-xs text-gray-500 flex-shrink-0 pr-1">
          {job_deadline && deadlineInfo && (
            <div
              className={`flex items-center gap-1 ${isUrgent ? 'text-orange-600 font-medium' : ''}`}
            >
              <Clock className="h-3 w-3 flex-shrink-0" />
              <span className="whitespace-nowrap">Deadline: {deadlineInfo}</span>
            </div>
          )}
        </div>
      </div>

      {/* Action button */}
      {job_source_url && !isExpired && (
        <div className="flex justify-end mt-3">
          <Button size="sm" asChild>
            <a
              href={job_source_url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1"
            >
              Apply Now
              <ExternalLink className="h-3 w-3" />
            </a>
          </Button>
        </div>
      )}
    </div>
  );
}
