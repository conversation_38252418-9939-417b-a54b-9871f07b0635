'use client';

import { useState, useEffect } from 'react';
import { QuestionDetail, Comment } from '@/types/forum';
import { addComment, getQuestionComments, transformWPCommentToComment } from '@/lib/api/forum';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { MessageCircle, Clock, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';

// Safe date parsing function
function safeFormatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'some time ago';
    }
    return formatDistanceToNow(date, { addSuffix: true });
  } catch {
    return 'some time ago';
  }
}

interface QuestionDetailProps {
  question: QuestionDetail;
}

export default function QuestionDetailComponent({ question }: QuestionDetailProps) {
  const [mounted, setMounted] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Track when component is mounted to avoid hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  const loadComments = async () => {
    setLoading(true);
    try {
      const fetchedComments = await getQuestionComments(question.id);
      const transformedComments = fetchedComments.map(transformWPCommentToComment);
      setComments(transformedComments);
    } catch (err) {
      console.error('Error loading comments:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load comments on mount - use question.comments if available, otherwise fetch
  useEffect(() => {
    if (question.comments && question.comments.length > 0) {
      setComments(question.comments);
    } else {
      loadComments();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [question.id, question.comments]);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newComment.trim()) {
      setError('Please enter a response');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const result = await addComment(question.id, newComment.trim());
      if (result) {
        // Transform the comment data to ensure proper format
        const transformedComment = transformWPCommentToComment(result);

        // Add the new comment to the list
        setComments((prev) => [...prev, transformedComment]);
        setNewComment('');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add response';
      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Question Header */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          {question.isResolved && <CheckCircle className="h-6 w-6 text-green-500" />}
          <h1 className="text-2xl font-bold text-dark-text">{question.title}</h1>
        </div>

        <div className="flex items-center gap-4 text-sm text-gray-500 mb-6">
          {mounted ? (
            <>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>Asked {safeFormatDate(question.createdAt)}</span>
              </div>

              {question.author && (
                <div className="flex items-center gap-2">
                  <UserAvatarWithRank
                    userId={
                      typeof question.author.id === 'string'
                        ? parseInt(question.author.id)
                        : question.author.id
                    }
                    avatarUrl={question.author.avatar || '/images/avatar-placeholder.svg'}
                    displayName={question.author.name}
                    size="h-6 w-6"
                    containerSize="w-8 h-8"
                    userRoles={question.author.roles || []}
                    showRankBadge={true}
                  />
                  <span>by {question.author.name}</span>
                </div>
              )}

              <div className="flex items-center gap-1">
                <MessageCircle className="h-4 w-4" />
                <span>{comments.length} responses</span>
              </div>
            </>
          ) : (
            // Placeholder during hydration
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>Asked some time ago</span>
              </div>
              {question.author && <span className="text-gray-500">by {question.author.name}</span>}
              <div className="flex items-center gap-1">
                <MessageCircle className="h-4 w-4" />
                <span>{comments.length} responses</span>
              </div>
            </div>
          )}
        </div>

        {/* Question Content */}
        <div
          className="prose prose-lg max-w-none text-gray-700 prose-p:my-3 prose-p:leading-relaxed [&>p]:mb-4 [&>p]:leading-relaxed"
          dangerouslySetInnerHTML={{
            __html: formatCommentContent(question.content),
          }}
        />
      </div>

      {/* Responses Section */}
      <div className="border-t border-gray-200 pt-8">
        <h2 className="text-xl font-semibold text-dark-text mb-6">
          {comments.length} Response{comments.length !== 1 ? 's' : ''}
        </h2>

        {/* Comments List */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">Loading responses...</span>
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-8">
            <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No responses yet. Be the first to help!</p>
          </div>
        ) : (
          <div className="space-y-6">
            {comments.map((comment) => (
              <CommentCard key={comment.id} comment={comment} />
            ))}
          </div>
        )}

        {/* Add Response Form */}
        <div className="mt-8 border-t border-gray-200 pt-8">
          <h3 className="text-lg font-medium text-dark-text mb-4">Your Response</h3>

          <form onSubmit={handleSubmitComment} className="space-y-4">
            <Textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Share your knowledge and help answer this question..."
              rows={6}
              className="w-full"
              disabled={submitting}
            />

            {error && (
              <div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <span className="text-red-700">{error}</span>
              </div>
            )}

            <div className="flex items-center gap-4">
              <Button
                type="submit"
                disabled={submitting || !newComment.trim()}
                className="flex items-center gap-2"
              >
                {submitting && <Loader2 className="h-4 w-4 animate-spin" />}
                {submitting ? 'Posting...' : 'Post Response'}
              </Button>

              <p className="text-sm text-gray-500">
                You&apos;ll earn 1 IQ point for helping the community!
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

// Helper function to format plain text comments into HTML
function formatCommentContent(content: string): string {
  // First, check if the content already contains HTML tags
  if (content.includes('<p>') || content.includes('<br')) {
    return content;
  }

  // Convert plain text to HTML with proper paragraph formatting
  const paragraphs = content
    .split(/\n\n+/) // Split by double line breaks for paragraphs
    .filter((p) => p.trim()) // Remove empty paragraphs
    .map((p) => {
      // Convert single line breaks within paragraphs to <br>
      const withBreaks = p.trim().replace(/\n/g, '<br />');
      return `<p>${withBreaks}</p>`;
    });

  return paragraphs.join('');
}

// Comment Card Component
function CommentCard({ comment }: { comment: Comment }) {
  // Format the comment content
  const formattedContent = formatCommentContent(comment.content);

  return (
    <div className="bg-gray-50 rounded-lg p-6">
      <div className="flex items-start gap-4">
        {comment.author && (
          <div className="flex-shrink-0">
            <UserAvatarWithRank
              userId={
                typeof comment.author.id === 'string'
                  ? parseInt(comment.author.id)
                  : comment.author.id
              }
              avatarUrl={comment.author.avatar || '/images/avatar-placeholder.svg'}
              displayName={comment.author.name}
              size="h-10 w-10"
              containerSize="w-12 h-12"
              userRoles={comment.author.roles || []}
            />
          </div>
        )}

        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            {comment.author && (
              <span className="font-medium text-dark-text">{comment.author.name}</span>
            )}
            <span className="text-sm text-gray-500">{safeFormatDate(comment.createdAt)}</span>
          </div>

          <div
            className="prose prose-sm max-w-none text-gray-700 prose-p:my-2 prose-p:leading-relaxed [&>p]:mb-3 [&>p]:leading-relaxed"
            dangerouslySetInnerHTML={{ __html: formattedContent }}
          />
        </div>
      </div>
    </div>
  );
}
