/*********************************************
# frontend/src/components/forum/forum-layout.tsx
# 02/06/2025 11:30am Fixed header alignment by removing centering wrapper to align with content below
# 02/06/2025 11:35am Added light blue question mark icon and made button half container width
# 02/06/2025 11:50am Removed extra left padding (pl-[30px]) to match content width of other pages
**********************************************/

'use client';

import { ReactNode } from 'react';
import Link from 'next/link';
import { HelpCircle } from 'lucide-react';

interface ForumLayoutProps {
  children: ReactNode;
}

export default function ForumLayout({ children }: ForumLayoutProps) {
  return (
    <div className="min-h-screen bg-[#eff1f4]">
      <div className="max-w-[1360px] mx-auto py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-dark-text">Community Q&A</h1>
          <p className="mt-2 text-gray-600">
            Ask questions and share knowledge with the tourism community
          </p>
          <Link href="/forum/new" className="block mt-6 group w-1/2">
            <div
              className="w-full bg-white border-2 border-gray-200 rounded-full shadow-sm hover:shadow-md transition-all p-0 cursor-pointer hover:border-[#5cc8ff]"
              tabIndex={0}
              role="button"
              aria-label="Ask a question"
            >
              <div className="px-6 py-4 text-base font-bold text-[#3d405b] text-left flex items-center">
                <HelpCircle className="w-5 h-5 text-[#5cc8ff] mr-3" />
                Ask a question
              </div>
            </div>
          </Link>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">{children}</div>
      </div>
    </div>
  );
}
