'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { createQuestion } from '@/lib/api/forum';
import { AlertCircle, Loader2 } from 'lucide-react';

export default function NewQuestionForm() {
  const router = useRouter();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim() || !content.trim()) {
      setError('Please fill in both title and content');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const question = await createQuestion({
        title: title.trim(),
        content: content.trim(),
      });

      if (question) {
        router.push(`/forum/${question.id}`);
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create question';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            Question Title
          </label>
          <Input
            id="title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="What's your question about?"
            className="w-full"
            disabled={loading}
            maxLength={200}
          />
          <p className="mt-1 text-sm text-gray-500">
            Be specific and clear. This will help others understand your question.
          </p>
        </div>

        {/* Content */}
        <div>
          <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
            Question Details
          </label>
          <Textarea
            id="content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Provide more details about your question. Include any relevant context, what you've tried, or specific information that would help others provide better answers."
            rows={12}
            className="w-full"
            disabled={loading}
          />
          <p className="mt-1 text-sm text-gray-500">
            The more details you provide, the better answers you&apos;ll receive.
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <span className="text-red-700">{error}</span>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center gap-4 pt-6 border-t border-gray-200">
          <Button
            type="submit"
            disabled={loading || !title.trim() || !content.trim()}
            className="flex items-center gap-2"
          >
            {loading && <Loader2 className="h-4 w-4 animate-spin" />}
            {loading ? 'Publishing...' : 'Publish Question'}
          </Button>

          <Button type="button" variant="outline" onClick={() => router.back()} disabled={loading}>
            Cancel
          </Button>
        </div>

        {/* Tips */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Tips for asking great questions:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Be specific and clear in your title</li>
            <li>• Provide context and background information</li>
            <li>• Mention what you&apos;ve already tried</li>
            <li>• Ask one question at a time</li>
            <li>• Use proper grammar and formatting</li>
          </ul>
        </div>
      </form>
    </div>
  );
}
