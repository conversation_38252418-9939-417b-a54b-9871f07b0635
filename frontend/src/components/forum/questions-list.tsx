/*********************************************
# frontend/src/components/forum/questions-list.tsx
# 02/06/2025 11:40am Applied Jobs Hub search bar styling (rounded-full, h-12, white background)
**********************************************/

'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageCircle } from 'lucide-react';
import { getQuestions, transformWPPostToQuestion } from '@/lib/api/forum';
import { Question } from '@/types/forum';

interface QuestionsListProps {
  initialQuestions?: Question[];
  totalPages?: number;
  currentPage?: number;
  currentSort?: 'new' | 'trending';
}

export default function QuestionsList({
  initialQuestions = [],
  totalPages = 1,
  currentPage = 1,
  currentSort = 'new',
}: QuestionsListProps) {
  const [mounted, setMounted] = useState(false);
  const [questions, setQuestions] = useState<Question[]>(initialQuestions);
  const [loading, setLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [page, setPage] = useState(currentPage);
  const [sort, setSort] = useState(currentSort);
  const [hasMore, setHasMore] = useState(page < totalPages);
  const [searchQuery, setSearchQuery] = useState('');
  const observer = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Update questions when initialQuestions prop changes
  useEffect(() => {
    if (initialQuestions.length > 0) {
      setQuestions(initialQuestions);
      setHasMore(currentPage < totalPages);
    }
  }, [initialQuestions, currentPage, totalPages]);

  // Load more questions for infinite scroll
  const loadMore = useCallback(async () => {
    if (isLoadingMore || !hasMore) return;

    setIsLoadingMore(true);
    try {
      const response = await getQuestions({
        page: page + 1,
        sort,
        search: searchQuery,
      });

      const transformedQuestions = response.questions.map(transformWPPostToQuestion);
      setQuestions((prev) => [...prev, ...transformedQuestions]);
      setPage((prev) => prev + 1);
      setHasMore(page + 1 < response.totalPages);
    } catch (error) {
      console.error('Error loading more questions:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, hasMore, page, sort, searchQuery]);

  // Setup intersection observer for infinite scroll
  useEffect(() => {
    if (isLoadingMore) return () => {};

    if (observer.current) {
      observer.current.disconnect();
    }

    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting && hasMore) {
          loadMore();
        }
      },
      { threshold: 0.5 }
    );

    if (loadingRef.current) {
      observer.current.observe(loadingRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [loadMore, isLoadingMore, hasMore]);

  // Handle sort change
  const handleSortChange = async (newSort: 'new' | 'trending') => {
    if (newSort === sort) return;

    setLoading(true);
    setSort(newSort);
    setPage(1);

    try {
      const response = await getQuestions({
        page: 1,
        sort: newSort,
        search: searchQuery,
      });

      const transformedQuestions = response.questions.map(transformWPPostToQuestion);
      setQuestions(transformedQuestions);
      setHasMore(1 < response.totalPages);
    } catch (error) {
      console.error('Error changing sort:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    setLoading(true);
    setPage(1);

    try {
      const response = await getQuestions({
        page: 1,
        sort,
        search: query,
      });

      const transformedQuestions = response.questions.map(transformWPPostToQuestion);
      setQuestions(transformedQuestions);
      setHasMore(1 < response.totalPages);
    } catch (error) {
      console.error('Error searching questions:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Search and Sort Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search questions..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full px-6 py-3 border border-gray-200 rounded-full h-12 bg-white focus:ring-2 focus:ring-[#5cc8ff] focus:border-[#5cc8ff] focus:outline-none text-base"
          />
        </div>
        <div className="flex bg-white border border-gray-200 rounded-full p-1 h-12">
          <button
            onClick={() => handleSortChange('new')}
            className={`flex-1 px-6 rounded-full text-sm transition-all duration-200 flex items-center justify-center ${
              sort === 'new'
                ? 'bg-[#5cc8ff] text-[#3d405b] !font-extrabold'
                : 'text-gray-600 hover:bg-[#5cc8ff]/10 font-medium'
            }`}
          >
            Recent
          </button>
          <button
            onClick={() => handleSortChange('trending')}
            className={`flex-1 px-6 rounded-full text-sm transition-all duration-200 flex items-center justify-center ${
              sort === 'trending'
                ? 'bg-[#5cc8ff] text-[#3d405b] !font-extrabold'
                : 'text-gray-600 hover:bg-[#5cc8ff]/10 font-medium'
            }`}
          >
            Trending
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {!mounted ? (
          // Show loading state during hydration
          <div className="text-center py-12">
            <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Loading questions...</p>
          </div>
        ) : questions.length === 0 && !loading ? (
          <div className="text-center py-12">
            <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-dark-text mb-2">
              {searchQuery ? 'No results found' : 'No questions yet'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery ? 'Try adjusting your search terms' : 'Be the first to ask a question!'}
            </p>
            {!searchQuery && (
              <Link href="/forum/new">
                <Button className="bg-[#5cc8ff] hover:bg-[#5cc8ff]/80 text-white font-bold">
                  Ask Question
                </Button>
              </Link>
            )}
          </div>
        ) : (
          questions.map((question) => (
            <QuestionCard key={question.id} question={question} mounted={mounted} />
          ))
        )}
      </div>

      {/* Loading indicator for infinite scroll */}
      {isLoadingMore && (
        <div ref={loadingRef} className="flex justify-center mt-8 py-6">
          <div className="flex items-center gap-3">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
            <span className="text-[#3d405b] font-medium">Loading more questions...</span>
          </div>
        </div>
      )}

      {/* End of results indicator */}
      {!hasMore && questions.length > 0 && (
        <div className="text-center mt-8 py-4">
          <p className="text-sm text-muted-foreground">
            {searchQuery ? 'End of search results' : "You've reached the end of the questions"}
          </p>
        </div>
      )}
    </div>
  );
}

function QuestionCard({ question, mounted }: { question: Question; mounted: boolean }) {
  return (
    <Link href={`/forum/${question.id}`} className="block">
      <Card className="hover:shadow-md hover:border-[#5cc8ff] transition-all duration-200 group cursor-pointer">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              {question.isResolved && (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Resolved
                </Badge>
              )}
              <span className="text-sm text-gray-500">
                {mounted && new Date(question.createdAt).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <MessageCircle className="h-4 w-4" />
              {question.commentCount}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-[#5cc8ff] transition-colors mb-2">
              {question.title}
            </h3>
            <div
              className="text-gray-600 line-clamp-3"
              dangerouslySetInnerHTML={{ __html: question.content }}
            />
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
