'use client';

import { useEffect } from 'react';
import { cacheInvalidationManager } from '@/lib/cache-invalidation';

export function CacheInvalidationProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Start polling for cache invalidations
    cacheInvalidationManager.startPolling(5000); // Poll every 5 seconds

    // Cleanup on unmount
    return () => {
      cacheInvalidationManager.stopPolling();
    };
  }, []);

  return <>{children}</>;
}
