'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';

interface SubscriptionStatus {
  is_paid: boolean;
  subscription_status: string;
  subscription_id: string;
  customer_id: string;
}

interface VendorSubscriptionProps {
  vendorId: number;
  paymentLinkUrl: string; // Stripe Payment Link URL
  className?: string;
}

export default function VendorSubscription({
  vendorId,
  paymentLinkUrl,
  className = '',
}: VendorSubscriptionProps) {
  const { user } = useAuth();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [portalLoading, setPortalLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch current subscription status
  useEffect(() => {
    if (!user || !vendorId) return;

    const fetchSubscriptionStatus = async () => {
      try {
        const response = await fetch(`/api/wp-proxy/vendor-subscription/${vendorId}`, {
          credentials: 'include',
        });

        if (response.ok) {
          const data = await response.json();
          setSubscriptionStatus(data);
        } else {
          setError('Failed to load subscription status');
        }
      } catch (err) {
        setError('Failed to load subscription status');
        console.error('Subscription status error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptionStatus();
  }, [user, vendorId]);

  // Handle opening customer portal
  const handleManageSubscription = async () => {
    if (!subscriptionStatus?.customer_id) return;

    setPortalLoading(true);
    try {
      const response = await fetch(
        `/api/wp-proxy/vendor-subscription/${vendorId}/customer-portal`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.portal_url) {
          window.open(data.portal_url, '_blank');
        }
      } else {
        setError('Failed to open customer portal');
      }
    } catch (err) {
      setError('Failed to open customer portal');
      console.error('Portal error:', err);
    } finally {
      setPortalLoading(false);
    }
  };

  // Handle opening payment link
  const handleSubscribe = () => {
    // Add vendor ID to payment link metadata
    const url = new URL(paymentLinkUrl);
    url.searchParams.set('client_reference_id', vendorId.toString());
    window.open(url.toString(), '_blank');
  };

  if (!user) {
    return (
      <div className={`p-4 bg-gray-50 rounded-lg ${className}`}>
        <p className="text-gray-600">Please log in to manage your vendor subscription.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className={`p-4 bg-gray-50 rounded-lg animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 bg-red-50 border border-red-200 rounded-lg ${className}`}>
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  const isActive =
    subscriptionStatus?.is_paid &&
    ['active', 'trialing'].includes(subscriptionStatus.subscription_status);
  const isPastDue = subscriptionStatus?.subscription_status === 'past_due';
  const isCancelled = subscriptionStatus?.subscription_status === 'cancelled';

  return (
    <div className={`p-6 bg-white border rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Vendor Premium Subscription</h3>
        <div className="flex items-center space-x-2">
          <span
            className={`px-2 py-1 text-xs font-medium rounded-full ${
              isActive
                ? 'bg-green-100 text-green-800'
                : isPastDue
                  ? 'bg-yellow-100 text-yellow-800'
                  : isCancelled
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-800'
            }`}
          >
            {isActive ? 'Active' : isPastDue ? 'Past Due' : isCancelled ? 'Cancelled' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Subscription Info */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">Price:</span>
            <span className="ml-2 text-gray-900">$99/month</span>
          </div>
          {subscriptionStatus?.subscription_status && (
            <div>
              <span className="font-medium text-gray-700">Status:</span>
              <span className="ml-2 text-gray-900 capitalize">
                {subscriptionStatus.subscription_status.replace('_', ' ')}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Features List */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Premium Features:</h4>
        <ul className="space-y-2 text-sm text-gray-600">
          <li className="flex items-center">
            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
            Social media links on profile
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
            Resource uploads and downloads
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
            Lead generation forms
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
            Priority in search results
          </li>
          <li className="flex items-center">
            <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
            Enhanced profile features
          </li>
        </ul>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3">
        {!isActive ? (
          <button
            onClick={handleSubscribe}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
          >
            {isCancelled ? 'Resubscribe' : 'Subscribe Now'} - $99/month
          </button>
        ) : (
          <div className="flex-1 bg-green-50 border border-green-200 px-4 py-2 rounded-md">
            <p className="text-green-800 font-medium">✓ Premium Active</p>
          </div>
        )}

        {subscriptionStatus?.customer_id && (
          <button
            onClick={handleManageSubscription}
            disabled={portalLoading}
            className="flex-1 sm:flex-none bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors font-medium disabled:opacity-50"
          >
            {portalLoading ? 'Loading...' : 'Manage Subscription'}
          </button>
        )}
      </div>

      {/* Past Due Warning */}
      {isPastDue && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-yellow-800 text-sm">
            <strong>Payment Required:</strong> Your subscription payment is past due. Please update
            your payment method to continue enjoying premium features.
          </p>
        </div>
      )}

      {/* Cancelled Notice */}
      {isCancelled && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-800 text-sm">
            Your subscription has been cancelled. Premium features are no longer available. You can
            resubscribe at any time to restore access.
          </p>
        </div>
      )}
    </div>
  );
}
