'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { Loader2, AlertCircle, Check } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Post } from '@/lib/types';
import { EditPostAcfFields } from '@/components/post-edit/EditPostAcfFields';
import { FileUploadWithPreview } from '@/components/ui/file-upload-with-preview';

interface EditPostDialogProps {
  post: Post | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

// Helper function to validate ACF fields based on category
const validateAcfFields = (
  categorySlug: string | undefined,
  fields: Record<string, any>,
  imageFile: File | null
): string | null => {
  if (!categorySlug) return null;

  switch (categorySlug) {
    case 'news':
      if (!fields.url?.trim()) return 'External Article URL is required for news posts';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'blog-post':
      if (!fields.url?.trim()) return 'External URL is required for blog posts';
      if (!fields.author?.trim()) return 'Author is required for blog posts';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'event':
      if (!fields.event_date) return 'Event Start Date is required';
      if (!fields.event_end_date) return 'Event End Date is required';
      if (!fields.host_company_organization?.trim()) return 'Host Company/Organization is required';
      if (!fields.additional_details_link_url?.trim()) return 'Additional Details Link is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'webinar':
      if (!fields.webinar_type) return 'Webinar Type is required';
      if (fields.webinar_type === 'live' && !fields.date)
        return 'Date is required for live webinars';
      if (!fields.webinar_host_company_organization?.trim())
        return 'Host Company/Organization is required';
      if (fields.webinar_type === 'live' && !fields.register_url?.trim())
        return 'Registration URL is required for live webinars';
      if (
        fields.webinar_type === 'recorded' &&
        !fields.youtube_url?.trim() &&
        !fields.vimeo_url?.trim()
      )
        return 'YouTube or Vimeo URL is required for recorded webinars';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'video':
      if (!fields.video_source?.trim()) return 'Video Source is required';
      if (fields.video_source === 'youtube' && !fields.youtube_id?.trim())
        return 'YouTube URL is required';
      if (fields.video_source === 'vimeo' && !fields.vimeo_id?.trim())
        return 'Vimeo URL is required';
      if (fields.video_source === 'twitter' && !fields.x?.trim())
        return 'X (Twitter) Embed is required';
      if (fields.video_source === 'linkedin' && !fields.linkedin_iframe?.trim())
        return 'LinkedIn Embed is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'course':
      if (!fields.course_url?.trim()) return 'Course Details URL is required';
      if (!fields.company?.trim()) return 'Course Provider is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'presentation':
      if (!fields.host_company_organization?.trim()) return 'Company/Organization is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'whitepaper':
      if (!fields.author?.trim()) return 'Author is required for whitepapers';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'case-study':
      if (!fields.author?.trim()) return 'Author is required for case studies';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'book':
      if (!fields.book_title?.trim()) return 'Book Title is required';
      if (!fields.author_names?.trim()) return 'Author Names are required';
      if (!fields.purchase_url?.trim()) return 'Purchase URL is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'podcast':
      if (!fields.podcast_episode_title?.trim()) return 'Episode Title is required';
      if (!fields.podcast_name?.trim()) return 'Podcast Name is required';
      if (!fields.episode_url?.trim()) return 'Episode URL is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'press-release':
      if (!fields.press_release_url?.trim()) return 'Press Release URL is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'template':
      if (!fields.creator_name?.trim()) return 'Creator Name is required';
      if (!fields.url?.trim()) return 'Template URL is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'thought-leadership':
      if (!fields.media_type?.trim()) return 'Media Type is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    default:
      return null;
  }
  return null;
};

export function EditPostDialog({ post, open, onOpenChange, onSuccess }: EditPostDialogProps) {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<{
    id: number;
    name: string;
    slug: string;
  } | null>(null);
  const [acfFields, setAcfFields] = useState<Record<string, any>>({});
  const [existingFiles, setExistingFiles] = useState<
    Record<string, { url: string; filename?: string }>
  >({});
  const [fileChanges, setFileChanges] = useState<
    Record<string, { file: File | null; keepExisting: boolean }>
  >({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Helper function to strip HTML tags for plain text
  const stripHtml = (html: string) => {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  };

  // Initialize form with post data when dialog opens
  useEffect(() => {
    if (post && open) {
      // Pre-populate title (strip HTML if any)
      setTitle(stripHtml(post.title?.rendered || ''));

      // Pre-populate content
      setContent(post.content?.rendered || '');

      setError('');
      setSuccess(false);

      // Get post categories (read-only, not editable)
      const postCategories = post._embedded?.['wp:term']?.[0] || [];
      if (postCategories.length > 0) {
        const category = postCategories[0] as any;
        setSelectedCategory({
          id: category.id,
          name: category.name,
          slug: category.slug || category.name.toLowerCase().replace(/\s+/g, '-'),
        });
      }

      // Pre-populate all ACF fields
      if ((post as any).acf) {
        const acf = (post as any).acf;

        // Clean up iframe content from oembed fields and extract URLs
        const cleanedAcf = { ...acf };

        // Helper function to extract URL from iframe
        const extractUrlFromIframe = (content: string) => {
          if (!content || typeof content !== 'string') return content;

          // If it's already a clean URL, return it
          if (content.startsWith('http') && !content.includes('<iframe')) {
            return content;
          }

          // Extract URL from iframe src attribute
          const iframeMatch = content.match(/src="([^"]+)"/);
          if (iframeMatch) {
            const srcUrl = iframeMatch[1];

            // Convert YouTube embed URL back to watch URL
            if (srcUrl.includes('youtube.com/embed/')) {
              const videoId = srcUrl.split('/embed/')[1]?.split('?')[0];
              return videoId ? `https://www.youtube.com/watch?v=${videoId}` : srcUrl;
            }

            // Convert Vimeo embed URL back to regular URL
            if (srcUrl.includes('player.vimeo.com/video/')) {
              const videoId = srcUrl.split('/video/')[1]?.split('?')[0];
              return videoId ? `https://vimeo.com/${videoId}` : srcUrl;
            }

            return srcUrl;
          }

          return content;
        };

        // Clean video/embed fields
        const fieldsToClean = ['youtube_url', 'vimeo_url', 'youtube_id', 'vimeo_id'];
        fieldsToClean.forEach((field) => {
          if (cleanedAcf[field]) {
            cleanedAcf[field] = extractUrlFromIframe(cleanedAcf[field]);
          }
        });

        setAcfFields(cleanedAcf);

        // Extract file URLs from ACF fields for preview
        const files: Record<string, { url: string; filename?: string }> = {};

        // Common file fields across different post types
        const fileFields = [
          'host_company_organization_logo',
          'webinar_host_company_organization_logo',
          'creator_logo',
          'company_logo',
          'companyorganization_logo',
          'pdf_upload',
          'upload_pdf',
          'file_upload',
          'logo',
        ];

        // Handle featured image separately
        if ((post as any).featured_media && post._embedded?.['wp:featuredmedia']?.[0]) {
          const featuredMedia = post._embedded['wp:featuredmedia'][0] as any;
          files['featured_image'] = {
            url: featuredMedia.source_url || featuredMedia.url,
            filename: featuredMedia.title?.rendered || featuredMedia.alt_text || 'Featured Image',
          };
        }

        fileFields.forEach((field) => {
          if (acf[field] && typeof acf[field] === 'object' && acf[field].url) {
            files[field] = {
              url: acf[field].url,
              filename: acf[field].filename || acf[field].title || 'Uploaded file',
            };
          } else if (
            acf[field] &&
            typeof acf[field] === 'string' &&
            acf[field].startsWith('http')
          ) {
            files[field] = {
              url: acf[field],
              filename: 'Uploaded file',
            };
          }
        });

        setExistingFiles(files);

        // Initialize file changes to keep existing files
        const changes: Record<string, { file: File | null; keepExisting: boolean }> = {};
        Object.keys(files).forEach((field) => {
          changes[field] = { file: null, keepExisting: true };
        });
        setFileChanges(changes);
      }
    }
  }, [post, open]);

  const handleFieldChange = (field: string, value: any, keepExisting?: boolean) => {
    // Handle file uploads differently
    if (value instanceof File || keepExisting !== undefined) {
      setFileChanges((prev) => ({
        ...prev,
        [field]: { file: value, keepExisting: keepExisting || false },
      }));
    } else {
      // Regular field update
      setAcfFields((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!post) return;

    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      // Validate required fields
      if (!title.trim()) {
        setError('Title is required');
        setLoading(false);
        return;
      }

      // Check if content is empty (handling rich text)
      const textContent = stripHtml(content).trim();
      if (!textContent) {
        setError('Content is required');
        setLoading(false);
        return;
      }

      // Validate ACF fields
      const acfError = validateAcfFields(selectedCategory?.slug, acfFields, null);
      if (acfError) {
        setError(acfError);
        setLoading(false);
        return;
      }

      // Prepare form data for submission (including files)
      const formData = new FormData();
      formData.append('title', title);
      formData.append('content', content);

      // Add ACF fields (non-file fields)
      Object.entries(acfFields).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(`acf_fields[${key}]`, JSON.stringify(value));
        }
      });

      // Add file uploads and keep-existing flags
      Object.entries(fileChanges).forEach(([field, change]) => {
        if (change.file) {
          formData.append(`files[${field}]`, change.file);
        } else if (change.keepExisting) {
          formData.append(`keep_existing[${field}]`, 'true');
        }
      });

      // Submit the edit
      const response = await fetch(`/api/wp-proxy/posts/${post.id}`, {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update post');
      }

      const result = await response.json();

      if (result.success) {
        setSuccess(true);
        // Close dialog immediately and refresh to show updated content
        onSuccess();
        // Small delay to allow dialog to close gracefully
        setTimeout(() => {
          window.location.reload();
        }, 100);
      } else {
        setError(result.message || 'Failed to update post');
      }
    } catch (err) {
      console.error('Error updating post:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while updating the post');
    } finally {
      setLoading(false);
    }
  };

  if (!post) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Post</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Success Alert */}
          {success && (
            <Alert variant="default" className="bg-green-50 border-green-200">
              <Check className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-700">
                Post updated successfully!
              </AlertDescription>
            </Alert>
          )}

          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter post title..."
              required
            />
          </div>

          {/* Content */}
          <div className="space-y-2">
            <Label htmlFor="content">Content *</Label>
            <RichTextEditor
              value={content}
              onChange={setContent}
              placeholder="Write your post content..."
              className="min-h-[150px]"
            />
          </div>

          {/* Featured Image */}
          <div className="space-y-2">
            <FileUploadWithPreview
              label="Featured Image"
              accept="image/*"
              currentFileUrl={existingFiles.featured_image?.url}
              currentFileName={existingFiles.featured_image?.filename}
              fieldName="featured_image"
              onChange={handleFieldChange}
            />
          </div>

          {/* Category (Read-only) */}
          {selectedCategory && (
            <div className="space-y-2">
              <Label>Category</Label>
              <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-md border">
                <span className="text-sm font-medium">{selectedCategory.name}</span>
                <span className="text-xs text-gray-500">(cannot be changed)</span>
              </div>
            </div>
          )}

          {/* ACF Fields */}
          {selectedCategory && (
            <div className="space-y-4">
              <EditPostAcfFields
                categorySlug={selectedCategory.slug}
                fields={acfFields}
                handleFieldChange={handleFieldChange}
                existingFiles={existingFiles}
              />
            </div>
          )}

          {/* Common Image Caption field for posts with featured images */}
          {existingFiles.featured_image && (
            <div className="space-y-2">
              <Label htmlFor="image-caption" className="text-sm text-gray-600">
                Image Caption
              </Label>
              <Input
                id="image-caption"
                type="text"
                placeholder="Describe the featured image..."
                value={acfFields.image_caption || ''}
                onChange={(e) => handleFieldChange('image_caption', e.target.value)}
              />
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Post'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
