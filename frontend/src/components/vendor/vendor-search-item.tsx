/*********************************************
# frontend/src/components/vendor/vendor-search-item.tsx
# 02/05/2025 12:00am Created condensed vendor search item component
**********************************************/

import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Building, ExternalLink, Star } from 'lucide-react';
import { VendorCardData } from '@/types/vendor';

interface VendorSearchItemProps {
  vendor: VendorCardData;
}

export function VendorSearchItem({ vendor }: VendorSearchItemProps) {
  return (
    <Card
      className={`overflow-hidden hover:shadow-md transition-all duration-200 ${
        vendor.isPaid
          ? 'border-l-4 border-l-blue-500 bg-blue-50/30 hover:bg-blue-50/50'
          : 'hover:bg-gray-50'
      }`}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          {/* Logo/Avatar */}
          <div className="flex-shrink-0">
            <Avatar
              className={`${vendor.isPaid ? 'h-14 w-14' : 'h-12 w-12'} border-2 border-white shadow-sm`}
            >
              <AvatarImage src={vendor.logo || ''} alt={vendor.name} />
              <AvatarFallback>
                <Building className="h-6 w-6 text-muted-foreground" />
              </AvatarFallback>
            </Avatar>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-3">
              <div className="flex-1 min-w-0">
                {/* Name and Partner Badge */}
                <div className="flex items-center gap-2 mb-1">
                  <Link
                    href={`/vendors/${vendor.slug}`}
                    className="hover:underline font-semibold text-lg text-gray-900 truncate"
                  >
                    {vendor.name}
                  </Link>
                  {vendor.isPaid && (
                    <div className="flex-shrink-0 flex items-center gap-1 bg-blue-600 text-white text-xs font-medium px-2 py-1 rounded-full">
                      <Star className="h-3 w-3 fill-current" />
                      <span>Partner</span>
                    </div>
                  )}
                </div>

                {/* Categories */}
                <div className="flex flex-wrap gap-1 mb-2">
                  {vendor.categories.slice(0, 3).map((category, index) => (
                    <span
                      key={index}
                      className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded"
                    >
                      {category}
                    </span>
                  ))}
                  {vendor.categories.length > 3 && (
                    <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                      +{vendor.categories.length - 3} more
                    </span>
                  )}
                </div>

                {/* Description */}
                {vendor.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                    {vendor.description}
                  </p>
                )}
              </div>

              {/* View Button */}
              <div className="flex-shrink-0">
                <Button variant="outline" size="sm" asChild className="whitespace-nowrap">
                  <Link href={`/vendors/${vendor.slug}`}>
                    <ExternalLink className="h-3 w-3 mr-1" />
                    View
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
