/*********************************************
# frontend/src/components/vendor/vendor-card.tsx
# 02/07/2025 2:20pm Fixed inconsistent card heights by making cards consistent height with button stuck to bottom
# 02/07/2025 2:20pm Removed gray bar (CardFooter border) and moved button directly into card content
# 02/07/2025 2:25pm Updated View Profile button to have 2px light blue border instead of default 1px border
# 02/07/2025 2:50pm Fixed View Profile button icon spacing by adding gap-1 and removing individual icon margin
**********************************************/

import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Building, ExternalLink } from 'lucide-react';
import { VendorCardData } from '@/types/vendor';

interface VendorCardProps {
  vendor: VendorCardData;
  featured?: boolean;
}

export function VendorCard({ vendor }: VendorCardProps) {
  // For unpaid vendors, render a simplified card
  if (!vendor.isPaid) {
    return (
      <Card className="overflow-hidden hover:shadow-md transition-shadow">
        <CardContent>
          <div className="py-[5.33px]">
            <Link href={`/vendors/${vendor.slug}`} className="hover:underline">
              <h3 className="font-semibold text-lg">{vendor.name}</h3>
            </Link>
            <div className="flex flex-wrap gap-1 mt-2">
              {vendor.categories.map((category, index) => (
                <span
                  key={index}
                  className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded"
                >
                  {category}
                </span>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // For paid vendors, render the full card with consistent height
  return (
    <Card className="overflow-hidden hover:shadow-lg p-0 transition-shadow h-[400px] flex flex-col">
      <CardContent className="p-0 flex flex-col h-full">
        {/* Cover photo area */}
        <div className="h-32 relative overflow-hidden">
          {vendor.coverPhoto ? (
            <>
              <img
                src={vendor.coverPhoto}
                alt={`${vendor.name}'s cover`}
                className="w-full h-full object-cover"
              />
              {/* Subtle gradient overlay to ensure text readability */}
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/10"></div>
            </>
          ) : (
            <div className="w-full h-full bg-gray-200"></div>
          )}
        </div>

        {/* Content area - flex-1 to take remaining space */}
        <div className="px-6 pt-0 pb-4 relative flex-1 flex flex-col">
          {/* Logo/Avatar */}
          <Avatar className="h-20 w-20 border-4 border-white absolute -top-10 left-6 shadow-sm">
            <AvatarImage src={vendor.logo || ''} alt={vendor.name} />
            <AvatarFallback>
              <Building className="h-8 w-8 text-muted-foreground" />
            </AvatarFallback>
          </Avatar>

          {/* Content that can grow */}
          <div className="pt-12 flex-1 flex flex-col">
            <div className="flex justify-between items-start">
              <div className="flex-1 min-w-0">
                <Link href={`/vendors/${vendor.slug}`} className="hover:underline">
                  <h3 className="font-semibold text-lg">{vendor.name}</h3>
                </Link>
                <div className="flex flex-wrap gap-1 mt-1">
                  {vendor.categories.map((category, index) => (
                    <span
                      key={index}
                      className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded"
                    >
                      {category}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {vendor.description && (
              <div className="mt-3 flex-1">
                <p className="text-sm line-clamp-3">{vendor.description}</p>
              </div>
            )}
          </div>

          {/* Button stuck to bottom */}
          <div className="mt-auto pt-4">
            <Button variant="outline" className="w-full border-2 border-[#5cc8ff] gap-1" asChild>
              <Link href={`/vendors/${vendor.slug}`}>
                <ExternalLink className="h-4 w-4" />
                View Profile
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
