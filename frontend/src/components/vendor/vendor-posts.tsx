'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  MessageSquare,
  Calendar,
  ThumbsUp,
  Building,
  Edit,
  Trash2,
  MoreHorizontal,
  Loader2,
  FileText,
  Newspaper,
  Users,
  Briefcase,
  Presentation,
  Video,
  Mic,
  Play,
  GraduationCap,
  Download,
  Lightbulb,
  BookOpen,
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import Link from 'next/link';
import { decodeHtmlEntities } from '@/lib/text-utils';
import { useAuth } from '@/contexts/auth-context';
import { EditPostDialog } from '@/components/post-edit/EditPostDialog';
import { Post } from '@/lib/types';
import { getCategoryStyles } from '@/lib/utils/categoryStyles';
// import { useQueryClient } from '@tanstack/react-query';
// import { invalidateQueries } from '@/lib/react-query';

// Helper function to get category icon based on category name
function getCategoryIcon(categoryName: string) {
  const name = categoryName.toLowerCase().trim();

  if (name === 'news' || name.includes('news')) {
    return Newspaper;
  }

  if (name === 'thought leadership' || name.includes('thought')) {
    return Lightbulb;
  }

  if (name === 'people-on-the-move' || name.includes('people')) {
    return Users;
  }

  if (name === 'jobs' || name.includes('job')) {
    return Briefcase;
  }

  if (name === 'events' || name.includes('event')) {
    return Users;
  }

  if (name === 'courses' || name === 'course') {
    return GraduationCap;
  }

  if (name === 'podcasts' || name === 'podcast') {
    return Mic;
  }

  if (name === 'videos' || name === 'video') {
    return Play;
  }

  if (name === 'books' || name === 'book') {
    return BookOpen;
  }

  if (name === 'webinars' || name === 'webinar') {
    return Video;
  }

  if (name === 'presentations' || name === 'presentation') {
    return Presentation;
  }

  if (name === 'press-releases' || name === 'press-release') {
    return Newspaper;
  }

  if (name === 'blog-posts' || name === 'blog-post') {
    return FileText;
  }

  if (
    name === 'whitepapers' ||
    name === 'whitepaper' ||
    name === 'case-studies' ||
    name === 'case-study' ||
    name === 'templates' ||
    name === 'template'
  ) {
    return FileText;
  }

  if (
    name.includes('resource') ||
    name.includes('tool') ||
    name.includes('guide') ||
    name.includes('download')
  ) {
    return Download;
  }

  return FileText;
}

interface VendorPost {
  id: number;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  excerpt: {
    rendered: string;
  };
  date: string;
  slug: string;
  author: number;
  categories: number[];
  featured_media: number;
  _embedded?: {
    author?: Array<{
      id: number;
      name: string;
      avatar_urls?: {
        [key: string]: string;
      };
    }>;
    'wp:featuredmedia'?: Array<{
      source_url: string;
      alt_text: string;
    }>;
    'wp:term'?: Array<
      Array<{
        id: number;
        name: string;
        slug: string;
      }>
    >;
  };
}

interface VendorPostsProps {
  vendorSlug: string;
  vendorName: string;
  vendorLogo?: string;
  canEditPosts?: boolean;
}

export function VendorPosts({
  vendorSlug,
  vendorName,
  vendorLogo,
  canEditPosts = false,
}: VendorPostsProps) {
  const [posts, setPosts] = useState<VendorPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingPost, setEditingPost] = useState<Post | null>(null);
  const [deletingPost, setDeletingPost] = useState<Post | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const { user } = useAuth();
  // const queryClient = useQueryClient();

  const fetchVendorPosts = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/wp-proxy/vendors/${vendorSlug}/posts?_embed=1`);

      if (!response.ok) {
        throw new Error(`Failed to fetch posts: ${response.status}`);
      }

      const postsData = await response.json();
      setPosts(postsData);
    } catch (error) {
      console.error('Error fetching vendor posts:', error);
      setError('Failed to load posts');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVendorPosts();
  }, [vendorSlug]);

  // Clean up body pointer-events when dialog closes
  useEffect(() => {
    if (!deletingPost) {
      document.body.style.pointerEvents = '';
    }
  }, [deletingPost]);

  const handleEditPost = (post: VendorPost) => {
    // Convert VendorPost to Post format
    const convertedPost: Post = {
      ...post,
      title: post.title,
      content: post.content,
      excerpt: post.excerpt,
      _embedded: post._embedded,
    };
    setEditingPost(convertedPost);
  };

  const handleDeletePost = (post: VendorPost) => {
    // Convert VendorPost to Post format
    const convertedPost: Post = {
      ...post,
      title: post.title,
      content: post.content,
      excerpt: post.excerpt,
      _embedded: post._embedded,
    };
    setDeletingPost(convertedPost);
  };

  const confirmDeletePost = async () => {
    if (!deletingPost) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/wp-proxy/posts/${deletingPost.id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        // Refresh the posts list
        fetchVendorPosts();
      } else {
        const errorData = await response.json();
        console.error('Failed to delete post:', errorData);
        alert('Failed to delete post. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting post:', error);
      alert('Error deleting post. Please try again.');
    } finally {
      // Always clear the loading state and close the dialog
      setIsDeleting(false);
      setDeletingPost(null);
      // Force cleanup of body pointer-events
      setTimeout(() => {
        document.body.style.pointerEvents = '';
      }, 100);
    }
  };

  const handleEditSuccess = () => {
    setEditingPost(null);
    fetchVendorPosts(); // Refresh the posts list
  };

  // Check if current user can edit a specific post
  const canEditPost = (post: VendorPost) => {
    if (!canEditPosts || !user) return false;
    // User can edit if they have vendor edit permissions and are the author of the post
    return post.author === user.id;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const stripHtml = (html: string) => {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  const getPostCategories = (post: VendorPost) => {
    return post._embedded?.['wp:term']?.[0] || [];
  };

  const getFeaturedImage = (post: VendorPost) => {
    return post._embedded?.['wp:featuredmedia']?.[0];
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Latest Posts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              <p className="text-muted-foreground">Loading posts...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Latest Posts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (posts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Latest Posts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">No posts from {vendorName} yet.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Latest Posts ({posts.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {posts.map((post) => {
              const featuredImage = getFeaturedImage(post);
              const categories = getPostCategories(post);
              const excerpt = stripHtml(post.excerpt.rendered);
              const getTruncatedContent = (content: string, maxLength: number = 200) => {
                if (content.length <= maxLength) return content;
                return content.substr(0, maxLength).trim() + '...';
              };

              return (
                <div key={post.id} className="relative">
                  <Card className="p-0 hover:shadow-lg transition-all duration-200">
                    <CardContent className="p-0">
                      {/* Edit/Delete Menu - Only show if user can edit this post */}
                      {canEditPost(post) && (
                        <div className="absolute top-2 right-2 z-10">
                          <DropdownMenu modal={false}>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 bg-white/80 hover:bg-white"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" style={{ borderRadius: '25px' }}>
                              <DropdownMenuItem
                                onClick={() => handleEditPost(post)}
                                className="rounded-full hover:!bg-[rgba(92,200,255,0.2)] hover:!text-[#3d405b] focus:!bg-[rgba(92,200,255,0.2)] focus:!text-[#3d405b] transition-all duration-200"
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeletePost(post)}
                                className="text-red-600 focus:text-red-600 rounded-full hover:!bg-[rgba(239,68,68,0.2)] hover:!text-red-600 focus:!bg-[rgba(239,68,68,0.2)] focus:!text-red-600 transition-all duration-200"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      )}
                      <Link href={`/posts/${post.slug}`} className="block cursor-pointer">
                        {/* Featured Image */}
                        {featuredImage && (
                          <div className="relative h-48 w-full">
                            <img
                              src={featuredImage.source_url}
                              alt={featuredImage.alt_text || post.title.rendered}
                              className="w-full h-full object-cover rounded-t-lg"
                            />
                          </div>
                        )}

                        <div className="p-6">
                          {/* Categories */}
                          {categories.length > 0 && (
                            <div className="flex flex-wrap gap-2 mb-3">
                              {categories.slice(0, 3).map((category) => {
                                const CategoryIcon = getCategoryIcon(category.name);
                                return (
                                  <span
                                    key={category.id}
                                    className={`inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full text-white border-0 transition-all duration-200 cursor-pointer ${getCategoryStyles(
                                      category.name
                                    )}`}
                                  >
                                    <CategoryIcon className="h-4 w-4 text-white" />
                                    {category.name}
                                  </span>
                                );
                              })}
                              {categories.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{categories.length - 3} more
                                </Badge>
                              )}
                            </div>
                          )}

                          {/* Title */}
                          <h3 className="text-lg font-semibold mb-2 line-clamp-2 group-hover:text-[#5cc8ff] transition-colors">
                            {decodeHtmlEntities(post.title.rendered)}
                          </h3>

                          {/* Excerpt */}
                          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                            {getTruncatedContent(excerpt)}
                          </p>

                          {/* Author and Date */}
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={vendorLogo || ''} alt={vendorName} />
                                <AvatarFallback>
                                  {vendorLogo ? (
                                    vendorName.charAt(0).toUpperCase()
                                  ) : (
                                    <Building className="h-4 w-4" />
                                  )}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="text-sm font-medium">{vendorName}</p>
                                <div className="flex items-center text-xs text-gray-500">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  {formatDate(post.date)}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Post Stats */}
                          <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <div className="flex items-center">
                                <MessageSquare className="h-4 w-4 mr-1" />
                                <span>0</span>
                              </div>
                              <div className="flex items-center">
                                <ThumbsUp className="h-4 w-4 mr-1" />
                                <span>0</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Link>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Edit Post Dialog */}
      <EditPostDialog
        post={editingPost}
        open={!!editingPost}
        onOpenChange={(open) => !open && setEditingPost(null)}
        onSuccess={handleEditSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingPost} onOpenChange={(open) => !open && setDeletingPost(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Post</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{deletingPost?.title?.rendered}&quot;? This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isDeleting}
              className="border-2 border-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b]"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeletePost}
              disabled={isDeleting}
              className="bg-[#5cc8ff] hover:bg-[#5cc8ff]/80 text-white font-extrabold"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
