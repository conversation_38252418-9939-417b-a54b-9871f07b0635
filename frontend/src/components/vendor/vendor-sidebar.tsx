'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Building, ExternalLink } from 'lucide-react';
import { VendorCardData, VendorListResponse, transformVendorToCardData } from '@/types/vendor';

export function VendorSidebar() {
  const [featuredVendors, setFeaturedVendors] = useState<VendorCardData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFeaturedVendors();
  }, []);

  const fetchFeaturedVendors = async () => {
    try {
      // Fetch all vendors and filter for paid ones
      const response = await fetch('/api/wp-proxy/vendors?orderby=title&order=asc');
      if (response.ok) {
        const data: VendorListResponse = await response.json();
        // Transform all vendors and filter for paid ones, take first 3
        const transformedVendors = data.vendors
          .map(transformVendorToCardData)
          .filter((vendor) => vendor.isPaid)
          .slice(0, 3);
        setFeaturedVendors(transformedVendors);
      }
    } catch (error) {
      console.error('Error fetching featured vendors:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card className="border-gray-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Partner Showcase</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-2 text-center text-sm text-muted-foreground">Loading vendors...</div>
        </CardContent>
      </Card>
    );
  }

  if (featuredVendors.length === 0) {
    return (
      <Card className="border-gray-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Partner Showcase</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-2 text-center text-sm text-muted-foreground">
            No featured vendors yet
          </div>
          <div className="mt-4">
            <Link href="/vendors" passHref>
              <Button variant="ghost" size="sm" className="w-full text-primary">
                Browse Directory
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-gray-200">
      <CardHeader className="pb-3">
        <CardTitle className="text-base">Partner Showcase</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {featuredVendors.map((vendor) => (
          <div key={vendor.id} className="space-y-2">
            <Link
              href={`/vendors/${vendor.slug}`}
              className="flex items-start gap-3 hover:opacity-80 transition-opacity"
            >
              <Avatar className="h-10 w-10 flex-shrink-0">
                <AvatarImage src={vendor.logo || ''} alt={vendor.name} />
                <AvatarFallback>
                  <Building className="h-5 w-5 text-muted-foreground" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <p className="font-medium text-sm truncate">{vendor.name}</p>
                  {/* Removed featured badge */}
                </div>
                <p className="text-xs text-muted-foreground">
                  {vendor.categories[0] || 'Uncategorized'}
                </p>
              </div>
            </Link>

            {vendor.website && (
              <div className="ml-13">
                <Button variant="outline" size="sm" className="h-7 text-xs" asChild>
                  <a href={vendor.website} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Visit
                  </a>
                </Button>
              </div>
            )}
          </div>
        ))}

        <div className="mt-6 pt-4 border-t">
          <Link href="/vendors" passHref>
            <Button variant="ghost" size="sm" className="w-full text-primary">
              View All Vendors
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
