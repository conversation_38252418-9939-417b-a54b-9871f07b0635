'use client';

import { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Building,
  Upload,
  X,
  Loader2,
  Save,
  FileText,
  Plus,
  Trash2,
  Edit,
  Users,
  Search,
} from 'lucide-react';
import { Vendor, VendorTeamMember } from '@/types/vendor';
import { toast } from 'sonner';
import { textToParagraphs, paragraphsToText, decodeHtmlEntities } from '@/lib/text-utils';

const vendorEditSchema = z.object({
  name: z.string().min(1, 'Vendor name is required'),
  content: z.string().optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
});

type VendorEditFormData = z.infer<typeof vendorEditSchema>;

interface VendorResource {
  title: string;
  description?: string;
  file: {
    id: number;
    url: string;
    filename: string;
    filesize: number;
    mime_type: string;
  };
}

interface VendorEditFormProps {
  vendor: Vendor;
  onUpdate: () => void;
}

export function VendorEditForm({ vendor, onUpdate }: VendorEditFormProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [coverPhotoFile, setCoverPhotoFile] = useState<File | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [coverPhotoPreview, setCoverPhotoPreview] = useState<string | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [resources, setResources] = useState<VendorResource[]>(vendor.vendor_meta.resources || []);
  const [newResource, setNewResource] = useState<{
    title: string;
    description: string;
    file: File | null;
  }>({
    title: '',
    description: '',
    file: null,
  });
  const [memberSearch, setMemberSearch] = useState('');
  const [searchResults, setSearchResults] = useState<VendorTeamMember[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [assignedMembers, setAssignedMembers] = useState<VendorTeamMember[]>(
    vendor.vendor_meta.assigned_members || []
  );

  // Sync assigned members when vendor prop changes
  useEffect(() => {
    setAssignedMembers(vendor.vendor_meta.assigned_members || []);
  }, [vendor.vendor_meta.assigned_members]);

  // Reset form state when dialog opens/closes
  const handleDialogOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      // Reset all form state when closing
      setCoverPhotoFile(null);
      setLogoFile(null);
      setCoverPhotoPreview(null);
      setLogoPreview(null);
      setResources(vendor.vendor_meta.resources || []);
      setNewResource({ title: '', description: '', file: null });
      setMemberSearch('');
      setSearchResults([]);
      setAssignedMembers(vendor.vendor_meta.assigned_members || []);
      form.reset({
        name: decodeHtmlEntities(vendor.title.rendered),
        content: paragraphsToText(vendor.content.rendered),
        website: vendor.vendor_meta.website || '',
      });
    }
  };

  const coverPhotoInputRef = useRef<HTMLInputElement>(null);
  const logoInputRef = useRef<HTMLInputElement>(null);
  const resourceFileInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<VendorEditFormData>({
    resolver: zodResolver(vendorEditSchema),
    defaultValues: {
      name: decodeHtmlEntities(vendor.title.rendered),
      content: paragraphsToText(vendor.content.rendered),
      website: vendor.vendor_meta.website || '',
    },
  });

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    type: 'cover' | 'logo' | 'resource'
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/') && type !== 'resource') {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      return;
    }

    if (type === 'cover') {
      setCoverPhotoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setCoverPhotoPreview(e.target?.result as string);
      reader.readAsDataURL(file);
    } else if (type === 'logo') {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLogoPreview(result);
      };
      reader.readAsDataURL(file);
    } else if (type === 'resource') {
      setNewResource((prev) => ({ ...prev, file }));
    }
  };

  const uploadFile = async (file: File): Promise<{ url: string; id: number } | null> => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to upload file');
      }

      const data = await response.json();
      return { url: data.url, id: data.id };
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('Failed to upload file');
      return null;
    }
  };

  const addResource = async () => {
    if (!newResource.title || !newResource.file) {
      toast.error('Please provide a title and select a file');
      return;
    }

    const uploadResult = await uploadFile(newResource.file);
    if (!uploadResult) return;

    const resource: VendorResource = {
      title: newResource.title,
      description: newResource.description,
      file: {
        id: uploadResult.id,
        url: uploadResult.url,
        filename: newResource.file.name,
        filesize: newResource.file.size,
        mime_type: newResource.file.type,
      },
    };

    setResources((prev) => [...prev, resource]);
    setNewResource({ title: '', description: '', file: null });

    // Reset file input
    if (resourceFileInputRef.current) {
      resourceFileInputRef.current.value = '';
    }

    toast.success('Resource added successfully');
  };

  const searchMembers = async (query: string) => {
    if (!query.trim() || query.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(`/api/wp-proxy/users/search?q=${encodeURIComponent(query)}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const users = await response.json();
        // Filter out already assigned members
        const assignedIds = assignedMembers.map((m) => m.id);
        const filteredUsers = users.filter(
          (user: VendorTeamMember) => !assignedIds.includes(user.id)
        );
        setSearchResults(filteredUsers);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Error searching members:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const assignMember = (member: VendorTeamMember) => {
    setAssignedMembers((prev) => [...prev, member]);
    setSearchResults((prev) => prev.filter((m) => m.id !== member.id));
    setMemberSearch('');
    toast.success(`${member.name} assigned to vendor`);
  };

  const removeMember = (memberId: number) => {
    setAssignedMembers((prev) => prev.filter((m) => m.id !== memberId));
    toast.success('Member removed from vendor');
  };

  const removeResource = (index: number) => {
    setResources((prev) => prev.filter((_, i) => i !== index));
    toast.success('Resource removed');
  };

  const onSubmit = async (data: VendorEditFormData) => {
    setIsSubmitting(true);

    try {
      const updateData: Record<string, unknown> = {
        name: data.name,
        content: data.content ? textToParagraphs(data.content) : '',
        website: data.website,
        resources: resources,
        assigned_members: assignedMembers.map((m) => m.id),
      };

      // Upload cover photo if changed
      if (coverPhotoFile) {
        const coverUploadResult = await uploadFile(coverPhotoFile);
        if (coverUploadResult) {
          updateData.cover_photo = coverUploadResult.url;
        }
      }

      // Upload logo if changed
      if (logoFile) {
        const logoUploadResult = await uploadFile(logoFile);
        if (logoUploadResult) {
          updateData.logo_url = logoUploadResult.url;
        }
      }

      // Update vendor
      const response = await fetch(`/api/wp-proxy/vendors/${vendor.slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update vendor');
      }

      toast.success('Vendor profile updated successfully!');
      onUpdate(); // Refresh vendor data first
      handleDialogOpenChange(false); // Then close dialog
    } catch (error) {
      console.error('Error updating vendor:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update vendor');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Edit className="h-4 w-4" />
          Edit Profile
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Vendor Profile</DialogTitle>
          <DialogDescription>
            Update your vendor information, images, and resources.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name" className="mb-2 block">
                  Vendor Name
                </Label>
                <Input id="name" {...form.register('name')} placeholder="Enter vendor name" />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-500 mt-1">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="website" className="mb-2 block">
                  Website
                </Label>
                <Input
                  id="website"
                  {...form.register('website')}
                  placeholder="https://example.com"
                />
                {form.formState.errors.website && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.website.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="content" className="mb-2 block">
                  About Content
                </Label>
                <p className="text-sm text-muted-foreground mb-2">
                  Describe your business. Use double line breaks to create new paragraphs.
                </p>
                <Textarea
                  id="content"
                  {...form.register('content')}
                  placeholder="Describe your business...

Use double line breaks to create new paragraphs.

Single line breaks will be preserved within paragraphs."
                  rows={8}
                />
              </div>
            </CardContent>
          </Card>

          {/* Images */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Images</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Cover Photo */}
              <div>
                <Label className="mb-2 block">Cover Photo</Label>
                <div className="mt-2">
                  <div className="h-32 border-2 border-dashed border-gray-300 rounded-lg relative overflow-hidden">
                    {coverPhotoPreview || vendor.vendor_meta.cover_photo ? (
                      <img
                        src={coverPhotoPreview || vendor.vendor_meta.cover_photo}
                        alt="Cover preview"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full text-gray-500">
                        <Upload className="h-8 w-8 mr-2" />
                        No cover photo
                      </div>
                    )}
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button
                        type="button"
                        variant="secondary"
                        size="sm"
                        onClick={() => coverPhotoInputRef.current?.click()}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Change Cover
                      </Button>
                    </div>
                  </div>
                  <input
                    ref={coverPhotoInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange(e, 'cover')}
                    className="hidden"
                  />
                </div>
              </div>

              {/* Logo */}
              <div>
                <Label className="mb-2 block">Logo</Label>
                <div className="mt-2 flex items-center gap-4">
                  <div className="relative">
                    <Avatar className="h-20 w-20 border-2 border-gray-300">
                      {logoPreview ? (
                        <img
                          src={logoPreview}
                          alt="Logo preview"
                          className="w-full h-full object-cover rounded-full"
                        />
                      ) : vendor.vendor_meta.logo_url ? (
                        <AvatarImage src={vendor.vendor_meta.logo_url} alt="Current logo" />
                      ) : (
                        <AvatarFallback>
                          <Building className="h-8 w-8 text-muted-foreground" />
                        </AvatarFallback>
                      )}
                    </Avatar>
                    {logoPreview && (
                      <div className="absolute -top-1 -right-1">
                        <div className="bg-green-500 text-white rounded-full p-1">
                          <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => logoInputRef.current?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      {logoPreview ? 'Change Logo' : 'Upload Logo'}
                    </Button>
                    {logoPreview && (
                      <p className="text-xs text-green-600 font-medium">✓ New logo selected</p>
                    )}
                    {logoFile && (
                      <p className="text-xs text-muted-foreground">
                        {logoFile.name} ({formatFileSize(logoFile.size)})
                      </p>
                    )}
                    {!logoPreview && !vendor.vendor_meta.logo_url && (
                      <p className="text-xs text-muted-foreground">No logo uploaded</p>
                    )}
                  </div>
                  <input
                    ref={logoInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange(e, 'logo')}
                    className="hidden"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Resources */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Resources & Downloads</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Existing Resources */}
              {resources.length > 0 && (
                <div className="space-y-3">
                  {resources.map((resource, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <FileText className="h-6 w-6 text-muted-foreground" />
                        <div>
                          <h4 className="font-medium">{resource.title}</h4>
                          {resource.description && (
                            <p className="text-sm text-muted-foreground">{resource.description}</p>
                          )}
                          <p className="text-xs text-muted-foreground">
                            {resource.file.mime_type.toUpperCase()} •{' '}
                            {formatFileSize(resource.file.filesize)}
                          </p>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeResource(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {/* Add New Resource */}
              <div className="border-t pt-4">
                <h4 className="font-medium mb-3">Add New Resource</h4>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="resource-title" className="mb-2 block">
                      Title
                    </Label>
                    <Input
                      id="resource-title"
                      value={newResource.title}
                      onChange={(e) =>
                        setNewResource((prev) => ({ ...prev, title: e.target.value }))
                      }
                      placeholder="Resource title"
                    />
                  </div>
                  <div>
                    <Label htmlFor="resource-description" className="mb-2 block">
                      Description (optional)
                    </Label>
                    <Input
                      id="resource-description"
                      value={newResource.description}
                      onChange={(e) =>
                        setNewResource((prev) => ({ ...prev, description: e.target.value }))
                      }
                      placeholder="Resource description"
                    />
                  </div>
                  <div>
                    <Label htmlFor="resource-file" className="mb-2 block">
                      File
                    </Label>
                    <div className="flex gap-2">
                      <Input
                        ref={resourceFileInputRef}
                        type="file"
                        onChange={(e) => handleFileChange(e, 'resource')}
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        onClick={addResource}
                        disabled={!newResource.title || !newResource.file}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Team Members */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5" />
                Team Members
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Current Assigned Members */}
              {assignedMembers.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium">Assigned Members</h4>
                  {assignedMembers.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage
                            src={member.profile_picture?.url || member.avatar_url}
                            alt={member.name}
                          />
                          <AvatarFallback>
                            {member.first_name && member.last_name
                              ? `${member.first_name[0]}${member.last_name[0]}`
                              : member.name[0]?.toUpperCase() || <Users className="h-4 w-4" />}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">
                            {member.first_name && member.last_name
                              ? `${member.first_name} ${member.last_name}`
                              : member.name}
                          </p>
                          <p className="text-sm text-muted-foreground">@{member.username}</p>
                          {member.job_title && (
                            <p className="text-xs text-muted-foreground">{member.job_title}</p>
                          )}
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeMember(member.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {/* Add New Member */}
              <div className="border-t pt-4">
                <h4 className="font-medium mb-3">Add Team Member</h4>
                <div className="space-y-3">
                  <div className="relative">
                    <Label htmlFor="member-search" className="mb-2 block">
                      Search Members
                    </Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="member-search"
                        value={memberSearch}
                        onChange={(e) => {
                          setMemberSearch(e.target.value);
                          searchMembers(e.target.value);
                        }}
                        placeholder="Search by name or username..."
                        className="pl-10"
                      />
                      {isSearching && (
                        <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin" />
                      )}
                    </div>

                    {/* Search Results */}
                    {searchResults.length > 0 && (
                      <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto">
                        {searchResults.map((user) => (
                          <button
                            key={user.id}
                            type="button"
                            onClick={() => assignMember(user)}
                            className="w-full flex items-center gap-3 p-3 hover:bg-gray-50 text-left"
                          >
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={user.profile_picture?.url || user.avatar_url}
                                alt={user.name}
                              />
                              <AvatarFallback>
                                {user.first_name && user.last_name
                                  ? `${user.first_name[0]}${user.last_name[0]}`
                                  : user.name[0]?.toUpperCase() || <Users className="h-3 w-3" />}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium text-sm">
                                {user.first_name && user.last_name
                                  ? `${user.first_name} ${user.last_name}`
                                  : user.name}
                              </p>
                              <p className="text-xs text-muted-foreground">@{user.username}</p>
                              {user.job_title && (
                                <p className="text-xs text-muted-foreground">{user.job_title}</p>
                              )}
                            </div>
                          </button>
                        ))}
                      </div>
                    )}

                    {memberSearch.length >= 2 && searchResults.length === 0 && !isSearching && (
                      <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg p-3">
                        <p className="text-sm text-muted-foreground">No members found</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleDialogOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
