/*********************************************
# frontend/src/components/messaging/messaging-provider.tsx
# 01/27/2025 9:00pm Fixed excessive API polling by stabilizing useEffect dependency and increasing interval to 60s
# 01/27/2025 9:05pm Further reduced polling to 2 minutes, added proper cleanup, and reduced development logging
# 01/27/2025 9:10pm Increased polling to 3 minutes and added debouncing to prevent mount/unmount issues
# 01/27/2025 9:15pm TEMPORARILY DISABLED all messaging API polling to fix excessive API calls
# 01/27/2025 9:20pm RE-ENABLED with proper throttling: 10min intervals, 5s debounce, structured logging
# 01/27/2025 11:45pm Moved messages button to header, removed fixed bottom-right button
**********************************************/

'use client';

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
  ReactNode,
} from 'react';
import { MessageList } from '@/components/messaging/message-list';
import { ChatWindow } from '@/components/messaging/chat-window';
import { useAuth } from '@/contexts/auth-context';
import { useNotifications } from '@/contexts/NotificationContext';
import { messagingApi } from '@/lib/api/messaging';
import type { Conversation } from '@/lib/api/messaging';
import { logger } from '@/lib/logger';

// Default spacing between chat windows (narrower for a more compact layout)
const CHAT_WINDOW_SPACING = 8;
// Width of minimized chat tabs
// const MINIMIZED_CHAT_WIDTH = 200;
// Width of normal chat windows
const CHAT_WINDOW_WIDTH = 320;

interface MessagingContextType {
  openMessageList: () => void;
  closeMessageList: () => void;
  openChat: (conversation: Conversation) => void;
  closeChat: (conversationId: string) => void;
  minimizeChat: (conversationId: string) => void;
  maximizeChat: (conversationId: string) => void;
  isMessageListOpen: boolean;
  hasUnreadMessages: boolean;
  totalUnreadCount: number;
  refreshConversations: () => void;
  isChatOpen: (participantId: string | number) => boolean;
}

interface ActiveChat {
  conversation: Conversation;
  position: {
    bottom: number;
    right: number;
  };
  isMinimized: boolean;
  isMaximized: boolean;
}

const MessagingContext = createContext<MessagingContextType | undefined>(undefined);

export const useMessaging = () => {
  const context = useContext(MessagingContext);
  if (!context) {
    throw new Error('useMessaging must be used within a MessagingProvider');
  }
  return context;
};

interface MessagingProviderProps {
  children: ReactNode;
}

export function MessagingProvider({ children }: MessagingProviderProps) {
  const { user } = useAuth();
  const { socket } = useNotifications();
  const [isMessageListOpen, setIsMessageListOpen] = useState(false);
  const [activeChats, setActiveChats] = useState<ActiveChat[]>([]);
  const [hasUnreadMessages, setHasUnreadMessages] = useState(false);
  const [totalUnreadCount, setTotalUnreadCount] = useState(0);

  // Calculate position for chat at specific index (simplified)
  const calculatePositionForIndex = useCallback((index: number) => {
    return {
      bottom: 0,
      right: CHAT_WINDOW_SPACING + index * (CHAT_WINDOW_WIDTH + CHAT_WINDOW_SPACING),
    };
  }, []);

  // Close all chats when user logs out
  useEffect(() => {
    if (!user) {
      setActiveChats([]);
      setIsMessageListOpen(false);
    }
  }, [user]);

  // Fetch unread count on mount and periodically (only when authenticated)
  useEffect(() => {
    if (!user?.id) {
      // Clear state when not authenticated
      setTotalUnreadCount(0);
      setHasUnreadMessages(false);
      return () => {};
    }

    let isActive = true; // Flag to prevent state updates if component unmounts
    let intervalId: NodeJS.Timeout;
    let debounceTimeout: NodeJS.Timeout;

    const fetchUnreadCount = async () => {
      if (!isActive) return;

      try {
        const { unreadCount } = await messagingApi.getUnreadCount();
        if (isActive) {
          setTotalUnreadCount(unreadCount);
          setHasUnreadMessages(unreadCount > 0);
        }
      } catch (error) {
        if (isActive) {
          logger.error('Failed to fetch unread count', error);
          // On 401 errors, reset the count
          if (error instanceof Error && error.message.includes('Sorry, you are not allowed')) {
            setTotalUnreadCount(0);
            setHasUnreadMessages(false);
          }
        }
      }
    };

    // Initial fetch (debounced to prevent multiple calls if component re-mounts quickly)
    debounceTimeout = setTimeout(fetchUnreadCount, 2000); // 2 second delay

    // Refresh unread count every 10 minutes only when authenticated (much less aggressive)
    intervalId = setInterval(() => {
      if (isActive) {
        fetchUnreadCount();
      }
    }, 600000); // 10 minutes

    return () => {
      isActive = false;
      clearInterval(intervalId);
      clearTimeout(debounceTimeout);
    };
  }, [user?.id]); // Only depend on user.id to prevent unnecessary re-runs

  // Function to refresh conversations
  const refreshConversations = useCallback(() => {
    // This will be called by MessageList to refresh its data
    setIsMessageListOpen((prev) => prev); // Trigger re-render
  }, []);

  // Open the message list
  const openMessageList = useCallback(() => {
    setIsMessageListOpen(true);
  }, []);

  // Close the message list
  const closeMessageList = useCallback(() => {
    setIsMessageListOpen(false);
  }, []);

  // Socket event listeners for real-time updates
  useEffect(() => {
    if (!socket) return () => {};

    const handleNewMessage = (messageData: { senderId: string; [key: string]: unknown }) => {
      // Update unread count
      setTotalUnreadCount((prev) => prev + 1);
      setHasUnreadMessages(true);

      // If the chat window for this conversation is open, add the message
      const senderId = messageData.senderId;
      const existingChatIndex = activeChats.findIndex(
        (chat) => chat.conversation.participants[0]?.id === senderId
      );

      if (existingChatIndex !== -1) {
        // Chat is open, the chat window will handle the message display
        return;
      }

      // Optionally show a notification or update the conversation list
      refreshConversations();
    };

    const handleMessageRead = () => {
      // Refresh unread count when messages are marked as read
      // Use a debounced approach to prevent excessive API calls
      setTimeout(() => {
        messagingApi
          .getUnreadCount()
          .then(({ unreadCount }) => {
            setTotalUnreadCount(unreadCount);
            setHasUnreadMessages(unreadCount > 0);
          })
          .catch((error) => {
            logger.error('Failed to refresh unread count after message read', error);
          });
      }, 5000); // 5 second debounce to prevent rapid-fire calls
    };

    socket.on('new_message', handleNewMessage);
    socket.on('messages_read', handleMessageRead);

    return () => {
      socket.off('new_message', handleNewMessage);
      socket.off('messages_read', handleMessageRead);
    };
  }, [socket, activeChats, refreshConversations]);

  // Open a chat window for a specific conversation
  const openChat = useCallback(
    (conversation: Conversation) => {
      setActiveChats((prev) => {
        // Check if this chat is already open by participant ID (more reliable than conversation ID)
        const participantId = conversation.participants[0]?.id?.toString();
        const existingChatIndex = prev.findIndex(
          (chat) => chat.conversation.participants[0]?.id?.toString() === participantId
        );

        if (existingChatIndex !== -1) {
          // Chat is already open, just make sure it's not minimized
          return prev.map((chat, index) =>
            index === existingChatIndex ? { ...chat, isMinimized: false } : chat
          );
        }

        // Add new chat window
        const newChat: ActiveChat = {
          conversation,
          position: calculatePositionForIndex(prev.length),
          isMinimized: false,
          isMaximized: false,
        };

        return [...prev, newChat];
      });

      // Close the message list
      setIsMessageListOpen(false);

      // Mark messages as read for this conversation
      const firstParticipant = conversation.participants[0];
      if (firstParticipant) {
        const otherUserId = parseInt(firstParticipant.id);
        messagingApi.markMessagesAsRead(otherUserId).catch(console.error);
      }
    },
    [calculatePositionForIndex]
  );

  // Close a chat window
  const closeChat = (conversationId: string) => {
    // Get the index of the chat being closed
    const closingChatIndex = activeChats.findIndex(
      (chat) => chat.conversation.id === conversationId
    );
    if (closingChatIndex === -1) return;

    // Remove the chat and recalculate positions for remaining chats
    setActiveChats((prev) => {
      const updatedChats = prev.filter((chat) => chat.conversation.id !== conversationId);

      // Recalculate positions for all chats after the closed one
      return updatedChats.map((chat, index) => {
        if (index >= closingChatIndex) {
          return {
            ...chat,
            position: calculatePositionForIndex(index),
          };
        }
        return chat;
      });
    });
  };

  // Minimize a chat window
  const minimizeChat = (conversationId: string) => {
    setActiveChats((prev) =>
      prev.map((chat) =>
        chat.conversation.id === conversationId
          ? {
              ...chat,
              isMinimized: !chat.isMinimized,
              isMaximized: false, // Ensure maximized is false when minimizing
            }
          : chat
      )
    );
  };

  // Maximize a chat window
  const maximizeChat = (conversationId: string) => {
    // When maximizing one chat, make sure all others are not maximized
    setActiveChats((prev) =>
      prev.map(
        (chat) =>
          chat.conversation.id === conversationId
            ? {
                ...chat,
                isMaximized: !chat.isMaximized,
                isMinimized: false, // Ensure minimized is false when maximizing
              }
            : { ...chat, isMaximized: false } // Ensure other chats are not maximized
      )
    );
  };

  // Check if a chat is already open with a specific participant
  const isChatOpen = useCallback(
    (participantId: string | number): boolean => {
      const participantIdStr = participantId.toString();
      return activeChats.some(
        (chat) => chat.conversation.participants[0]?.id?.toString() === participantIdStr
      );
    },
    [activeChats]
  );

  // Value for the context provider
  const contextValue: MessagingContextType = {
    openMessageList,
    closeMessageList,
    openChat,
    closeChat,
    minimizeChat,
    maximizeChat,
    isMessageListOpen,
    hasUnreadMessages,
    totalUnreadCount,
    refreshConversations,
    isChatOpen,
  };

  return (
    <MessagingContext.Provider value={contextValue}>
      {children}

      {/* Message List */}
      <MessageList isOpen={isMessageListOpen} onClose={closeMessageList} onOpenChat={openChat} />

      {/* Active Chat Windows */}
      {activeChats.map((chat) => (
        <ChatWindow
          key={chat.conversation.id}
          conversation={chat.conversation}
          position={chat.position}
          isMinimized={chat.isMinimized}
          isMaximized={chat.isMaximized}
          onClose={() => closeChat(chat.conversation.id)}
          onMinimize={() => minimizeChat(chat.conversation.id)}
          onMaximize={() => maximizeChat(chat.conversation.id)}
        />
      ))}
    </MessagingContext.Provider>
  );
}
