/*********************************************
# frontend/src/components/messaging/chat-window.tsx
# 02/07/2025 12:50pm Reduced blue message bubble opacity from 50% to 30% for lighter appearance
# 02/07/2025 12:55pm Updated chat header to use dark blue background with white text and light blue icons with 15% hover opacity
# 02/07/2025 1:00pm Ensured icons remain light blue on hover for consistent styling
**********************************************/

'use client';

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { X, Minus, Maximize2, Minimize2, Send } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

import { cn } from '@/lib/utils';
import { messagingApi } from '@/lib/api/messaging';
import type { Conversation, Message } from '@/lib/api/messaging';
import { useNotifications } from '@/contexts/NotificationContext';
import { useAuth } from '@/contexts/auth-context';

interface ChatWindowProps {
  conversation: Conversation;
  position?: {
    bottom: number;
    right: number;
  };
  isMinimized: boolean;
  isMaximized: boolean;
  onClose: () => void;
  onMinimize: () => void;
  onMaximize: () => void;
}

export function ChatWindow({
  conversation,
  position = { bottom: 0, right: 340 },
  isMinimized,
  isMaximized,
  onClose,
  onMinimize,
  onMaximize,
}: ChatWindowProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [isUserOnline, setIsUserOnline] = useState(false);
  const [windowHeight, setWindowHeight] = useState(384); // Default height (h-96 = 384px)
  const [isResizing, setIsResizing] = useState(false);
  const messageContainerRef = useRef<HTMLDivElement>(null);

  const windowRef = useRef<HTMLDivElement>(null);
  const { socket } = useNotifications();
  const { isAuthenticated } = useAuth();

  // Handle resize functionality
  const handleResizeStart = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      setIsResizing(true);

      const startY = e.clientY;
      const startHeight = windowHeight;

      const handleMouseMove = (e: MouseEvent) => {
        const deltaY = startY - e.clientY;
        const newHeight = Math.max(200, Math.min(800, startHeight + deltaY)); // Min 200px, max 800px
        setWindowHeight(newHeight);
      };

      const handleMouseUp = () => {
        setIsResizing(false);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    },
    [windowHeight]
  );

  // Get participant details - memoize to prevent infinite loops
  const participant = useMemo(() => conversation.participants[0], [conversation.participants]);
  const participantId = useMemo(() => participant?.id, [participant?.id]);

  // Close chat window when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      onClose();
    }
  }, [isAuthenticated, onClose]);

  // Load messages for this conversation
  useEffect(() => {
    if (!participantId) return;

    const loadMessages = async () => {
      try {
        setIsLoading(true);
        const conversationMessages = await messagingApi.getConversationMessages(
          parseInt(participantId)
        );
        setMessages(conversationMessages);

        // Mark messages as read
        await messagingApi.markMessagesAsRead(parseInt(participantId));
      } catch (error) {
        console.error('Failed to load messages:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadMessages();
  }, [conversation.id, participantId]);

  // Initialize online status when component loads
  useEffect(() => {
    if (!socket || !participantId) return () => {};

    // Request online users to check if this user is online
    socket.emit('get_user_status', { userId: participantId });

    // Listen for the response
    const handleUserStatus = (data: { userId: string; isOnline: boolean }) => {
      if (data.userId === participantId) {
        setIsUserOnline(data.isOnline);
      }
    };

    socket.on('user_status_response', handleUserStatus);

    return () => {
      socket.off('user_status_response', handleUserStatus);
    };
  }, [socket, participantId]);

  // Socket event listeners for real-time updates
  useEffect(() => {
    if (!socket || !participantId) return () => {};

    const handleNewMessage = (messageData: {
      id: string;
      senderId: string;
      content: string;
      timestamp: string;
    }) => {
      // Check if this message belongs to this conversation
      const currentParticipantId = String(participantId);
      const senderId = String(messageData.senderId);

      // Only add messages from the other participant (not our own messages)
      if (senderId === currentParticipantId) {
        // Normalize the message format to match API messages
        const normalizedMessage = {
          id: String(messageData.id),
          conversationId: conversation.id,
          senderId: String(messageData.senderId),
          content: messageData.content,
          timestamp: messageData.timestamp,
          isRead: false, // New messages are unread initially
        };

        setMessages((prev) => [...prev, normalizedMessage]);

        // Mark as read if the chat window is open
        if (!isMinimized) {
          messagingApi.markMessagesAsRead(parseInt(participantId));
        }
      }
    };

    const handleTypingStart = (data: { userId: string }) => {
      if (data.userId === participantId) {
        setIsTyping(true);
      }
    };

    const handleTypingStop = (data: { userId: string }) => {
      if (data.userId === participantId) {
        setIsTyping(false);
      }
    };

    const handleUserStatusChanged = (data: { userId: string; isOnline: boolean }) => {
      if (data.userId === participantId) {
        setIsUserOnline(data.isOnline);
      }
    };

    socket.on('new_message', handleNewMessage);
    socket.on('typing_start', handleTypingStart);
    socket.on('typing_stop', handleTypingStop);
    socket.on('user_status_changed', handleUserStatusChanged);

    return () => {
      socket.off('new_message', handleNewMessage);
      socket.off('typing_start', handleTypingStart);
      socket.off('typing_stop', handleTypingStop);
      socket.off('user_status_changed', handleUserStatusChanged);
    };
  }, [socket, conversation.id, isMinimized, participantId]);

  // Handle sending a new message
  const handleSendMessage = useCallback(async () => {
    if (newMessage.trim() === '' || !participantId) return;

    try {
      const sentMessage = await messagingApi.sendMessage({
        recipient_id: parseInt(participantId),
        content: newMessage.trim(),
      });
      setMessages((prev) => [...prev, sentMessage.message]);
      setNewMessage('');

      // Emit typing stop event
      if (socket) {
        socket.emit('typing_stop', { receiverId: participantId });
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }, [newMessage, participantId, socket]);

  // Handle typing indicators
  const handleTyping = useCallback(() => {
    if (socket && participantId) {
      socket.emit('typing_start', { receiverId: participantId });

      // Stop typing after 3 seconds of inactivity
      setTimeout(() => {
        socket.emit('typing_stop', { receiverId: participantId });
      }, 3000);
    }
  }, [socket, participantId]);

  // Auto-scroll to bottom when messages change or when chat is maximized/minimized
  useEffect(() => {
    if (messageContainerRef.current && !isMinimized) {
      messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
    }
  }, [messages, isMinimized, isMaximized]);

  // Early return if no participant (after all hooks)
  if (!participant) {
    return null;
  }

  // Get initials for avatar fallback
  const getInitials = (): string => {
    if (participant?.firstName && participant?.lastName) {
      return `${participant.firstName.charAt(0)}${participant.lastName.charAt(0)}`;
    }
    return (participant?.username ?? 'User').substring(0, 2).toUpperCase();
  };

  // Get display name
  const getDisplayName = (): string => {
    if (participant?.firstName && participant?.lastName) {
      return `${participant.firstName} ${participant.lastName}`;
    }
    return participant?.username ?? 'User';
  };

  // Format time for message timestamp
  const formatMessageTime = (timestamp: string): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format date for message groups
  const formatMessageDate = (timestamp: string): string => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });
    }
  };

  // Group messages by date
  const groupMessagesByDate = (): { date: string; messages: Message[] }[] => {
    const groups: { [key: string]: Message[] } = {};

    messages.forEach((message) => {
      const dateStr = new Date(message.timestamp).toDateString();
      if (!groups[dateStr]) {
        groups[dateStr] = [];
      }
      groups[dateStr].push(message);
    });

    return Object.entries(groups)
      .map(([dateStr, messages]) => ({
        date: formatMessageDate(new Date(dateStr).toISOString()),
        messages,
      }))
      .sort((a, b) => {
        // Sort by date (oldest first)
        if (a.date === 'Today') return 1;
        if (b.date === 'Today') return -1;
        if (a.date === 'Yesterday') return 1;
        if (b.date === 'Yesterday') return -1;
        return (
          new Date(a.messages[0]?.timestamp || 0).getTime() -
          new Date(b.messages[0]?.timestamp || 0).getTime()
        );
      });
  };

  // Handle key press in textarea
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    } else {
      handleTyping();
    }
  };

  // Render minimized view
  if (isMinimized) {
    return (
      <div
        ref={windowRef}
        className="fixed bg-white border border-gray-200 rounded-t-lg shadow-lg z-50 w-48"
        style={{
          bottom: `${position.bottom}px`,
          right: `${position.right}px`,
        }}
      >
        <div
          className="flex items-center justify-between p-3 bg-primary text-primary-foreground rounded-t-lg cursor-pointer"
          onClick={onMinimize}
        >
          <div className="flex items-center space-x-2 min-w-0">
            <Avatar className="h-6 w-6">
              {participant.avatar?.url && (
                <AvatarImage src={participant.avatar.url} alt={getDisplayName()} />
              )}
              <AvatarFallback className="text-xs">{getInitials()}</AvatarFallback>
            </Avatar>
            <span className="text-sm font-medium truncate">{getDisplayName()}</span>
            {conversation.unreadCount > 0 && (
              <span className="bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center">
                {conversation.unreadCount}
              </span>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-primary-foreground hover:bg-white/15"
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={windowRef}
      className={cn(
        'fixed bg-white border border-gray-200 rounded-t-lg shadow-lg z-50 select-none',
        isMaximized ? 'inset-4' : 'w-80',
        isResizing && 'cursor-ns-resize'
      )}
      style={
        !isMaximized
          ? {
              bottom: `${position.bottom}px`,
              right: `${position.right}px`,
              height: `${windowHeight}px`,
            }
          : {}
      }
    >
      {/* Resize handle */}
      {!isMaximized && (
        <div
          className="absolute top-0 left-0 right-0 h-1 cursor-ns-resize hover:bg-primary/20 transition-colors"
          onMouseDown={handleResizeStart}
        />
      )}

      {/* Header */}
      <div className="flex items-center justify-between p-3 bg-[#3d405b] text-white rounded-t-lg">
        <div className="flex items-center space-x-3 min-w-0">
          <Avatar className="h-8 w-8">
            {participant.avatar?.url && (
              <AvatarImage src={participant.avatar.url} alt={getDisplayName()} />
            )}
            <AvatarFallback>{getInitials()}</AvatarFallback>
          </Avatar>
          <div className="min-w-0">
            <h3 className="font-medium truncate text-white">{getDisplayName()}</h3>
            <p className="text-xs text-white/80 truncate">
              {isUserOnline ? 'Active now' : 'Offline'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]"
            onClick={onMinimize}
          >
            <Minus className="h-4 w-4" />
          </Button>

          {!isMaximized ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]"
              onClick={onMaximize}
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]"
              onClick={onMinimize}
            >
              <Minimize2 className="h-4 w-4" />
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-[#5cc8ff] hover:bg-white/15 hover:text-[#5cc8ff]"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Messages */}
      <div
        ref={messageContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
        style={{ height: `${windowHeight - 140}px` }} // Subtract header and input area height
      >
        {isLoading ? (
          <div className="flex justify-center items-center h-full">
            <div className="text-gray-500">Loading messages...</div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex justify-center items-center h-full">
            <div className="text-gray-500 text-center">
              <p>No messages yet</p>
              <p className="text-sm">Start a conversation with {getDisplayName()}</p>
            </div>
          </div>
        ) : (
          groupMessagesByDate().map((group, groupIndex) => (
            <div key={groupIndex}>
              {/* Date separator */}
              <div className="flex justify-center mb-4">
                <span className="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                  {group.date}
                </span>
              </div>

              {/* Messages for this date */}
              {group.messages.map((message, messageIndex) => {
                const isCurrentUser = message.senderId !== participantId;
                const showAvatar =
                  !isCurrentUser &&
                  (messageIndex === group.messages.length - 1 ||
                    group.messages[messageIndex + 1]?.senderId !== message.senderId);

                // Check if this message is part of a sequence from the same user
                const isFirstInSequence =
                  messageIndex === 0 ||
                  group.messages[messageIndex - 1]?.senderId !== message.senderId;
                const isLastInSequence =
                  messageIndex === group.messages.length - 1 ||
                  group.messages[messageIndex + 1]?.senderId !== message.senderId;

                return (
                  <div
                    key={message.id}
                    className={cn(
                      'flex items-end space-x-2',
                      isCurrentUser ? 'justify-end' : 'justify-start',
                      // Add margin bottom for spacing between different users
                      !isLastInSequence && 'mb-2'
                    )}
                  >
                    {!isCurrentUser && (
                      <Avatar className={cn('h-6 w-6', showAvatar ? 'opacity-100' : 'opacity-0')}>
                        {participant.avatar?.url && (
                          <AvatarImage src={participant.avatar.url} alt={getDisplayName()} />
                        )}
                        <AvatarFallback className="text-xs">{getInitials()}</AvatarFallback>
                      </Avatar>
                    )}

                    <div
                      className={cn(
                        'max-w-xs px-3 py-2 relative',
                        // Different border radius based on position in sequence
                        isCurrentUser
                          ? isFirstInSequence
                            ? 'rounded-t-lg rounded-bl-lg'
                            : isLastInSequence
                              ? 'rounded-t-lg rounded-bl-lg'
                              : 'rounded-t-lg'
                          : isFirstInSequence
                            ? 'rounded-t-lg rounded-br-lg'
                            : isLastInSequence
                              ? 'rounded-t-lg rounded-br-lg'
                              : 'rounded-t-lg',
                        isCurrentUser
                          ? 'bg-primary/30 text-primary-foreground'
                          : 'bg-gray-100 text-dark-text'
                      )}
                    >
                      <p className="text-sm">{message.content}</p>
                      <p
                        className={cn(
                          'text-xs mt-1',
                          isCurrentUser ? 'text-primary-foreground/80' : 'text-gray-500'
                        )}
                      >
                        {formatMessageTime(message.timestamp)}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          ))
        )}

        {/* Typing indicator */}
        {isTyping && (
          <div className="flex items-center space-x-2">
            <Avatar className="h-6 w-6">
              {participant.avatar?.url && (
                <AvatarImage src={participant.avatar.url} alt={getDisplayName()} />
              )}
              <AvatarFallback className="text-xs">{getInitials()}</AvatarFallback>
            </Avatar>
            <div className="bg-gray-100 px-3 py-2 rounded-lg">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: '0.1s' }}
                ></div>
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: '0.2s' }}
                ></div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Message input */}
      <div className="border-t border-gray-200 p-3">
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <Textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={`Message ${getDisplayName()}...`}
              className="min-h-[36px] max-h-24 resize-none rounded-full border-gray-300 px-4 py-2 focus:border-primary focus:ring-primary"
              rows={1}
            />
          </div>

          <Button
            onClick={handleSendMessage}
            disabled={newMessage.trim() === ''}
            size="sm"
            className="h-9 w-9 p-0 bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 border-2 border-[#5cc8ff] rounded-full"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
