/*********************************************
# frontend/src/components/messaging/message-list.tsx
# 02/07/2025 4:15pm Removed overlay backdrop and enhanced with sophisticated shadow for non-modal slideout behavior
**********************************************/

'use client';

import { useState, useEffect } from 'react';
import { X, Search, Plus, ChevronRight, Circle } from 'lucide-react';
import Image from 'next/image';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetClose } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { messagingApi, Message, Conversation } from '@/lib/api/messaging';
import { useConnectionsList } from '@/hooks/queries/useConnections';

// Export types for use in other components
export type { Message, Conversation };

interface MessageListProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenChat: (conversation: Conversation) => void;
}

export function MessageList({ isOpen, onClose, onOpenChat }: MessageListProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConnectionsDialog, setShowConnectionsDialog] = useState(false);

  // Get user connections
  const { connections, loading: connectionsLoading } = useConnectionsList();

  // Get display name for participant
  const getDisplayName = (participant: Conversation['participants'][0]): string => {
    if (participant.firstName && participant.lastName) {
      return `${participant.firstName} ${participant.lastName}`;
    }
    return participant.username;
  };

  // Get initials for avatar fallback
  const getInitials = (participant: Conversation['participants'][0]): string => {
    if (participant.firstName && participant.lastName) {
      return `${participant.firstName.charAt(0)}${participant.lastName.charAt(0)}`;
    }
    return participant.username.substring(0, 2).toUpperCase();
  };

  // Format timestamp for display
  const formatTime = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  // Fetch conversations when component mounts or when opened
  useEffect(() => {
    if (isOpen) {
      fetchConversations();
    }
  }, [isOpen]);

  const fetchConversations = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await messagingApi.getConversations();
      setConversations(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch conversations');
      console.error('Failed to fetch conversations:', err);
    } finally {
      setLoading(false);
    }
  };

  // Filter conversations based on search term
  const filteredConversations = conversations.filter((conversation) => {
    const participant = conversation.participants[0];
    if (!participant) return false;
    const displayName = getDisplayName(participant);
    return displayName.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Get unread conversations
  const unreadConversations = filteredConversations.filter((conv) => conv.unreadCount > 0);
  // const readConversations = filteredConversations.filter(conv => conv.unreadCount === 0);

  // Mark conversation as read
  const markAsRead = async (conversationId: string) => {
    const conversation = conversations.find((c) => c.id === conversationId);
    if (!conversation) return;

    try {
      const participant = conversation.participants[0];
      if (!participant) return;
      const otherUserId = parseInt(participant.id);
      await messagingApi.markMessagesAsRead(otherUserId);

      // Update local state
      setConversations((prev) =>
        prev.map((conv) => (conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv))
      );
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
    }
  };

  // Handle opening a chat
  const handleOpenChat = (conversation: Conversation) => {
    markAsRead(conversation.id);
    onOpenChat(conversation);
  };

  // Handle starting a chat with a connection
  const handleStartChatWithConnection = (connection: any) => {
    // Check if conversation already exists with this user
    const existingConversation = conversations.find((conv) =>
      conv.participants.some((participant) => participant.id === connection.user_id.toString())
    );

    if (existingConversation) {
      // If conversation exists, open it
      handleOpenChat(existingConversation);
    } else {
      // Create a new conversation object for this connection
      const newConversation: Conversation = {
        id: connection.user_id.toString(),
        participants: [
          {
            id: connection.user_id.toString(),
            username: connection.username || connection.slug || `user-${connection.user_id}`,
            firstName: connection.name?.split(' ')[0] || null,
            lastName: connection.name?.split(' ').slice(1).join(' ') || null,
            avatar: connection.profile_picture
              ? typeof connection.profile_picture === 'string'
                ? { url: connection.profile_picture }
                : connection.profile_picture.url
                  ? { url: connection.profile_picture.url }
                  : null
              : connection.avatar
                ? { url: connection.avatar }
                : null,
            isOnline: false,
          },
        ],
        unreadCount: 0,
      };
      onOpenChat(newConversation);
    }
    setShowConnectionsDialog(false);
  };

  // Render conversation item
  const renderConversationItem = (conversation: Conversation) => {
    const participant = conversation.participants[0];
    if (!participant) return null;
    const displayName = getDisplayName(participant);
    const initials = getInitials(participant);
    const lastMessage = conversation.lastMessage;

    return (
      <div
        key={conversation.id}
        className="flex items-center gap-3 p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200 last:border-b-0"
        onClick={() => handleOpenChat(conversation)}
      >
        <div className="relative">
          <Avatar className="h-12 w-12">
            {participant.avatar?.url && (
              <AvatarImage src={participant.avatar.url} alt={displayName} />
            )}
            <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
              {initials}
            </AvatarFallback>
          </Avatar>
          {participant.isOnline && (
            <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h3 className="font-medium text-dark-text truncate">{displayName}</h3>
            <div className="flex items-center gap-2">
              {lastMessage && (
                <span className="text-xs text-gray-500">{formatTime(lastMessage.timestamp)}</span>
              )}
              {conversation.unreadCount > 0 && (
                <Badge variant="destructive" className="h-5 min-w-[20px] text-xs">
                  {conversation.unreadCount > 9 ? '9+' : conversation.unreadCount}
                </Badge>
              )}
            </div>
          </div>

          {lastMessage && <p className="text-sm text-gray-600 truncate">{lastMessage.content}</p>}
        </div>

        <ChevronRight className="h-4 w-4 text-gray-400" />
      </div>
    );
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="right" className="w-full sm:w-96 p-0" hideOverlay>
          <SheetHeader className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <SheetTitle className="flex items-center gap-2">
                <Image
                  src="/images/icons/icon-messages.svg"
                  alt="Messages"
                  width={20}
                  height={20}
                  className="h-5 w-5"
                />
                Messages
              </SheetTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowConnectionsDialog(true)}
                  title="Start new conversation"
                >
                  <Plus className="h-4 w-4" />
                </Button>
                <SheetClose asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <X className="h-4 w-4" />
                  </Button>
                </SheetClose>
              </div>
            </div>
          </SheetHeader>

          <div className="p-4 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="flex-1 overflow-hidden">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-sm text-gray-500">Loading conversations...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-sm text-red-500">{error}</div>
              </div>
            ) : filteredConversations.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-32 text-center p-4">
                <Image
                  src="/images/icons/icon-messages.svg"
                  alt="Messages"
                  width={32}
                  height={32}
                  className="h-8 w-8 text-gray-400 mb-2"
                />
                <div className="text-sm text-gray-500">
                  {searchTerm ? 'No conversations found' : 'No messages yet'}
                </div>
                {!searchTerm && (
                  <div className="text-xs text-gray-400 mt-1">
                    Start a conversation with someone!
                  </div>
                )}
              </div>
            ) : (
              <Tabs defaultValue="all" className="h-full">
                <TabsList className="grid w-full grid-cols-2 mx-4 mt-2">
                  <TabsTrigger value="all" className="text-xs">
                    All ({filteredConversations.length})
                  </TabsTrigger>
                  <TabsTrigger value="unread" className="text-xs">
                    Unread ({unreadConversations.length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="mt-2 h-full overflow-y-auto">
                  <div className="space-y-0">
                    {filteredConversations.map(renderConversationItem)}
                  </div>
                </TabsContent>

                <TabsContent value="unread" className="mt-2 h-full overflow-y-auto">
                  {unreadConversations.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-32 text-center p-4">
                      <Circle className="h-8 w-8 text-gray-400 mb-2" />
                      <div className="text-sm text-gray-500">No unread messages</div>
                      <div className="text-xs text-gray-400 mt-1">You&apos;re all caught up!</div>
                    </div>
                  ) : (
                    <div className="space-y-0">
                      {unreadConversations.map(renderConversationItem)}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Connections Dialog */}
      <Dialog open={showConnectionsDialog} onOpenChange={setShowConnectionsDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Start a conversation</DialogTitle>
          </DialogHeader>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {connectionsLoading ? (
              <div className="py-8 text-center text-sm text-gray-500">Loading connections...</div>
            ) : connections.length === 0 ? (
              <div className="py-8 text-center text-sm text-gray-500">
                No connections yet. Connect with other members to start messaging.
              </div>
            ) : (
              connections.map((connection) => {
                const displayName =
                  connection.name || connection.display_name || connection.username || 'Unknown';
                const avatarUrl =
                  (typeof connection.profile_picture === 'string'
                    ? connection.profile_picture
                    : connection.profile_picture?.url) ||
                  connection.avatar ||
                  '/images/avatar-placeholder.svg';

                return (
                  <button
                    key={connection.user_id}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-100 transition-colors text-left"
                    onClick={() => handleStartChatWithConnection(connection)}
                  >
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={avatarUrl} alt={displayName} />
                      <AvatarFallback>{displayName.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">{displayName}</p>
                      <p className="text-xs text-gray-500 truncate">
                        {connection.job_title || 'Member'}
                      </p>
                    </div>
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  </button>
                );
              })
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
