/**
 * VendorSubscription Component Usage Examples
 *
 * This file shows how to integrate the VendorSubscription component
 * into your vendor dashboard or settings pages.
 */

import VendorSubscription from './VendorSubscription';

// Example 1: Basic usage in a vendor settings page
export function VendorSettingsPage({ vendorId }: { vendorId: number }) {
  // Get the payment link from environment variables
  const paymentLinkUrl = process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK || '';

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Vendor Settings</h1>

      {/* Other vendor settings components */}
      <div className="mb-8">{/* Vendor profile settings, team members, etc. */}</div>

      {/* Subscription management section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Subscription Management</h2>
        <VendorSubscription
          vendorId={vendorId}
          paymentLinkUrl={paymentLinkUrl}
          className="max-w-2xl"
        />
      </div>
    </div>
  );
}

// Example 2: Usage in a vendor dashboard card
export function VendorDashboardCard({ vendorId }: { vendorId: number }) {
  const paymentLinkUrl = process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK || '';

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Other dashboard widgets */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
        {/* Stats content */}
      </div>

      {/* Subscription status widget */}
      <VendorSubscription
        vendorId={vendorId}
        paymentLinkUrl={paymentLinkUrl}
        className="col-span-1 lg:col-span-2"
      />
    </div>
  );
}

// Example 3: Usage with dynamic vendor ID from URL params
export function VendorPage() {
  // In Next.js app router, you might get this from params
  // const vendorId = params.id;

  // For this example, we'll assume you have a way to get the vendor ID
  const vendorId = 123; // Replace with actual logic
  const paymentLinkUrl = process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK || '';

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Vendor Management</h1>
        <p className="text-gray-600 mt-2">Manage your vendor profile and subscription</p>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Vendor profile section */}
        <div className="xl:col-span-2">{/* Vendor profile form, team management, etc. */}</div>

        {/* Subscription sidebar */}
        <div className="xl:col-span-1">
          <VendorSubscription vendorId={vendorId} paymentLinkUrl={paymentLinkUrl} />
        </div>
      </div>
    </div>
  );
}

/**
 * Setup Instructions:
 *
 * 1. Make sure you have the Stripe configuration set up:
 *    - Add NEXT_PUBLIC_STRIPE_PAYMENT_LINK to your .env.local file
 *    - Configure WordPress with Stripe API keys and webhook secret
 *
 * 2. Import and use the VendorSubscription component:
 *    import VendorSubscription from '@/components/VendorSubscription';
 *
 * 3. Pass the required props:
 *    - vendorId: The WordPress vendor post ID
 *    - paymentLinkUrl: Your Stripe Payment Link URL
 *    - className (optional): Additional CSS classes
 *
 * 4. The component will automatically:
 *    - Check the user's subscription status
 *    - Show appropriate subscribe/manage buttons
 *    - Handle payment link redirection with vendor metadata
 *    - Display subscription status and features
 *
 * 5. Webhook integration will automatically:
 *    - Update vendor_is_paid ACF field when subscription changes
 *    - Store Stripe customer and subscription IDs
 *    - Handle payment failures and subscription cancellations
 */
