'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardFooter } from '@/components/ui/card';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import Link from 'next/link';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import { PostComments } from './PostComments';
import { FileText, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';

// Vendor info interface
interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}
import {
  formatPostDate,
  getUserFullName,
  getUserAvatarUrl,
  getCategoryColor,
  useImageObjectFit,
} from './feed-item-helpers';
import Image from 'next/image';

// Complete interface based on ACF fields for case studies
interface CaseStudyPost extends Post {
  acf?: {
    companyorganization_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    upload_pdf?: {
      url?: string;
      [key: string]: unknown;
    };
  };
  vendor_info?: VendorInfo;
  vendor_data?: any;
}

interface FeedItemCaseStudyProps {
  post: CaseStudyPost;
}

export function FeedItemCaseStudy({ post }: FeedItemCaseStudyProps) {
  const [showComments, setShowComments] = useState(false);
  const [excerpt, setExcerpt] = useState('');
  const decodedTitle = decodeHtml(post.title.rendered);
  const { objectFitClass, handleImageLoad } = useImageObjectFit();

  useEffect(() => {
    setExcerpt(decodeHtml(post.excerpt?.rendered || ''));
  }, [post.excerpt?.rendered]);

  // Get case study-specific data from ACF fields

  // Use featured image as main case study image
  const caseStudyImageUrl = post._embedded?.['wp:featuredmedia']?.[0]?.source_url;

  // Check if this is a vendor post
  const vendorId = post.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);
  const vendorData = post.vendor_info || post.vendor_data || null;

  // Get author information
  const authorData = post._embedded?.author?.[0];
  const authorId = Number(authorData?.id || post.author || 1);

  // Determine display name and avatar based on vendor vs personal post
  let displayName: string;
  let avatarUrl: string;
  let displayId: number;

  if (isVendorPost && vendorData) {
    // Use vendor information
    displayName = vendorData.name || 'Vendor';
    avatarUrl = vendorData.logo_url || '';
    displayId = vendorData.id;
  } else {
    // Use regular author information
    displayName = getUserFullName(authorData);
    avatarUrl = getUserAvatarUrl(authorData);
    displayId = authorId;
  }
  const postDate = formatPostDate(post.date);
  const categoryColor = getCategoryColor('case_studies');

  return (
    <Card className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200 w-full max-w-full">
      <Link href={`/posts/${post.slug}`} className="cursor-pointer">
        <div className="relative">
          {caseStudyImageUrl && (
            <div className="aspect-video overflow-hidden bg-muted">
              <Image
                src={caseStudyImageUrl}
                alt={decodedTitle}
                width={1200}
                height={675}
                className={`${objectFitClass} w-full h-full post-image-glow`}
                onLoad={handleImageLoad}
                priority={false}
              />
            </div>
          )}
        </div>

        <CardHeader className="flex-row items-center gap-4 space-y-0 bg-white dark:bg-gray-900 pb-2">
          <UserAvatarWithRank
            userId={displayId}
            avatarUrl={avatarUrl}
            displayName={displayName}
            size="h-10 w-10"
            containerSize="w-12 h-12"
            userRoles={(authorData?.roles as string[]) || []}
          />
          <div className="flex flex-col">
            <div className="font-semibold text-[#3d405b] dark:text-gray-100">{displayName}</div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>{postDate}</span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-4 md:p-6 pt-2">
          <div
            className="inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full capitalize mb-4"
            style={{
              backgroundColor: categoryColor,
              color: 'white',
            }}
          >
            <FileText className="h-4 w-4 text-white" />
            Case Study
          </div>
          <h3 className="text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-2">
            {decodedTitle}
          </h3>
          <div className="prose prose-sm dark:prose-invert line-clamp-4">
            <p
              className="text-[#3d405b] dark:text-gray-300 mb-4 last:mb-0"
              dangerouslySetInnerHTML={{ __html: excerpt }}
            />
          </div>
        </CardContent>
      </Link>

      {/* View Post Button */}
      <div className="px-6 pb-8">
        <Button
          asChild
          variant="outline"
          size="sm"
          className="w-fit text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
        >
          <Link href={`/posts/${post.slug}`}>
            <ExternalLink className="h-4 w-4 mr-2" />
            View Post
          </Link>
        </Button>
      </div>

      <CardFooter className="flex justify-center border-t p-4 bg-gray-100/50 dark:bg-gray-900">
        <PostInteractions
          postId={post.id}
          authorId={isVendorPost ? displayId : authorId}
          initialUpvotes={(() => {
            if (post.upvotes && typeof post.upvotes.count === 'number') {
              return post.upvotes.count;
            }
            return Number(post.meta?._upvotes) || 0;
          })()}
          initialCommentCount={
            Number(post.total_comments) || Number(post.meta?._comments_count) || 0
          }
          postTitle={decodedTitle}
          layout="full-width"
          onCommentsToggle={setShowComments}
        />
      </CardFooter>

      <PostComments
        postId={post.id}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        commentCount={Number(post.total_comments) || Number(post.meta?._comments_count) || 0}
      />
    </Card>
  );
}
