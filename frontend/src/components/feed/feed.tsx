'use client';

import { useState, useEffect, useRef, useCallback, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { FeedItem } from './feed-item';
import { FeedItemSponsored } from './feed-item-sponsored';
import { WPPost as Post } from '@/lib/api/wordpress';
import { getPostsByCategory, getFeaturedPosts } from '@/lib/api/posts';
import { useUpvotes } from '@/contexts/UpvoteContext';
import { useFeed } from '@/contexts/FeedContext';

// Add TypeScript declaration for the global window property
declare global {
  interface Window {
    setFeedCategory?: ((categorySlug: string | null) => void) | undefined;
  }
}

export type SortOption = 'newest' | 'upvotes';

interface FeedProps {
  initialPosts: Post[];
  initialTotalPages?: number;
  initialTotalPosts?: number;
  categoryFilter?: boolean; // Whether to enable category filtering
  sortBy?: SortOption; // Current sort option
  onSortChange?: (sort: SortOption) => void; // Sort change handler
  activeCategory?: string | null; // Currently active category
  onClearFilter?: () => void; // Handler to clear the active filter
  onCategoryChange?: (category: string | null) => void; // Handler for category changes
}

// Internal Feed component that uses useSearchParams
function FeedInternal({
  initialPosts,
  initialTotalPages = 1,
  initialTotalPosts = 0,
  categoryFilter = false,
  sortBy = 'newest',
  activeCategory = null,
  onClearFilter,
  onCategoryChange,
}: FeedProps) {
  const searchParams = useSearchParams();
  const { posts, setPosts, appendPosts } = useFeed();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(initialTotalPages > 1);
  const [totalPosts, setTotalPosts] = useState<number>(initialTotalPosts);
  const observer = useRef<IntersectionObserver | null>(null);
  const loadingRef = useRef<HTMLDivElement>(null);
  const { batchFetchUpvotes } = useUpvotes();
  const [categoryChanging, setCategoryChanging] = useState(false);
  const previousCategoryRef = useRef<string | null>(null);
  const [internalCategory, setInternalCategory] = useState<string | null>(activeCategory);

  // Use internal category state for filtering, fall back to URL params only on initial load
  const categoryParam = internalCategory;

  // Sync internal category with activeCategory prop
  useEffect(() => {
    setInternalCategory(activeCategory);
  }, [activeCategory]);

  // Handle initial URL parameter on mount
  useEffect(() => {
    const urlCategory = searchParams.get('category');
    if (urlCategory && internalCategory === null) {
      setInternalCategory(urlCategory);
    }
  }, [searchParams, internalCategory]);

  // Batch fetch upvote data for all posts when the feed loads or posts change
  // Use a ref to track the posts we've already requested upvotes for
  const fetchedPostIdsRef = useRef<Set<number>>(new Set());

  // Define the fetchPosts function to handle both filtered and unfiltered posts
  const fetchPosts = useCallback(
    async (
      categoryParam: string | null,
      pageNum: number = 1,
      append: boolean = false,
      currentSort: SortOption = sortBy
    ) => {
      // Always set loading state when starting a fetch
      setIsLoading(true);
      setError(null);

      try {
        let result: { posts: Post[]; totalPages: number; totalPosts: number };

        // Determine sort parameters
        const orderby = currentSort === 'upvotes' ? 'meta_value_num' : 'date';
        const metaKey = currentSort === 'upvotes' ? '_upvotes' : undefined;

        if (categoryParam) {
          // Use the existing getPostsByCategory function from the API
          result = await getPostsByCategory(categoryParam, 20, pageNum, orderby, metaKey);
        } else if (pageNum === 1 && !append && currentSort === 'newest') {
          // If no category filter and first page (not appending), use initial posts if available and sorting by newest
          if (initialPosts.length > 0) {
            result = {
              posts: initialPosts,
              totalPages: initialTotalPages,
              totalPosts: initialTotalPosts,
            };
          } else {
            // If no initial posts, fetch featured posts including vendor posts
            result = await getFeaturedPosts(pageNum, 20, orderby, metaKey, false);
          }
        } else {
          // For pagination, upvote sorting, or when no initial posts, get featured posts including vendor posts
          result = await getFeaturedPosts(pageNum, 20, orderby, metaKey, false);
        }

        // Update state with the new posts and pagination info
        if (append) {
          appendPosts(result.posts);
        } else {
          setPosts(result.posts);
        }

        setTotalPosts(result.totalPosts);
        setHasMore(pageNum < result.totalPages);
      } catch (error) {
        console.error('Error fetching posts:', error);
        setError(error instanceof Error ? error.message : 'An error occurred');
        if (!append) {
          setPosts([]);
        }
        setHasMore(false);
      } finally {
        setIsLoading(false);
      }
    },
    [
      setPosts,
      appendPosts,
      setTotalPosts,
      setHasMore,
      initialPosts,
      initialTotalPages,
      initialTotalPosts,
      sortBy,
    ]
  ); // Note: posts.length intentionally omitted to avoid infinite loops

  useEffect(() => {
    if (posts.length > 0) {
      // Only fetch upvotes for posts we haven't fetched yet
      const newPostIds = posts
        .map((post) => post.id)
        .filter((id) => !fetchedPostIdsRef.current.has(id));

      if (newPostIds.length > 0) {
        // Add these IDs to our tracking set
        newPostIds.forEach((id) => fetchedPostIdsRef.current.add(id));
        // Fetch upvotes for new posts only
        batchFetchUpvotes(newPostIds);
      }
    }
  }, [posts, batchFetchUpvotes]);

  // Initialize feed context with initial posts if posts are empty and no category filter
  useEffect(() => {
    if (posts.length === 0 && initialPosts.length > 0 && !categoryParam) {
      // Check if initial posts have proper embedded data
      const hasCompleteEmbeddedData = initialPosts.every(
        (post) => post._embedded && typeof post._embedded === 'object'
      );

      if (hasCompleteEmbeddedData) {
        setPosts(initialPosts);
      } else {
        // If embedded data is missing, fetch fresh posts
        console.log('Initial posts missing embedded data, fetching fresh posts');
        fetchPosts(null, 1, false, sortBy);
      }
    }
  }, [initialPosts, posts.length, setPosts, categoryParam, fetchPosts, sortBy]);

  // Fetch posts when the category or sort changes or on initial load
  useEffect(() => {
    // Detect if category has changed
    const categoryChanged = previousCategoryRef.current !== categoryParam;
    if (categoryChanged) {
      setCategoryChanging(true);
      previousCategoryRef.current = categoryParam;
    }

    // Reset pagination when category or sort changes
    setPage(1);
    // Clear existing posts immediately when applying a category filter or changing sort to prevent flash of wrong content
    if (categoryParam !== null || sortBy === 'upvotes') {
      setPosts([]);
    }
    fetchPosts(categoryParam, 1, false, sortBy).finally(() => {
      setCategoryChanging(false);
    });
  }, [categoryParam, sortBy, fetchPosts, setPosts]);

  // Function to load more posts
  const loadMorePosts = useCallback(async () => {
    if (isLoading || !hasMore) return;

    const nextPage = page + 1;
    await fetchPosts(categoryParam, nextPage, true, sortBy);
    setPage(nextPage);
  }, [categoryParam, page, isLoading, hasMore, fetchPosts, sortBy]);

  // Setup intersection observer for infinite scroll
  useEffect(() => {
    if (isLoading) return () => {};

    if (observer.current) {
      observer.current.disconnect();
    }

    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0]?.isIntersecting && hasMore) {
          loadMorePosts();
        }
      },
      { threshold: 0.5 }
    );

    if (loadingRef.current) {
      observer.current.observe(loadingRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [loadMorePosts, isLoading, hasMore]);

  // Function to update the category filter
  const setCategory = useCallback(
    (categorySlug: string | null) => {
      if (!categoryFilter) return; // Only proceed if category filtering is enabled

      // Set the internal category immediately for instant filtering
      setInternalCategory(categorySlug);
      setCategoryChanging(true);

      // Notify parent component about the category change
      if (onCategoryChange) {
        onCategoryChange(categorySlug);
      }

      // Reset pagination
      setPage(1);
    },
    [categoryFilter, onCategoryChange]
  );

  // Make the setCategory function available globally for the Sidebar component
  useEffect(() => {
    if (categoryFilter) {
      // Create a wrapped function that will handle the loading state
      const wrappedSetCategory = (categorySlug: string | null) => {
        // Set category changing state immediately
        setCategoryChanging(true);
        // Notify parent component about the category change
        if (onCategoryChange) {
          onCategoryChange(categorySlug);
        }
        setCategory(categorySlug);
      };

      // Expose the wrapped function globally for the Sidebar component
      window.setFeedCategory = wrappedSetCategory;
    }

    return () => {
      // Clean up on unmount
      if (categoryFilter) {
        window.setFeedCategory = undefined;
      }
    };
  }, [categoryFilter, setCategory, onCategoryChange]);

  return (
    <div className="space-y-6 w-full">
      {/* Error state */}
      {error && <div className="bg-red-50 p-4 rounded-lg text-red-500">{error}</div>}

      {/* Posts feed */}
      {!categoryChanging && (!isLoading || posts.length > 0) ? (
        posts.length > 0 && (
          <div className="space-y-6">
            {posts.map((post) => {
              // Check if this is a sponsored post
              const isSponsored =
                post.is_sponsored || (post.acf && (post.acf as any)?.sponsored === true);

              if (isSponsored) {
                return <FeedItemSponsored key={post.id} post={post} />;
              }

              return <FeedItem key={post.id} post={post} />;
            })}
          </div>
        )
      ) : (
        /* Initial loading state for category changes */
        <div className="space-y-6">
          {/* Skeleton loader for posts */}
          {[...Array(3)].map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse"
            >
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                  <div className="space-y-2 mt-4">
                    <div className="h-3 bg-gray-200 rounded w-full"></div>
                    <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                  </div>
                  <div className="h-3 bg-gray-200 rounded w-1/3 mt-4"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Loading indicator for infinite scroll */}
      {isLoading && posts.length > 0 && (
        <div className="flex justify-center py-6">
          <div className="flex items-center gap-3">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
            <span className="text-[#3d405b] font-medium">Loading more posts...</span>
          </div>
        </div>
      )}

      {/* Invisible element for intersection observer */}
      {hasMore && !isLoading && <div ref={loadingRef} className="h-10" />}

      {/* End of posts message */}
      {!hasMore && posts.length > 0 && !isLoading && (
        <div className="text-center py-4 text-gray-500">
          {posts.length === totalPosts
            ? `You've reached the end. ${totalPosts} posts loaded.`
            : 'No more posts to load.'}
        </div>
      )}

      {/* Empty state */}
      {!isLoading && posts.length === 0 && !error && (
        <div className="bg-gray-50 p-8 rounded-lg text-center">
          <p className="text-gray-500">
            No posts found{categoryParam ? ' for this category' : ''}.
          </p>
          {categoryParam && (
            <button
              onClick={() => {
                setCategory(null);
                if (onClearFilter) {
                  onClearFilter();
                }
              }}
              className="mt-4 text-primary hover:underline"
            >
              View all posts
            </button>
          )}
        </div>
      )}
    </div>
  );
}

export function Feed({
  initialPosts,
  initialTotalPages = 1,
  initialTotalPosts = 0,
  categoryFilter = false,
  sortBy = 'newest',
  onSortChange = () => {},
  activeCategory = null,
  onClearFilter = () => {},
  onCategoryChange,
}: FeedProps) {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#5cc8ff] mx-auto mb-3"></div>
            <p className="text-gray-600">Loading posts...</p>
          </div>
        </div>
      }
    >
      <FeedInternal
        initialPosts={initialPosts}
        initialTotalPages={initialTotalPages}
        initialTotalPosts={initialTotalPosts}
        categoryFilter={categoryFilter}
        sortBy={sortBy}
        onSortChange={onSortChange}
        activeCategory={activeCategory}
        onClearFilter={onClearFilter}
        onCategoryChange={onCategoryChange}
      />
    </Suspense>
  );
}
