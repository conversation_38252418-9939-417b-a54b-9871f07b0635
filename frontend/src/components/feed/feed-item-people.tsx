/*********************************************
# frontend/src/components/feed/feed-item-people.tsx
# 01/30/2025 7:15pm Created file
# 01/30/2025 8:00pm Updated opacity to 60%, square images with max height 140px, added horizontal padding, consistent dropshadow
# 01/30/2025 8:15pm Simplified to use 4-image template for all variations - consistent square images, centered rows
**********************************************/

'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ExternalLink, Users } from 'lucide-react';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import { formatPostDate } from './feed-item-helpers';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import Image from 'next/image';

interface GalleryImage {
  ID: number;
  url: string;
  alt: string;
  title: string;
  sizes: {
    full: string;
    large: string;
    medium: string;
    thumbnail: string;
  };
}

interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

// Complete interface based on ACF fields for people posts
interface PeoplePost extends Post {
  acf?: {
    people_gallery?: GalleryImage[];
    move_type?: string;
    position_level?: string;
    [key: string]: unknown;
  };
  people_meta?: {
    people_gallery?: GalleryImage[];
    [key: string]: unknown;
  };
  vendor_info?: VendorInfo;
}

interface FeedItemPeopleProps {
  post: PeoplePost;
}

// Component to render the new image grid layout - simplified using 4-image template
function PeopleImageGrid({ images }: { images: GalleryImage[] }) {
  // Helper to get image URL with fallback to different sizes
  const getImageUrl = (image: GalleryImage): string => {
    return image.sizes.large || image.sizes.medium || image.url || '';
  };

  // Determine grid layout based on image count - mobile responsive
  const getGridCols = (count: number): string => {
    if (count === 1) return 'grid-cols-1 max-w-20 sm:max-w-24 md:max-w-[140px]';
    if (count === 2) return 'grid-cols-2 max-w-44 sm:max-w-52 md:max-w-[284px]';
    if (count === 3) return 'grid-cols-2 sm:grid-cols-3 max-w-44 sm:max-w-80 md:max-w-[426px]';
    return 'grid-cols-2 sm:grid-cols-4'; // 4 or more images
  };

  // Split images into rows of maximum 4
  const createRows = (images: GalleryImage[]) => {
    const rows = [];
    for (let i = 0; i < images.length; i += 4) {
      rows.push(images.slice(i, i + 4));
    }
    return rows;
  };

  const rows = createRows(images);

  return (
    <div
      className="w-full py-6 md:py-10 px-4 md:px-10 rounded-lg"
      style={{ backgroundColor: 'rgba(170, 215, 238, 0.6)' }}
    >
      <div className="space-y-2">
        {rows.map((row, rowIndex) => (
          <div key={rowIndex} className={`grid gap-2 mx-auto ${getGridCols(row.length)}`}>
            {row.map((image, index) => (
              <div
                key={image.ID || index}
                className="aspect-square overflow-hidden bg-muted rounded-lg w-full"
              >
                <Image
                  src={getImageUrl(image)}
                  alt={image.alt || 'Person image'}
                  width={140}
                  height={140}
                  className="object-cover w-full h-full"
                  priority={false}
                />
              </div>
            ))}
          </div>
        ))}
        {images.length > 10 && (
          <div className="text-center text-sm text-gray-500 mt-2">
            +{images.length - 10} more images
          </div>
        )}
      </div>
    </div>
  );
}

export default function FeedItemPeople({ post }: FeedItemPeopleProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [titleHtml, setTitleHtml] = useState('');

  useEffect(() => {
    setTitleHtml(post.title.rendered);
  }, [post.title.rendered]);

  // Get gallery images from ACF or fallback to people_meta
  const galleryImages = post.acf?.people_gallery || post.people_meta?.people_gallery || [];

  // If no gallery images, try to use featured image as fallback
  const featuredImage = post._embedded?.['wp:featuredmedia']?.[0];
  const images: GalleryImage[] =
    galleryImages.length > 0
      ? galleryImages
      : featuredImage
        ? [
            {
              ID: featuredImage.id,
              url: featuredImage.source_url,
              alt: featuredImage.alt_text || 'Featured image',
              title: featuredImage.alt_text || '',
              sizes: {
                full: featuredImage.source_url,
                large:
                  featuredImage.media_details?.sizes?.large?.source_url || featuredImage.source_url,
                medium:
                  featuredImage.media_details?.sizes?.medium?.source_url ||
                  featuredImage.source_url,
                thumbnail:
                  featuredImage.media_details?.sizes?.thumbnail?.source_url ||
                  featuredImage.source_url,
              },
            },
          ]
        : [];

  // Get author info with vendor fallback
  const authorData = post._embedded?.author?.[0] as any;
  const vendorData = post.vendor_info;

  const authorName = vendorData?.name || authorData?.name || 'Unknown Author';

  const authorAvatar =
    vendorData?.logo_url ||
    authorData?.acf?.profile_picture?.url ||
    authorData?.avatar_urls?.['96'] ||
    '/images/avatar-placeholder.svg';

  const excerpt = post.excerpt?.rendered
    ? decodeHtml(post.excerpt.rendered.replace(/<[^>]*>/g, ''))
    : '';

  const maxExcerptLength = 150;
  const shouldTruncate = excerpt.length > maxExcerptLength;
  const displayExcerpt =
    isExpanded || !shouldTruncate ? excerpt : excerpt.substring(0, maxExcerptLength) + '...';

  return (
    <Card className="bg-white shadow-lg border rounded-lg overflow-hidden w-full max-w-full">
      <CardContent className="p-0">
        {/* Images Section */}
        {images.length > 0 && <PeopleImageGrid images={images} />}

        {/* Content Section */}
        <div className="p-4 md:p-6">
          <CardHeader className="p-0 pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                {vendorData ? (
                  <img
                    src={authorAvatar}
                    alt={authorName}
                    className="h-10 w-10 rounded-full object-cover ring-2 ring-[#5cc8ff]/20"
                  />
                ) : (
                  <UserAvatarWithRank
                    userId={authorData?.id || 1}
                    avatarUrl={authorAvatar}
                    displayName={authorName}
                    size="h-10 w-10"
                    containerSize="w-12 h-12"
                    userRoles={authorData?.roles || []}
                  />
                )}
                <div>
                  <div className="font-semibold text-[#3d405b]">{authorName}</div>
                  <div className="text-sm text-muted-foreground">{formatPostDate(post.date)}</div>
                </div>
              </div>

              <div className="flex items-center gap-2 bg-purple-100 px-3 py-1 rounded-full">
                <Users className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-purple-700">People on the Move</span>
              </div>
            </div>

            <h3
              className="text-xl font-bold text-[#3d405b] leading-tight"
              dangerouslySetInnerHTML={{ __html: titleHtml }}
            />
          </CardHeader>

          {excerpt && (
            <div className="mb-4">
              <p className="text-gray-600">
                {displayExcerpt}
                {shouldTruncate && (
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="ml-2 text-[#5cc8ff] hover:underline"
                  >
                    {isExpanded ? 'Show less' : 'Read more'}
                  </button>
                )}
              </p>
            </div>
          )}

          <div className="flex items-center justify-between">
            <Link href={`/posts/${post.slug}`}>
              <Button
                variant="outline"
                size="sm"
                className="text-[#5cc8ff] border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-white"
              >
                Read Full Post
                <ExternalLink className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-0">
        <PostInteractions
          postId={post.id}
          authorId={vendorData?.id || authorData?.id || 1}
          initialUpvotes={Number(post.meta?._upvotes) || 0}
          initialCommentCount={Number(post.meta?._comments_count) || 0}
          postTitle={post.title.rendered}
          layout="card"
        />
      </CardFooter>
    </Card>
  );
}

export { FeedItemPeople };
