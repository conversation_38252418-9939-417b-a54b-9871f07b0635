'use client';

import { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { MessageCircle, Bookmark, BookmarkCheck } from 'lucide-react';
import { useAuthStatus } from '@/hooks/queries/useAuth';
import { useBookmarks } from '@/hooks/useBookmarks';
import { SimpleUpvoteButton } from './SimpleUpvoteButton';
import { ShareButton } from './ShareButton';

interface PostInteractionsProps {
  postId: string | number;
  authorId: string | number;
  initialUpvotes: number;
  initialCommentCount: number;
  postTitle: string;
  layout?: 'card' | 'full-width';
  onCommentsToggle?: (show: boolean) => void;
  postSlug?: string;
  postExcerpt?: string;
}

export function PostInteractions({
  postId,
  authorId,
  initialUpvotes,
  initialCommentCount,
  postTitle,
  onCommentsToggle,
  postSlug,
  postExcerpt,
}: PostInteractionsProps) {
  const { isLoggedIn } = useAuthStatus();
  const { toggleBookmark, isBookmarked } = useBookmarks();
  const [showComments, setShowComments] = useState(false);
  const bookmarked = isBookmarked(Number(postId));

  const handleBookmarkToggle = useCallback(async () => {
    if (!isLoggedIn) return;

    try {
      await toggleBookmark(Number(postId));
    } catch (error) {
      console.error('Error toggling bookmark:', error);
    }
  }, [isLoggedIn, toggleBookmark, postId]);

  const handleCommentsToggle = () => {
    const newState = !showComments;
    setShowComments(newState);
    if (onCommentsToggle) {
      onCommentsToggle(newState);
    }
  };

  return (
    <div className="flex gap-2 sm:gap-4 items-center">
      <SimpleUpvoteButton
        postId={Number(postId)}
        authorId={Number(authorId)}
        initialCount={initialUpvotes}
        postTitle={postTitle}
      />
      <Button
        variant="ghost"
        size="sm"
        className="gap-2 text-sm sm:text-base font-medium text-primary hover:text-primary hover:bg-gray-200/80 rounded-full px-2 sm:px-3 py-2 min-h-[36px] transition-colors flex items-center justify-center"
        onClick={handleCommentsToggle}
      >
        <MessageCircle className="size-4 sm:size-5" />
        <span className="text-xs sm:text-sm">{initialCommentCount || 0}</span>
      </Button>
      <ShareButton
        postUrl={postSlug ? `/posts/${postSlug}` : `/posts/${postId}`}
        postTitle={postTitle}
        postExcerpt={postExcerpt}
        className="flex items-center justify-center min-h-[36px]"
      />
      <Button
        variant="ghost"
        size="sm"
        className="gap-2 text-sm sm:text-base font-medium text-foreground hover:text-foreground hover:bg-gray-200/80 rounded-full px-2 sm:px-3 py-2 min-h-[36px] transition-colors flex items-center justify-center"
        onClick={handleBookmarkToggle}
        disabled={!isLoggedIn}
      >
        {bookmarked ? (
          <BookmarkCheck className="size-4 sm:size-5" />
        ) : (
          <Bookmark className="size-4 sm:size-5" />
        )}
      </Button>
    </div>
  );
}
