'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Heart } from 'lucide-react';
import { useNotifications } from '@/contexts/NotificationContext';
import { useAuthStatus } from '@/hooks/queries/useAuth';
import { useUpvotes } from '@/contexts/UpvoteContext';

interface SimpleUpvoteButtonProps {
  postId: number;
  authorId: number;
  initialCount?: number;
  postTitle: string;
}

export function SimpleUpvoteButton({
  postId,
  authorId,
  initialCount = 0,
  postTitle,
}: SimpleUpvoteButtonProps) {
  const [upvoted, setUpvoted] = useState(false);
  const [count, setCount] = useState(initialCount);
  const [isLoading, setIsLoading] = useState(false);
  const { sendNotification } = useNotifications();
  const { isLoggedIn, user } = useAuthStatus();
  const { getUpvoteStatus, toggleUpvote } = useUpvotes();

  // Get upvote status from context
  useEffect(() => {
    if (isLoggedIn && user?.id) {
      const cachedStatus = getUpvoteStatus(postId);
      if (cachedStatus) {
        setUpvoted(cachedStatus.upvoted);
        setCount(cachedStatus.count);
      }
    }
  }, [isLoggedIn, user?.id, postId, getUpvoteStatus]);

  const handleUpvote = async () => {
    if (!isLoggedIn || isLoading) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await toggleUpvote(postId);
      setUpvoted(result.upvoted);
      setCount(result.count);

      // Send notification if upvote was added (not removed) AND it's not the post author upvoting their own post
      if (result.upvoted && user?.id !== authorId) {
        const notificationContent = `${user?.display_name || user?.username} upvoted your post "${postTitle}"`;
        await sendNotification(authorId, 'upvote', notificationContent, postId, 'post');
      }
    } catch (error) {
      console.error('Error toggling upvote:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      className={`gap-1 sm:gap-2 text-xs sm:text-sm font-medium hover:text-[#ff625c] hover:bg-gray-200/80 rounded-full px-2 sm:px-3 py-2 min-h-[36px] transition-colors ${
        upvoted ? 'text-[#ff625c]' : 'text-[#ff625c]'
      } ${!isLoggedIn ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={handleUpvote}
      disabled={!isLoggedIn || isLoading}
    >
      <Heart className={`size-4 sm:size-5 ${upvoted ? 'fill-current' : ''}`} />
      <span className="text-xs sm:text-sm">{count}</span>
    </Button>
  );
}
