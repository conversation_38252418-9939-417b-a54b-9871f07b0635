'use client';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';

export type SortOption = 'newest' | 'upvotes';

interface SortDropdownProps {
  currentSort: SortOption;
  onSortChange: (sort: SortOption) => void;
}

const sortOptions: { value: SortOption; label: string }[] = [
  { value: 'newest', label: 'Newest First' },
  { value: 'upvotes', label: 'Most Upvoted' },
];

export function SortDropdown({ currentSort, onSortChange }: SortDropdownProps) {
  const currentOption = sortOptions.find((option) => option.value === currentSort);

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="text-sm">
          {currentOption?.label}
          <ChevronDown className="ml-1 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" style={{ borderRadius: '25px' }}>
        {sortOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => onSortChange(option.value)}
            className={`rounded-full transition-all duration-200 ${
              currentSort === option.value
                ? '!bg-[rgba(92,200,255,0.2)] !text-[#3d405b]'
                : 'hover:!bg-[rgba(92,200,255,0.2)] hover:!text-[#3d405b] focus:!bg-[rgba(92,200,255,0.2)] focus:!text-[#3d405b]'
            }`}
          >
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
