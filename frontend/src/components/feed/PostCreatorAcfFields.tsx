/*********************************************
# frontend/src/components/feed/PostCreatorAcfFields.tsx
# 02/07/2025 12:30pm Updated details container to 20px corner radius and input fields to pill-style with white backgrounds
# 02/07/2025 12:35pm Added 3px padding bottom to last text entry field and updated image caption field styling
# 02/07/2025 1:05pm Updated all select dropdowns and date/time inputs to pill-style with white backgrounds for consistency
# 02/07/2025 1:15pm Fixed podcast name field to have pill-style with white background for complete consistency across all post types
# 02/07/2025 1:20pm Transformed all file upload fields to custom button-style with white background, 2px blue border, and dark blue text for better UX
# 02/07/2025 1:30pm Updated file upload button hover states to use light blue with 20% opacity matching sidebar nav, and updated detail header icons to match corresponding tag pill colors from main feed
**********************************************/

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  ExternalLink,
  Calendar,
  MapPin,
  Video,
  BookOpen,
  FileText,
  Mic,
  Building,
  User,
  Tag,
  Plus,
  X,
  Upload,
} from 'lucide-react';

interface AcfFieldsProps {
  categorySlug: string;
  fields: Record<string, any>;
  handleFieldChange: (field: string, value: any) => void;
  imageFile: File | null;
}

export const PostCreatorAcfFields: React.FC<AcfFieldsProps> = ({
  categorySlug,
  fields,
  handleFieldChange,
  imageFile,
}) => {
  const categoryConfig: Record<string, { title: string; icon: React.ReactNode; color: string }> = {
    news: {
      title: 'News Article Details',
      icon: <Tag className="h-4 w-4" />,
      color: 'text-[#ff5ce0]',
    },
    'blog-post': {
      title: 'Blog Post Details',
      icon: <FileText className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    event: {
      title: 'Event Details',
      icon: <Calendar className="h-4 w-4" />,
      color: 'text-[#1dd05b]',
    },
    webinar: {
      title: 'Webinar Details',
      icon: <Video className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    video: { title: 'Video Details', icon: <Video className="h-4 w-4" />, color: 'text-[#ffa15c]' },
    course: {
      title: 'Course Details',
      icon: <BookOpen className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    presentation: {
      title: 'Presentation Details',
      icon: <FileText className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    whitepaper: {
      title: 'Whitepaper Details',
      icon: <FileText className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    'case-study': {
      title: 'Case Study Details',
      icon: <FileText className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    book: {
      title: 'Book Details',
      icon: <BookOpen className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    podcast: {
      title: 'Podcast Details',
      icon: <Mic className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    'press-release': {
      title: 'Press Release Details',
      icon: <FileText className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    template: {
      title: 'Template Details',
      icon: <FileText className="h-4 w-4" />,
      color: 'text-[#ffa15c]',
    },
    'thought-leadership': {
      title: 'Thought Leadership Details',
      icon: <FileText className="h-4 w-4" />,
      color: 'text-[#5cc8ff]',
    },
  };

  const config = categoryConfig[categorySlug];

  // If no specific config found, only show if we have an image file (for image caption)
  if (!config && !imageFile) return null;

  const defaultConfig = {
    title: 'Post Details',
    icon: <FileText className="h-4 w-4" />,
    color: 'text-gray-600',
  };

  const renderFields = () => {
    switch (categorySlug) {
      case 'news':
        return (
          <div className="space-y-2">
            <Label htmlFor="external-url" className="text-sm text-gray-600">
              External Article URL *
            </Label>
            <div className="relative">
              <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="external-url"
                type="url"
                placeholder="https://example.com/article"
                className="pl-10 rounded-full bg-white"
                value={fields.url || ''}
                onChange={(e) => handleFieldChange('url', e.target.value)}
                required
              />
            </div>
          </div>
        );

      case 'blog-post':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="author" className="text-sm text-gray-600">
                Author *
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="author"
                  type="text"
                  placeholder="Author name"
                  className="pl-10 rounded-full bg-white"
                  value={fields.author || ''}
                  onChange={(e) => handleFieldChange('author', e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="blog-url" className="text-sm text-gray-600">
                External Blog URL *
              </Label>
              <div className="relative">
                <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="blog-url"
                  type="url"
                  placeholder="https://example.com/blog-post"
                  className="pl-10 rounded-full bg-white"
                  value={fields.url || ''}
                  onChange={(e) => handleFieldChange('url', e.target.value)}
                  required
                />
              </div>
            </div>
          </>
        );

      case 'event':
        return (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="event-start" className="text-sm text-gray-600">
                  Event Start Date *
                </Label>
                <Input
                  id="event-start"
                  type="date"
                  value={fields.event_date || ''}
                  onChange={(e) => handleFieldChange('event_date', e.target.value)}
                  className="rounded-full bg-white"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="event-end" className="text-sm text-gray-600">
                  Event End Date *
                </Label>
                <Input
                  id="event-end"
                  type="date"
                  value={fields.event_end_date || ''}
                  onChange={(e) => handleFieldChange('event_end_date', e.target.value)}
                  className="rounded-full bg-white"
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="event-time" className="text-sm text-gray-600">
                  Time (Optional)
                </Label>
                <Input
                  id="event-time"
                  type="time"
                  value={fields.event_time || ''}
                  onChange={(e) => handleFieldChange('event_time', e.target.value)}
                  className="rounded-full bg-white"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="time-zone" className="text-sm text-gray-600">
                  Time Zone (Optional)
                </Label>
                <select
                  id="time-zone"
                  className="w-full h-10 px-3 rounded-full border border-input bg-white text-sm"
                  value={fields.time_zone || ''}
                  onChange={(e) => handleFieldChange('time_zone', e.target.value)}
                >
                  <option value="">Select timezone</option>
                  <option value="EST">EST</option>
                  <option value="CST">CST</option>
                  <option value="MST">MST</option>
                  <option value="PST">PST</option>
                  <option value="AKST">AKST</option>
                  <option value="HST">HST</option>
                </select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="event-location" className="text-sm text-gray-600">
                Event Location (Optional)
              </Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="event-location"
                  type="text"
                  placeholder="Venue or address"
                  className="pl-10 rounded-full bg-white"
                  value={fields.event_location || ''}
                  onChange={(e) => handleFieldChange('event_location', e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="host-org" className="text-sm text-gray-600">
                Host Company/Organization *
              </Label>
              <div className="relative">
                <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="host-org"
                  type="text"
                  placeholder="Organization name"
                  className="pl-10 rounded-full bg-white"
                  value={fields.host_company_organization || ''}
                  onChange={(e) => handleFieldChange('host_company_organization', e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="host-logo" className="text-sm text-gray-600">
                Host Company/Organization Logo *
              </Label>
              <div className="relative">
                <input
                  id="host-logo"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('host_company_organization_logo', file);
                    }
                  }}
                  required
                />
                <label
                  htmlFor="host-logo"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  {fields.host_company_organization_logo ? 'File selected' : 'Choose logo file'}
                </label>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="event-details-url" className="text-sm text-gray-600">
                Additional Details Link *
              </Label>
              <div className="relative">
                <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="event-details-url"
                  type="url"
                  placeholder="https://example.com/event-details"
                  className="pl-10 rounded-full bg-white"
                  value={fields.additional_details_link_url || ''}
                  onChange={(e) => handleFieldChange('additional_details_link_url', e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="registration-url" className="text-sm text-gray-600">
                Registration Link (Optional)
              </Label>
              <div className="relative">
                <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="registration-url"
                  type="url"
                  placeholder="https://example.com/register"
                  className="pl-10 rounded-full bg-white"
                  value={fields.registration_link_url || ''}
                  onChange={(e) => handleFieldChange('registration_link_url', e.target.value)}
                />
              </div>
            </div>
          </>
        );

      case 'webinar':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="webinar-type" className="text-sm text-gray-600">
                Webinar Type *
              </Label>
              <select
                id="webinar-type"
                className="w-full h-10 px-3 rounded-full border border-input bg-white text-sm"
                value={fields.webinar_type || ''}
                onChange={(e) => handleFieldChange('webinar_type', e.target.value)}
                required
              >
                <option value="">Select type</option>
                <option value="live">Live</option>
                <option value="recorded">Recorded</option>
              </select>
            </div>

            {/* Presenters field - always shown */}
            <div className="space-y-2">
              <Label className="text-sm text-gray-600">Presenters *</Label>
              <div className="space-y-2">
                {(fields.presenters || [{ presenter_name: '' }]).map(
                  (presenter: any, index: number) => (
                    <div key={index} className="flex gap-2 items-center">
                      <div className="flex-1 relative">
                        <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          type="text"
                          placeholder="Presenter name"
                          className="pl-10 rounded-full bg-white"
                          value={presenter.presenter_name || ''}
                          onChange={(e) => {
                            const newPresenters = [...(fields.presenters || [])];
                            newPresenters[index] = { presenter_name: e.target.value };
                            handleFieldChange('presenters', newPresenters);
                          }}
                          required
                        />
                      </div>
                      {(fields.presenters || []).length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const newPresenters = (fields.presenters || []).filter(
                              (_: any, i: number) => i !== index
                            );
                            handleFieldChange('presenters', newPresenters);
                          }}
                          className="p-2 h-10 w-10"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  )
                )}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newPresenters = [...(fields.presenters || []), { presenter_name: '' }];
                    handleFieldChange('presenters', newPresenters);
                  }}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Presenter
                </Button>
              </div>
            </div>

            {/* Host company/organization - always shown */}
            <div className="space-y-2">
              <Label htmlFor="webinar-host" className="text-sm text-gray-600">
                Host Company/Organization *
              </Label>
              <div className="relative">
                <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="webinar-host"
                  type="text"
                  placeholder="Organization name"
                  className="pl-10 rounded-full bg-white"
                  value={fields.webinar_host_company_organization || ''}
                  onChange={(e) =>
                    handleFieldChange('webinar_host_company_organization', e.target.value)
                  }
                  required
                />
              </div>
            </div>

            {/* Host company logo - always shown */}
            <div className="space-y-2">
              <Label htmlFor="webinar-host-logo" className="text-sm text-gray-600">
                Host Company Logo *
              </Label>
              <div className="relative">
                <input
                  id="webinar-host-logo"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('webinar_host_company_organization_logo', file);
                    }
                  }}
                  required
                />
                <label
                  htmlFor="webinar-host-logo"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  {fields.webinar_host_company_organization_logo
                    ? 'File selected'
                    : 'Choose logo file'}
                </label>
              </div>
            </div>

            {/* Live webinar fields */}
            {fields.webinar_type === 'live' && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="webinar-date" className="text-sm text-gray-600">
                      Date *
                    </Label>
                    <Input
                      id="webinar-date"
                      type="date"
                      value={fields.date || ''}
                      onChange={(e) => handleFieldChange('date', e.target.value)}
                      className="rounded-full bg-white"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="webinar-time" className="text-sm text-gray-600">
                      Time *
                    </Label>
                    <Input
                      id="webinar-time"
                      type="time"
                      value={fields.time || ''}
                      onChange={(e) => handleFieldChange('time', e.target.value)}
                      className="rounded-full bg-white"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="time-zone" className="text-sm text-gray-600">
                    Time Zone *
                  </Label>
                  <select
                    id="time-zone"
                    className="w-full h-10 px-3 rounded-full border border-input bg-white text-sm"
                    value={fields.time_zone || ''}
                    onChange={(e) => handleFieldChange('time_zone', e.target.value)}
                    required
                  >
                    <option value="">Select timezone</option>
                    <option value="EST">EST</option>
                    <option value="CST">CST</option>
                    <option value="MST">MST</option>
                    <option value="PST">PST</option>
                    <option value="AKST">AKST</option>
                    <option value="HST">HST</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="register-url" className="text-sm text-gray-600">
                    Registration URL (Optional)
                  </Label>
                  <div className="relative">
                    <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="register-url"
                      type="url"
                      placeholder="https://example.com/register"
                      className="pl-10 rounded-full bg-white"
                      value={fields.register_url || ''}
                      onChange={(e) => handleFieldChange('register_url', e.target.value)}
                    />
                  </div>
                </div>
              </>
            )}

            {/* Recorded webinar fields */}
            {fields.webinar_type === 'recorded' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="video-type" className="text-sm text-gray-600">
                    Video Type *
                  </Label>
                  <select
                    id="video-type"
                    className="w-full h-10 px-3 rounded-full border border-input bg-white text-sm"
                    value={fields.video_type || ''}
                    onChange={(e) => handleFieldChange('video_type', e.target.value)}
                    required
                  >
                    <option value="">Select video type</option>
                    <option value="youtube">YouTube URL</option>
                    <option value="vimeo">Vimeo URL</option>
                    <option value="twitter">X Embed</option>
                    <option value="linkedin">LinkedIn Embed</option>
                  </select>
                </div>

                {fields.video_type === 'youtube' && (
                  <div className="space-y-2">
                    <Label htmlFor="youtube-url" className="text-sm text-gray-600">
                      YouTube URL *
                    </Label>
                    <div className="relative">
                      <Video className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="youtube-url"
                        type="url"
                        placeholder="https://youtube.com/watch?v=..."
                        className="pl-10 rounded-full bg-white"
                        value={fields.youtube_url || ''}
                        onChange={(e) => handleFieldChange('youtube_url', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                )}

                {fields.video_type === 'vimeo' && (
                  <div className="space-y-2">
                    <Label htmlFor="vimeo-url" className="text-sm text-gray-600">
                      Vimeo URL *
                    </Label>
                    <div className="relative">
                      <Video className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="vimeo-url"
                        type="url"
                        placeholder="https://vimeo.com/..."
                        className="pl-10 rounded-full bg-white"
                        value={fields.vimeo_url || ''}
                        onChange={(e) => handleFieldChange('vimeo_url', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                )}

                {fields.video_type === 'twitter' && (
                  <div className="space-y-2">
                    <Label htmlFor="twitter-embed" className="text-sm text-gray-600">
                      X (Twitter) Embed *
                    </Label>
                    <Input
                      id="twitter-embed"
                      type="text"
                      placeholder="Paste in the X Embed link only..."
                      className="pl-10 rounded-full bg-white"
                      value={fields.twitter || ''}
                      onChange={(e) => handleFieldChange('twitter', e.target.value)}
                      required
                    />
                  </div>
                )}

                {fields.video_type === 'linkedin' && (
                  <div className="space-y-2">
                    <Label htmlFor="linkedin-embed" className="text-sm text-gray-600">
                      LinkedIn Embed *
                    </Label>
                    <Input
                      id="linkedin-embed"
                      type="text"
                      placeholder="Paste in the LinkedIn Embed link only..."
                      className="pl-10 rounded-full bg-white"
                      value={fields.linkedin_iframe || ''}
                      onChange={(e) => handleFieldChange('linkedin_iframe', e.target.value)}
                      required
                    />
                  </div>
                )}
              </>
            )}
          </>
        );

      case 'video':
        return (
          <>
            <div className="space-y-2">
              <Label className="text-sm text-gray-600">Creators</Label>
              <div className="space-y-2">
                {(fields.creators || [{ creator_name: '' }]).map((creator: any, index: number) => (
                  <div key={index} className="flex gap-2 items-center">
                    <div className="flex-1 relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        type="text"
                        placeholder="Creator name"
                        className="pl-10 rounded-full bg-white"
                        value={creator.creator_name || ''}
                        onChange={(e) => {
                          const newCreators = [...(fields.creators || [])];
                          newCreators[index] = { creator_name: e.target.value };
                          handleFieldChange('creators', newCreators);
                        }}
                      />
                    </div>
                    {(fields.creators || []).length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newCreators = (fields.creators || []).filter(
                            (_: any, i: number) => i !== index
                          );
                          handleFieldChange('creators', newCreators);
                        }}
                        className="p-2 h-10 w-10"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newCreators = [...(fields.creators || []), { creator_name: '' }];
                    handleFieldChange('creators', newCreators);
                  }}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Creator
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="creator-logo" className="text-sm text-gray-600">
                Creator Logo (Optional)
              </Label>
              <div className="relative">
                <input
                  id="creator-logo"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('creator_logo', file);
                    }
                  }}
                />
                <label
                  htmlFor="creator-logo"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  {fields.creator_logo ? 'File selected' : 'Choose logo file'}
                </label>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="video-source" className="text-sm text-gray-600">
                Video Source *
              </Label>
              <select
                id="video-source"
                className="w-full h-10 px-3 rounded-full border border-input bg-white text-sm"
                value={fields.video_source || ''}
                onChange={(e) => handleFieldChange('video_source', e.target.value)}
                required
              >
                <option value="">Select video source</option>
                <option value="youtube">YouTube URL</option>
                <option value="vimeo">Vimeo URL</option>
                <option value="twitter">X Embed</option>
                <option value="linkedin">LinkedIn Embed</option>
              </select>
            </div>

            {fields.video_source === 'youtube' && (
              <div className="space-y-2">
                <Label htmlFor="youtube-url" className="text-sm text-gray-600">
                  YouTube URL *
                </Label>
                <div className="relative">
                  <Video className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="youtube-url"
                    type="url"
                    placeholder="https://youtube.com/watch?v=..."
                    className="pl-10 rounded-full bg-white"
                    value={fields.youtube_id || ''}
                    onChange={(e) => handleFieldChange('youtube_id', e.target.value)}
                    required
                  />
                </div>
              </div>
            )}

            {fields.video_source === 'vimeo' && (
              <div className="space-y-2">
                <Label htmlFor="vimeo-url" className="text-sm text-gray-600">
                  Vimeo URL *
                </Label>
                <div className="relative">
                  <Video className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="vimeo-url"
                    type="url"
                    placeholder="https://vimeo.com/..."
                    className="pl-10 rounded-full bg-white"
                    value={fields.vimeo_id || ''}
                    onChange={(e) => handleFieldChange('vimeo_id', e.target.value)}
                    required
                  />
                </div>
              </div>
            )}

            {fields.video_source === 'twitter' && (
              <div className="space-y-2">
                <Label htmlFor="x-embed" className="text-sm text-gray-600">
                  X (Twitter) Embed *
                </Label>
                <Input
                  id="x-embed"
                  type="text"
                  placeholder="Paste in the X Embed link only..."
                  className="pl-10 rounded-full bg-white"
                  value={fields.x || ''}
                  onChange={(e) => handleFieldChange('x', e.target.value)}
                  required
                />
              </div>
            )}

            {fields.video_source === 'linkedin' && (
              <div className="space-y-2">
                <Label htmlFor="linkedin-embed" className="text-sm text-gray-600">
                  LinkedIn Embed *
                </Label>
                <Input
                  id="linkedin-embed"
                  type="text"
                  placeholder="Paste in the LinkedIn Embed link only..."
                  className="pl-10 rounded-full bg-white"
                  value={fields.linkedin_iframe || ''}
                  onChange={(e) => handleFieldChange('linkedin_iframe', e.target.value)}
                  required
                />
              </div>
            )}
          </>
        );

      case 'course':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="course-provider" className="text-sm text-gray-600">
                Course Provider *
              </Label>
              <div className="relative">
                <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="course-provider"
                  type="text"
                  placeholder="Provider name"
                  className="pl-10 rounded-full bg-white"
                  value={fields.company || ''}
                  onChange={(e) => handleFieldChange('company', e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-logo" className="text-sm text-gray-600">
                Company Logo (Optional)
              </Label>
              <div className="relative">
                <input
                  id="company-logo"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('company_logo', file);
                    }
                  }}
                />
                <label
                  htmlFor="company-logo"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  {fields.company_logo ? 'File selected' : 'Choose logo file'}
                </label>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="course-details-url" className="text-sm text-gray-600">
                Course Details URL *
              </Label>
              <div className="relative">
                <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="course-details-url"
                  type="url"
                  placeholder="https://example.com/course"
                  className="pl-10 rounded-full bg-white"
                  value={fields.course_url || ''}
                  onChange={(e) => handleFieldChange('course_url', e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="signup-url" className="text-sm text-gray-600">
                Sign Up URL (Optional)
              </Label>
              <div className="relative">
                <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="signup-url"
                  type="url"
                  placeholder="https://example.com/enroll"
                  className="pl-10 rounded-full bg-white"
                  value={fields.sign_up_url || ''}
                  onChange={(e) => handleFieldChange('sign_up_url', e.target.value)}
                />
              </div>
            </div>
          </>
        );

      case 'presentation':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="presentation-host" className="text-sm text-gray-600">
                Company/Organization *
              </Label>
              <div className="relative">
                <Building className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="presentation-host"
                  type="text"
                  placeholder="Organization name"
                  className="pl-10 rounded-full bg-white"
                  value={fields.host_company_organization || ''}
                  onChange={(e) => handleFieldChange('host_company_organization', e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-logo" className="text-sm text-gray-600">
                Company/Organization Logo (Optional)
              </Label>
              <div className="relative">
                <input
                  id="company-logo"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('host_company_organization_logo', file);
                    }
                  }}
                />
                <label
                  htmlFor="company-logo"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  {fields.host_company_organization_logo ? 'File selected' : 'Choose logo file'}
                </label>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="pdf-upload" className="text-sm text-gray-600">
                PDF Upload *
              </Label>
              <div className="relative">
                <input
                  id="pdf-upload"
                  type="file"
                  accept=".pdf"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('pdf_upload', file);
                    }
                  }}
                  required
                />
                <label
                  htmlFor="pdf-upload"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <FileText className="h-4 w-4" />
                  {fields.pdf_upload ? 'File selected' : 'Choose PDF file'}
                </label>
              </div>
            </div>
          </>
        );

      case 'whitepaper':
      case 'case-study':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="author" className="text-sm text-gray-600">
                Author (Optional)
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="author"
                  type="text"
                  placeholder="Author name"
                  className="pl-10 rounded-full bg-white"
                  value={fields.author || ''}
                  onChange={(e) => handleFieldChange('author', e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-logo" className="text-sm text-gray-600">
                Company/Organization Logo (Optional)
              </Label>
              <div className="relative">
                <input
                  id="company-logo"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('companyorganization_logo', file);
                    }
                  }}
                />
                <label
                  htmlFor="company-logo"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  {fields.companyorganization_logo ? 'File selected' : 'Choose logo file'}
                </label>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="upload-pdf" className="text-sm text-gray-600">
                Upload PDF *
              </Label>
              <div className="relative">
                <input
                  id="upload-pdf"
                  type="file"
                  accept=".pdf"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('upload_pdf', file);
                    }
                  }}
                  required
                />
                <label
                  htmlFor="upload-pdf"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <FileText className="h-4 w-4" />
                  {fields.upload_pdf ? 'File selected' : 'Choose PDF file'}
                </label>
              </div>
            </div>
          </>
        );

      case 'book':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="author-names" className="text-sm text-gray-600">
                Author Name(s) *
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="author-names"
                  type="text"
                  placeholder="Author name(s)"
                  className="pl-10 rounded-full bg-white"
                  value={fields.author_names || ''}
                  onChange={(e) => handleFieldChange('author_names', e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="purchase-url" className="text-sm text-gray-600">
                Purchase URL *
              </Label>
              <div className="relative">
                <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="purchase-url"
                  type="url"
                  placeholder="https://example.com/book"
                  className="pl-10 rounded-full bg-white"
                  value={fields.purchase_url || ''}
                  onChange={(e) => handleFieldChange('purchase_url', e.target.value)}
                  required
                />
              </div>
            </div>
          </>
        );

      case 'podcast':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="podcast-name" className="text-sm text-gray-600">
                Podcast Name *
              </Label>
              <Input
                id="podcast-name"
                type="text"
                placeholder="Podcast name"
                className="rounded-full bg-white"
                value={fields.podcast_name || ''}
                onChange={(e) => handleFieldChange('podcast_name', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm text-gray-600">Hosts *</Label>
              <div className="space-y-2">
                {(fields.hosts || [{ host_name: '' }]).map((host: any, index: number) => (
                  <div key={index} className="flex gap-2 items-center">
                    <div className="flex-1 relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        type="text"
                        placeholder="Host name"
                        className="pl-10 rounded-full bg-white"
                        value={host.host_name || ''}
                        onChange={(e) => {
                          const newHosts = [...(fields.hosts || [])];
                          newHosts[index] = { host_name: e.target.value };
                          handleFieldChange('hosts', newHosts);
                        }}
                        required
                      />
                    </div>
                    {(fields.hosts || []).length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const newHosts = (fields.hosts || []).filter(
                            (_: any, i: number) => i !== index
                          );
                          handleFieldChange('hosts', newHosts);
                        }}
                        className="p-2 h-10 w-10"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newHosts = [...(fields.hosts || []), { host_name: '' }];
                    handleFieldChange('hosts', newHosts);
                  }}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Host
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="episode-url" className="text-sm text-gray-600">
                Episode URL (Optional)
              </Label>
              <div className="relative">
                <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="episode-url"
                  type="url"
                  placeholder="https://example.com/episode"
                  className="pl-10 rounded-full bg-white"
                  value={fields.episode_url || ''}
                  onChange={(e) => handleFieldChange('episode_url', e.target.value)}
                />
              </div>
            </div>
          </>
        );

      case 'press-release':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="pdf-upload" className="text-sm text-gray-600">
                PDF Upload *
              </Label>
              <div className="relative">
                <input
                  id="pdf-upload"
                  type="file"
                  accept=".pdf"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('upload_pdf', file);
                    }
                  }}
                  required
                />
                <label
                  htmlFor="pdf-upload"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-gray-50 transition-colors"
                >
                  <FileText className="h-4 w-4" />
                  {fields.upload_pdf ? 'File selected' : 'Choose PDF file'}
                </label>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="press-release-url" className="text-sm text-gray-600">
                Press Release URL (Optional)
              </Label>
              <div className="relative">
                <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="press-release-url"
                  type="url"
                  placeholder="https://example.com/press-release"
                  className="pl-10 rounded-full bg-white"
                  value={fields.press_release_url || ''}
                  onChange={(e) => handleFieldChange('press_release_url', e.target.value)}
                />
              </div>
            </div>
          </>
        );

      case 'template':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="creator-name" className="text-sm text-gray-600">
                Creator Name *
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="creator-name"
                  type="text"
                  placeholder="Creator or company name"
                  className="pl-10 rounded-full bg-white"
                  value={fields.creator_name || ''}
                  onChange={(e) => handleFieldChange('creator_name', e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="template-logo" className="text-sm text-gray-600">
                Logo (Optional)
              </Label>
              <div className="relative">
                <input
                  id="template-logo"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('logo', file);
                    }
                  }}
                />
                <label
                  htmlFor="template-logo"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  {fields.logo ? 'File selected' : 'Choose logo file'}
                </label>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="template-url" className="text-sm text-gray-600">
                Preview URL (Optional)
              </Label>
              <div className="relative">
                <ExternalLink className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="template-url"
                  type="url"
                  placeholder="https://example.com/template"
                  className="pl-10 rounded-full bg-white"
                  value={fields.url || ''}
                  onChange={(e) => handleFieldChange('url', e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="template-file" className="text-sm text-gray-600">
                File Upload *
              </Label>
              <div className="relative">
                <input
                  id="template-file"
                  type="file"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleFieldChange('file_upload', file);
                    }
                  }}
                  required
                />
                <label
                  htmlFor="template-file"
                  className="flex items-center justify-center gap-2 w-full h-10 px-4 py-2 bg-white border-2 border-[#5cc8ff] rounded-full text-[#3d405b] text-sm font-medium cursor-pointer hover:bg-[rgba(92,200,255,0.2)] transition-colors"
                >
                  <Upload className="h-4 w-4" />
                  {fields.file_upload ? 'File selected' : 'Choose file'}
                </label>
              </div>
            </div>
          </>
        );

      case 'thought-leadership':
        return (
          <>
            <div className="space-y-2">
              <Label htmlFor="media-type" className="text-sm text-gray-600">
                Media Type
              </Label>
              <select
                id="media-type"
                className="w-full h-10 px-3 rounded-full border border-input bg-white text-sm"
                value={fields.media_type || ''}
                onChange={(e) => handleFieldChange('media_type', e.target.value)}
              >
                <option value="">Select media type</option>
                <option value="image">Image</option>
                <option value="video">Video</option>
              </select>
            </div>

            {fields.media_type === 'image' && (
              <div className="space-y-2 text-sm text-gray-600">
                <p>For image posts, use the main Featured Image upload above.</p>
              </div>
            )}

            {fields.media_type === 'video' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="video-type" className="text-sm text-gray-600">
                    Video Type *
                  </Label>
                  <select
                    id="video-type"
                    className="w-full h-10 px-3 rounded-full border border-input bg-white text-sm"
                    value={fields.video_type || ''}
                    onChange={(e) => handleFieldChange('video_type', e.target.value)}
                    required
                  >
                    <option value="">Select video type</option>
                    <option value="youtube">YouTube</option>
                    <option value="vimeo">Vimeo</option>
                    <option value="twitter">X (Twitter)</option>
                    <option value="linkedin">LinkedIn</option>
                  </select>
                </div>

                {fields.video_type === 'youtube' && (
                  <div className="space-y-2">
                    <Label htmlFor="youtube-url" className="text-sm text-gray-600">
                      YouTube URL
                    </Label>
                    <div className="relative">
                      <Video className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="youtube-url"
                        type="url"
                        placeholder="https://youtube.com/watch?v=..."
                        className="pl-10 rounded-full bg-white"
                        value={fields.youtube_url || ''}
                        onChange={(e) => handleFieldChange('youtube_url', e.target.value)}
                      />
                    </div>
                  </div>
                )}

                {fields.video_type === 'vimeo' && (
                  <div className="space-y-2">
                    <Label htmlFor="vimeo-url" className="text-sm text-gray-600">
                      Vimeo URL
                    </Label>
                    <div className="relative">
                      <Video className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="vimeo-url"
                        type="url"
                        placeholder="https://vimeo.com/..."
                        className="pl-10 rounded-full bg-white"
                        value={fields.vimeo_url || ''}
                        onChange={(e) => handleFieldChange('vimeo_url', e.target.value)}
                      />
                    </div>
                  </div>
                )}

                {fields.video_type === 'twitter' && (
                  <div className="space-y-2">
                    <Label htmlFor="twitter-embed" className="text-sm text-gray-600">
                      X (Twitter) Embed
                    </Label>
                    <Input
                      id="twitter-embed"
                      type="text"
                      placeholder="Paste in the X Embed link only..."
                      className="pl-10 rounded-full bg-white"
                      value={fields.twitter || ''}
                      onChange={(e) => handleFieldChange('twitter', e.target.value)}
                    />
                  </div>
                )}

                {fields.video_type === 'linkedin' && (
                  <div className="space-y-2">
                    <Label htmlFor="linkedin-embed" className="text-sm text-gray-600">
                      LinkedIn Embed
                    </Label>
                    <Input
                      id="linkedin-embed"
                      type="text"
                      placeholder="Paste in the LinkedIn Embed link only..."
                      className="pl-10 rounded-full bg-white"
                      value={fields.linkedin_iframe || ''}
                      onChange={(e) => handleFieldChange('linkedin_iframe', e.target.value)}
                    />
                  </div>
                )}
              </>
            )}
          </>
        );

      default:
        return null;
    }
  };

  const finalConfig = config || defaultConfig;

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-[20px] border">
      <div className="flex items-center gap-2 mb-3">
        <span className={finalConfig.color}>{finalConfig.icon}</span>
        <span className="text-sm font-medium text-gray-700">{finalConfig.title}</span>
      </div>

      {renderFields()}

      {/* Common image caption field - show for all categories if image is uploaded */}
      {imageFile && (
        <div className="space-y-2 pb-3">
          <Label htmlFor="image-caption" className="text-sm text-gray-600">
            Image Caption *
          </Label>
          <Input
            id="image-caption"
            type="text"
            placeholder="Describe the featured image..."
            value={fields.image_caption || ''}
            onChange={(e) => handleFieldChange('image_caption', e.target.value)}
            className="rounded-full bg-white"
            required
          />
        </div>
      )}
    </div>
  );
};
