'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardFooter } from '@/components/ui/card';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import Link from 'next/link';
import Image from 'next/image';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import { PostComments } from './PostComments';
import { VideoEmbed } from '@/components/ui/VideoEmbed';
import { Video, ExternalLink, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  formatPostDate,
  getUserFullName,
  getUserAvatarUrl,
  getCategoryColor,
  useImageObjectFit,
} from './feed-item-helpers';

// Vendor info interface
interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

// Complete interface based on ACF video fields
interface VideoPost extends Post {
  acf?: {
    video_title?: string;
    creators?: Array<{
      creator_name?: string;
    }>;
    creator_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    video_source?: string;
    video_embed_url?: string;
    video_thumbnail?: {
      url?: string;
      [key: string]: unknown;
    };
    youtube_id?: string;
    vimeo_id?: string;
    x?: string; // Twitter/X embed HTML
  };
  vendor_info?: VendorInfo;
  vendor_data?: any;
}

interface FeedItemVideoProps {
  post: VideoPost;
}

export function FeedItemVideo({ post }: FeedItemVideoProps) {
  const [showComments, setShowComments] = useState(false);
  const [excerpt, setExcerpt] = useState('');
  const { objectFitClass, handleImageLoad } = useImageObjectFit();
  const acf = post.acf;
  const decodedTitle = decodeHtml(post.title.rendered);

  useEffect(() => {
    setExcerpt(decodeHtml(post.excerpt?.rendered || ''));
  }, [post.excerpt?.rendered]);

  // Get video-specific data from ACF fields
  const videoTitle = acf?.video_title || decodedTitle;
  const videoSource = acf?.video_source || 'Video';
  const videoEmbedUrl = acf?.video_embed_url;
  const twitterEmbed = acf?.x;

  // Use video thumbnail if available, otherwise fallback to featured image
  const videoThumbnailUrl =
    acf?.video_thumbnail && typeof acf.video_thumbnail === 'object' && acf.video_thumbnail.url
      ? acf.video_thumbnail.url
      : post._embedded?.['wp:featuredmedia']?.[0]?.source_url;

  // Check if this is a vendor post
  const vendorId = post.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);
  const vendorData = post.vendor_info || post.vendor_data || null;

  // Get author information
  const authorData = post._embedded?.author?.[0];
  const authorId = Number(authorData?.id || post.author || 1);

  // Determine display name and avatar based on vendor vs personal post
  let displayName: string;
  let avatarUrl: string;
  let displayId: number;

  if (isVendorPost && vendorData) {
    // Use vendor information
    displayName = vendorData.name || 'Vendor';
    avatarUrl = vendorData.logo_url || '';
    displayId = vendorData.id;
  } else {
    // Use regular author information
    displayName = getUserFullName(authorData);
    avatarUrl = getUserAvatarUrl(authorData);
    displayId = authorId;
  }
  const postDate = formatPostDate(post.date);
  const categoryColor = getCategoryColor('videos');

  return (
    <Card className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200 w-full max-w-full">
      <Link href={`/posts/${post.slug}`} className="cursor-pointer">
        <div className="relative">
          {/* Video Embed - Only show YouTube and Vimeo embeds, others use thumbnail */}
          {videoEmbedUrl &&
          (videoEmbedUrl.includes('youtube.com/embed/') ||
            videoEmbedUrl.includes('player.vimeo.com/video/')) ? (
            <div className="aspect-video overflow-hidden bg-muted">
              <VideoEmbed
                embedUrl={videoEmbedUrl}
                videoSource={videoSource}
                title={videoTitle}
                className="h-full"
              />
            </div>
          ) : (
            /* Fallback to video thumbnail for non-YouTube/Vimeo or no embed URL */
            videoThumbnailUrl && (
              <div className="aspect-video overflow-hidden bg-muted relative">
                <Image
                  src={videoThumbnailUrl}
                  alt={videoTitle}
                  width={1200}
                  height={675}
                  className={`${objectFitClass} w-full h-full post-image-glow`}
                  priority={false}
                  onLoad={handleImageLoad}
                />
                <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group-hover:bg-opacity-40 transition-all">
                  <div className="bg-white bg-opacity-90 rounded-full p-4 group-hover:bg-opacity-100 transition-all">
                    <Play className="h-8 w-8 text-dark-text ml-1" />
                  </div>
                </div>
                <div className="absolute top-2 right-2">
                  <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-black bg-opacity-70 text-white">
                    <Video className="h-3 w-3 mr-1" />
                    Video
                  </span>
                </div>
              </div>
            )
          )}
        </div>

        <CardHeader className="flex-row items-center gap-4 space-y-0 bg-white dark:bg-gray-900 pb-2">
          {isVendorPost && vendorData ? (
            // Show vendor logo and name for vendor posts
            <>
              <div className="h-10 w-10 ring-2 ring-[#5cc8ff]/20 rounded-full overflow-hidden">
                <img
                  src={avatarUrl || '/images/avatar-placeholder.svg'}
                  alt={displayName}
                  className="h-10 w-10 object-cover rounded-full"
                />
              </div>
              <div className="flex flex-col">
                <div className="font-semibold text-[#3d405b] dark:text-gray-100">{displayName}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                </div>
              </div>
            </>
          ) : (
            // Show user avatar and name for personal posts
            <>
              <UserAvatarWithRank
                userId={displayId}
                avatarUrl={avatarUrl}
                displayName={displayName}
                size="h-10 w-10"
                containerSize="w-12 h-12"
                userRoles={(authorData?.roles as string[]) || []}
              />
              <div className="flex flex-col">
                <div className="font-semibold text-[#3d405b] dark:text-gray-100">{displayName}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                </div>
              </div>
            </>
          )}
        </CardHeader>

        <CardContent className="p-4 md:p-6 pt-2">
          <div
            className="inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full capitalize mb-4"
            style={{
              backgroundColor: categoryColor,
              color: 'white',
            }}
          >
            <Video className="h-4 w-4 text-white" />
            Video
          </div>
          <h3 className="text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-2">
            {videoTitle}
          </h3>
          <div className="prose prose-sm dark:prose-invert line-clamp-4">
            <div
              className="text-[#3d405b] dark:text-gray-300 mb-4 last:mb-0"
              dangerouslySetInnerHTML={{ __html: excerpt }}
            />
          </div>
        </CardContent>
      </Link>

      {/* Action Buttons */}
      <div className="px-6 pb-8 flex gap-3 flex-wrap">
        <Button
          asChild
          variant="outline"
          size="sm"
          className="w-fit text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
        >
          <Link href={`/posts/${post.slug}`}>
            <ExternalLink className="h-4 w-4 mr-2" />
            View Post
          </Link>
        </Button>

        {/* For Twitter videos, show external link button */}
        {videoSource === 'twitter' &&
          twitterEmbed &&
          (() => {
            // Extract Twitter URL from embed HTML
            const twitterUrlMatch = twitterEmbed.match(/href="(https:\/\/twitter\.com\/[^"]+)"/);
            const twitterUrl = twitterUrlMatch ? twitterUrlMatch[1] : null;

            return twitterUrl ? (
              <Button asChild size="sm">
                <a href={twitterUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View on X/Twitter
                </a>
              </Button>
            ) : null;
          })()}
      </div>

      <CardFooter className="flex justify-center border-t p-4 bg-gray-100/50 dark:bg-gray-900">
        <PostInteractions
          postId={post.id}
          authorId={authorId}
          initialUpvotes={(() => {
            if (post.upvotes && typeof post.upvotes.count === 'number') {
              return post.upvotes.count;
            }
            return Number(post.meta?._upvotes) || 0;
          })()}
          initialCommentCount={
            Number(post.total_comments) || Number(post.meta?._comments_count) || 0
          }
          postTitle={videoTitle}
          layout="full-width"
          onCommentsToggle={setShowComments}
        />
      </CardFooter>

      <PostComments
        postId={post.id}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        commentCount={Number(post.total_comments) || Number(post.meta?._comments_count) || 0}
      />
    </Card>
  );
}
