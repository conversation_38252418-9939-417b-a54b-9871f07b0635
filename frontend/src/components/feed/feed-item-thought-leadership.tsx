'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { FileText, ExternalLink } from 'lucide-react';
import { formatPostDate, getCategoryColor, useImageObjectFit } from './feed-item-helpers';
import Image from 'next/image';
import { FeedItemHeader } from './shared/FeedItemHeader';
import { CategoryBadge } from './shared/CategoryBadge';
import { FeedItemFooter } from './shared/FeedItemFooter';
import { usePostData } from './shared/usePostData';

// Vendor info interface
interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

// Complete interface based on ACF fields for thought leadership
interface ThoughtLeadershipPost extends Post {
  acf?: {
    // Thought Leadership fields (featured_image removed - now uses WordPress featured_image)
    // Sponsorship fields
    sponsored?: boolean;
    background?: string;
    logo_background?: string;
    thank_you_banner?: string;
    sponsorship_logo?: {
      url?: string;
      sizes?: {
        thumbnail?: string;
        medium?: string;
        large?: string;
      };
    };
    [key: string]: unknown;
  };
  vendor_info?: VendorInfo;
  vendor_data?: any;
  is_sponsored?: boolean;
}

interface FeedItemThoughtLeadershipProps {
  post: ThoughtLeadershipPost;
}

export function FeedItemThoughtLeadership({ post }: FeedItemThoughtLeadershipProps) {
  const [excerpt, setExcerpt] = useState('');
  const acf = post.acf;
  const decodedTitle = decodeHtml(post.title.rendered);

  useEffect(() => {
    setExcerpt(decodeHtml(post.excerpt?.rendered || ''));
  }, [post.excerpt?.rendered]);

  // Check if this is a sponsored post and get sponsor data
  const isSponsored = post.is_sponsored || acf?.sponsored === true;
  const sponsorLogo = acf?.sponsorship_logo?.url || acf?.sponsorship_logo?.sizes?.medium;
  const backgroundColor = acf?.background || '#f5c842'; // Default yellow
  const logoBackgroundColor = acf?.logo_background || '#ffffff';
  const thankYouText = acf?.thank_you_banner || 'Thank you for supporting our community.';

  // Use shared hook to get common post data
  const { isVendorPost, vendorData, authorData, authorId, initialUpvotes, initialCommentCount } =
    usePostData(post);

  // Use WordPress featured media (ACF featured_image field removed)
  const thoughtLeadershipImageUrl = post._embedded?.['wp:featuredmedia']?.[0]?.source_url;

  // Use image object fit hook
  const { objectFitClass, handleImageLoad } = useImageObjectFit();

  // Check for video content from ACF fields
  const mediaType = acf?.media_type;
  const videoType = acf?.video_type;
  const youtubeUrl = acf?.youtube_url;
  const vimeoUrl = acf?.vimeo_url;
  const twitterEmbed = acf?.twitter;
  const linkedinEmbed = acf?.linkedin_iframe;

  const postDate = formatPostDate(post.date);
  const categoryColor = getCategoryColor('thought-leadership');

  // Function to render video content
  const renderVideoContent = () => {
    if (mediaType !== 'video') return null;

    switch (videoType) {
      case 'youtube':
        if (!youtubeUrl) return null;
        const youtubeId = extractYouTubeId(youtubeUrl as string);
        if (!youtubeId) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted">
            <iframe
              src={`https://www.youtube.com/embed/${youtubeId}`}
              title="YouTube video player"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              className="w-full h-full"
            />
          </div>
        );

      case 'vimeo':
        if (!vimeoUrl) return null;
        const vimeoId = extractVimeoId(vimeoUrl as string);
        if (!vimeoId) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted">
            <iframe
              src={`https://player.vimeo.com/video/${vimeoId}`}
              title="Vimeo video player"
              allow="autoplay; fullscreen; picture-in-picture"
              allowFullScreen
              className="w-full h-full"
            />
          </div>
        );

      case 'twitter':
        if (!twitterEmbed) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted flex items-center justify-center">
            <div dangerouslySetInnerHTML={{ __html: twitterEmbed }} />
          </div>
        );

      case 'linkedin':
        if (!linkedinEmbed) return null;
        return (
          <div className="aspect-video overflow-hidden bg-muted flex items-center justify-center">
            <div dangerouslySetInnerHTML={{ __html: linkedinEmbed }} />
          </div>
        );

      default:
        return null;
    }
  };

  // Helper functions to extract video IDs
  const extractYouTubeId = (url: string) => {
    const match = url.match(
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    );
    return match ? match[1] : null;
  };

  const extractVimeoId = (url: string) => {
    const match = url.match(/(?:vimeo\.com\/)([0-9]+)/);
    return match ? match[1] : null;
  };

  return (
    <Card
      className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200 w-full max-w-full"
      style={isSponsored ? { backgroundColor: backgroundColor } : {}}
    >
      {/* Sponsor Banner */}
      {isSponsored && (
        <div className="px-6 py-3 flex items-center justify-between border-b border-black/10">
          <div className="flex items-center gap-2">
            <span className="text-sm font-bold text-gray-800">Founding Sponsor Post</span>
            <span className="text-sm italic text-gray-700">{thankYouText}</span>
          </div>
          {sponsorLogo && (
            <div
              className="h-8 w-auto rounded px-2 py-1"
              style={{ backgroundColor: logoBackgroundColor }}
            >
              <img src={sponsorLogo} alt="Sponsor Logo" className="h-6 w-auto object-contain" />
            </div>
          )}
        </div>
      )}

      <div className={isSponsored ? 'bg-white' : ''}>
        <Link href={`/posts/${post.slug}`} className="cursor-pointer">
          <div className="relative">
            {/* Show video content if it's a video type */}
            {mediaType === 'video'
              ? renderVideoContent()
              : /* Show image for image type or fallback */
                thoughtLeadershipImageUrl && (
                  <div>
                    <div className="aspect-video overflow-hidden bg-muted">
                      <Image
                        src={thoughtLeadershipImageUrl}
                        alt={decodedTitle}
                        width={1200}
                        height={675}
                        className={`${objectFitClass} w-full h-full post-image-glow`}
                        onLoad={handleImageLoad}
                        priority={false}
                      />
                    </div>
                    {/* Image caption */}
                    {acf?.image_caption && (
                      <div className="px-4 py-2">
                        <p className="text-xs text-gray-400 text-right italic">
                          {acf.image_caption as string}
                        </p>
                      </div>
                    )}
                  </div>
                )}
          </div>

          <FeedItemHeader
            authorData={authorData}
            vendorData={vendorData}
            isVendorPost={isVendorPost}
            postDate={postDate}
            authorId={authorId}
          />

          <CardContent className="p-4 md:p-6 pt-2">
            <CategoryBadge
              categoryName="Thought Leadership"
              categoryColor={categoryColor}
              Icon={FileText}
            />
            <h3 className="text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-2">
              {decodedTitle}
            </h3>
            <div className="prose prose-sm dark:prose-invert line-clamp-4">
              <p
                className="text-[#3d405b] dark:text-gray-300 mb-4 last:mb-0"
                dangerouslySetInnerHTML={{ __html: excerpt }}
              />
            </div>
          </CardContent>
        </Link>

        {/* View Post Button - Outside of main link */}
        <div className="px-6 pb-8">
          <Button
            asChild
            variant="outline"
            size="sm"
            className="w-fit text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
          >
            <Link href={`/posts/${post.slug}`}>
              <ExternalLink className="h-4 w-4 mr-2" />
              View Post
            </Link>
          </Button>
        </div>

        <FeedItemFooter
          postId={post.id}
          authorId={authorId}
          postTitle={decodedTitle}
          postSlug={post.slug}
          postExcerpt={post.excerpt?.rendered}
          initialUpvotes={initialUpvotes}
          initialCommentCount={initialCommentCount}
          layout="full-width"
        />
      </div>
    </Card>
  );
}
