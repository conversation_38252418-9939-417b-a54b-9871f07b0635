'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { X, Loader2 } from 'lucide-react';
import { useAuthStatus } from '@/hooks/queries/useAuth';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';

// Component to handle comment content with client-side rendering
function CommentContent({ content }: { content: string }) {
  const [html, setHtml] = useState('');

  useEffect(() => {
    setHtml(content);
  }, [content]);

  return <div className="text-sm text-gray-700" dangerouslySetInnerHTML={{ __html: html }} />;
}

interface AuthorInfo {
  id: string | number;
  name: string;
  username: string;
  profile_picture: string;
  avatar_urls?: {
    [key: string]: string;
  };
  url: string;
}

interface Comment {
  id: number;
  author_name: string;
  author_id?: number;
  content: {
    rendered: string;
  };
  date: string;
  author_avatar_urls?: {
    [key: string]: string;
  };
  author_info?: AuthorInfo | undefined;
  isOptimistic?: boolean;
}

interface PostCommentsProps {
  postId: string | number;
  isOpen: boolean;
  onClose: () => void;
  commentCount: number;
}

export function PostComments({ postId, isOpen, onClose, commentCount }: PostCommentsProps) {
  const { user, isLoggedIn: isAuthenticated } = useAuthStatus();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(false);
  const [commentContent, setCommentContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [commentError, setCommentError] = useState<string | null>(null);

  const fetchComments = useCallback(
    async (forceFresh = false) => {
      try {
        const url = forceFresh
          ? `/api/wp-proxy/posts/${postId}/comments?_=${Date.now()}`
          : `/api/wp-proxy/posts/${postId}/comments`;

        const response = await fetch(url, {
          credentials: 'include',
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch comments');
        }

        if (data.success) {
          if (!Array.isArray(data.comments)) {
            setComments([]);
            return;
          }

          const sortedComments = data.comments.sort(
            (a: Comment, b: Comment) => new Date(b.date).getTime() - new Date(a.date).getTime()
          );
          const fetchedComments = sortedComments.map((c: Comment) => ({
            ...c,
            isOptimistic: false,
          }));

          setComments(fetchedComments);
        }
      } catch (error) {
        console.error('Error loading comments:', error);
      }
    },
    [postId]
  );

  // Load comments when opened
  useEffect(() => {
    if (isOpen && comments.length === 0) {
      setLoading(true);
      fetchComments().finally(() => setLoading(false));
    }
  }, [isOpen, comments.length, fetchComments]);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!commentContent.trim() || !isAuthenticated) return;

    // Create optimistic comment
    const optimisticComment: Comment = {
      id: Date.now(),
      author_name: user?.display_name || user?.username || 'You',
      author_id: user?.id || 0,
      content: { rendered: commentContent },
      date: new Date().toISOString(),
      author_avatar_urls: user?.avatar_urls || {},
      author_info: user
        ? {
            id: user.id,
            name: user.display_name || user.username || 'You',
            username: user.username || '',
            profile_picture: user.avatar_urls?.['96'] || '',
            avatar_urls: user.avatar_urls || {},
            url: '',
          }
        : undefined,
      isOptimistic: true,
    };

    // Add optimistic comment
    setComments([optimisticComment, ...comments]);
    const originalCommentContent = commentContent;
    setCommentContent('');
    setIsSubmitting(true);
    setCommentError('');

    try {
      const response = await fetch(`/api/wp-proxy/posts/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'same-origin',
        body: JSON.stringify({
          content: originalCommentContent,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to post comment');
      }

      if (data.success) {
        // Remove optimistic comment and refetch
        setComments((prev) => prev.filter((c) => c.id !== optimisticComment.id));
        await fetchComments(true);
      }
    } catch (error) {
      // Remove optimistic comment on error
      setComments((prev) => prev.filter((c) => c.id !== optimisticComment.id));
      setCommentContent(originalCommentContent);
      setCommentError(error instanceof Error ? error.message : 'Failed to post comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="border-t border-gray-200 bg-white">
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h4 className="text-lg font-medium">Comments ({commentCount})</h4>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={18} />
          </button>
        </div>

        {/* New Comment Form */}
        {isAuthenticated ? (
          <form onSubmit={handleSubmitComment} className="mb-6">
            <div className="flex items-start gap-3 mb-2">
              <UserAvatarWithRank
                userId={user?.id || 0}
                avatarUrl={user?.avatar_urls?.['96'] || '/images/avatar-placeholder.svg'}
                displayName={user?.display_name || user?.username || 'You'}
                size="h-8 w-8"
                containerSize="w-10 h-10"
                userRoles={user?.roles || []}
              />
              <Textarea
                placeholder="Write a comment..."
                value={commentContent}
                onChange={(e) => setCommentContent(e.target.value)}
                className="min-h-[80px] flex-1"
              />
            </div>

            {commentError && (
              <div className="text-red-500 text-sm my-2 p-2 bg-red-50 rounded">
                <strong>Error:</strong> {commentError}
              </div>
            )}

            <div className="flex justify-end mt-2">
              <Button
                type="submit"
                disabled={isSubmitting || !commentContent.trim()}
                className="bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff] font-extrabold"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Posting...
                  </>
                ) : (
                  'Post Comment'
                )}
              </Button>
            </div>
          </form>
        ) : (
          <div className="bg-gray-50 p-4 rounded-md mb-6 text-center">
            <p className="text-gray-700">Please sign in to post a comment.</p>
          </div>
        )}

        {/* Comments List */}
        <div className="space-y-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-600">Loading comments...</span>
            </div>
          ) : comments.length > 0 ? (
            comments.map((comment) => (
              <div key={comment.id} className="flex gap-3">
                <UserAvatarWithRank
                  userId={comment.author_id || 0}
                  avatarUrl={comment.author_avatar_urls?.['96'] || '/images/avatar-placeholder.svg'}
                  displayName={comment.author_name}
                  size="h-8 w-8"
                  containerSize="w-10 h-10"
                  userRoles={[]} // TODO: Add comment author roles to API response
                />
                <div className="flex-1">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{comment.author_name}</span>
                      <span className="text-xs text-gray-500">
                        {new Date(comment.date).toLocaleDateString()}
                      </span>
                    </div>
                    <CommentContent content={comment.content?.rendered || ''} />
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center text-gray-500 py-4">
              No comments yet. Be the first to comment!
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
