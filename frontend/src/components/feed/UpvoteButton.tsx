'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { useNotifications } from '@/contexts/NotificationContext';
import { useAuthStatus } from '@/hooks/queries/useAuth';
import { useUpvotes } from '@/contexts/UpvoteContext';

interface UpvoteButtonProps {
  postId: number;
  authorId: number;
  initialCount?: number;
  postTitle: string;
  className?: string;
}

// Create a cache to track sent notifications across all components
// This persists between renders and component instances
const notificationSentCache = new Set<string>();

export function UpvoteButton({
  postId,
  authorId,
  initialCount = 0,
  postTitle,
  className,
}: UpvoteButtonProps) {
  const [upvoted, setUpvoted] = useState(false);
  const [count, setCount] = useState(initialCount);
  const [isLoading, setIsLoading] = useState(false);
  const { sendNotification } = useNotifications();
  const { isLoggedIn, user } = useAuthStatus();
  const { getUpvoteStatus, toggleUpvote } = useUpvotes();

  // Track if we've already sent a notification for this post from this user
  const hasNotifiedRef = useRef(false);

  // Check if notification was already sent in the current session
  useEffect(() => {
    if (user?.id) {
      const cacheKey = `notification:upvote:${user.id}:${postId}`;
      hasNotifiedRef.current = notificationSentCache.has(cacheKey);
    }
  }, [user?.id, postId]);

  // Get upvote status from context only - no individual fetching
  useEffect(() => {
    if (isLoggedIn && user?.id) {
      // Only use cached data, don't trigger individual fetches
      // The Feed component will handle batch fetching
      const cachedStatus = getUpvoteStatus(postId);
      if (cachedStatus) {
        setUpvoted(cachedStatus.upvoted);
        setCount(cachedStatus.count);
      }
    }
  }, [isLoggedIn, user?.id, postId, getUpvoteStatus]);

  // Update when upvote data changes in the context
  useEffect(() => {
    // This will run whenever the context has new data for this post
    const checkForUpdates = () => {
      const cachedStatus = getUpvoteStatus(postId);
      if (cachedStatus) {
        setUpvoted(cachedStatus.upvoted);
        setCount(cachedStatus.count);
      }
    };

    // Check immediately
    checkForUpdates();

    // Set up an interval to check periodically (but not too often)
    const intervalId = setInterval(checkForUpdates, 5000);

    return () => clearInterval(intervalId);
  }, [postId, getUpvoteStatus]);

  const handleUpvote = async () => {
    if (!isLoggedIn || isLoading) {
      return;
    }

    setIsLoading(true);

    try {
      // Use the context to handle the upvote (includes optimistic updates)
      const result = await toggleUpvote(postId);

      // Update local state with the result
      setUpvoted(result.upvoted);
      setCount(result.count);

      // Send notification if upvote was added (not removed) AND we haven't already notified
      // AND it's not the post author upvoting their own post
      if (result.upvoted && user?.id !== authorId && !hasNotifiedRef.current) {
        const notificationContent = `${user?.display_name || user?.username} upvoted your post "${postTitle}"`;
        await sendNotification(authorId, 'upvote', notificationContent, postId, 'post');

        // Mark that we've sent a notification for this post/user combination
        if (user?.id) {
          const cacheKey = `notification:upvote:${user.id}:${postId}`;
          notificationSentCache.add(cacheKey);
          hasNotifiedRef.current = true;
        }
      }
    } catch (error) {
      console.error('Error toggling upvote:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleUpvote}
      disabled={!isLoggedIn || isLoading}
      className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-300 transform-none scale-100 ${
        upvoted
          ? 'bg-gradient-to-r from-blue-500 to-sky-600 text-white shadow-lg shadow-blue-500/25 hover:from-blue-600 hover:to-sky-700 hover:shadow-blue-500/40'
          : 'bg-gray-100 text-gray-600 hover:bg-gradient-to-r hover:from-blue-500 hover:to-sky-600 hover:text-white hover:shadow-lg hover:shadow-blue-500/25'
      } ${className} ${!isLoggedIn ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
    >
      <Image
        src="/images/icons/icon-upvote.svg"
        alt="Upvote"
        width={16}
        height={16}
        className={`h-4 w-4 transition-all duration-300 ${
          isLoading ? 'animate-pulse' : ''
        } ${upvoted ? 'brightness-0 invert' : ''}`}
      />
      <span className="text-sm font-medium transition-all duration-300">
        {upvoted ? 'Upvoted' : 'Upvote'}
      </span>
      {count > 0 && (
        <span
          className={`text-xs px-2 py-1 rounded-full font-semibold transition-all duration-300 ${
            upvoted ? 'bg-white text-blue-600 shadow-sm' : 'bg-white/70 text-gray-700'
          }`}
        >
          {count}
        </span>
      )}
    </button>
  );
}
