'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardFooter } from '@/components/ui/card';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import Link from 'next/link';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import { PostComments } from './PostComments';
import { Button } from '@/components/ui/button';
import { ExternalLink, Newspaper } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import { useImageObjectFit } from './feed-item-helpers';

// Vendor info interface
interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

// Complete interface based on ACF fields for news posts
interface NewsPost extends Post {
  acf?: {
    url?: string;
    image_caption?: string;
  };
  vendor_info?: VendorInfo;
  vendor_data?: any;
}

interface FeedItemNewsProps {
  post: NewsPost;
}

export function FeedItemNews({ post }: FeedItemNewsProps) {
  const [showComments, setShowComments] = useState(false);
  const [excerpt, setExcerpt] = useState('');
  const acf = post.acf;
  const decodedTitle = decodeHtml(post.title.rendered);
  const { objectFitClass, handleImageLoad } = useImageObjectFit();

  useEffect(() => {
    setExcerpt(decodeHtml(post.excerpt?.rendered || ''));
  }, [post.excerpt?.rendered]);

  // Get news-specific data from ACF fields
  const externalArticleUrl = acf?.url;

  // Use featured image
  const newsImageUrl = post._embedded?.['wp:featuredmedia']?.[0]?.source_url;

  // Check if this is a vendor post
  const vendorId = post.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);
  const vendorData = post.vendor_info || post.vendor_data || null;

  // Get author information
  const authorData = post._embedded?.author?.[0];
  const authorId = Number(authorData?.id || post.author || 1);

  // Helper function to format date
  const formatPostDate = (date: string): string => {
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  };

  // Helper function to get user's full name
  const getUserFullName = (author?: any): string => {
    if (!author) return 'Anonymous';
    return author.name || 'Anonymous';
  };

  // Helper function to get user's avatar URL
  const getUserAvatarUrl = (author?: any): string => {
    if (!author) return '';
    return author.acf?.profile_picture?.url || author.avatar_urls?.['96'] || '';
  };

  // Determine display name and avatar based on vendor vs personal post
  let displayName: string;
  let avatarUrl: string;
  let displayId: number;

  if (isVendorPost && vendorData) {
    // Use vendor information
    displayName = vendorData.name || 'Vendor';
    avatarUrl = vendorData.logo_url || '';
    displayId = vendorData.id;
  } else {
    // Use regular author information
    displayName = getUserFullName(authorData);
    avatarUrl = getUserAvatarUrl(authorData);
    displayId = authorId;
  }
  const postDate = formatPostDate(post.date);
  const categoryColor = '#ff5ce0'; // Pink for news

  return (
    <Card className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200 w-full max-w-full">
      <Link href={`/posts/${post.slug}`} className="cursor-pointer">
        <div className="relative">
          {newsImageUrl && (
            <div className="aspect-video overflow-hidden bg-muted">
              <Image
                src={newsImageUrl}
                alt={decodedTitle}
                width={1200}
                height={675}
                className={`${objectFitClass} w-full h-full post-image-glow`}
                onLoad={handleImageLoad}
                priority={false}
              />
            </div>
          )}
        </div>

        <CardHeader className="flex-row items-center gap-4 space-y-0 bg-white dark:bg-gray-900 pb-2">
          {isVendorPost && vendorData ? (
            // Show vendor logo and name for vendor posts
            <>
              <div className="h-10 w-10 ring-2 ring-[#5cc8ff]/20 rounded-full overflow-hidden">
                <img
                  src={avatarUrl || '/images/avatar-placeholder.svg'}
                  alt={displayName}
                  className="h-10 w-10 object-cover rounded-full"
                />
              </div>
              <div className="flex flex-col">
                <div className="font-semibold text-[#3d405b] dark:text-gray-100">{displayName}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                </div>
              </div>
            </>
          ) : (
            // Show user avatar and name for personal posts
            <>
              <UserAvatarWithRank
                userId={displayId}
                avatarUrl={avatarUrl}
                displayName={displayName}
                size="h-10 w-10"
                containerSize="w-12 h-12"
                userRoles={(authorData?.roles as string[]) || []}
              />
              <div className="flex flex-col">
                <div className="font-semibold text-[#3d405b] dark:text-gray-100">{displayName}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                </div>
              </div>
            </>
          )}
        </CardHeader>

        <CardContent className="p-4 md:p-6 pt-2">
          <div
            className="inline-flex items-center gap-1 md:gap-1.5 text-xs md:text-sm font-bold px-2 md:px-3 py-1 rounded-full capitalize mb-3 md:mb-4"
            style={{
              backgroundColor: categoryColor,
              color: 'white',
            }}
          >
            <Newspaper className="h-3 w-3 md:h-4 md:w-4 text-white" />
            News
          </div>
          <h3 className="text-xl md:text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-2">
            {decodedTitle}
          </h3>
          <div className="prose prose-sm dark:prose-invert line-clamp-3 md:line-clamp-4">
            <div
              className="text-sm md:text-base text-[#3d405b] dark:text-gray-300 mb-3 md:mb-4 last:mb-0"
              dangerouslySetInnerHTML={{ __html: excerpt }}
            />
          </div>
        </CardContent>
      </Link>

      {/* External Article Button - Outside of main link */}
      {externalArticleUrl && (
        <div className="px-4 md:px-6 pb-6 md:pb-8">
          <Button
            asChild
            variant="outline"
            size="sm"
            className="w-fit text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
          >
            <a href={externalArticleUrl} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              View Article
            </a>
          </Button>
        </div>
      )}

      <CardFooter className="flex justify-center border-t p-3 md:p-4 bg-gray-100/50 dark:bg-gray-900">
        <PostInteractions
          postId={post.id}
          authorId={authorId}
          initialUpvotes={(() => {
            if (post.upvotes && typeof post.upvotes.count === 'number') {
              return post.upvotes.count;
            }
            return Number(post.meta?._upvotes) || 0;
          })()}
          initialCommentCount={
            Number(post.total_comments) || Number(post.meta?._comments_count) || 0
          }
          postTitle={decodedTitle}
          layout="full-width"
          onCommentsToggle={setShowComments}
        />
      </CardFooter>

      <PostComments
        postId={post.id}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        commentCount={Number(post.total_comments) || Number(post.meta?._comments_count) || 0}
      />
    </Card>
  );
}
