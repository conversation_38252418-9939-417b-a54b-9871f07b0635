import { formatDistanceToNow } from 'date-fns';
import {
  FileText,
  Newspaper,
  Users,
  Briefcase,
  Presentation,
  Video,
  Mic,
  Play,
  GraduationCap,
  Download,
  Lightbulb,
  BookOpen,
} from 'lucide-react';
import { useState } from 'react';

/**
 * Helper function to get category badge styles based on post category
 */
export function getCategoryStyles(category?: string): string {
  if (!category) return 'bg-[#5cc8ff] hover:bg-[#43b0e6]';

  const name = category.toLowerCase().trim();

  // News category
  if (name === 'news' || name.includes('news')) {
    return 'bg-[#ff5ce0] hover:bg-[#e54cc7]';
  }

  // Resource categories - comprehensive list using orange color
  const resourceCategories = [
    'blog-post',
    'blog-posts',
    'blog_post',
    'blog_posts',
    'blog_feeds',
    'book',
    'books',
    'case-study',
    'case-studies',
    'case_study',
    'case_studies',
    'course',
    'courses',
    'presentation',
    'presentations',
    'template',
    'templates',
    'video',
    'videos',
    'webinar',
    'webinars',
    'whitepaper',
    'whitepapers',
    'press-release',
    'press-releases',
    'press_release',
    'press_releases',
    'resource',
    'resources',
    'tool',
    'tools',
    'guide',
    'guides',
    'tutorial',
    'tutorials',
    'download',
    'downloads',
  ];

  if (
    resourceCategories.includes(name) ||
    name.includes('resource') ||
    name.includes('tool') ||
    name.includes('guide') ||
    name.includes('tutorial') ||
    name.includes('download')
  ) {
    return 'bg-[#ffa15c] hover:bg-[#e68943]';
  }

  // Thought Leadership and Questions
  if (
    name === 'thought-leadership' ||
    name === 'thought_leadership' ||
    name === 'forum_question' ||
    name.includes('thought') ||
    name === 'questions' ||
    name.includes('question') ||
    name.includes('opinion') ||
    name.includes('insight')
  ) {
    return 'bg-[#5cc8ff] hover:bg-[#43b0e6]';
  }

  // Events
  if (
    name === 'events' ||
    name.includes('event') ||
    name.includes('conference') ||
    name.includes('meetup')
  ) {
    return 'bg-[#1dd05b] hover:bg-[#17b749]';
  }

  // Jobs
  if (
    name === 'jobs' ||
    name.includes('job') ||
    name.includes('career') ||
    name.includes('hiring') ||
    name.includes('employment')
  ) {
    return 'bg-[#ff625c] hover:bg-[#e54943]';
  }

  // Default fallback
  return 'bg-[#5cc8ff] hover:bg-[#43b0e6]';
}

/**
 * Legacy function for backwards compatibility - returns appropriate colors for each category
 * @deprecated Use getCategoryStyles instead for proper Tailwind classes
 */
export function getCategoryColor(category?: string): string {
  if (!category) return 'hsl(var(--foreground))';

  // Thought leadership categories
  const isThoughtLeadership = [
    'thought-leadership',
    'thought_leadership',
    'forum_question',
  ].includes(category);
  if (isThoughtLeadership) return 'hsl(var(--primary))';

  // Resource categories (should be orange #ffa15c)
  const resourceCategories = [
    'blog-post',
    'blog-posts',
    'blog_post',
    'blog_posts',
    'blog_feeds',
    'book',
    'books',
    'case-study',
    'case-studies',
    'case_study',
    'case_studies',
    'course',
    'courses',
    'podcasts',
    'presentation',
    'presentations',
    'press-release',
    'press-releases',
    'press_release',
    'press_releases',
    'template',
    'templates',
    'video',
    'videos',
    'webinar',
    'webinars',
    'whitepaper',
    'whitepapers',
    'resource',
    'resources',
    'tool',
    'tools',
    'guide',
    'guides',
    'tutorial',
    'tutorials',
    'download',
    'downloads',
  ];
  if (resourceCategories.includes(category)) return '#ffa15c';

  // Default fallback
  return 'hsl(var(--foreground))';
}

/**
 * Helper function to get category icon
 */
export function getCategoryIcon(category: string) {
  switch (category?.toLowerCase()) {
    case 'thought-leadership':
    case 'thought_leadership':
      return Lightbulb;
    case 'news':
      return Newspaper;
    case 'people-on-the-move':
      return Users;
    case 'jobs':
      return Briefcase;
    case 'events':
      return Users;
    case 'courses':
    case 'course':
      return GraduationCap;
    case 'podcasts':
    case 'podcast':
      return Mic;
    case 'videos':
    case 'video':
      return Play;
    case 'books':
    case 'book':
      return BookOpen;
    case 'webinars':
    case 'webinar':
      return Video;
    case 'presentations':
    case 'presentation':
      return Presentation;
    case 'press-releases':
    case 'press-release':
      return Newspaper;
    case 'blog-posts':
    case 'blog-post':
      return FileText;
    case 'whitepapers':
    case 'whitepaper':
    case 'case-studies':
    case 'case-study':
    case 'templates':
    case 'template':
      return FileText;
    case 'resources':
      return Download;
    default:
      return FileText;
  }
}

/**
 * Helper function to format the post date
 */
export function formatPostDate(date: string): string {
  return formatDistanceToNow(new Date(date), { addSuffix: true });
}

/**
 * Helper function to get user's full name
 */
export function getUserFullName(user?: any): string {
  if (!user) return 'Anonymous';
  return user.name || user.display_name || 'Anonymous';
}

/**
 * Helper function to get user's avatar URL or empty string
 */
export function getUserAvatarUrl(user?: any): string {
  if (!user) return '';
  return user.acf?.profile_picture?.url || user.avatar_urls?.['96'] || '';
}

/**
 * Format category name for display
 */
export function formatCategoryName(category: string): string {
  return category
    .split(/[-_]/)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Helper function to determine image object-fit class based on aspect ratio
 * Uses a state to track whether image is portrait or landscape
 */
export function useImageObjectFit() {
  const [isPortrait, setIsPortrait] = useState(false);

  const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
    const img = event.currentTarget;
    // Check if image is portrait (height > width)
    setIsPortrait(img.naturalHeight > img.naturalWidth);
  };

  const objectFitClass = isPortrait ? 'object-contain' : 'object-cover';

  return { objectFitClass, handleImageLoad };
}
