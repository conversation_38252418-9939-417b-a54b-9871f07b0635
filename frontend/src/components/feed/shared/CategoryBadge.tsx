'use client';

import { LucideIcon } from 'lucide-react';

interface CategoryBadgeProps {
  categoryName: string;
  categoryColor: string;
  Icon: LucideIcon;
  className?: string;
}

export function CategoryBadge({
  categoryName,
  categoryColor,
  Icon,
  className = '',
}: CategoryBadgeProps) {
  return (
    <div
      className={`inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full capitalize mb-4 ${className}`}
      style={{
        backgroundColor: categoryColor,
        color: 'white',
      }}
    >
      <Icon className="h-4 w-4 text-white" />
      {categoryName}
    </div>
  );
}
