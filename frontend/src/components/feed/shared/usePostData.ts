import { WPPost as Post } from '@/lib/api/wordpress';

interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

interface ExtendedPost extends Post {
  vendor_info?: VendorInfo;
  vendor_data?: any;
}

export function usePostData(post: ExtendedPost) {
  // Check if this is a vendor post
  const vendorId = post.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);
  const vendorData = post.vendor_info || post.vendor_data || null;

  // Get author data
  const authorData = post._embedded?.author?.[0];
  const authorId = Number(authorData?.id || post.author || 1);

  // Get upvote count
  const initialUpvotes = (() => {
    if (post.upvotes && typeof post.upvotes.count === 'number') {
      return post.upvotes.count;
    }
    const metaUpvotes = post.meta?._upvotes;
    if (typeof metaUpvotes === 'number') return metaUpvotes;
    if (typeof metaUpvotes === 'string') return parseInt(metaUpvotes, 10) || 0;
    return 0;
  })();

  // Get comment count
  const initialCommentCount = (() => {
    const totalComments = post.total_comments;
    const metaComments = post.meta?._comments_count;
    if (typeof totalComments === 'number') return totalComments;
    if (typeof totalComments === 'string') return parseInt(totalComments, 10) || 0;
    if (typeof metaComments === 'number') return metaComments;
    if (typeof metaComments === 'string') return parseInt(metaComments, 10) || 0;
    return 0;
  })();

  return {
    vendorId,
    isVendorPost,
    vendorData,
    authorData,
    authorId,
    initialUpvotes,
    initialCommentCount,
  };
}
