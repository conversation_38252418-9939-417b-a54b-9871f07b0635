'use client';

import { useState } from 'react';
import { CardFooter } from '@/components/ui/card';
import { PostInteractions } from '../PostInteractions';
import { PostComments } from '../PostComments';

interface FeedItemFooterProps {
  postId: number;
  authorId: number;
  postTitle: string;
  postSlug?: string;
  postExcerpt?: string;
  initialUpvotes: number;
  initialCommentCount: number;
  layout?: 'card' | 'full-width';
  className?: string;
}

export function FeedItemFooter({
  postId,
  authorId,
  postTitle,
  postSlug,
  postExcerpt,
  initialUpvotes,
  initialCommentCount,
  layout = 'full-width',
  className = '',
}: FeedItemFooterProps) {
  const [showComments, setShowComments] = useState(false);

  return (
    <>
      <CardFooter
        className={`flex justify-center border-t p-3 md:p-4 bg-gray-100/50 dark:bg-gray-900 ${className}`}
      >
        <PostInteractions
          postId={postId}
          authorId={authorId}
          initialUpvotes={initialUpvotes}
          initialCommentCount={initialCommentCount}
          postTitle={postTitle}
          postSlug={postSlug}
          postExcerpt={postExcerpt}
          layout={layout}
          onCommentsToggle={setShowComments}
        />
      </CardFooter>

      <PostComments
        postId={postId}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        commentCount={initialCommentCount}
      />
    </>
  );
}
