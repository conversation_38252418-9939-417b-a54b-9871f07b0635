'use client';

import { CardHeader } from '@/components/ui/card';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import { getUserFullName, getUserAvatarUrl } from '../feed-item-helpers';

interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

interface AuthorData {
  id?: number;
  name?: string;
  first_name?: string;
  last_name?: string;
  roles?: string[] | { [key: string]: string };
  avatar_urls?: {
    [key: string]: string;
  };
  acf?: {
    profile_picture?: string | { url?: string; [key: string]: unknown };
    first_name?: string;
    last_name?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

interface FeedItemHeaderProps {
  authorData?: AuthorData;
  vendorData?: VendorInfo | null;
  isVendorPost: boolean;
  postDate: string;
  authorId: number;
}

export function FeedItemHeader({
  authorData,
  vendorData,
  isVendorPost,
  postDate,
  authorId,
}: FeedItemHeaderProps) {
  // Determine display name and avatar based on vendor vs personal post
  let displayName: string;
  let avatarUrl: string;

  if (isVendorPost && vendorData) {
    // Use vendor information
    displayName = vendorData.name || 'Vendor';
    avatarUrl = vendorData.logo_url || '';
  } else {
    // Use regular author information
    displayName = getUserFullName(authorData);
    avatarUrl = getUserAvatarUrl(authorData);
  }

  return (
    <CardHeader className="flex-row items-center gap-3 md:gap-4 space-y-0 bg-white dark:bg-gray-900 pb-2">
      {isVendorPost && vendorData ? (
        // Show vendor logo and name for vendor posts
        <>
          <div className="h-8 w-8 md:h-10 md:w-10 ring-2 ring-[#5cc8ff]/20 rounded-full overflow-hidden">
            <img
              src={avatarUrl || '/images/avatar-placeholder.svg'}
              alt={displayName}
              className="h-8 w-8 md:h-10 md:w-10 object-cover rounded-full"
            />
          </div>
          <div className="flex flex-col">
            <div className="font-semibold text-sm md:text-base text-[#3d405b] dark:text-gray-100">
              {displayName}
            </div>
            <div className="flex items-center gap-2 text-xs md:text-sm text-muted-foreground">
              <span>{postDate}</span>
            </div>
          </div>
        </>
      ) : (
        // Show user avatar and name for personal posts
        <>
          <UserAvatarWithRank
            userId={authorId}
            avatarUrl={avatarUrl}
            displayName={displayName}
            size="h-8 w-8 md:h-10 md:w-10"
            containerSize="w-8 h-8 md:w-12 md:h-12"
            userRoles={(authorData?.roles as string[]) || []}
          />
          <div className="flex flex-col">
            <div className="font-semibold text-sm md:text-base text-[#3d405b] dark:text-gray-100">
              {displayName}
            </div>
            <div className="flex items-center gap-2 text-xs md:text-sm text-muted-foreground">
              <span>{postDate}</span>
            </div>
          </div>
        </>
      )}
    </CardHeader>
  );
}
