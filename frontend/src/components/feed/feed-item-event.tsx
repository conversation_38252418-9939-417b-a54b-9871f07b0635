'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import Link from 'next/link';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import { Calendar, Clock, MapPin, ExternalLink } from 'lucide-react';
import {
  formatPostDate,
  getUserFullName,
  getUserAvatarUrl,
  useImageObjectFit,
} from './feed-item-helpers';
import Image from 'next/image';

// Vendor info interface
interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
  [key: string]: unknown;
}

interface EventPost extends Post {
  acf?: {
    event_date?: string;
    event_time?: string;
    time_zone?: string;
    event_end_date?: string;
    event_location?: string;
    host_company_organization?: string;
    host_company_organization_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    registration_link_url?: string;
    additional_details_link_url?: string;
    event_promo_image?: {
      url?: string;
      [key: string]: unknown;
    };
  };
  vendor_info?: VendorInfo;
  vendor_data?: VendorInfo;
}

interface FeedItemEventProps {
  post: EventPost;
}

// Helper function to get ACF image URL
function getAcfImageUrl(
  image: { url?: string; [key: string]: unknown } | string | undefined
): string | null {
  if (!image) return null;

  if (typeof image === 'string') {
    return image;
  }

  if (typeof image === 'object' && image.url) {
    return image.url;
  }

  return null;
}

export function FeedItemEvent({ post }: FeedItemEventProps) {
  const [excerpt, setExcerpt] = useState('');
  const acf = post.acf;
  const decodedTitle = decodeHtml(post.title.rendered);
  const { objectFitClass, handleImageLoad } = useImageObjectFit();

  useEffect(() => {
    setExcerpt(decodeHtml(post.excerpt?.rendered || ''));
  }, [post.excerpt?.rendered]);

  // Get event-specific image or fallback to featured image
  const eventImageUrl: string | null =
    getAcfImageUrl(acf?.event_promo_image) ||
    (post._embedded?.['wp:featuredmedia']?.[0]?.source_url as string) ||
    (post.featured_media_url as string) ||
    null;

  // Get author information
  const authorData = post._embedded?.author?.[0];
  const authorId = Number(authorData?.id || post.author || 1);

  // Check if this is a vendor post
  const vendorId = post.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);
  const vendorData = post.vendor_info || post.vendor_data || null;

  // Determine display name and avatar based on vendor vs personal post
  let displayName: string;
  let avatarUrl: string;
  let displayId: number;

  if (isVendorPost && vendorData) {
    // Use vendor information
    displayName = vendorData.name || 'Vendor';
    avatarUrl = vendorData.logo_url || '';
    displayId = vendorData.id;
  } else {
    // Use regular author information
    displayName = getUserFullName(authorData);
    avatarUrl = getUserAvatarUrl(authorData);
    displayId = authorId;
  }

  const postDate = formatPostDate(post.date);
  const categoryColor = '#1dd05b'; // Green for events

  // Format event date
  const eventDate = acf?.event_date
    ? acf.event_end_date && acf.event_end_date !== acf.event_date
      ? `${acf.event_date} - ${acf.event_end_date}`
      : acf.event_date
    : null;

  return (
    <Card className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200 w-full max-w-full">
      <Link href={`/posts/${post.slug}`} className="cursor-pointer">
        <div className="relative">
          {eventImageUrl && (
            <div className="aspect-video overflow-hidden bg-muted">
              <Image
                src={eventImageUrl as string}
                alt={decodedTitle}
                width={1200}
                height={675}
                className={`${objectFitClass} w-full h-full post-image-glow`}
                onLoad={handleImageLoad}
                priority={false}
              />
            </div>
          )}
        </div>

        <CardHeader className="flex-row items-center gap-4 space-y-0 bg-white dark:bg-gray-900 pb-2">
          {isVendorPost && vendorData ? (
            // Show vendor logo and name for vendor posts
            <>
              <div className="h-10 w-10 ring-2 ring-primary/20 rounded-full overflow-hidden">
                <Image
                  src={avatarUrl || '/images/avatar-placeholder.svg'}
                  alt={displayName}
                  width={40}
                  height={40}
                  className="h-10 w-10 object-cover rounded-full"
                />
              </div>
              <div className="flex flex-col">
                <div className="font-semibold text-foreground dark:text-gray-100">
                  {displayName}
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                </div>
              </div>
            </>
          ) : (
            // Show user avatar and name for personal posts
            <>
              <UserAvatarWithRank
                userId={displayId}
                avatarUrl={avatarUrl}
                displayName={displayName}
                size="h-10 w-10"
                containerSize="w-12 h-12"
                userRoles={(authorData?.roles as string[]) || []}
              />
              <div className="flex flex-col">
                <div className="font-semibold text-foreground dark:text-gray-100">
                  {displayName}
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                </div>
              </div>
            </>
          )}
        </CardHeader>

        <CardContent className="p-4 md:p-6 pt-2">
          <div
            className="inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full capitalize mb-4"
            style={{
              backgroundColor: categoryColor,
              color: 'white',
            }}
          >
            <Calendar className="h-4 w-4 text-white" />
            Event
          </div>
          <h3 className="text-[28px] font-extrabold text-foreground dark:text-gray-100 leading-tight mb-2">
            {decodedTitle}
          </h3>
          <div className="prose prose-sm dark:prose-invert line-clamp-4">
            <p
              className="text-foreground dark:text-gray-300 mb-4 last:mb-0"
              dangerouslySetInnerHTML={{
                __html: excerpt,
              }}
            />
          </div>

          {/* Event Details */}
          <div className="mt-4 space-y-3 bg-gray-50 p-4 rounded-lg">
            {eventDate && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-category-events" />
                <span className="text-sm font-medium">{eventDate}</span>
              </div>
            )}

            {acf?.event_time && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-category-events" />
                <span className="text-sm">
                  {acf.event_time}
                  {acf.time_zone && ` (${acf.time_zone})`}
                </span>
              </div>
            )}

            {acf?.event_location && (
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-category-events" />
                <span className="text-sm">{acf.event_location}</span>
              </div>
            )}

            {acf?.host_company_organization && (
              <div>
                <span className="text-sm text-gray-600">
                  Hosted by <strong>{acf.host_company_organization}</strong>
                </span>
                {acf.host_company_organization_logo?.url && (
                  <Image
                    src={acf.host_company_organization_logo.url}
                    alt={`${acf.host_company_organization} logo`}
                    width={128}
                    height={128}
                    className="max-w-32 max-h-32 object-contain rounded mt-3"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Link>

      {/* Action buttons - moved outside Link to avoid nested anchor tags */}
      <div className="px-6 pb-8">
        <div className="flex gap-3">
          {/* View Details Button */}
          <Button
            asChild
            variant="outline"
            size="sm"
            className="w-fit text-foreground border-2 border-primary hover:bg-primary hover:text-primary-foreground font-extrabold"
          >
            <Link href={`/posts/${post.slug}`}>
              <ExternalLink className="h-4 w-4 mr-2" />
              View Details
            </Link>
          </Button>

          {/* Register Button */}
          {acf?.registration_link_url && (
            <Button
              asChild
              size="sm"
              className="w-fit bg-gradient-to-r from-blue-500 to-sky-600 text-white hover:from-blue-600 hover:to-sky-700 font-extrabold"
            >
              <a href={acf.registration_link_url} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Register
              </a>
            </Button>
          )}
        </div>
      </div>

      <CardFooter className="flex justify-center border-t p-4 bg-gray-100/50 dark:bg-gray-900">
        <PostInteractions
          postId={post.id}
          authorId={authorId}
          initialUpvotes={(() => {
            if (post.upvotes && typeof post.upvotes.count === 'number') {
              return post.upvotes.count;
            }
            return Number(post.meta?._upvotes) || 0;
          })()}
          initialCommentCount={
            Number(post.total_comments) || Number(post.meta?._comments_count) || 0
          }
          postTitle={decodedTitle}
          layout="full-width"
        />
      </CardFooter>
    </Card>
  );
}
