/*********************************************
# frontend/src/components/feed/feed-item-book.tsx
# 02/07/2025 2:05pm Added beautiful gradient placeholder for book posts without cover images to maintain consistent layout and visual appeal
**********************************************/

'use client';

import { useState, useEffect } from 'react';
import { Card, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import { PostComments } from './PostComments';
import { BookOpen, ExternalLink } from 'lucide-react';

// Vendor info interface
interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}
import { getCategoryColor } from './feed-item-helpers';
import Image from 'next/image';

// Complete interface based on book ACF fields
interface BookPost extends Post {
  acf?: {
    book_title?: string;
    author_names?: string;
    book_cover_image?: {
      url?: string;
      [key: string]: unknown;
    };
    purchase_url?: string;
  };
  vendor_info?: VendorInfo;
  vendor_data?: any;
}

interface FeedItemBookProps {
  post: BookPost;
}

// Helper function to get ACF image URL
function getAcfImageUrl(
  image: { url?: string; [key: string]: unknown } | string | undefined
): string | null {
  if (!image) return null;

  if (typeof image === 'string') {
    return image;
  }

  if (typeof image === 'object' && image.url) {
    return image.url;
  }

  return null;
}

export function FeedItemBook({ post }: FeedItemBookProps) {
  const [showComments, setShowComments] = useState(false);
  const [excerpt, setExcerpt] = useState('');
  const acf = post.acf;
  const decodedTitle = decodeHtml(post.title.rendered);

  useEffect(() => {
    setExcerpt(decodeHtml(post.excerpt?.rendered || ''));
  }, [post.excerpt?.rendered]);

  // Get book-specific data from ACF fields
  const bookTitle = acf?.book_title || decodedTitle;
  const bookCoverUrl = getAcfImageUrl(acf?.book_cover_image);

  // Use book cover image if available, otherwise fallback to featured image
  const bookImageUrl = bookCoverUrl || post._embedded?.['wp:featuredmedia']?.[0]?.source_url;

  // Check if this is a vendor post
  const vendorId = post.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);
  const vendorData = post.vendor_info || post.vendor_data || null;

  // Get author information
  const authorData = post._embedded?.author?.[0];
  const authorId = Number(authorData?.id || post.author || 1);

  // Determine display ID for vendor vs personal post
  let displayId: number;

  if (isVendorPost && vendorData) {
    displayId = vendorData.id;
  } else {
    displayId = authorId;
  }
  const categoryColor = getCategoryColor('books');

  return (
    <Card className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200 w-full max-w-full">
      {/* Two Column Layout */}
      <div className="flex p-4 md:p-6 gap-3 md:gap-6">
        {/* Left Column - Book Cover */}
        <Link href={`/posts/${post.slug}`} className="cursor-pointer flex-shrink-0">
          <div className="w-16 h-24 sm:w-24 sm:h-36 md:w-[200px] md:h-[300px] rounded-lg overflow-hidden flex-shrink-0">
            {bookImageUrl ? (
              <Image
                src={bookImageUrl}
                alt={bookTitle}
                width={200}
                height={300}
                className="object-contain rounded-lg post-image-glow"
                priority={false}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-[#fef7cd] via-[#e0e7ff] via-[#fce7f3] to-[#dbeafe] flex items-center justify-center">
                <BookOpen className="h-12 w-12 text-[#3d405b]/60" />
              </div>
            )}
          </div>
        </Link>

        {/* Right Column - Content */}
        <div className="flex-1 min-w-0 overflow-hidden">
          <div
            className="inline-flex items-center gap-1 md:gap-1.5 text-xs md:text-sm font-bold px-2 md:px-3 py-1 rounded-full capitalize mb-3 md:mb-4"
            style={{
              backgroundColor: categoryColor,
              color: 'white',
            }}
          >
            <BookOpen className="h-3 w-3 md:h-4 md:w-4 text-white" />
            Book
          </div>
          <Link href={`/posts/${post.slug}`} className="cursor-pointer">
            <h3 className="text-lg sm:text-xl md:text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-2 hover:underline break-words">
              {bookTitle}
            </h3>
          </Link>
          {acf?.author_names && (
            <p className="text-sm text-muted-foreground mb-3">By: {acf.author_names}</p>
          )}
          <div className="prose prose-sm dark:prose-invert line-clamp-4 mb-4">
            <p
              className="text-[#3d405b] dark:text-gray-300 mb-4 last:mb-0"
              dangerouslySetInnerHTML={{ __html: excerpt }}
            />
          </div>

          {/* View Book Button - Inside right column, outside of any link wrapper */}
          <Button
            asChild
            variant="outline"
            size="sm"
            className="w-fit text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
          >
            <Link href={`/posts/${post.slug}`}>
              <ExternalLink className="h-4 w-4 mr-2" />
              View Book
            </Link>
          </Button>
        </div>
      </div>

      <CardFooter className="flex justify-center border-t p-4 bg-gray-100/50 dark:bg-gray-900">
        <PostInteractions
          postId={post.id}
          authorId={isVendorPost ? displayId : authorId}
          initialUpvotes={(() => {
            if (post.upvotes && typeof post.upvotes.count === 'number') {
              return post.upvotes.count;
            }
            return Number(post.meta?._upvotes) || 0;
          })()}
          initialCommentCount={
            Number(post.total_comments) || Number(post.meta?._comments_count) || 0
          }
          postTitle={bookTitle}
          layout="full-width"
          onCommentsToggle={setShowComments}
        />
      </CardFooter>

      <PostComments
        postId={post.id}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        commentCount={Number(post.total_comments) || Number(post.meta?._comments_count) || 0}
      />
    </Card>
  );
}
