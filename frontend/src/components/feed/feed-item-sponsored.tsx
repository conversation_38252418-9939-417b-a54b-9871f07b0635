'use client';

import { Card } from '@/components/ui/card';
import Link from 'next/link';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import { useState, useEffect } from 'react';
import { PostComments } from './PostComments';

interface SponsoredPost extends Post {
  acf?: {
    sponsored?: boolean;
    background?: string;
    logo_background?: string;
    thank_you_banner?: string;
    sponsorship_logo?: {
      url?: string;
      sizes?: {
        thumbnail?: string;
        medium?: string;
        large?: string;
      };
    };
    [key: string]: unknown;
  };
  is_sponsored?: boolean;
}

interface FeedItemSponsoredProps {
  post: SponsoredPost;
}

export function FeedItemSponsored({ post }: FeedItemSponsoredProps) {
  const [showComments, setShowComments] = useState(false);
  const [thankYouHtml, setThankYouHtml] = useState('');

  // Get sponsor data from ACF fields
  const acf = post.acf;
  const sponsorLogo = acf?.sponsorship_logo?.url || acf?.sponsorship_logo?.sizes?.medium;
  const backgroundColor = acf?.background || '#e5e7eb'; // Default light gray
  const logoBackgroundColor = acf?.logo_background || '#ffffff';
  const thankYouBannerHtml = acf?.thank_you_banner || 'Thank you for supporting our community.';

  useEffect(() => {
    setThankYouHtml(thankYouBannerHtml);
  }, [thankYouBannerHtml]);

  return (
    <>
      <Card className="mb-6 overflow-hidden border-none shadow-lg hover:shadow-xl transition-shadow duration-200">
        {/* Sponsor Banner */}
        <div className="px-4 py-1.5 flex items-center justify-between rounded-t-lg bg-gradient-to-r from-[#f7dd78] via-[#d6aa3f] via-[#e6af21] via-[#ebd074] via-[#f9be0e] via-[#f9be0e] to-[#f6d973]">
          <div className="flex items-center justify-start gap-2">
            <span className="text-sm font-bold text-white">Founding Sponsor Post</span>
            <span
              className="text-xs text-dark m-0"
              dangerouslySetInnerHTML={{ __html: thankYouHtml }}
            />
          </div>
        </div>

        <div style={{ backgroundColor: backgroundColor }}>
          <Link href={`/posts/${post.slug}`} className="cursor-pointer block">
            {/* Two Column Layout */}
            <div className="grid grid-cols-1 md:grid-cols-[200px_1fr] gap-6 p-6">
              {/* Left Column - Logo */}
              <div className="flex justify-center md:justify-start">
                {sponsorLogo && (
                  <div
                    className="rounded-lg p-4 flex items-center justify-center"
                    style={{ backgroundColor: logoBackgroundColor }}
                  >
                    <img
                      src={sponsorLogo}
                      alt="Sponsor Logo"
                      className="max-w-full max-h-24 object-contain"
                    />
                  </div>
                )}
              </div>

              {/* Right Column - Content */}
              <div className="flex flex-col justify-center">
                {/* Title Only */}
                <h3 className="text-[28px] font-extrabold text-[#3d405b] leading-tight">
                  {decodeHtml(post.title?.rendered || 'Untitled Post')}
                </h3>
              </div>
            </div>
          </Link>

          {/* Interactions */}
          <div className="flex justify-center border-t p-4 bg-gray-100/50">
            <PostInteractions
              postId={post.id}
              authorId={Number(post._embedded?.author?.[0]?.id || 1)}
              initialUpvotes={(() => {
                if (post.upvotes && typeof post.upvotes.count === 'number') {
                  return post.upvotes.count;
                }
                const metaUpvotes = post.meta?._upvotes;
                if (typeof metaUpvotes === 'number') return metaUpvotes;
                if (typeof metaUpvotes === 'string') return parseInt(metaUpvotes, 10) || 0;
                return 0;
              })()}
              initialCommentCount={(() => {
                const totalComments = post.total_comments;
                const metaComments = post.meta?._comments_count;
                if (typeof totalComments === 'number') return totalComments;
                if (typeof totalComments === 'string') return parseInt(totalComments, 10) || 0;
                if (typeof metaComments === 'number') return metaComments;
                if (typeof metaComments === 'string') return parseInt(metaComments, 10) || 0;
                return 0;
              })()}
              postTitle={decodeHtml(post.title?.rendered || 'Untitled Post')}
              postSlug={post.slug}
              postExcerpt={post.excerpt?.rendered}
              layout="card"
              onCommentsToggle={setShowComments}
            />
          </div>
        </div>
      </Card>

      <PostComments
        postId={post.id}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        commentCount={(() => {
          const totalComments = post.total_comments;
          const metaComments = post.meta?._comments_count;
          if (typeof totalComments === 'number') return totalComments;
          if (typeof totalComments === 'string') return parseInt(totalComments, 10) || 0;
          if (typeof metaComments === 'number') return metaComments;
          if (typeof metaComments === 'string') return parseInt(metaComments, 10) || 0;
          return 0;
        })()}
      />
    </>
  );
}
