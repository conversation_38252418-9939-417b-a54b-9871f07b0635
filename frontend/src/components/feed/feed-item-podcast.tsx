/*********************************************
# frontend/src/components/feed/feed-item-podcast.tsx
# 02/07/2025 2:00pm Added beautiful gradient placeholder for podcast posts without images to maintain consistent layout and visual appeal
**********************************************/

'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { Mic, ExternalLink } from 'lucide-react';
import { formatPostDate, getCategoryColor } from './feed-item-helpers';
import Image from 'next/image';
import { FeedItemFooter } from './shared/FeedItemFooter';
import { usePostData } from './shared/usePostData';

// Vendor info interface
interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

// Complete interface based on ACF fields from acf-podcast.php
interface PodcastPost extends Post {
  acf?: {
    podcast_name?: string; // required
    hosts?: Array<{
      host_name?: string;
    }>; // required
    episode_url?: string;
  };
  vendor_info?: VendorInfo;
  vendor_data?: any;
}

interface FeedItemPodcastProps {
  post: PodcastPost;
}

export function FeedItemPodcast({ post }: FeedItemPodcastProps) {
  const acf = post.acf;
  const decodedTitle = decodeHtml(post.title.rendered);

  // Use shared hook to get common post data
  const { authorId, initialUpvotes, initialCommentCount } = usePostData(post);

  // Get podcast-specific data from ACF fields
  const episodeTitle = decodedTitle; // Use post title since podcast_episode_title was removed

  // Use WordPress featured image only (standardized approach)
  // Handle cases where featured media exists but has permission errors
  const featuredMedia = post._embedded?.['wp:featuredmedia']?.[0];
  const podcastImageUrl =
    featuredMedia && !(featuredMedia as any).code ? featuredMedia.source_url : null;

  const postDate = formatPostDate(post.date);
  const categoryColor = getCategoryColor('podcasts');

  return (
    <Card className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200 w-full max-w-full">
      {/* Two Column Layout */}
      <div className="flex p-4 md:p-6 gap-3 md:gap-6">
        {/* Left Column - Podcast Image and Badge */}
        <div className="flex-shrink-0 flex flex-col">
          <Link href={`/posts/${post.slug}`} className="cursor-pointer mb-3">
            <div className="w-20 h-20 sm:w-32 sm:h-32 md:w-[200px] md:h-[200px] rounded-lg overflow-hidden flex-shrink-0">
              {podcastImageUrl ? (
                <Image
                  src={podcastImageUrl}
                  alt={episodeTitle}
                  width={200}
                  height={200}
                  className="object-contain rounded-lg post-image-glow"
                  priority={false}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-[#fef7cd] via-[#e0e7ff] via-[#fce7f3] to-[#dbeafe] flex items-center justify-center">
                  <Mic className="h-12 w-12 text-[#3d405b]/60" />
                </div>
              )}
            </div>
          </Link>

          {/* Podcast Badge - Under image, left aligned */}
          <div
            className="inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full capitalize w-fit"
            style={{
              backgroundColor: categoryColor,
              color: 'white',
            }}
          >
            <Mic className="h-4 w-4 text-white" />
            Podcast
          </div>
        </div>

        {/* Right Column - Content */}
        <div className="flex-1 min-w-0 overflow-hidden">
          <Link href={`/posts/${post.slug}`} className="cursor-pointer">
            <h3 className="text-lg sm:text-xl md:text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-2 hover:underline break-words">
              {episodeTitle}
            </h3>
          </Link>
          {acf?.podcast_name && (
            <p className="text-sm sm:text-base md:text-lg font-semibold text-[#3d405b] dark:text-gray-100 mb-2">
              {acf.podcast_name}
            </p>
          )}
          {acf?.hosts && acf.hosts.length > 0 && (
            <p className="text-sm text-muted-foreground mb-2">
              Hosted by: {acf.hosts.map((host) => host.host_name).join(', ')}
            </p>
          )}
          <p className="text-sm text-muted-foreground mb-4">Posted {postDate}</p>

          {/* Listen to Episode Button - Inside right column */}
          <Button
            asChild
            variant="outline"
            size="sm"
            className="w-fit text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
          >
            <Link href={`/posts/${post.slug}`}>
              <ExternalLink className="h-4 w-4 mr-2" />
              View Podcast
            </Link>
          </Button>
        </div>
      </div>

      <FeedItemFooter
        postId={post.id}
        authorId={authorId}
        postTitle={episodeTitle}
        postSlug={post.slug}
        postExcerpt={post.excerpt?.rendered}
        initialUpvotes={initialUpvotes}
        initialCommentCount={initialCommentCount}
        layout="full-width"
      />
    </Card>
  );
}
