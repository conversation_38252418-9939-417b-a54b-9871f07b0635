'use client';

import { Card } from '@/components/ui/card';
import Link from 'next/link';
import { stripHtml, decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import {
  getCategoryColor,
  getCategoryIcon,
  formatPostDate,
  formatCategoryName,
  useImageObjectFit,
} from './feed-item-helpers';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import { FeedItemEvent } from './feed-item-event';
import { FeedItemPodcast } from './feed-item-podcast';
import { FeedItemWebinar } from './feed-item-webinar';
import { FeedItemJob } from './feed-item-job';
import { FeedItemVideo } from './feed-item-video';
import { FeedItemCourse } from './feed-item-course';
import { FeedItemNews } from './feed-item-news';
import { FeedItemBook } from './feed-item-book';
import { FeedItemCaseStudy } from './feed-item-case-study';
import { FeedItemPresentation } from './feed-item-presentation';
import { FeedItemPressRelease } from './feed-item-press-release';
import { FeedItemWhitepaper } from './feed-item-whitepaper';
import { FeedItemTemplate } from './feed-item-template';
import { FeedItemBlogPost } from './feed-item-blog-post';
import { FeedItemThoughtLeadership } from './feed-item-thought-leadership';
import { FeedItemPeople } from './feed-item-people';
import { PostComments } from './PostComments';
import { useState } from 'react';

interface WPCategory {
  id: number;
  name: string;
  slug: string;
  link?: string;
  [key: string]: unknown; // Allow for additional properties
}

interface Category {
  id: number;
  name: string;
  slug: string;
  link: string;
}

interface VendorData {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
  title?: {
    rendered: string;
  };
  vendor_meta?: {
    logo_url?: string;
  };
  [key: string]: unknown;
}

interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

interface ExtendedPost extends Post {
  vendor_data?: VendorData;
  vendor_info?: VendorInfo;
  acf?: {
    // Event fields
    event_date?: string;
    event_time?: string;
    time_zone?: string;
    event_end_date?: string;
    event_location?: string;
    host_company_organization?: string;
    host_company_organization_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    registration_link_url?: string;
    additional_details_link_url?: string;
    event_promo_image?: {
      url?: string;
      [key: string]: unknown;
    };

    // Webinar fields
    webinar_type?:
      | {
          value?: string;
          label?: string;
        }
      | string;
    date?: string; // For live webinars
    time?: string; // For live webinars
    presenters?: Array<{
      presenter_name?: string;
    }>;
    webinar_host_company_organization?: string;
    webinar_host_company_organization_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    register_url?: string;
    youtube_url?: string;
    vimeo_url?: string;
    webinar_promo_image?: {
      url?: string;
      [key: string]: unknown;
    };

    // Job fields
    position_title?: string;
    company?: string;
    company_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    location?: string;
    job_type?:
      | {
          value?: string;
          label?: string;
        }
      | string;
    experience_level?: string;
    compensation?: string;
    posting_link_url?: string;

    // Course fields
    course_name?: string;
    course_image?: {
      url?: string;
      [key: string]: unknown;
    };
    course_url?: string;
    sign_up_url?: string;
    thoughts?: string;

    // News fields (article_image removed - now uses featured_image)
    external_article_url?: string;
    image_caption?: string;

    // Podcast fields
    podcast_episode_title?: string;
    podcast_name?: string;
    podcast_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    hosts?: Array<{
      host_name?: string;
    }>;
    episode_url?: string;
    podcast_preview_image?: {
      url?: string;
      [key: string]: unknown;
    };

    // Video fields
    video_title?: string;
    creators?: Array<{
      creator_name?: string;
    }>;
    creator_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    video_source?: string;
    video_embed_url?: string;
    video_thumbnail?: {
      url?: string;
      [key: string]: unknown;
    };

    // Book fields
    book_title?: string;
    author_names?: string;
    book_cover_image?: {
      url?: string;
      [key: string]: unknown;
    };
    purchase_url?: string;

    // Case Study fields
    companyorganization_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    upload_pdf?: {
      url?: string;
      [key: string]: unknown;
    };

    // Presentation fields
    presentation_title?: string;
    select_files?: {
      url?: string;
      [key: string]: unknown;
    };

    // Press Release fields
    press_release_url?: string;

    // Whitepaper fields (uses same fields as case study)

    // Template fields
    creator_name?: string;
    logo?: {
      url?: string;
      [key: string]: unknown;
    };
    file_upload?: {
      url?: string;
      [key: string]: unknown;
    };
    url?: string;

    // Blog Post fields
    title?: string; // Custom title from ACF
    author?: string; // Custom author from ACF
    website_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    post_publish_date?: string;

    // Thought Leadership fields
    featured_image?: {
      url?: string;
      [key: string]: unknown;
    };

    [key: string]: unknown;
  };
}

interface FeedItemProps {
  post: Post;
}

export function FeedItem({ post }: FeedItemProps) {
  // State for comments - must be at the top before any early returns
  const [showComments, setShowComments] = useState(false);

  // Image object fit hook
  const { objectFitClass, handleImageLoad } = useImageObjectFit();

  // Early return if post is invalid
  if (!post || !post.id) {
    // Avoid console statements during render to prevent hydration issues
    if (typeof window !== 'undefined') {
      console.warn('Invalid post data received:', post);
    }
    return null;
  }

  // Check if this is a vendor post - check both new ACF field and legacy meta field
  const postWithVendor = post as ExtendedPost;
  const vendorId = postWithVendor.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);

  // Get vendor data from the new ACF field or legacy embedded response
  const vendorData = postWithVendor.vendor_info || postWithVendor.vendor_data || null;

  // Get author data - use raw author data without type casting
  const authorData = post._embedded?.author?.[0];

  // Determine display name based on whether this is a vendor post
  let authorDisplayName: string;

  if (isVendorPost && vendorData) {
    // Use vendor information - check both new ACF field format and legacy format
    authorDisplayName = vendorData.name || (vendorData as VendorData).title?.rendered || 'Vendor';
  } else {
    // Use regular author information
    authorDisplayName = (authorData as any)?.name || 'Tourism Thought Leader';

    // If the embedded author has a full display name in meta or ACF fields, use that
    if (
      (authorData as any)?.acf &&
      ((authorData as any).acf.first_name || (authorData as any).acf.last_name)
    ) {
      const firstName = (authorData as any).acf.first_name || '';
      const lastName = (authorData as any).acf.last_name || '';
      if (firstName || lastName) {
        authorDisplayName = `${firstName} ${lastName}`.trim();
      }
    }

    // Otherwise, WordPress sometimes provides these fields directly
    else if ((authorData as any)?.first_name || (authorData as any)?.last_name) {
      const firstName = (authorData as any).first_name || '';
      const lastName = (authorData as any).last_name || '';
      if (firstName || lastName) {
        authorDisplayName = `${firstName} ${lastName}`.trim();
      }
    }

    // If we just have a display name like "johndoe", try to format it better
    if (authorDisplayName.indexOf(' ') === -1 && authorDisplayName === (authorData as any)?.name) {
      // This looks like a username - attempt to capitalize it nicely
      authorDisplayName = authorDisplayName
        .replace(/([A-Z])/g, ' $1') // Add space before capital letters
        .replace(/^./, (str: string) => str.toUpperCase()) // Capitalize first letter
        .trim();
    }
  }

  // Get the first category from the post with proper null checking
  const defaultCategory: Category = {
    name: 'Uncategorized',
    id: 1,
    slug: 'uncategorized',
    link: '/category/uncategorized',
  };

  const wpCategory = (() => {
    try {
      const terms = post._embedded?.['wp:term'];
      if (
        Array.isArray(terms) &&
        terms.length > 0 &&
        Array.isArray(terms[0]) &&
        terms[0].length > 0
      ) {
        return terms[0][0] as WPCategory | undefined;
      }
      return undefined;
    } catch (error) {
      // Avoid console statements during render to prevent hydration issues
      if (typeof window !== 'undefined') {
        console.warn('Error accessing post categories:', error);
      }
      return undefined;
    }
  })();

  // Safely extract category with proper typing and validation
  const getCategory = (): Category => {
    if (!wpCategory || typeof wpCategory !== 'object') {
      return defaultCategory;
    }

    try {
      // Validate required fields exist
      if (!wpCategory.name && !wpCategory.slug && !wpCategory.id) {
        return defaultCategory;
      }

      // Determine the link - prioritize wpCategory.link, then use slug or ID
      let link = defaultCategory.link;
      if (wpCategory.link && typeof wpCategory.link === 'string') {
        link = wpCategory.link;
      } else if (wpCategory.slug && typeof wpCategory.slug === 'string') {
        link = `/category/${wpCategory.slug}`;
      } else if (wpCategory.id) {
        link = `/category/${wpCategory.id}`;
      }

      return {
        id:
          (typeof wpCategory.id === 'number' ? wpCategory.id : Number(wpCategory.id)) ||
          defaultCategory.id,
        name:
          (typeof wpCategory.name === 'string' ? wpCategory.name : String(wpCategory.name)) ||
          defaultCategory.name,
        slug:
          (typeof wpCategory.slug === 'string' ? wpCategory.slug : String(wpCategory.slug)) ||
          defaultCategory.slug,
        link: link,
      };
    } catch (error) {
      // Avoid console statements during render to prevent hydration issues
      if (typeof window !== 'undefined') {
        console.error('Error parsing category:', error);
      }
      return defaultCategory;
    }
  };

  const category = getCategory();

  // Post type detection with better organization
  const POST_TYPE_SLUGS = {
    event: ['event', 'events'],
    webinar: ['webinar', 'webinars'],
    job: ['job', 'jobs'],
    course: ['course', 'courses'],
    news: ['news'],
    podcast: ['podcast', 'podcasts'],
    video: ['video', 'videos'],
    book: ['book'],
    caseStudy: ['case-study'],
    presentation: ['presentation'],
    pressRelease: ['press-release'],
    whitepaper: ['whitepaper'],
    template: ['template'],
    blogPost: ['blog-post'],
    thoughtLeadership: ['thought-leadership'],
    people: ['people-on-the-move', 'people'],
  } as const;

  // Helper function to check post type
  const isPostType = (type: keyof typeof POST_TYPE_SLUGS): boolean => {
    const slugs = POST_TYPE_SLUGS[type];
    return slugs.some((slug) => slug === category.slug);
  };

  // Check all post types
  const isEvent = isPostType('event');
  const isPodcast = isPostType('podcast');
  const isWebinar = isPostType('webinar');
  const isJob = isPostType('job');
  const isCourse = isPostType('course');
  const isVideo = isPostType('video');
  const isNews = isPostType('news');
  const isBook = isPostType('book');
  const isCaseStudy = isPostType('caseStudy');
  const isPresentation = isPostType('presentation');
  const isPressRelease = isPostType('pressRelease');
  const isWhitepaper = isPostType('whitepaper');
  const isTemplate = isPostType('template');
  const isBlogPost = isPostType('blogPost');
  const isThoughtLeadership = isPostType('thoughtLeadership');
  const isPeople = isPostType('people');

  // Use dedicated components for better styling
  if (isEvent) {
    return <FeedItemEvent post={post} />;
  }

  if (isPodcast) {
    return <FeedItemPodcast post={post} />;
  }

  if (isWebinar) {
    return <FeedItemWebinar post={post} />;
  }

  if (isJob) {
    return <FeedItemJob post={post} />;
  }

  if (isCourse) {
    return <FeedItemCourse post={post} />;
  }

  if (isVideo) {
    return <FeedItemVideo post={post} />;
  }

  if (isNews) {
    return <FeedItemNews post={post} />;
  }

  if (isBook) {
    return <FeedItemBook post={post} />;
  }

  if (isCaseStudy) {
    return <FeedItemCaseStudy post={post} />;
  }

  if (isPresentation) {
    return <FeedItemPresentation post={post} />;
  }

  if (isPressRelease) {
    return <FeedItemPressRelease post={post} />;
  }

  if (isWhitepaper) {
    return <FeedItemWhitepaper post={post} />;
  }

  if (isTemplate) {
    return <FeedItemTemplate post={post} />;
  }

  if (isBlogPost) {
    return <FeedItemBlogPost post={post} />;
  }

  if (isThoughtLeadership) {
    return <FeedItemThoughtLeadership post={post} />;
  }

  if (isPeople) {
    return <FeedItemPeople post={post} />;
  }

  // Get V2 design values

  const categoryColor = getCategoryColor(category.slug);
  const CategoryIcon = getCategoryIcon(category.slug);

  // Fallback for any uncategorized posts - use V2 design
  return (
    <>
      <Card className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200 w-full max-w-full">
        <div className="cursor-pointer">
          <div className="relative">
            {post._embedded?.['wp:featuredmedia']?.[0]?.source_url && (
              <div className="aspect-video overflow-hidden bg-muted">
                <img
                  src={post._embedded['wp:featuredmedia'][0].source_url}
                  alt={decodeHtml(post.title?.rendered || 'Post image')}
                  className={`${objectFitClass} w-full h-full post-image-glow`}
                  onLoad={handleImageLoad}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://via.placeholder.com/800x400?text=TourismIQ';
                  }}
                  loading="lazy"
                />
              </div>
            )}
          </div>

          <div className="flex-row items-center gap-3 md:gap-4 space-y-0 bg-white dark:bg-gray-900 pb-2 p-4 md:p-6">
            <div className="flex items-center gap-3 md:gap-4 mb-3 md:mb-4">
              <div className="h-8 w-8 md:h-10 md:w-10 ring-2 ring-[#5cc8ff]/20 rounded-full overflow-hidden">
                {isVendorPost && vendorData ? (
                  // Show vendor logo for vendor posts
                  <img
                    src={
                      ('logo_url' in vendorData && vendorData.logo_url) ||
                      ('vendor_meta' in vendorData &&
                        (vendorData as VendorData).vendor_meta?.logo_url) ||
                      '/images/avatar-placeholder.svg'
                    }
                    alt={
                      ('name' in vendorData && vendorData.name) ||
                      ('title' in vendorData && (vendorData as VendorData).title?.rendered) ||
                      'Vendor'
                    }
                    className="h-8 w-8 md:h-10 md:w-10 object-cover rounded-full"
                  />
                ) : (
                  // Show user avatar for personal posts
                  <UserAvatarWithRank
                    userId={Number(post._embedded?.author?.[0]?.id || 1)}
                    avatarUrl={
                      post._embedded?.author?.[0]?.avatar_urls?.['96'] ||
                      '/images/avatar-placeholder.svg'
                    }
                    displayName={authorDisplayName}
                    size="h-8 w-8 md:h-10 md:w-10"
                    containerSize="w-8 h-8 md:w-10 md:h-10"
                    userRoles={(post._embedded?.author?.[0]?.roles as string[]) || []}
                  />
                )}
              </div>
              <div className="flex flex-col">
                <div className="font-semibold text-sm md:text-base text-[#3d405b] dark:text-gray-100">
                  {authorDisplayName}
                </div>
                <div className="flex items-center gap-2 text-xs md:text-sm text-muted-foreground">
                  <span>{formatPostDate(post.date)}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="px-4 md:px-6 pt-2">
            <div
              className="inline-flex items-center gap-1 md:gap-1.5 text-xs md:text-sm font-bold px-2 md:px-3 py-1 rounded-full capitalize mb-3 md:mb-4"
              style={{
                backgroundColor: categoryColor,
                color: 'white',
              }}
            >
              <CategoryIcon className="h-3 w-3 md:h-4 md:w-4 text-white" />
              {formatCategoryName(category.name || 'uncategorized')}
            </div>
            <h3 className="text-xl md:text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-2">
              <Link href={`/posts/${post.slug}`} className="hover:text-primary transition-colors">
                {decodeHtml(post.title?.rendered || 'Untitled Post')}
              </Link>
            </h3>
            <div className="prose prose-sm dark:prose-invert line-clamp-3 md:line-clamp-4 mb-3 md:mb-4">
              <p className="text-sm md:text-base text-[#3d405b] dark:text-gray-300">
                {stripHtml(post.excerpt?.rendered || '')}
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-center border-t p-3 md:p-4 bg-gray-100/50 dark:bg-gray-900">
          <PostInteractions
            postId={post.id}
            authorId={Number(post._embedded?.author?.[0]?.id || 1)}
            initialUpvotes={(() => {
              if (post.upvotes && typeof post.upvotes.count === 'number') {
                return post.upvotes.count;
              }
              const metaUpvotes = post.meta?._upvotes;
              if (typeof metaUpvotes === 'number') return metaUpvotes;
              if (typeof metaUpvotes === 'string') return parseInt(metaUpvotes, 10) || 0;
              return 0;
            })()}
            initialCommentCount={(() => {
              const totalComments = post.total_comments;
              const metaComments = post.meta?._comments_count;
              if (typeof totalComments === 'number') return totalComments;
              if (typeof totalComments === 'string') return parseInt(totalComments, 10) || 0;
              if (typeof metaComments === 'number') return metaComments;
              if (typeof metaComments === 'string') return parseInt(metaComments, 10) || 0;
              return 0;
            })()}
            postTitle={decodeHtml(post.title?.rendered || 'Untitled Post')}
            postSlug={post.slug}
            postExcerpt={post.excerpt?.rendered}
            layout="card"
            onCommentsToggle={setShowComments}
          />
        </div>
      </Card>

      <PostComments
        postId={post.id}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        commentCount={(() => {
          const totalComments = post.total_comments;
          const metaComments = post.meta?._comments_count;
          if (typeof totalComments === 'number') return totalComments;
          if (typeof totalComments === 'string') return parseInt(totalComments, 10) || 0;
          if (typeof metaComments === 'number') return metaComments;
          if (typeof metaComments === 'string') return parseInt(metaComments, 10) || 0;
          return 0;
        })()}
      />
    </>
  );
}
