/*********************************************
# frontend/src/components/feed/ShareButton.tsx
# 01/27/2025 4:00pm Created file
# 01/27/2025 5:45pm Updated share button styling - icons 20x20px, text 14px, font-medium, pill hover
# 01/27/2025 6:15pm Increased icon and text sizes by 15% - icons 24x24px, text 16px
# 01/28/2025 12:15am Removed mr-2 from Share2 icon to fix pill hover centering
# 02/07/2025 12:40pm Updated to use solid social icons instead of line versions
**********************************************/

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Share2, Copy, Check } from 'lucide-react';
import { FacebookSolid, TwitterSolid, LinkedinSolid } from '@/components/ui/SocialIcons';

interface ShareButtonProps {
  postUrl: string;
  postTitle: string;
  postExcerpt?: string;
  className?: string;
}

export function ShareButton({ postUrl, postTitle, postExcerpt, className = '' }: ShareButtonProps) {
  const [copied, setCopied] = useState(false);

  // Create full URL if relative
  const fullUrl = postUrl.startsWith('http')
    ? postUrl
    : `${typeof window !== 'undefined' ? window.location.origin : 'https://mytourismiq.wpenginepowered.com'}${postUrl}`;

  // Clean and truncate text for sharing
  const cleanTitle = postTitle.replace(/[<>]/g, '').trim();
  const cleanExcerpt = postExcerpt?.replace(/[<>]/g, '').trim() || '';
  const shareText = cleanExcerpt ? `${cleanTitle} - ${cleanExcerpt}` : cleanTitle;

  const shareToFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(fullUrl)}`;
    window.open(
      facebookUrl,
      'facebook-share',
      'width=626,height=436,toolbar=no,menubar=no,scrollbars=no'
    );
  };

  const shareToTwitter = () => {
    const twitterText = shareText.length > 240 ? shareText.substring(0, 237) + '...' : shareText;
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(twitterText)}&url=${encodeURIComponent(fullUrl)}`;
    window.open(
      twitterUrl,
      'twitter-share',
      'width=626,height=436,toolbar=no,menubar=no,scrollbars=no'
    );
  };

  const shareToLinkedIn = () => {
    const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(fullUrl)}`;
    window.open(
      linkedinUrl,
      'linkedin-share',
      'width=626,height=436,toolbar=no,menubar=no,scrollbars=no'
    );
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(fullUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = fullUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`gap-2 text-base font-medium text-[#44a00a] hover:text-[#44a00a] hover:bg-gray-200/80 rounded-full px-3 py-1 transition-colors ${className}`}
        >
          <Share2 className="size-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-48">
        <DropdownMenuItem onClick={shareToFacebook} className="cursor-pointer">
          <FacebookSolid className="h-4 w-4 mr-2 text-blue-600" />
          Share on Facebook
        </DropdownMenuItem>
        <DropdownMenuItem onClick={shareToTwitter} className="cursor-pointer">
          <TwitterSolid className="h-4 w-4 mr-2 text-blue-400" />
          Share on Twitter
        </DropdownMenuItem>
        <DropdownMenuItem onClick={shareToLinkedIn} className="cursor-pointer">
          <LinkedinSolid className="h-4 w-4 mr-2 text-blue-700" />
          Share on LinkedIn
        </DropdownMenuItem>
        <DropdownMenuItem onClick={copyToClipboard} className="cursor-pointer">
          {copied ? (
            <>
              <Check className="h-4 w-4 mr-2 text-green-600" />
              Link Copied!
            </>
          ) : (
            <>
              <Copy className="h-4 w-4 mr-2" />
              Copy Link
            </>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
