'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardFooter } from '@/components/ui/card';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import Link from 'next/link';
import Image from 'next/image';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import { PostComments } from './PostComments';
import { VideoEmbed } from '@/components/ui/VideoEmbed';
import { Video, ExternalLink, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  formatPostDate,
  getUserFullName,
  getUserAvatarUrl,
  getCategoryColor,
  useImageObjectFit,
} from './feed-item-helpers';

// Vendor info interface
interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

// Complete interface based on ACF fields for webinars
interface WebinarPost extends Post {
  acf?: {
    webinar_type?:
      | {
          value?: string;
          label?: string;
        }
      | string;
    date?: string; // For live webinars
    time?: string; // For live webinars
    time_zone?: string; // For live webinars
    presenters?: Array<{
      presenter_name?: string;
    }>;
    webinar_host_company_organization?: string;
    webinar_host_company_organization_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    register_url?: string;
    youtube_url?: string;
    vimeo_url?: string;
    webinar_embed_url?: string;
    webinar_promo_image?: {
      url?: string;
      [key: string]: unknown;
    };
  };
  vendor_info?: VendorInfo;
  vendor_data?: any;
}

interface FeedItemWebinarProps {
  post: WebinarPost;
}

// Helper function to get webinar type value
function getWebinarType(
  webinarType: string | { value?: string; label?: string } | undefined
): string | null {
  if (!webinarType) return null;

  if (typeof webinarType === 'object' && webinarType !== null) {
    return 'value' in webinarType ? webinarType.value || null : null;
  }

  return typeof webinarType === 'string' ? webinarType : null;
}

export function FeedItemWebinar({ post }: FeedItemWebinarProps) {
  const [showComments, setShowComments] = useState(false);
  const [excerpt, setExcerpt] = useState('');
  const { objectFitClass, handleImageLoad } = useImageObjectFit();
  const acf = post.acf;
  const decodedTitle = decodeHtml(post.title.rendered);

  useEffect(() => {
    setExcerpt(decodeHtml(post.excerpt?.rendered || ''));
  }, [post.excerpt?.rendered]);

  // Get webinar-specific data from ACF fields
  const webinarType = getWebinarType(acf?.webinar_type);
  const isLive = webinarType === 'live';
  const isRecorded = webinarType === 'recorded';
  const youtubeUrl = acf?.youtube_url;
  const vimeoUrl = acf?.vimeo_url;
  const webinarEmbedUrl = acf?.webinar_embed_url;

  // Use webinar promo image if available, otherwise fallback to featured image
  const webinarImageUrl =
    acf?.webinar_promo_image &&
    typeof acf.webinar_promo_image === 'object' &&
    acf.webinar_promo_image.url
      ? acf.webinar_promo_image.url
      : post._embedded?.['wp:featuredmedia']?.[0]?.source_url;

  // Get author information
  const authorData = post._embedded?.author?.[0];
  const authorId = Number(authorData?.id || post.author || 1);

  // Check if this is a vendor post
  const vendorId = post.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);
  const vendorData = post.vendor_info || post.vendor_data || null;

  // Determine display name and avatar based on vendor vs personal post
  let displayName: string;
  let avatarUrl: string;
  let displayId: number;

  if (isVendorPost && vendorData) {
    // Use vendor information
    displayName = vendorData.name || 'Vendor';
    avatarUrl = vendorData.logo_url || '';
    displayId = vendorData.id;
  } else {
    // Use regular author information
    displayName = getUserFullName(authorData);
    avatarUrl = getUserAvatarUrl(authorData);
    displayId = authorId;
  }

  const postDate = formatPostDate(post.date);
  const categoryColor = getCategoryColor('webinars');

  return (
    <Card className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200 w-full max-w-full">
      <Link href={`/posts/${post.slug}`} className="cursor-pointer">
        <div className="relative">
          {/* Video Embed for recorded webinars */}
          {isRecorded && (webinarEmbedUrl || youtubeUrl || vimeoUrl) ? (
            <div className="aspect-video overflow-hidden bg-muted">
              <VideoEmbed
                embedUrl={webinarEmbedUrl || youtubeUrl || vimeoUrl || ''}
                videoSource={youtubeUrl ? 'youtube' : 'vimeo'}
                title={decodedTitle}
                className="h-full"
              />
            </div>
          ) : (
            /* Fallback to webinar promo image */
            webinarImageUrl && (
              <div className="aspect-video overflow-hidden bg-muted relative">
                <Image
                  src={webinarImageUrl}
                  alt={decodedTitle}
                  width={1200}
                  height={675}
                  className={`${objectFitClass} w-full h-full post-image-glow`}
                  priority={false}
                  onLoad={handleImageLoad}
                />
                {/* Video overlay for recorded webinars */}
                {isRecorded && (youtubeUrl || vimeoUrl) && (
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                    <div className="bg-white bg-opacity-90 rounded-full p-4">
                      <Play className="h-8 w-8 text-dark-text" />
                    </div>
                  </div>
                )}
              </div>
            )
          )}
        </div>

        <CardHeader className="flex-row items-center gap-4 space-y-0 bg-white dark:bg-gray-900 pb-2">
          {isVendorPost && vendorData ? (
            // Show vendor logo and name for vendor posts
            <>
              <div className="h-10 w-10 ring-2 ring-[#5cc8ff]/20 overflow-hidden">
                <Image
                  src={avatarUrl || '/images/avatar-placeholder.svg'}
                  alt={displayName}
                  width={40}
                  height={40}
                  className="h-10 w-10 object-cover"
                />
              </div>
              <div className="flex flex-col">
                <div className="font-semibold text-[#3d405b] dark:text-gray-100">{displayName}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                </div>
              </div>
            </>
          ) : (
            // Show user avatar and name for personal posts
            <>
              <UserAvatarWithRank
                userId={displayId}
                avatarUrl={avatarUrl}
                displayName={displayName}
                size="h-10 w-10"
                containerSize="w-12 h-12"
                userRoles={(authorData?.roles as string[]) || []}
              />
              <div className="flex flex-col">
                <div className="font-semibold text-[#3d405b] dark:text-gray-100">{displayName}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                </div>
              </div>
            </>
          )}
        </CardHeader>

        <CardContent className="p-4 md:p-6 pt-2">
          <div
            className="inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full capitalize mb-4"
            style={{
              backgroundColor: categoryColor,
              color: 'white',
            }}
          >
            <Video className="h-4 w-4 text-white" />
            {isLive ? 'Live Webinar' : 'Recorded Webinar'}
          </div>
          <h3 className="text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-2">
            {decodedTitle}
          </h3>
          <div className="prose prose-sm dark:prose-invert line-clamp-4">
            <div
              className="text-[#3d405b] dark:text-gray-300 mb-4 last:mb-0"
              dangerouslySetInnerHTML={{ __html: excerpt }}
            />
          </div>
        </CardContent>
      </Link>

      {/* View Webinar Button */}
      <div className="px-6 pb-8">
        <Button
          asChild
          variant="outline"
          size="sm"
          className="w-fit text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
        >
          <Link href={`/posts/${post.slug}`}>
            <ExternalLink className="h-4 w-4 mr-2" />
            View Webinar
          </Link>
        </Button>
      </div>

      <CardFooter className="flex justify-center border-t p-4 bg-gray-100/50 dark:bg-gray-900">
        <PostInteractions
          postId={post.id}
          authorId={authorId}
          initialUpvotes={(() => {
            if (post.upvotes && typeof post.upvotes.count === 'number') {
              return post.upvotes.count;
            }
            return Number(post.meta?._upvotes) || 0;
          })()}
          initialCommentCount={
            Number(post.total_comments) || Number(post.meta?._comments_count) || 0
          }
          postTitle={decodedTitle}
          layout="full-width"
          onCommentsToggle={setShowComments}
        />
      </CardFooter>

      <PostComments
        postId={post.id}
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        commentCount={Number(post.total_comments) || Number(post.meta?._comments_count) || 0}
      />
    </Card>
  );
}
