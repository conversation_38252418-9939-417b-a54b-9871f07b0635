/*********************************************
# frontend/src/components/feed/PostCreator.tsx
# 01/27/2025 11:50pm Updated dropdown menu styling to match reference design with correct blue hover, proper spacing, and border radius
# 01/28/2025 12:00am Updated Start a post bar highlight color and made dropdown seamlessly expand from the bar
# 01/28/2025 12:05am Positioned dropdown 5px below bar, removed blue border, added subtle shadow, conditional scrollbar
# 02/07/2025 12:25pm Reduced personal/organization dropdown container padding by half and added spacing between menu items
# 02/07/2025 12:30pm Updated title input to pill-style, textarea to 20px corner radius, and details container styling
# 02/07/2025 12:35pm Added 20px corner radius to main post creation modal container
# 02/07/2025 1:10pm Replaced small image button with interactive drop zone above title field for better UX
# 02/07/2025 1:35pm Added "Post as:" label to personal/organization selector for better UX clarity
# 02/07/2025 1:40pm Enhanced modal header with 20% larger, bolder text and replaced dash with light blue chevron icon
# 02/07/2025 1:55pm Increased modal header text size by additional 30% for better prominence
**********************************************/

'use client';

import { useState, useEffect } from 'react';
// import { useRouter } from "next/navigation"; // Not currently used
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Image, Loader2, AlertCircle, ChevronDown, ChevronRight } from 'lucide-react';
// Using shadcn/ui's dropdown menu instead of popover
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogFooter } from '@/components/ui/dialog';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/auth-context';
import { createPost, getUserAssignedVendors } from '@/lib/api';
import { getCategories } from '@/lib/api';
import { Alert, AlertDescription } from '@/components/ui/alert';
import type { AssignedVendor } from '@/lib/api/categories';
import { useFeed } from '@/contexts/FeedContext';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import { PostCreatorAcfFields } from './PostCreatorAcfFields';
// import { useQueryClient } from '@tanstack/react-query';
import { invalidateQueries } from '@/lib/react-query';

// Helper function to validate ACF fields based on category
const validateAcfFields = (
  categorySlug: string | undefined,
  fields: Record<string, any>,
  imageFile: File | null
): string | null => {
  if (!categorySlug) return null;

  switch (categorySlug) {
    case 'news':
      if (!fields.url?.trim()) return 'External Article URL is required for news posts';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'blog-post':
      if (!fields.url?.trim()) return 'External URL is required for blog posts';
      if (!fields.author?.trim()) return 'Author is required for blog posts';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'event':
      if (!fields.event_date) return 'Event Start Date is required';
      if (!fields.event_end_date) return 'Event End Date is required';
      if (!fields.host_company_organization?.trim()) return 'Host Company/Organization is required';
      if (!fields.additional_details_link_url?.trim()) return 'Additional Details Link is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'webinar':
      if (!fields.webinar_type) return 'Webinar Type is required';
      if (fields.webinar_type === 'live' && !fields.date)
        return 'Date is required for live webinars';
      if (!fields.webinar_host_company_organization?.trim())
        return 'Host Company/Organization is required';
      if (fields.webinar_type === 'live' && !fields.register_url?.trim())
        return 'Registration URL is required for live webinars';
      if (
        fields.webinar_type === 'recorded' &&
        !fields.youtube_url?.trim() &&
        !fields.vimeo_url?.trim()
      )
        return 'YouTube or Vimeo URL is required for recorded webinars';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'video':
      if (!fields.video_source?.trim()) return 'Video Source is required';
      if (fields.video_source === 'youtube' && !fields.youtube_id?.trim())
        return 'YouTube URL is required';
      if (fields.video_source === 'vimeo' && !fields.vimeo_id?.trim())
        return 'Vimeo URL is required';
      if (fields.video_source === 'twitter' && !fields.x?.trim())
        return 'X (Twitter) Embed is required';
      if (fields.video_source === 'linkedin' && !fields.linkedin_iframe?.trim())
        return 'LinkedIn Embed is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'course':
      if (!fields.course_url?.trim()) return 'Course Details URL is required';
      if (!fields.company?.trim()) return 'Course Provider is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'presentation':
      if (!fields.host_company_organization?.trim()) return 'Company/Organization is required';
      if (!fields.pdf_upload) return 'PDF Upload is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'whitepaper':
    case 'case-study':
      if (!fields.upload_pdf) return 'PDF upload is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'book':
      if (!fields.author_names?.trim()) return 'Author Name(s) is required';
      if (!fields.purchase_url?.trim()) return 'Purchase URL is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'podcast':
      if (!fields.podcast_name?.trim()) return 'Podcast Name is required';
      if (!fields.hosts || !Array.isArray(fields.hosts) || fields.hosts.length === 0)
        return 'At least one host is required';
      if (fields.hosts.some((host: any) => !host.host_name?.trim()))
        return 'All host names must be filled in';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'press-release':
      if (!fields.press_release_url?.trim()) return 'Press Release URL is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'template':
      if (!fields.creator_name?.trim()) return 'Creator Name is required';
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
    case 'thought-leadership':
      if (fields.media_type === 'video') {
        if (!fields.video_type?.trim()) return 'Video Type is required when Media Type is Video';
        if (fields.video_type === 'youtube' && !fields.youtube_url?.trim())
          return 'YouTube URL is required';
        if (fields.video_type === 'vimeo' && !fields.vimeo_url?.trim())
          return 'Vimeo URL is required';
        if (fields.video_type === 'twitter' && !fields.twitter?.trim())
          return 'X (Twitter) Embed is required';
        if (fields.video_type === 'linkedin' && !fields.linkedin_iframe?.trim())
          return 'LinkedIn Embed is required';
      }
      if (imageFile && !fields.image_caption?.trim())
        return 'Image Caption is required when uploading an image';
      break;
  }

  return null;
};

export function PostCreator() {
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showPostModal, setShowPostModal] = useState(false);
  const [content, setContent] = useState('');
  const [title, setTitle] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<Array<{ id: number; name: string; slug: string }>>(
    []
  );
  const [selectedCategory, setSelectedCategory] = useState<number | ''>('');
  const [isLoading, setIsLoading] = useState(true);
  const [assignedVendors, setAssignedVendors] = useState<AssignedVendor[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<number | ''>('');
  const [isVendorOpen, setIsVendorOpen] = useState(false);

  // ACF Fields for all categories
  const [acfFields, setAcfFields] = useState<Record<string, any>>({});

  // const router = useRouter(); // Commented out - not currently used
  const { user } = useAuth();
  const { addPost } = useFeed();
  // const queryClient = useQueryClient();

  // Fetch categories and assigned vendors on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const cats = await getCategories();
        if (cats && cats.length > 0) {
          setCategories(
            cats
              .filter((cat) => cat.slug !== 'uncategorized' && cat.slug !== 'job')
              .map((cat) => ({
                id: cat.id,
                name: cat.name,
                slug: cat.slug,
              }))
          );
        } else {
          console.warn('No categories found');
        }

        // Fetch assigned vendors if user is logged in
        if (user) {
          try {
            const vendors = await getUserAssignedVendors();
            setAssignedVendors(vendors);
          } catch (error) {
            console.error('Error fetching assigned vendors:', error);
            // Don't show error to user by default
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        // Don't show error to user by default
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [user]);

  // Get the selected category name and slug
  const selectedCategoryObj = selectedCategory
    ? categories.find((cat) => cat.id === selectedCategory)
    : null;
  const selectedCategorySlug = selectedCategoryObj?.slug;

  // Get the selected vendor name
  const selectedVendorName = selectedVendor
    ? assignedVendors.find((vendor) => vendor.id === selectedVendor)?.name
    : null;

  if (!user) return null;

  const handleStartPost = () => {
    setShowCategoryDropdown(true);
  };

  const handleCategorySelect = (categoryId: number) => {
    setSelectedCategory(categoryId);
    setShowCategoryDropdown(false);
    setShowPostModal(true);
  };

  const handleCancel = () => {
    setShowPostModal(false);
    setShowCategoryDropdown(false);
    setContent('');
    setTitle('');
    setImagePreview(null);
    setImageFile(null);
    setSelectedCategory('');
    setSelectedVendor('');
    // Reset ACF fields
    setAcfFields({});
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Clear any previous errors
    setError(null);

    const file = e.target.files?.[0];
    if (file) {
      // Validate file size (8MB limit)
      if (file.size > 8 * 1024 * 1024) {
        setError('Image size is too large. Please select an image under 8MB.');
        return;
      }

      // Validate file type
      const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/webp', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        setError('Invalid file type. Please select PNG, JPG, JPEG, WEBP, or GIF images only.');
        return;
      }

      // Store the file object for upload
      setImageFile(file);

      // Create preview with FileReader
      const reader = new FileReader();
      reader.onloadend = () => {
        if (typeof reader.result === 'string') {
          setImagePreview(reader.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setImagePreview(null);
    setImageFile(null);
    // Clear image caption when removing image
    setAcfFields((prev) => {
      const newFields = { ...prev };
      delete newFields.image_caption;
      return newFields;
    });
  };

  // ACF field handlers
  const handleAcfFieldChange = (field: string, value: string) => {
    setAcfFields((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Clear any previous errors
    setError(null);

    // Validate form
    if (!title.trim()) {
      setError('Please add a title');
      return;
    }

    if (!content.trim()) {
      setError('Please add some content');
      return;
    }

    if (!selectedCategory) {
      setError('Please select a category');
      return;
    }

    // Validate required ACF fields based on category
    const validationError = validateAcfFields(selectedCategorySlug, acfFields, imageFile);
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setIsSubmitting(true);

      const postData = {
        title: title.trim(),
        content: content.trim(),
        imageFile,
        categories: [Number(selectedCategory)],
        ...(selectedVendor ? { vendorId: Number(selectedVendor) } : {}),
        // Add ACF fields based on category
        ...(Object.keys(acfFields).length > 0 && {
          acfFields: Object.entries(acfFields).reduce(
            (acc, [key, value]) => {
              // Only include non-empty values and image_caption only if there's an image
              if (key === 'image_caption' && !imageFile) return acc;
              if (value !== '' && value !== null && value !== undefined) {
                // Special handling for hosts repeater field
                if (key === 'hosts' && Array.isArray(value)) {
                  // Filter out empty hosts and format for ACF repeater
                  const validHosts = value.filter(
                    (host) => host.host_name && host.host_name.trim()
                  );
                  if (validHosts.length > 0) {
                    // Format as ACF repeater field expects
                    acc[key] = validHosts.map((host) => ({
                      host_name: host.host_name.trim(),
                    }));
                  }
                } else {
                  acc[key] = typeof value === 'string' ? value.trim() : value;
                }
              }
              return acc;
            },
            {} as Record<string, any>
          ),
        }),
      };

      // DEBUG LOG
      console.log('[PostCreator] Creating post with data:', {
        ...postData,
        imageFile: postData.imageFile ? 'File included' : 'No file',
        selectedVendor,
        selectedCategory,
        selectedCategorySlug,
        acfFields: postData.acfFields, // Log the ACF fields specifically
      });

      const result = await createPost(postData);

      if (result) {
        console.log('[PostCreator] Post created successfully:', result);

        // Fetch the complete post data with embedded information (author, categories, etc.)
        try {
          const response = await fetch(`/api/wp-proxy/posts?include=${result.id}&_embed=1`, {
            credentials: 'include',
          });

          if (response.ok) {
            const [completePost] = await response.json();
            console.log('[PostCreator] Complete post data fetched:', completePost);
            if (completePost) {
              // Add the complete post with all embedded data to the feed
              addPost(completePost);
            } else {
              // Fallback to the basic post if fetching complete data fails
              addPost(result);
            }
          } else {
            // Fallback to the basic post if fetching complete data fails
            addPost(result);
          }
        } catch (fetchError) {
          console.warn('Failed to fetch complete post data, using basic post:', fetchError);
          // Fallback to the basic post
          addPost(result);
        }

        // Invalidate caches to update both main feed and profile posts tab
        invalidateQueries.userPosts(user.id);
        invalidateQueries.allPosts();

        handleCancel(); // Reset form
        // No need to refresh the page anymore
      } else {
        throw new Error('Failed to create post');
      }
    } catch (error) {
      console.error('Error creating post:', error);
      setError(
        `Failed to create post: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {/* Collapsed State - New Design */}
      <Card
        className={cn(
          'bg-white border-2 border-gray-200 rounded-full shadow-sm hover:shadow-md transition-all p-0 hover:border-[#5cc8ff]',
          showCategoryDropdown && 'ring-2 ring-[#5cc8ff] border-[#5cc8ff]'
        )}
      >
        <CardContent className="p-3">
          <DropdownMenu
            open={showCategoryDropdown}
            onOpenChange={setShowCategoryDropdown}
            modal={false}
          >
            <DropdownMenuTrigger asChild>
              <div
                className="flex items-center gap-3 w-full cursor-pointer"
                onClick={handleStartPost}
              >
                <div className="flex-shrink-0">
                  <UserAvatarWithRank
                    userId={user.id}
                    avatarUrl={
                      user.avatarUrl ||
                      (typeof user.acf?.profile_picture === 'string'
                        ? user.acf.profile_picture
                        : user.acf?.profile_picture?.url) ||
                      ''
                    }
                    displayName={user.name || ''}
                    userRoles={user.roles || []}
                    size="h-8 w-8"
                    containerSize="w-10 h-10"
                  />
                </div>
                <div className="flex-grow">
                  <span className="font-bold">Start a post</span>
                </div>
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-full min-w-[var(--radix-dropdown-menu-trigger-width)] bg-white border border-gray-200 rounded-[30px] shadow-[0_8px_32px_rgba(0,0,0,0.12)] p-0 mt-[15px] relative z-10"
              align="start"
            >
              <div className="max-h-[60vh] overflow-y-auto p-2">
                {isLoading ? (
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  categories.map((category) => (
                    <DropdownMenuItem
                      key={category.id}
                      className="cursor-pointer rounded-[30px] px-4 py-3 text-base font-medium text-[#3d405b] hover:bg-[#5cc8ff]/15 focus:bg-[#5cc8ff]/15 hover:text-[#3d405b] focus:text-[#3d405b] transition-all duration-200 ease-in-out focus:outline-none border-0 mb-1"
                      onSelect={(e) => {
                        e.preventDefault();
                        handleCategorySelect(category.id);
                      }}
                    >
                      <span>{category.name}</span>
                    </DropdownMenuItem>
                  ))
                )}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardContent>
      </Card>

      {/* Modal Dialog for Post Creation */}
      <Dialog open={showPostModal} onOpenChange={setShowPostModal}>
        <DialogContent className="max-w-2xl max-w-[calc(100%-3rem)] max-h-[90vh] overflow-y-auto rounded-[20px]">
          <DialogHeader>
            <div className="flex items-center gap-2 text-[1.56em] font-extrabold text-[#3d405b]">
              <span>Create Post</span>
              <ChevronRight className="h-5 w-5 text-[#5cc8ff]" />
              <span>{selectedCategoryObj?.name}</span>
            </div>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {selectedVendor ? (
                  <Avatar className="h-10 w-10">
                    <AvatarImage
                      src={assignedVendors.find((v) => v.id === selectedVendor)?.logo_url || ''}
                      alt={selectedVendorName || ''}
                    />
                    <AvatarFallback>{selectedVendorName?.charAt(0) || 'V'}</AvatarFallback>
                  </Avatar>
                ) : (
                  <UserAvatarWithRank
                    userId={user.id}
                    avatarUrl={
                      user.avatarUrl ||
                      (typeof user.acf?.profile_picture === 'string'
                        ? user.acf.profile_picture
                        : user.acf?.profile_picture?.url) ||
                      ''
                    }
                    displayName={user.name || ''}
                    userRoles={user.roles || []}
                  />
                )}
                <div>
                  <span className="text-sm font-medium">
                    {selectedVendorName ||
                      (user.firstName && user.lastName
                        ? `${user.firstName} ${user.lastName}`
                        : user.name)}
                  </span>
                  {selectedVendorName && (
                    <p className="text-xs text-muted-foreground">Posting as {selectedVendorName}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Posting As Selector */}
                {assignedVendors.length > 0 && (
                  <>
                    <span className="text-sm text-gray-600 font-medium">Post as:</span>
                    <DropdownMenu onOpenChange={setIsVendorOpen} modal={false}>
                      <DropdownMenuTrigger asChild>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="flex items-center text-sm"
                        >
                          <span>{selectedVendor ? 'Vendor' : 'Personal'}</span>
                          <ChevronDown
                            className={cn(
                              'ml-1 h-4 w-4 transition-transform',
                              isVendorOpen ? 'transform rotate-180' : ''
                            )}
                          />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-56 p-1"
                        align="end"
                        style={{ borderRadius: '25px' }}
                      >
                        <DropdownMenuItem
                          className={cn(
                            'cursor-pointer flex items-center justify-between rounded-full transition-all duration-200 mb-1',
                            !selectedVendor
                              ? '!bg-[rgba(92,200,255,0.2)] !text-[#3d405b]'
                              : 'hover:!bg-[rgba(92,200,255,0.2)] hover:!text-[#3d405b] focus:!bg-[rgba(92,200,255,0.2)] focus:!text-[#3d405b]'
                          )}
                          onSelect={(e) => {
                            e.preventDefault();
                            setSelectedVendor('');
                            setIsVendorOpen(false);
                          }}
                        >
                          <div className="flex items-center gap-2">
                            <UserAvatarWithRank
                              userId={user.id}
                              avatarUrl={
                                user.avatarUrl ||
                                (typeof user.acf?.profile_picture === 'string'
                                  ? user.acf.profile_picture
                                  : user.acf?.profile_picture?.url) ||
                                ''
                              }
                              displayName={user.name || ''}
                              size="h-6 w-6"
                              containerSize="w-8 h-8"
                              userRoles={user.roles || []}
                            />
                            <span className="text-sm">Personal</span>
                          </div>
                          {!selectedVendor && <Check className="h-4 w-4 text-primary" />}
                        </DropdownMenuItem>

                        {assignedVendors.map((vendor) => (
                          <DropdownMenuItem
                            key={vendor.id}
                            className={cn(
                              'cursor-pointer flex items-center justify-between rounded-full transition-all duration-200',
                              selectedVendor === vendor.id
                                ? '!bg-[rgba(92,200,255,0.2)] !text-[#3d405b]'
                                : 'hover:!bg-[rgba(92,200,255,0.2)] hover:!text-[#3d405b] focus:!bg-[rgba(92,200,255,0.2)] focus:!text-[#3d405b]'
                            )}
                            onSelect={(e) => {
                              e.preventDefault();
                              setSelectedVendor(vendor.id);
                              setIsVendorOpen(false);
                            }}
                          >
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={vendor.logo_url || ''} alt={vendor.name} />
                                <AvatarFallback className="text-sm">
                                  {vendor.name.charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm">{vendor.name}</span>
                            </div>
                            {selectedVendor === vendor.id && (
                              <Check className="h-4 w-4 text-primary" />
                            )}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </>
                )}
              </div>
            </div>

            {/* Image Drop Zone */}
            <div className="space-y-4">
              <div
                className={`border-2 border-dashed rounded-[20px] p-6 text-center transition-all duration-200 cursor-pointer ${
                  imagePreview
                    ? 'border-[#5cc8ff] bg-[#5cc8ff]/5'
                    : 'border-gray-300 hover:border-[#5cc8ff] hover:bg-gray-50'
                }`}
                onClick={() => document.getElementById('image-upload')?.click()}
                onDragOver={(e) => {
                  e.preventDefault();
                  e.currentTarget.classList.add('border-[#5cc8ff]', 'bg-[#5cc8ff]/5');
                }}
                onDragLeave={(e) => {
                  e.preventDefault();
                  if (!imagePreview) {
                    e.currentTarget.classList.remove('border-[#5cc8ff]', 'bg-[#5cc8ff]/5');
                  }
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  const files = e.dataTransfer.files;
                  if (files.length > 0) {
                    handleImageChange({ target: { files } } as any);
                  }
                }}
              >
                {imagePreview ? (
                  <div className="space-y-3">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-32 object-cover rounded-lg mx-auto"
                    />
                    <div className="flex items-center justify-center gap-2">
                      <Image className="h-4 w-4 text-[#5cc8ff]" />
                      <span className="text-sm text-[#5cc8ff] font-medium">
                        Image uploaded successfully
                      </span>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeImage();
                      }}
                      className="text-red-600 border-red-200 hover:bg-red-50"
                    >
                      Remove Image
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <Image className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Drop an image here or click to browse
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        PNG, JPG, JPEG, WEBP, or GIF up to 8MB
                      </p>
                    </div>
                  </div>
                )}
                <input
                  id="image-upload"
                  type="file"
                  className="hidden"
                  accept=".png,.jpg,.jpeg,.webp,.gif"
                  onChange={handleImageChange}
                />
              </div>
            </div>

            <input
              type="text"
              placeholder="Add a title..."
              className="w-full text-sm font-medium focus:outline-none border border-gray-200 rounded-full px-3 py-2 placeholder:text-sm placeholder:text-gray-500"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
            />

            <div className="space-y-4">
              <Textarea
                placeholder="What would you like to share?"
                className="min-h-[120px] resize-none text-sm placeholder:text-sm placeholder:text-gray-500 rounded-[20px]"
                value={content}
                onChange={(e) => setContent(e.target.value)}
              />
            </div>

            {/* ACF Fields based on selected category */}
            {selectedCategorySlug && (
              <PostCreatorAcfFields
                categorySlug={selectedCategorySlug}
                fields={acfFields}
                handleFieldChange={handleAcfFieldChange}
                imageFile={imageFile}
              />
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff] hover:text-[#3d405b] font-extrabold"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!content.trim() || !title.trim() || isSubmitting}
                className="bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff] font-extrabold border-2 border-[#5cc8ff]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Posting...
                  </>
                ) : (
                  'Post'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}
