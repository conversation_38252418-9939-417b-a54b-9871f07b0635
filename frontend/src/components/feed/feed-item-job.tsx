'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardFooter } from '@/components/ui/card';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import Link from 'next/link';
import { decodeHtml } from '@/lib/api/utils';
import { WPPost as Post } from '@/lib/api/wordpress';
import { PostInteractions } from './PostInteractions';
import { Briefcase, ExternalLink } from 'lucide-react';
import {
  getCategoryColor,
  formatPostDate,
  getUserFullName,
  getUserAvatarUrl,
  useImageObjectFit,
} from './feed-item-helpers';
import Image from 'next/image';

// Vendor info interface
interface VendorInfo {
  id: number;
  name: string;
  slug: string;
  logo_url?: string;
}

// Complete interface based on ACF fields for job posts
interface JobPost extends Post {
  acf?: {
    position_title?: string;
    company?: string;
    company_logo?: {
      url?: string;
      [key: string]: unknown;
    };
    location?: string;
    job_type?:
      | {
          value?: string;
          label?: string;
        }
      | string;
    experience_level?: string;
    compensation?: string;
    posting_link_url?: string;
  };
  vendor_info?: VendorInfo;
  vendor_data?: any;
}

interface FeedItemJobProps {
  post: JobPost;
}

export function FeedItemJob({ post }: FeedItemJobProps) {
  const [excerpt, setExcerpt] = useState('');
  const { objectFitClass, handleImageLoad } = useImageObjectFit();
  const acf = post.acf;
  const decodedTitle = decodeHtml(post.title.rendered);

  useEffect(() => {
    setExcerpt(decodeHtml(post.excerpt?.rendered || ''));
  }, [post.excerpt?.rendered]);

  // Get job-specific data from ACF fields
  const positionTitle = acf?.position_title || decodedTitle;
  const location = acf?.location;
  const postingUrl = acf?.posting_link_url;

  // Use featured image if available
  const featuredImageUrl = post._embedded?.['wp:featuredmedia']?.[0]?.source_url;

  // Get author information
  const authorData = post._embedded?.author?.[0];
  const authorId = Number(authorData?.id || post.author || 1);

  // Check if this is a vendor post
  const vendorId = post.vendor_info?.id || post.meta?.post_vendor_id;
  const isVendorPost = Boolean(vendorId && vendorId !== 0);
  const vendorData = post.vendor_info || post.vendor_data || null;

  // Determine display name and avatar based on vendor vs personal post
  let displayName: string;
  let avatarUrl: string;
  let displayId: number;

  if (isVendorPost && vendorData) {
    // Use vendor information
    displayName = vendorData.name || 'Vendor';
    avatarUrl = vendorData.logo_url || '';
    displayId = vendorData.id;
  } else {
    // Use regular author information
    displayName = getUserFullName(authorData);
    avatarUrl = getUserAvatarUrl(authorData);
    displayId = authorId;
  }

  const postDate = formatPostDate(post.date);
  const categoryColor = getCategoryColor('jobs'); // Red for jobs

  return (
    <Card className="mb-4 overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-200">
      <Link href={`/posts/${post.slug}`} className="cursor-pointer">
        <div className="relative">
          {featuredImageUrl && (
            <div className="aspect-video overflow-hidden bg-muted">
              <Image
                src={featuredImageUrl}
                alt={positionTitle}
                width={1200}
                height={675}
                className={`${objectFitClass} w-full h-full post-image-glow`}
                onLoad={handleImageLoad}
                priority={false}
              />
            </div>
          )}
        </div>

        <CardHeader className="flex-row items-center gap-4 space-y-0 bg-white dark:bg-gray-900 pb-2">
          {isVendorPost && vendorData ? (
            // Show vendor logo and name for vendor posts
            <>
              <div className="h-10 w-10 ring-2 ring-[#5cc8ff]/20 rounded-full overflow-hidden">
                <img
                  src={avatarUrl || '/images/avatar-placeholder.svg'}
                  alt={displayName}
                  className="h-10 w-10 object-cover rounded-full"
                />
              </div>
              <div className="flex flex-col">
                <div className="font-semibold text-[#3d405b] dark:text-gray-100">{displayName}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                  {location && (
                    <>
                      <span>•</span>
                      <span>{location}</span>
                    </>
                  )}
                </div>
              </div>
            </>
          ) : (
            // Show user avatar and name for personal posts
            <>
              <UserAvatarWithRank
                userId={displayId}
                avatarUrl={avatarUrl}
                displayName={displayName}
                size="h-10 w-10"
                containerSize="w-12 h-12"
                userRoles={(authorData?.roles as string[]) || []}
              />
              <div className="flex flex-col">
                <div className="font-semibold text-[#3d405b] dark:text-gray-100">{displayName}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>{postDate}</span>
                  {location && (
                    <>
                      <span>•</span>
                      <span>{location}</span>
                    </>
                  )}
                </div>
              </div>
            </>
          )}
        </CardHeader>

        <CardContent className="p-4 md:p-6 pt-2">
          <div
            className="inline-flex items-center gap-1.5 text-sm font-bold px-3 py-1 rounded-full capitalize mb-4"
            style={{
              backgroundColor: categoryColor,
              color: 'white',
            }}
          >
            <Briefcase className="h-4 w-4 text-white" />
            Job
          </div>
          <h3 className="text-[28px] font-extrabold text-[#3d405b] dark:text-gray-100 leading-tight mb-2">
            {positionTitle}
          </h3>
          <div className="prose prose-sm dark:prose-invert line-clamp-4">
            <p
              className="text-[#3d405b] dark:text-gray-300 mb-4 last:mb-0"
              dangerouslySetInnerHTML={{
                __html: excerpt,
              }}
            />
          </div>
        </CardContent>
      </Link>

      {/* Apply Now Button - moved outside Link to avoid nested anchor tags */}
      {postingUrl && (
        <div className="p-6 pt-0">
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="flex items-start gap-2 text-sm">
              <ExternalLink className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <span className="text-gray-600">Apply Now: </span>
                <a
                  href={postingUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-colors text-sm font-medium"
                >
                  <ExternalLink className="h-4 w-4" />
                  Apply Now
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      <CardFooter className="flex justify-center border-t p-4 bg-gray-100/50 dark:bg-gray-900">
        <PostInteractions
          postId={post.id}
          authorId={authorId}
          initialUpvotes={(() => {
            if (post.upvotes && typeof post.upvotes.count === 'number') {
              return post.upvotes.count;
            }
            return Number(post.meta?._upvotes) || 0;
          })()}
          initialCommentCount={
            Number(post.total_comments) || Number(post.meta?._comments_count) || 0
          }
          postTitle={positionTitle}
          layout="full-width"
        />
      </CardFooter>
    </Card>
  );
}
