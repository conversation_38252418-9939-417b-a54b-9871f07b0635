/*********************************************
# frontend/src/components/leaderboard/Leaderboard.tsx
# 02/07/2025 4:15pm Removed overlay backdrop and enhanced with sophisticated shadow for non-modal slideout behavior
**********************************************/

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { X, Trophy, Info, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetClose,
} from '@/components/ui/sheet';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import { iqScoreAPI, type LeaderboardEntry } from '@/lib/api/iq-score';

interface LeaderboardProps {
  isOpen: boolean;
  onClose: () => void;
}

export function Leaderboard({ isOpen, onClose }: LeaderboardProps) {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch leaderboard data when component mounts or opens
  useEffect(() => {
    if (isOpen) {
      fetchLeaderboard();
    }
  }, [isOpen]);

  const fetchLeaderboard = async (forceRefresh: boolean = true) => {
    try {
      setLoading(true);
      setError(null);
      const response = await iqScoreAPI.getLeaderboard(20, forceRefresh);
      if (response.success) {
        setLeaderboard(response.data);
      } else {
        setError('Failed to load leaderboard');
      }
    } catch (err) {
      console.error('Error fetching leaderboard:', err);
      setError('Failed to load leaderboard');
    } finally {
      setLoading(false);
    }
  };

  // Format user name for display
  const getUserDisplayName = (user: LeaderboardEntry) => {
    return user.display_name || user.username;
  };

  // Get user avatar URL with proper fallback handling (avoid Gravatar)
  const getUserAvatarUrl = (user: LeaderboardEntry): string | undefined => {
    // If profile_picture exists and is not a Gravatar URL, use it
    if (
      user.profile_picture &&
      typeof user.profile_picture === 'string' &&
      user.profile_picture.trim() !== '' &&
      !user.profile_picture.includes('gravatar.com') &&
      !user.profile_picture.includes('secure.gravatar.com')
    ) {
      return user.profile_picture;
    }

    // Return undefined to trigger fallback initials instead of Gravatar
    return undefined;
  };

  // Get border color for top 3
  const getTopBorderColor = (position: number) => {
    if (position === 1) return 'border-amber-200';
    if (position === 2) return 'border-zinc-200';
    if (position === 3) return 'border-amber-700/30';
    return 'border-gray-200';
  };

  // Get trophy badge background for top 3
  const getTrophyBgColor = (position: number) => {
    if (position === 1) return 'bg-amber-400';
    if (position === 2) return 'bg-zinc-300';
    if (position === 3) return 'bg-amber-700';
    return 'bg-gray-300';
  };

  return (
    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <SheetContent className="w-[400px] rounded-none p-0 overflow-y-auto" side="right" hideOverlay>
        <SheetHeader className="sr-only">
          <SheetTitle>TourismIQ Leaderboard</SheetTitle>
          <SheetDescription>Top users ranked by IQ score</SheetDescription>
        </SheetHeader>

        {/* Header with close button */}
        <div className="sticky top-0 bg-white z-10 px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-6 w-6 text-primary" />
              <h2 className="text-xl font-bold">TourismIQ Leaderboard</h2>
            </div>
            <SheetClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full hover:bg-gray-100"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </SheetClose>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Top users ranked by IQ score. Engage more to increase your score!
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-5 w-5 p-0 ml-1">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    IQ Scores are earned through various activities: creating content, engaging with
                    others, daily logins, and community participation.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading leaderboard...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="px-6 py-12">
            <div className="text-center">
              <p className="text-sm text-red-600 mb-4">{error}</p>
              <Button onClick={() => fetchLeaderboard()} variant="outline" size="sm">
                Try Again
              </Button>
            </div>
          </div>
        )}

        {/* Leaderboard Content */}
        {!loading && !error && leaderboard.length > 0 && (
          <>
            {/* Top 3 Users - Featured Section */}
            <div className="bg-gradient-to-br from-[#fef7cd] via-[#e0e7ff] via-[#fce7f3] to-[#dbeafe] opacity-90 px-6 py-8">
              <h3 className="text-lg font-semibold mb-4">Top Contributors</h3>
              <div className="space-y-6">
                {leaderboard.slice(0, 3).map((user, index) => {
                  const position = index + 1;
                  return (
                    <div
                      key={user.id}
                      className={`flex items-center gap-4 p-4 bg-white rounded-[20px] border-2 ${getTopBorderColor(position)}`}
                      style={{
                        boxShadow: '0 10px 30px rgba(0,0,0,0.12), 0 4px 12px rgba(0,0,0,0.08)',
                      }}
                    >
                      <div className="relative flex-shrink-0">
                        <UserAvatarWithRank
                          userId={user.id}
                          avatarUrl={getUserAvatarUrl(user) || '/images/avatar-placeholder.svg'}
                          displayName={getUserDisplayName(user)}
                          size="h-16 w-16"
                          containerSize="w-20 h-20"
                          showRankBadge={true}
                          userRoles={user.is_founder ? ['founder'] : []}
                          iqScoreData={{
                            score: user.iq_score,
                            rank: user.rank.name,
                            rank_data: user.rank as any as any,
                          }}
                        />
                        <div
                          className={`absolute -bottom-2 -right-2 ${getTrophyBgColor(position)} rounded-full p-1`}
                        >
                          <Trophy className="h-5 w-5 text-white" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span
                            className={`text-sm font-semibold ${
                              position === 1
                                ? 'text-amber-500'
                                : position === 2
                                  ? 'text-zinc-500'
                                  : 'text-amber-700'
                            }`}
                          >
                            {position === 1 ? '1st' : position === 2 ? '2nd' : '3rd'} Place
                          </span>
                        </div>
                        <Link
                          href={`/profile/${user.username}`}
                          className="group"
                          onClick={onClose}
                        >
                          <div className="font-bold group-hover:underline">
                            {getUserDisplayName(user)}
                          </div>
                          <div className="text-sm text-muted-foreground">@{user.username}</div>
                        </Link>
                        <div className="mt-1 text-base font-bold text-primary">
                          {iqScoreAPI.formatScore(user.iq_score)} points
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Remaining Users (4+) */}
            {leaderboard.length > 3 && (
              <div className="px-6 py-4">
                <h3 className="text-md font-semibold mb-4">Leaderboard</h3>
                <div className="space-y-3">
                  {leaderboard.slice(3).map((user) => (
                    <Link
                      key={user.id}
                      href={`/profile/${user.username}`}
                      className="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors"
                      onClick={onClose}
                    >
                      <div className="flex-shrink-0 w-8 text-center font-semibold text-muted-foreground">
                        {user.position}
                      </div>
                      <UserAvatarWithRank
                        userId={user.id}
                        avatarUrl={getUserAvatarUrl(user) || '/images/avatar-placeholder.svg'}
                        displayName={getUserDisplayName(user)}
                        size="h-10 w-10"
                        containerSize="w-14 h-14"
                        showRankBadge={true}
                        userRoles={user.is_founder ? ['founder'] : []}
                        iqScoreData={{
                          score: user.iq_score,
                          rank: user.rank.name,
                          rank_data: user.rank as any,
                        }}
                      />
                      <div className="flex-1 min-w-0 ml-3">
                        <div className="font-medium truncate">{getUserDisplayName(user)}</div>
                        <div className="text-xs text-muted-foreground truncate">
                          @{user.username}
                        </div>
                      </div>
                      <div className="ml-2 font-bold text-primary">
                        {iqScoreAPI.formatScore(user.iq_score)}
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </>
        )}

        {/* Empty State */}
        {!loading && !error && leaderboard.length === 0 && (
          <div className="px-6 py-12">
            <div className="text-center">
              <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-sm text-muted-foreground">No leaderboard data available yet.</p>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
