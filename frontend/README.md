# TourismIQ Frontend

A Next.js 15 application with WordPress headless CMS integration, featuring real-time messaging, forums, and social features.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- pnpm (recommended) or npm
- WordPress backend with REST API enabled

### Installation

```bash
# Install dependencies
pnpm install

# Copy environment variables
cp .env.example .env

# Configure your environment variables (see Environment Configuration below)
# Edit .env with your actual values

# Install socket server dependencies
pnpm run setup:socket

# Run development server
pnpm run dev:all
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🔧 Environment Configuration

### Required Environment Variables

Create a `.env` file in the root directory with the following variables:

```bash
# WordPress REST API Configuration
NEXT_PUBLIC_WORDPRESS_API_URL=https://your-wordpress-site.com

# WordPress Authentication (Server-side only - NEVER expose to client)
WORDPRESS_USERNAME=your_username
WORDPRESS_APPLICATION_PASSWORD=your_application_password

# Production WordPress Domain (for image optimization)
WORDPRESS_DOMAIN=your-production-domain.com

# Socket.io Configuration
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
NEXT_PUBLIC_SOCKET_URL_PROD=https://tourismqwp-production.up.railway.app

# Optional: Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000
```

### Security Notes

⚠️ **CRITICAL**: Never commit `.env` files or expose WordPress credentials to the client-side.

- WordPress credentials are used server-side only for API authentication
- Use WordPress Application Passwords instead of regular passwords
- All sensitive data is handled server-side through API routes

## 🏗️ Architecture

### Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **State Management**: React Context + Custom Hooks
- **Data Fetching**: Native fetch with caching
- **Real-time**: Socket.io
- **Backend**: WordPress REST API

### Key Features

- 🔐 WordPress cookie-based authentication
- 💬 Real-time messaging with Socket.io
- 📱 Responsive design with Tailwind CSS
- 🚀 Performance optimized with caching
- 🛡️ Security headers and rate limiting
- 🎯 TypeScript strict mode
- 🔄 Error boundaries for graceful error handling

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes (WordPress proxy)
│   ├── (pages)/           # Application pages
│   └── layout.tsx         # Root layout
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components
│   ├── layout/           # Layout components
│   └── [feature]/        # Feature-specific components
├── contexts/             # React Context providers
├── hooks/                # Custom React hooks
├── lib/                  # Utilities and configurations
│   ├── api/             # API utilities
│   ├── cache.ts         # Caching utilities
│   └── rate-limit.ts    # Rate limiting
└── types/               # TypeScript type definitions
```

## 🔒 Security Features

### Implemented Security Measures

1. **Environment Variable Security**
   - WordPress credentials never exposed to client
   - Proper separation of public/private env vars

2. **Security Headers**
   - X-Frame-Options: DENY
   - X-Content-Type-Options: nosniff
   - X-XSS-Protection: 1; mode=block
   - Strict-Transport-Security
   - Referrer-Policy: strict-origin-when-cross-origin

3. **Rate Limiting**
   - API endpoint protection
   - Configurable limits per endpoint type
   - IP-based tracking with user agent

4. **Authentication**
   - WordPress cookie-based auth
   - Secure session handling
   - Protected route middleware

## ⚡ Performance Optimizations

### Caching Strategy

- **Server-side caching**: In-memory cache for API responses
- **Image optimization**: Next.js Image component with WebP/AVIF
- **Bundle optimization**: Tree shaking and code splitting
- **CSS optimization**: Tailwind CSS purging

### Cache Configuration

```typescript
// Cache TTL configurations
export const cacheTTL = {
  posts: 300,      // 5 minutes
  user: 600,       // 10 minutes
  categories: 1800, // 30 minutes
  vendors: 900,    // 15 minutes
  forum: 180,      // 3 minutes
  iqScore: 300,    // 5 minutes
};
```

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Connect Repository**
   ```bash
   # Push to GitHub
   git push origin main
   
   # Connect to Vercel
   vercel --prod
   ```

2. **Environment Variables**
   Set the following in Vercel dashboard:
   - `NEXT_PUBLIC_WORDPRESS_API_URL`
   - `WORDPRESS_USERNAME`
   - `WORDPRESS_APPLICATION_PASSWORD`
   - `WORDPRESS_DOMAIN`
   - `NEXT_PUBLIC_SOCKET_URL_PROD`

3. **Socket Server Deployment**
   Deploy the socket server separately:
   ```bash
   # Deploy to Heroku, Railway, or DigitalOcean
   cd socket-server
   # Follow your hosting provider's deployment guide
   ```

### Production Checklist

- [ ] Environment variables configured
- [ ] WordPress Application Passwords created
- [ ] Socket server deployed and accessible
- [ ] SSL certificates configured
- [ ] Domain DNS configured
- [ ] Error monitoring setup (optional)

## 🧪 Development

### Available Scripts

```bash
# Development
pnpm dev              # Next.js only
pnpm dev:socket       # Socket server only  
pnpm dev:all          # Both Next.js and Socket server

# Production
pnpm build            # Build for production
pnpm start            # Start production server
pnpm start:all        # Start both servers in production

# Utilities
pnpm lint             # Run ESLint
pnpm type-check       # Run TypeScript checks
```

### Code Quality

- **TypeScript**: Strict mode enabled
- **ESLint**: Next.js recommended config
- **Error Boundaries**: Graceful error handling
- **Type Safety**: Comprehensive type definitions

## 🔧 WordPress Integration

### Required WordPress Setup

1. **REST API**: Ensure WordPress REST API is enabled
2. **Application Passwords**: Create application passwords for API access
3. **CORS**: Configure CORS headers if needed
4. **Custom Fields**: ACF (Advanced Custom Fields) for extended user data

### API Endpoints

The application uses WordPress REST API through proxy routes:

- `/api/wp-proxy/auth/status` - Authentication status
- `/api/wp-proxy/posts` - Posts with caching
- `/api/wp-proxy/users/me` - Current user data
- `/api/wp-proxy/categories` - Post categories
- And more...

## 🐛 Troubleshooting

### Common Issues

1. **Authentication Issues**
   - Verify WordPress Application Passwords
   - Check CORS configuration
   - Ensure cookies are being set correctly

2. **Socket Connection Issues**
   - Verify socket server is running
   - Check NEXT_PUBLIC_SOCKET_URL configuration
   - Ensure firewall allows socket connections

3. **Build Issues**
   - Run `pnpm install` to ensure dependencies
   - Check TypeScript errors with `pnpm type-check`
   - Verify environment variables are set

### Development Tips

- Use browser dev tools to inspect network requests
- Check server logs for API errors
- Use React DevTools for component debugging
- Monitor console for client-side errors

## 📚 Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [WordPress REST API](https://developer.wordpress.org/rest-api/)
- [Socket.io Documentation](https://socket.io/docs/v4/)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Radix UI](https://www.radix-ui.com/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is proprietary and confidential.
