// Suppress Next.js cache logs completely
process.env.NEXT_PRIVATE_DEBUG_CACHE = '0';
process.env.NEXT_PRIVATE_DEBUG_CACHE_HANDLER = '0';
process.env.NEXT_PRIVATE_DEBUG_HANDLER = '0';
process.env.NEXT_PRIVATE_DEBUG_FLIGHT = '0';
process.env.NEXT_PRIVATE_DEBUG_INSTRUMENTATION = '0';

// Only suppress cache logs, keep other logs
if (process.env.NODE_ENV !== 'production') {
  const originalConsole = { ...console };
  const originalWrite = process.stdout.write;
  const originalErrWrite = process.stderr.write;

  // Override process streams to only block cache logs
  process.stdout.write = function (chunk, encoding, callback) {
    const str = chunk.toString();

    // Block only cache-related logs
    if (
      str.includes('use-cache:') ||
      str.includes('using filesystem cache handler') ||
      str.includes('using memory store for fetch cache') ||
      str.includes('cache skip') ||
      str.includes('cache hit') ||
      str.includes('cache miss') ||
      str.includes('Cache skipped reason:') ||
      str.includes('pending revalidates promise finished') ||
      str.includes('│ │ Cache skipped reason:')
    ) {
      if (typeof callback === 'function') callback();
      return true;
    }

    // Allow everything else
    return originalWrite.call(process.stdout, chunk, encoding, callback);
  };

  process.stderr.write = function (chunk, encoding, callback) {
    const str = chunk.toString();

    // Block only cache-related logs
    if (
      str.includes('use-cache:') ||
      str.includes('using filesystem cache handler') ||
      str.includes('using memory store for fetch cache') ||
      str.includes('cache skip') ||
      str.includes('cache hit') ||
      str.includes('cache miss') ||
      str.includes('Cache skipped reason:') ||
      str.includes('pending revalidates promise finished') ||
      str.includes('│ │ Cache skipped reason:')
    ) {
      if (typeof callback === 'function') callback();
      return true;
    }

    // Allow everything else
    return originalErrWrite.call(process.stderr, chunk, encoding, callback);
  };

  // Override console methods as backup for cache logs only
  ['log', 'info', 'warn', 'error', 'debug'].forEach((method) => {
    console[method] = (...args) => {
      const logString = args.join(' ');

      // Block only cache-related logs
      if (
        logString.includes('use-cache:') ||
        logString.includes('using filesystem cache handler') ||
        logString.includes('using memory store for fetch cache') ||
        logString.includes('cache skip') ||
        logString.includes('cache hit') ||
        logString.includes('cache miss') ||
        logString.includes('Cache skipped reason:') ||
        logString.includes('pending revalidates promise finished') ||
        logString.includes('│ │ Cache skipped reason:')
      ) {
        return; // Suppress only cache logs
      }

      // Allow everything else
      return originalConsole[method].apply(console, args);
    };
  });
}

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const { initSocketServer } = require('./socket-server/server');
const net = require('net');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const defaultPort = process.env.PORT || (dev ? 3000 : 8080);

// Function to check if a port is available
function isPortAvailable(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.listen(port, (err) => {
      if (err) {
        resolve(false);
      } else {
        server.once('close', () => {
          resolve(true);
        });
        server.close();
      }
    });
    server.on('error', () => {
      resolve(false);
    });
  });
}

// Function to find next available port
async function findAvailablePort(startPort) {
  let port = startPort;
  while (!(await isPortAvailable(port))) {
    port++;
    // Safety check to avoid infinite loop
    if (port > startPort + 100) {
      throw new Error(`No available ports found in range ${startPort}-${startPort + 100}`);
    }
  }
  return port;
}

// Initialize Next.js app with custom configuration
const app = next({
  dev,
  hostname,
  // Suppress all Next.js logging
  quiet: true,
  // Custom configuration to minimize logs
  conf: {
    // Disable build logs
    onDemandEntries: {
      // Disable build progress logs
      maxInactiveAge: 60 * 1000,
      pagesBufferLength: 2,
    },
    // Disable other verbose options
    experimental: {
      // Disable verbose logging
      logging: false,
    },
  },
});
const handle = app.getRequestHandler();

app.prepare().then(async () => {
  // Find an available port
  const port = await findAvailablePort(defaultPort);

  // Log the port being used
  if (port !== defaultPort) {
    console.log(`Port ${defaultPort} is occupied, using port ${port} instead`);
  }

  // Create HTTP server
  const server = createServer(async (req, res) => {
    try {
      // Parse the URL
      const parsedUrl = parse(req.url, true);

      // Filter out random numeric 404s (likely HMR or browser noise)
      if (req.url && /^\/\d+$/.test(req.url)) {
        res.statusCode = 404;
        res.end('Not found');
        return;
      }

      // Let Next.js handle the request
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('Internal server error');
    }
  });

  // Initialize Socket.io with our server
  const io = initSocketServer(server);

  // Set server to listen on the available port
  server.listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://${hostname}:${port}`);
  });
});
