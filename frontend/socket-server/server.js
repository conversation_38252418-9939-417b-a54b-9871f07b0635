const http = require('http');
const { Server } = require('socket.io');
const crypto = require('crypto');
const url = require('url');
const fetch = require('node-fetch');

// Function to initialize Socket.io server
function initSocketServer(server) {
  const io = new Server(server, {
    cors: {
      origin:
        process.env.NODE_ENV === 'production'
          ? [
              'https://hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com', // WPEngine frontend
              'https://mytourismiq.wpenginepowered.com', // WordPress backend
              ...(process.env.CORS_ORIGINS ? process.env.CORS_ORIGINS.split(',') : []),
            ]
          : 'http://localhost:3000',
      methods: ['GET', 'POST'],
      credentials: true,
    },
    // Production optimizations
    transports: ['websocket', 'polling'],
    pingTimeout: 60000,
    pingInterval: 25000,
  });

  // Connected users mapping (userId -> socketId)
  const connectedUsers = new Map();
  // User status mapping (userId -> { isOnline: boolean, lastSeen: timestamp })
  const userStatus = new Map();

  // HTTP request handling for WordPress notifications
  const originalRequestListener = server.listeners('request')[0];
  server.removeAllListeners('request');

  server.on('request', (req, res) => {
    const parsedUrl = url.parse(req.url, true);

    // Health check endpoint for Railway
    if (req.method === 'GET' && parsedUrl.pathname === '/health') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(
        JSON.stringify({
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          connectedUsers: connectedUsers.size,
        })
      );
      return;
    }

    if (req.method === 'POST' && parsedUrl.pathname === '/api/internal/send-notification') {
      let body = '';
      req.on('data', (chunk) => {
        body += chunk.toString();
      });
      req.on('end', () => {
        try {
          const data = JSON.parse(body);
          console.log('🔔 Socket server received notification request:', data);

          const {
            recipientId,
            type,
            content,
            referenceId,
            referenceType,
            senderId,
            senderName,
            senderAvatar,
            messageContent, // Added for messages
            // IQ Points specific fields
            activityType,
            pointsEarned,
            newTotal,
            rankedUp,
            newRank,
            // Pass through any other fields
            ...otherFields
          } = data;

          if (!recipientId || !type || !content) {
            console.error('Invalid notification data from HTTP');
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(
              JSON.stringify({
                success: false,
                message: 'Invalid notification data',
              })
            );
            return;
          }

          const notification = {
            id: crypto.randomUUID
              ? crypto.randomUUID()
              : Math.random().toString(36).substring(2, 11),
            type,
            content,
            referenceId,
            referenceType,
            senderId,
            senderName,
            senderAvatar,
            messageContent, // Include full message content for real-time messaging
            // IQ Points specific fields
            activityType,
            pointsEarned,
            newTotal,
            rankedUp,
            newRank,
            // Include any other fields that were passed
            ...otherFields,
            timestamp: Date.now(),
            read: false,
          };

          // For message notifications, only emit a message event (not a notification)
          if (type === 'message') {
            const messageEvent = {
              id: referenceId,
              senderId: senderId,
              content: messageContent,
              timestamp: new Date().toISOString(),
              senderName,
              senderAvatar,
            };
            io.to(`user_${recipientId}`).emit('new_message', messageEvent);
            // Don't emit a notification for messages - they have their own UI
          } else {
            // For all other types, emit as notification
            io.to(`user_${recipientId}`).emit('notification', notification);
          }
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: true, message: 'Notification sent' }));
        } catch (error) {
          console.error('Error processing HTTP notification:', error);
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(
            JSON.stringify({
              success: false,
              message: 'Error processing notification',
            })
          );
        }
      });
    } else {
      // Pass to original Next.js/Socket.IO handler if not our custom endpoint
      if (originalRequestListener) {
        originalRequestListener(req, res);
      } else {
        // Fallback if no original listener (should not happen in Next.js context)
        res.writeHead(404);
        res.end();
      }
    }
  });

  // Socket.io connection handling
  io.on('connection', async (socket) => {
    // Handle authentication
    socket.on('authenticate', async (data) => {
      // Very simple authentication - just check if userId exists
      if (!data || !data.userId) {
        console.error('Missing userId in authentication data');
        socket.emit('authenticated', {
          success: false,
          message: 'Missing userId in authentication data',
        });
        return;
      }

      // Always authenticate in development
      const userId = data.userId;
      const previousSocketId = connectedUsers.get(userId.toString());

      // If user was already connected with different socket, disconnect old one
      if (previousSocketId && previousSocketId !== socket.id) {
        const previousSocket = io.sockets.sockets.get(previousSocketId);
        if (previousSocket) {
          previousSocket.disconnect();
        }
      }

      connectedUsers.set(userId.toString(), socket.id);
      socket.userId = userId; // Store userId on socket for easy access
      socket.join(`user_${userId}`);

      console.log(`✅ User ${userId} joined room user_${userId} with socket ${socket.id}`);
      console.log('🏠 Room members after join:', io.sockets.adapter.rooms.get(`user_${userId}`));

      // Update user status
      userStatus.set(userId.toString(), {
        isOnline: true,
        lastSeen: Date.now(),
      });

      // Broadcast user online status to relevant users (optional)
      socket.broadcast.emit('user_status_changed', {
        userId: userId,
        isOnline: true,
      });

      // Signal successful authentication
      socket.emit('authenticated', { success: true });
    });

    // Handle real-time messaging
    socket.on('send_message', async (data) => {
      const { recipientId, content, conversationId } = data;

      if (!socket.userId) {
        socket.emit('message_error', { error: 'Not authenticated' });
        return;
      }

      if (!recipientId || !content) {
        socket.emit('message_error', { error: 'Missing recipientId or content' });
        return;
      }

      // Create message object
      const message = {
        id: crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 11),
        conversationId: conversationId || `conv_${[socket.userId, recipientId].sort().join('_')}`,
        senderId: socket.userId,
        recipientId: recipientId,
        content: content,
        timestamp: new Date().toISOString(),
        isRead: false,
      };

      // Emit to recipient if they're online
      const recipientSocketId = connectedUsers.get(recipientId.toString());
      if (recipientSocketId) {
        io.to(`user_${recipientId}`).emit('message_received', message);
      }

      // Confirm message sent to sender
      socket.emit('message_sent', message);
    });

    // Handle typing indicators
    socket.on('typing_start', (data) => {
      const { recipientId, conversationId } = data;
      if (socket.userId && recipientId) {
        io.to(`user_${recipientId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: conversationId,
          isTyping: true,
        });
      }
    });

    socket.on('typing_stop', (data) => {
      const { recipientId, conversationId } = data;
      if (socket.userId && recipientId) {
        io.to(`user_${recipientId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: conversationId,
          isTyping: false,
        });
      }
    });

    // Handle message read receipts
    socket.on('mark_messages_read', (data) => {
      const { conversationWith } = data;
      if (socket.userId && conversationWith) {
        // Notify the other user that their messages have been read
        io.to(`user_${conversationWith}`).emit('messages_read', {
          userId: socket.userId,
          readBy: socket.userId,
        });
      }
    });

    // Handle user presence (online/offline status)
    socket.on('update_presence', (data) => {
      if (socket.userId) {
        const { status } = data; // 'online', 'away', 'busy', 'offline'
        userStatus.set(socket.userId.toString(), {
          isOnline: status === 'online',
          status: status,
          lastSeen: Date.now(),
        });

        // Broadcast status change to relevant users
        socket.broadcast.emit('user_status_changed', {
          userId: socket.userId,
          status: status,
          isOnline: status === 'online',
        });
      }
    });

    // Handle notification sending
    socket.on('send_notification', (data) => {
      const {
        recipientId,
        type,
        content,
        referenceId,
        referenceType,
        senderId,
        senderName,
        senderAvatar,
      } = data;

      if (!recipientId || !type || !content) {
        console.error('Invalid notification data');
        return;
      }

      // Create notification object
      const notification = {
        id: crypto.randomUUID ? crypto.randomUUID() : Math.random().toString(36).substring(2, 11),
        type,
        content,
        referenceId,
        referenceType,
        senderId,
        senderName,
        senderAvatar,
        timestamp: Date.now(),
        read: false,
      };

      // Emit to recipient's room if they're online
      io.to(`user_${recipientId}`).emit('notification', notification);
    });

    // Handle client disconnect
    socket.on('disconnect', () => {
      let disconnectedUserId = null;

      // Remove user from connected users
      for (const [userId, socketId] of connectedUsers.entries()) {
        if (socketId === socket.id) {
          connectedUsers.delete(userId);
          disconnectedUserId = userId;
          break;
        }
      }

      if (disconnectedUserId) {
        // Update user status to offline
        userStatus.set(disconnectedUserId, {
          isOnline: false,
          lastSeen: Date.now(),
        });

        // Broadcast user offline status
        socket.broadcast.emit('user_status_changed', {
          userId: disconnectedUserId,
          isOnline: false,
          lastSeen: Date.now(),
        });
      }
    });

    // Handle getting online users (for debugging/admin purposes)
    socket.on('get_online_users', () => {
      if (socket.userId) {
        const onlineUsers = Array.from(connectedUsers.keys());
        console.log(`🔍 User ${socket.userId} requested online users:`, onlineUsers);
        socket.emit('online_users', onlineUsers);
      }
    });

    // Handle getting specific user status
    socket.on('get_user_status', (data) => {
      const { userId } = data;
      const isOnline = connectedUsers.has(userId.toString());
      const status = userStatus.get(userId.toString());

      const response = {
        userId: userId,
        isOnline: isOnline,
        status: status?.status || 'offline',
        lastSeen: status?.lastSeen || null,
      };
      socket.emit('user_status_response', response);
    });
  });

  return io;
}

module.exports = { initSocketServer };
