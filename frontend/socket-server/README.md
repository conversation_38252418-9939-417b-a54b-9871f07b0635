# TourismIQ Socket.io Server

This is a standalone Socket.io server that handles real-time notifications for the TourismIQ platform.

## Overview

The socket server:
- Runs as a separate process from the Next.js application
- Maintains WebSocket connections with connected clients
- Handles authentication of users
- Routes real-time notifications between users

## Setup

From the root frontend directory:

```bash
# Install socket server dependencies
npm run setup:socket

# Start socket server in development mode
npm run dev:socket

# Or start both Next.js app and socket server concurrently
npm run dev:all
```

## Architecture

The socket server:
1. Maintains a map of connected users (userId -> socketId)
2. Authenticates users using the token from WordPress
3. Handles sending notifications between users
4. Provides fallback to REST API for offline users

## Environment Variables

Create a `.env` file in the `socket-server` directory with:

```
SOCKET_PORT=3000
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000
```

## API

### Client to Server Events

- **authenticate**: Authenticate a user
  ```javascript
  socket.emit('authenticate', { userId: 123, token: 'user_token' });
  ```

- **send_notification**: Send a notification to another user
  ```javascript
  socket.emit('send_notification', {
    recipientId: 456,
    type: 'upvote',
    content: 'User liked your post',
    referenceId: 789,
    referenceType: 'post'
  });
  ```

### Server to Client Events

- **authenticated**: Response to authentication attempt
  ```javascript
  socket.on('authenticated', (response) => {
    // response = { success: true, userId: 123 }
    // or
    // response = { success: false, message: 'Authentication failed' }
  });
  ```

- **notification**: Incoming notification
  ```javascript
  socket.on('notification', (notification) => {
    // notification = {
    //   id: 'abc123',
    //   type: 'upvote',
    //   content: 'User liked your post',
    //   timestamp: 1620000000000,
    //   referenceId: 789,
    //   referenceType: 'post'
    // }
  });
  ```

## Production Deployment

For production, install PM2 globally and create a process file:

```bash
npm install -g pm2
```

Create a `ecosystem.config.js` file:

```javascript
module.exports = {
  apps: [{
    name: 'tourismiq-socket',
    script: 'index.js',
    cwd: './socket-server',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      SOCKET_PORT: 3000
    }
  }]
};
```

Then start with:

```bash
pm2 start ecosystem.config.js
```
