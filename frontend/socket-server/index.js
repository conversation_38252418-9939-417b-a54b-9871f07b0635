const { createServer } = require('http');
const { Server } = require('socket.io');
const axios = require('axios');
require('dotenv').config();

// Create HTTP server
const httpServer = createServer();
const io = new Server(httpServer, {
  cors: {
    origin: process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',
    methods: ['GET', 'POST'],
    credentials: true,
  },
});

// Set of connected users (userId -> socketId)
const connectedUsers = new Map();

// Handle socket connections
io.on('connection', (socket) => {
  let authenticatedUserId = null;

  // Handle authentication
  socket.on('authenticate', async (data) => {
    try {
      const { userId, token } = data;

      // Simple validation
      if (!userId || !token) {
        socket.emit('authenticated', {
          success: false,
          message: 'Missing user ID or token',
        });
        return;
      }

      // In a production app, we would verify the authentication
      // For example, by validating it with the WordPress cookies or API

      // For demonstration, we'll just trust the token and userId
      authenticatedUserId = userId;

      // Store connection in the map
      connectedUsers.set(userId.toString(), socket.id);

      socket.emit('authenticated', { success: true, userId });
    } catch (error) {
      console.error('Authentication error:', error);
      socket.emit('authenticated', {
        success: false,
        message: 'Authentication failed',
      });
    }
  });

  // Handle sending notifications
  socket.on('send_notification', (notification) => {
    try {
      const { recipientId, type, content, referenceId, referenceType } = notification;

      // Check if the recipient is connected
      const recipientSocketId = connectedUsers.get(recipientId.toString());

      if (recipientSocketId) {
        // Send notification to the recipient
        io.to(recipientSocketId).emit('notification', {
          id: generateId(),
          type,
          content,
          timestamp: Date.now(),
          referenceId,
          referenceType,
        });
      }
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    if (authenticatedUserId) {
      // Remove from connected users
      connectedUsers.delete(authenticatedUserId.toString());
    }
  });
});

// Generate a random ID for notifications
function generateId() {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Start the server
const PORT = process.env.PORT || process.env.SOCKET_PORT || 3000;
