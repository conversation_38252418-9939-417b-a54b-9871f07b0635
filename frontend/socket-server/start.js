const http = require('http');
const { initSocketServer } = require('./server');

// Create HTTP server
const server = http.createServer();

// Initialize Socket.io server
const io = initSocketServer(server);

// Use Railway's PORT environment variable or fallback to 3001 for development (Next.js uses 3000)
const PORT = process.env.PORT || process.env.SOCKET_PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 Socket server running on port ${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});
