# Socket Server Deployment Guide

This guide covers deploying the Socket.io server for production use with the TourismIQ frontend.

## 🚀 Deployment Options

### Option 1: Heroku (Recommended)

1. **Install Heroku CLI**
   ```bash
   # Install Heroku CLI if not already installed
   npm install -g heroku
   ```

2. **Prepare for Deployment**
   ```bash
   cd socket-server
   
   # Initialize git if not already done
   git init
   git add .
   git commit -m "Initial socket server commit"
   
   # Create Heroku app
   heroku create your-app-name-socket
   ```

3. **Configure Environment Variables**
   ```bash
   # Set environment variables
   heroku config:set NODE_ENV=production
   heroku config:set SOCKET_PORT=3000
   heroku config:set NEXT_PUBLIC_FRONTEND_URL=https://your-frontend-domain.vercel.app
   
   # Add CORS origins
   heroku config:set CORS_ORIGINS=https://your-frontend-domain.vercel.app,https://your-custom-domain.com
   ```

4. **Deploy**
   ```bash
   # Deploy to Heroku
   git push heroku main
   
   # Check logs
   heroku logs --tail
   ```

### Option 2: Railway

1. **Install Railway CLI**
   ```bash
   npm install -g @railway/cli
   ```

2. **Deploy**
   ```bash
   cd socket-server
   
   # Login to Railway
   railway login
   
   # Initialize project
   railway init
   
   # Set environment variables
   railway variables set NODE_ENV=production
   railway variables set SOCKET_PORT=3000
   railway variables set NEXT_PUBLIC_FRONTEND_URL=https://hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com
   
   # Deploy
   railway up
   ```

### Option 3: DigitalOcean App Platform

1. **Create App**
   - Go to DigitalOcean App Platform
   - Connect your GitHub repository
   - Select the `socket-server` directory

2. **Configure Build Settings**
   ```yaml
   # app.yaml
   name: tourismiq-socket
   services:
   - name: socket-server
     source_dir: /socket-server
     github:
       repo: your-username/your-repo
       branch: main
     run_command: npm start
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
     envs:
     - key: NODE_ENV
       value: production
     - key: SOCKET_PORT
               value: "3000"
     - key: NEXT_PUBLIC_FRONTEND_URL
               value: https://hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com
   ```

## 🔧 Environment Variables

### Required Variables

```bash
# Production environment
NODE_ENV=production

# Socket server port (usually 3000 for development, 8080 for Railway)
SOCKET_PORT=3000

# Frontend URL for CORS
NEXT_PUBLIC_FRONTEND_URL=https://your-frontend-domain.vercel.app

# Additional CORS origins (comma-separated)
CORS_ORIGINS=https://your-frontend-domain.vercel.app,https://your-custom-domain.com

# Optional: Redis URL for scaling (if using Redis adapter)
REDIS_URL=redis://your-redis-instance.com:6379
```

## 📝 Production Configuration

### Update socket-server/server.js

Ensure your server.js includes production configurations:

```javascript
const io = require('socket.io')(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? process.env.CORS_ORIGINS?.split(',') || []
      : ["http://localhost:3000"],
    methods: ["GET", "POST"],
    credentials: true
  },
  // Production optimizations
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
});
```

### Add package.json scripts

```json
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  }
}
```

## 🔄 Update Frontend Configuration

After deploying the socket server, update your frontend environment variables:

### Vercel Environment Variables

```bash
# Production socket URL
NEXT_PUBLIC_SOCKET_URL_PROD=https://your-socket-server.herokuapp.com

# Or for Railway
NEXT_PUBLIC_SOCKET_URL_PROD=https://your-app.railway.app

# Or for DigitalOcean
NEXT_PUBLIC_SOCKET_URL_PROD=https://your-app.ondigitalocean.app
```

### Update Frontend Socket Connection

In your frontend socket client code:

```typescript
const socketUrl = process.env.NODE_ENV === 'production'
  ? process.env.NEXT_PUBLIC_SOCKET_URL_PROD
  : process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';

const socket = io(socketUrl, {
  transports: ['websocket', 'polling'],
  timeout: 20000,
});
```

## 🔍 Monitoring & Debugging

### Health Check Endpoint

Add a health check to your socket server:

```javascript
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});
```

### Logging

Add proper logging for production:

```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Use logger instead of console.log
logger.info('Socket server started on port', port);
```

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Verify CORS_ORIGINS includes your frontend domain
   - Check that HTTPS is used in production

2. **Connection Timeouts**
   - Increase pingTimeout and pingInterval
   - Check firewall settings

3. **Scaling Issues**
   - Consider using Redis adapter for multiple instances
   - Monitor memory usage

### Testing Connection

Test your deployed socket server:

```bash
# Test health endpoint
curl https://your-socket-server.herokuapp.com/health

# Test socket connection (use a tool like wscat)
npm install -g wscat
wscat -c wss://your-socket-server.herokuapp.com/socket.io/?EIO=4&transport=websocket
```

## 📊 Scaling Considerations

### Redis Adapter (for multiple instances)

```javascript
const { createAdapter } = require('@socket.io/redis-adapter');
const { createClient } = require('redis');

const pubClient = createClient({ url: process.env.REDIS_URL });
const subClient = pubClient.duplicate();

io.adapter(createAdapter(pubClient, subClient));
```

### Load Balancing

When using multiple instances, ensure sticky sessions:

```nginx
# Nginx configuration
upstream socket_nodes {
    ip_hash;
    server socket1.example.com:8080;
server socket2.example.com:8080;
}
```

## 🔐 Security

### Production Security Checklist

- [ ] CORS properly configured
- [ ] HTTPS enabled
- [ ] Rate limiting implemented
- [ ] Authentication tokens validated
- [ ] Sensitive data not logged
- [ ] Environment variables secured

This completes the socket server deployment guide. Choose the deployment option that best fits your infrastructure needs. 