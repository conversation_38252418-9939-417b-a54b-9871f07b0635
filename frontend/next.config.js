/** @type {import('next').NextConfig} */
const nextConfig = {
  // Remove standalone mode for WP Engine compatibility
  // output: 'standalone',

  // Ensure trailing slashes are handled consistently
  trailingSlash: false,

  // Configure image domains and optimization
  images: {
    unoptimized: false,
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'mytourismiq.wpenginepowered.com',
      },
      {
        protocol: 'https',
        hostname: 'mytourismiq.wpenginepowered.com',
      },
      {
        protocol: 'http',
        hostname: 'mytourismiq.com',
      },
      {
        protocol: 'https',
        hostname: 'mytourismiq.com',
      },
      {
        protocol: 'http',
        hostname: 'tourismiq.local',
      },
      {
        protocol: 'https',
        hostname: 'tourismiq.local',
      },
    ],
  },

  // Disable ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Handle redirects from old WordPress URLs to new Next.js structure
  async redirects() {
    return [
      // Old WordPress post URLs to new structure
      {
        source: '/blog/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      // Redirect old leadership URLs to posts
      {
        source: '/leadership/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      // Redirect old content type URLs to posts
      {
        source: '/case-studies/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/book/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/course/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/event/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/news/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/podcast/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/presentation/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/press-release/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/templates/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/video/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/webinar/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      {
        source: '/whitepaper/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      // Redirect old job URLs to jobs page
      {
        source: '/recent-job/:path*',
        destination: '/jobs',
        permanent: true,
      },
      {
        source: '/job/:path*',
        destination: '/jobs',
        permanent: true,
      },
      {
        source: '/:year(\\d{4})/:month(\\d{2})/:day(\\d{2})/:slug*',
        destination: '/posts/:slug*',
        permanent: true,
      },
      // Old category URLs
      {
        source: '/blog-post/:path*',
        destination: '/category/news/:path*',
        permanent: true,
      },
      // Old WordPress sitemap URLs (redirect to new sitemap)
      {
        source: '/sitemap_index.xml',
        destination: '/sitemap.xml',
        permanent: true,
      },
      {
        source:
          '/:category(blog-post|book|case-study|course|event|forum|job|leadership|news|podcast|presentation|press-release|templates|video|webinar|whitepaper)-sitemap.xml',
        destination: '/sitemap.xml',
        permanent: true,
      },
      // Redirect old WordPress admin to login
      {
        source: '/wp-admin/:path*',
        destination: '/login',
        permanent: false,
      },
      // Redirect old WordPress login
      {
        source: '/wp-login.php',
        destination: '/login',
        permanent: true,
      },
    ];
  },

  // Ensure proper URL handling
  async rewrites() {
    return [];
  },

  // Security headers to enforce HTTPS
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          }
        ],
      },
    ];
  },
};

module.exports = nextConfig;
