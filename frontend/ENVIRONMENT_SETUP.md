# Environment Setup Guide

This document outlines the correct environment configuration for the TourismIQ notification system.

## Port Configuration

### Development (Local)
- **Next.js Frontend**: `localhost:3000`
- **Socket Server**: `localhost:3000` (integrated with Next.js)
- **WordPress**: `tourismiq.local` (Local by Flywheel)

### Production
- **Next.js + Socket Server**: Railway deployment on port `8080`
- **WordPress**: WP Engine hosting
- **Socket Server URL**: `https://tourismqwp-production.up.railway.app`

## Environment Variables

### 1. Railway (Production)
Set these in your Railway dashboard:
```
PORT=8080                                                    # Auto-set by Railway
NODE_ENV=production                                          # Auto-set by Railway
NEXT_PUBLIC_SOCKET_URL=https://tourismqwp-production.up.railway.app
SOCKET_SERVER_URL=https://tourismqwp-production.up.railway.app
NEXT_PUBLIC_FRONTEND_URL=https://hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com
```

### 2. WordPress (WP Engine)
Add to `wp-config.php`:
```php
define('SOCKET_SERVER_URL', 'https://tourismqwp-production.up.railway.app');
```

### 3. Local Development (.env.local)
Create in `frontend/.env.local`:
```
NEXT_PUBLIC_WORDPRESS_API_URL=http://tourismiq.local
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
SOCKET_SERVER_URL=http://localhost:3000
NODE_ENV=development
```

## Notification Flow

### Development
1. **Frontend** → **WordPress** (tourismiq.local)
2. **WordPress** → **Socket Server** (localhost:3000)
3. **Socket Server** → **Connected Users**

### Production
1. **Frontend** → **WordPress** (WP Engine)
2. **WordPress** → **Socket Server** (Railway:8080)
3. **Socket Server** → **Connected Users**

## Testing

### Health Check
- **Local**: `curl http://localhost:3000/health`
- **Production**: `curl https://tourismqwp-production.up.railway.app/health`

### Notification Test
- **Local**: `curl -X POST http://localhost:3000/api/internal/send-notification -H "Content-Type: application/json" -d '{"recipientId": 1, "type": "test", "content": "Test"}'`
- **Production**: `curl -X POST https://tourismqwp-production.up.railway.app/api/internal/send-notification -H "Content-Type: application/json" -d '{"recipientId": 1, "type": "test", "content": "Test"}'`

## Troubleshooting

### Common Issues
1. **No notifications received**: Check if users are connected to socket server via health endpoint
2. **CORS errors**: Verify `NEXT_PUBLIC_FRONTEND_URL` matches your actual frontend URL
3. **Connection timeouts**: Ensure Railway deployment is running and environment variables are set

### Debug Logs
- **WordPress**: Check `wp-content/debug.log` for socket notification logs
- **Railway**: Check Railway deployment logs for connection and notification activity
- **Browser**: Check Network tab for WebSocket connection status

## Migration Notes

### From Port 3001 to Current Setup
- All references to port 3001 have been updated
- Socket server now runs on same port as Next.js (3000 local, 8080 Railway)
- Environment variables now properly handle both development and production 