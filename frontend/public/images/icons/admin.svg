<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 74.59 74.59">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
        stroke-width: 0px;
      }
    </style>
    <linearGradient id="linear-gradient" x1="10.92" y1="63.66" x2="63.66" y2="10.92" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#5cc8ff"/>
      <stop offset=".27" stop-color="#81e9ff"/>
      <stop offset=".48" stop-color="#9bffff"/>
      <stop offset=".49" stop-color="#96fbff"/>
      <stop offset=".61" stop-color="#7de4ff"/>
      <stop offset=".73" stop-color="#6ad4ff"/>
      <stop offset=".86" stop-color="#5fcbff"/>
      <stop offset="1" stop-color="#5cc8ff"/>
    </linearGradient>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <path class="cls-1" d="M37.29,6c17.26,0,31.29,14.04,31.29,31.29s-14.04,31.29-31.29,31.29S6,54.55,6,37.29,20.04,6,37.29,6M37.29,0C16.7,0,0,16.7,0,37.29s16.7,37.29,37.29,37.29,37.29-16.7,37.29-37.29S57.89,0,37.29,0h0Z"/>
  </g>
</svg>