import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  safelist: [
    'bg-category-news',
    'bg-category-news-hover',
    'bg-category-resources',
    'bg-category-resources-hover',
    'bg-category-thought',
    'bg-category-thought-hover',
    'bg-category-events',
    'bg-category-events-hover',
    'bg-category-jobs',
    'bg-category-jobs-hover',
    'text-category-news',
    'text-category-resources',
    'text-category-thought',
    'text-category-events',
    'text-category-jobs',
    'hover:bg-category-news-hover',
    'hover:bg-category-resources-hover',
    'hover:bg-category-thought-hover',
    'hover:bg-category-events-hover',
    'hover:bg-category-jobs-hover',
    // Add specific hex color classes as backup
    'bg-[#ff5ce0]',
    'hover:bg-[#e54cc7]',
    'bg-[#ffa15c]',
    'hover:bg-[#e68943]',
    'bg-[#5cc8ff]',
    'hover:bg-[#43b0e6]',
    'bg-[#1dd05b]',
    'hover:bg-[#17b749]',
    'bg-[#ff625c]',
    'hover:bg-[#e54943]',
    // Upvote button gradient colors
    'bg-gradient-to-r',
    'from-green-500',
    'to-emerald-600',
    'hover:from-green-600',
    'hover:to-emerald-700',
    'shadow-lg',
    'shadow-green-500/25',
    'hover:shadow-green-500/40',
    'transition-all',
    'duration-300',
    'bg-white',
    'text-green-600',
    'shadow-sm',
    'bg-white/70',
    'text-gray-700',
    'brightness-0',
    'invert',
    'transform-none',
    'scale-100',
  ],
  darkMode: 'class',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    screens: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
      // Custom breakpoint for our new layout
      'container-lg': '1360px',
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        // Category colors
        category: {
          news: '#ff5ce0',
          'news-hover': '#e54cc7',
          resources: '#ffa15c',
          'resources-hover': '#e68943',
          thought: '#5cc8ff',
          'thought-hover': '#43b0e6',
          events: '#1dd05b',
          'events-hover': '#17b749',
          jobs: '#ff625c',
          'jobs-hover': '#e54943',
        },
        // Custom dark text color
        'dark-text': '#3d405b',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/line-clamp')],
};

export default config;
