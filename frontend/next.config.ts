import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  /* config options here */

  // Security: Remove hardcoded credentials - use environment variables instead
  env: {
    // Only expose public environment variables to client-side
    NEXT_PUBLIC_WORDPRESS_API_URL:
      process.env.NEXT_PUBLIC_WORDPRESS_API_URL || 'http://localhost:8000',
  },

  // Image optimization configuration
  images: {
    // Use environment variable for domains in production
    domains:
      process.env.NODE_ENV === 'production'
        ? [process.env.WORDPRESS_DOMAIN || 'mytourismiq.wpenginepowered.com']
        : ['tourismiq.local', 'localhost', 'mytourismiq.wpenginepowered.com'],
    remotePatterns: [
      {
        protocol: process.env.NODE_ENV === 'production' ? 'https' : 'https',
        hostname: 'mytourismiq.wpenginepowered.com',
        pathname: '/wp-content/uploads/**',
      },
      {
        protocol: 'http',
        hostname: 'tourismiq.local',
        pathname: '/wp-content/uploads/**',
      },
      // Add localhost for development
      {
        protocol: 'http',
        hostname: 'localhost',
        pathname: '/wp-content/uploads/**',
      },
    ],
    // Performance optimization
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains',
          },
        ],
      },
    ];
  },

  // Compression
  compress: true,

  // PoweredByHeader removal for security
  poweredByHeader: false,

  // TypeScript strict mode
  typescript: {
    ignoreBuildErrors: false,
  },

  // ESLint during builds
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Reduce verbose logging in development
  logging: {
    fetches: {
      fullUrl: false,
    },
  },

  // Performance optimizations
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-avatar', '@radix-ui/react-dialog'],
  },
};

export default nextConfig;
