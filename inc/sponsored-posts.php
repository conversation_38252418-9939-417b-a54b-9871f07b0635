<?php
/**
 * Sponsored Posts Functionality
 * Handles ordering and display of sponsored posts
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Sponsored posts functionality loaded successfully

/**
 * Modify post queries to prioritize sponsored posts
 */
add_action('pre_get_posts', 'tourismiq_prioritize_sponsored_posts', 20);

/**
 * Alternative approach: Filter REST API results to prioritize sponsored posts
 */
add_filter('rest_post_query', 'tourismiq_rest_post_query_sponsored', 10, 2);

/**
 * Another approach: Use posts_orderby filter to ensure sponsored posts come first
 */
add_filter('posts_orderby', 'tourismiq_posts_orderby_sponsored', 10, 2);

function tourismiq_prioritize_sponsored_posts($query) {
    // Only modify main queries on frontend
    if (is_admin() || !$query->is_main_query()) {
        return;
    }

    // Check if this is a REST API request for posts
    if (defined('REST_REQUEST') && REST_REQUEST) {
        $route = $_SERVER['REQUEST_URI'] ?? '';
        
        // Only modify post queries - updated to catch more patterns including proxy routes
        if (strpos($route, '/wp-json/wp/v2/posts') === false && 
            strpos($route, '/wp-json/tourismiq/v1/posts') === false &&
            strpos($route, '/api/wp-proxy/posts') === false) {
            return;
        }
        
        // Only apply sponsored sorting if there's a category filter
        $categories = $query->get('category__in') ? $query->get('category__in') : [];
        if (empty($categories)) {
            // Check for categories parameter in query
            $categories = $query->get('categories');
            if (empty($categories)) {
                // Check URL parameters directly
                $query_params = $_GET;
                if (empty($query_params['categories'])) {
                    return;
                }
            }
        }
    } else if (!is_home() && !is_archive() && !is_search()) {
        // For non-REST requests, only modify on specific pages
        return;
    }

    // Check if sorting by upvotes (don't interfere with upvote sorting)
    $orderby = $query->get('orderby');
    if ($orderby === 'meta_value_num' && $query->get('meta_key') === '_upvotes') {
        return;
    }

    // Add meta query to get sponsored field
    $meta_query = $query->get('meta_query') ?: [];
    
    // Don't add duplicate sponsored queries
    $has_sponsored_query = false;
    foreach ($meta_query as $clause) {
        if (isset($clause['key']) && $clause['key'] === 'sponsored') {
            $has_sponsored_query = true;
            break;
        }
    }

    if (!$has_sponsored_query) {
        // Sort by sponsored status first, then by date
        $query->set('meta_query', array_merge($meta_query, [
            'sponsored_clause' => [
                'key' => 'sponsored',
                'compare' => 'EXISTS',
                'type' => 'NUMERIC'
            ]
        ]));

        // Order by sponsored status (DESC to put true/1 first), then by date
        $query->set('orderby', [
            'sponsored_clause' => 'DESC',
            'date' => 'DESC'
        ]);
    }
}

/**
 * REST API specific filter for sponsored posts
 */
function tourismiq_rest_post_query_sponsored($args, $request) {
    // Only modify if not sorting by upvotes
    if (isset($args['orderby']) && $args['orderby'] === 'meta_value_num' && isset($args['meta_key']) && $args['meta_key'] === '_upvotes') {
        return $args;
    }
    
    // Only apply sponsored sorting if there's a category filter
    $has_category_filter = false;
    
    if (isset($args['category__in']) && !empty($args['category__in'])) {
        $has_category_filter = true;
    } elseif (isset($args['categories']) && !empty($args['categories'])) {
        $has_category_filter = true;
    } else {
        // Check URL parameters directly
        $query_params = $_GET;
        if (!empty($query_params['categories'])) {
            $has_category_filter = true;
        }
    }
    
    // If no category filter is present, exclude sponsored posts entirely
    if (!$has_category_filter) {
        // Add meta query to exclude sponsored posts
        if (!isset($args['meta_query'])) {
            $args['meta_query'] = [];
        }
        
        // Exclude sponsored posts when no category filter is present
        $sponsored_exclude_query = [
            'relation' => 'OR',
            [
                'key' => 'sponsored',
                'compare' => 'NOT EXISTS'
            ],
            [
                'key' => 'sponsored',
                'value' => '0',
                'compare' => '='
            ],
            [
                'key' => 'sponsored',
                'value' => '',
                'compare' => '='
            ]
        ];
        
        // If there are existing meta queries, combine them with AND relation
        if (!empty($args['meta_query'])) {
            $args['meta_query'] = [
                'relation' => 'AND',
                $args['meta_query'],
                $sponsored_exclude_query
            ];
        } else {
            $args['meta_query'] = $sponsored_exclude_query;
        }
        
        return $args;
    }
    
    // Add meta query for sponsored field - but make it non-filtering
    if (!isset($args['meta_query'])) {
        $args['meta_query'] = [];
    }
    
    // Add sponsored clause for ordering only - use OR relation with NOT EXISTS to include all posts
    $args['meta_query']['sponsored_clause'] = [
        'relation' => 'OR',
        [
            'key' => 'sponsored',
            'compare' => 'EXISTS',
            'type' => 'NUMERIC'
        ],
        [
            'key' => 'sponsored',
            'compare' => 'NOT EXISTS'
        ]
    ];
    
    // Set relation if there are multiple meta queries
    if (count($args['meta_query']) > 1) {
        $args['meta_query']['relation'] = 'AND';
    }
    
    // Set ordering by sponsored status first, then by date
    $args['orderby'] = [
        'sponsored_clause' => 'DESC',
        'date' => 'DESC'
    ];
    
    return $args;
}

/**
 * Posts orderby filter to ensure sponsored posts come first
 */
function tourismiq_posts_orderby_sponsored($orderby, $query) {
    global $wpdb;
    
    // Only modify for REST API requests
    if (!defined('REST_REQUEST') || !REST_REQUEST) {
        return $orderby;
    }
    
    // Don't interfere with upvote sorting
    if ($query->get('orderby') === 'meta_value_num' && $query->get('meta_key') === '_upvotes') {
        return $orderby;
    }
    
    // Only apply sponsored sorting if there's a category filter
    $categories = $query->get('category__in') ? $query->get('category__in') : [];
    if (empty($categories)) {
        // Check for categories parameter in query
        $categories = $query->get('categories');
        if (empty($categories)) {
            // Check URL parameters directly
            $query_params = $_GET;
            if (empty($query_params['categories'])) {
                return $orderby;
            }
        }
    }
    
    // Check if we're dealing with a posts query
    if ($query->get('post_type') === 'post' || (!$query->get('post_type') && $query->is_main_query())) {
        // Add sponsored meta join and orderby
        $orderby = "(
            SELECT IF(meta_value = '1', 1, 0) 
            FROM {$wpdb->postmeta} 
            WHERE post_id = {$wpdb->posts}.ID 
            AND meta_key = 'sponsored'
        ) DESC, {$wpdb->posts}.post_date DESC";
    }
    
    return $orderby;
}

/**
 * Add sponsored field to REST API response
 * Note: This is already handled by rest-post-fields.php which adds all ACF fields
 * This function is here as a safety measure
 */
add_filter('rest_prepare_post', 'tourismiq_add_sponsored_to_rest', 20, 3);

function tourismiq_add_sponsored_to_rest($response, $post, $request) {
    $data = $response->get_data();
    
    // Ensure ACF fields are included
    if (!isset($data['acf'])) {
        if (function_exists('get_fields')) {
            $data['acf'] = get_fields($post->ID);
        }
    }
    
    // Add sponsored flag at top level for easy access
    $data['is_sponsored'] = get_field('sponsored', $post->ID) ? true : false;
    
    $response->set_data($data);
    return $response;
}

/**
 * Filter to ensure only one sponsored post per category
 * This prevents multiple sponsored posts from appearing in the same category
 */
// Temporarily disabled to debug podcast category filtering
// add_filter('posts_results', 'tourismiq_limit_sponsored_per_category', 10, 2);

function tourismiq_limit_sponsored_per_category($posts, $query) {
    // Only apply to main queries on frontend
    if (is_admin() || !$query->is_main_query()) {
        return $posts;
    }

    // Check if this is a REST API request for posts
    if (defined('REST_REQUEST') && REST_REQUEST) {
        $route = $_SERVER['REQUEST_URI'] ?? '';
        
        // Check if this is a posts endpoint (including proxy routes)
        $is_posts_request = (
            strpos($route, '/wp-json/wp/v2/posts') !== false || 
            strpos($route, '/wp-json/tourismiq/v1/posts') !== false ||
            strpos($route, '/api/wp-proxy/posts') !== false
        );
        
        if ($is_posts_request) {
            // Check for category filters in multiple places
            $categories = $query->get('category__in') ? $query->get('category__in') : [];
            
            if (empty($categories)) {
                $categories = $query->get('categories');
            }
            
            if (empty($categories)) {
                // Check URL parameters directly
                $query_params = $_GET;
                $categories = $query_params['categories'] ?? null;
            }
            
            // If no category filter is present, filter out ALL sponsored posts
            if (empty($categories)) {
                $filtered_posts = [];
                foreach ($posts as $post) {
                    $is_sponsored = get_field('sponsored', $post->ID);
                    if (!$is_sponsored) {
                        $filtered_posts[] = $post;
                    }
                }
                return $filtered_posts;
            }
        }
    }

    // Track sponsored posts by category
    $sponsored_by_category = [];
    $filtered_posts = [];

    foreach ($posts as $post) {
        $is_sponsored = get_field('sponsored', $post->ID);
        
        if ($is_sponsored) {
            // Get post categories
            $categories = wp_get_post_categories($post->ID);
            $can_include = true;
            
            // Check if we already have a sponsored post for any of these categories
            foreach ($categories as $cat_id) {
                if (isset($sponsored_by_category[$cat_id])) {
                    $can_include = false;
                    break;
                }
            }
            
            if ($can_include) {
                // Mark this category as having a sponsored post
                foreach ($categories as $cat_id) {
                    $sponsored_by_category[$cat_id] = true;
                }
                $filtered_posts[] = $post;
            }
        } else {
            // Non-sponsored posts are always included
            $filtered_posts[] = $post;
        }
    }

    return $filtered_posts;
}

/**
 * Additional filter to ensure sponsored posts are excluded from main feed
 * This is a more direct approach that filters at the REST API level
 */
add_filter('rest_post_query', 'tourismiq_exclude_sponsored_from_main_feed', 5, 2);

function tourismiq_exclude_sponsored_from_main_feed($args, $request) {
    // Only apply to posts endpoint
    if ($request->get_route() !== '/wp/v2/posts') {
        return $args;
    }
    
    // Check if this request has category filters
    $has_category_filter = false;
    
    // Check various ways categories might be specified
    if (!empty($args['category__in']) || !empty($args['categories'])) {
        $has_category_filter = true;
    } elseif (!empty($request->get_param('categories'))) {
        $has_category_filter = true;
    } elseif (!empty($_GET['categories'])) {
        $has_category_filter = true;
    }
    
    // Debug logging (can be removed later)
    error_log('Sponsored Posts Filter: Route=' . $request->get_route() . ', Has Category Filter=' . ($has_category_filter ? 'yes' : 'no') . ', Categories Param=' . $request->get_param('categories'));
    
    // If no category filter, exclude sponsored posts entirely
    if (!$has_category_filter) {
        // Add meta query to exclude sponsored posts
        if (!isset($args['meta_query'])) {
            $args['meta_query'] = [];
        }
        
        // Add clause to exclude sponsored posts
        $args['meta_query'][] = [
            'relation' => 'OR',
            [
                'key' => 'sponsored',
                'compare' => 'NOT EXISTS'
            ],
            [
                'key' => 'sponsored',
                'value' => '0',
                'compare' => '='
            ],
            [
                'key' => 'sponsored',
                'value' => '',
                'compare' => '='
            ]
        ];
        
        // Set relation if there are multiple meta queries
        if (count($args['meta_query']) > 1) {
            $args['meta_query']['relation'] = 'AND';
        }
        
        error_log('Sponsored Posts Filter: Added meta query to exclude sponsored posts');
    }
    
    return $args;
}

/**
 * Clear cache when sponsored status changes
 */
add_action('acf/save_post', 'tourismiq_clear_cache_on_sponsored_change', 20);

function tourismiq_clear_cache_on_sponsored_change($post_id) {
    // Check if this is a post
    if (get_post_type($post_id) !== 'post') {
        return;
    }

    // Check if sponsored field was updated
    if (isset($_POST['acf']['field_6688c39f38fdd'])) { // sponsored field key
        // Clear relevant caches
        wp_cache_delete('posts_cache', 'tourismiq');
        
        // Clear transients if you're using them
        delete_transient('tourismiq_featured_posts');
        delete_transient('tourismiq_category_posts');
        
        // If using object cache
        if (function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group('tourismiq_posts');
        }
    }
}