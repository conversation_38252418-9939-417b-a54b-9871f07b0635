<?php
/**
 * Vendor Custom Post Type and Related Functionality
 */

// Register Vendor Custom Post Type
function register_vendor_post_type() {
    $labels = array(
        'name'                  => _x('Vendors', 'Post type general name', 'tourismiq'),
        'singular_name'         => _x('Vendor', 'Post type singular name', 'tourismiq'),
        'menu_name'             => _x('Vendors', 'Admin Menu text', 'tourismiq'),
        'name_admin_bar'        => _x('Vendor', 'Add New on Toolbar', 'tourismiq'),
        'add_new'               => __('Add New', 'tourismiq'),
        'add_new_item'          => __('Add New Vendor', 'tourismiq'),
        'new_item'              => __('New Vendor', 'tourismiq'),
        'edit_item'             => __('Edit Vendor', 'tourismiq'),
        'view_item'             => __('View Vendor', 'tourismiq'),
        'all_items'             => __('All Vendors', 'tourismiq'),
        'search_items'          => __('Search Vendors', 'tourismiq'),
        'parent_item_colon'     => __('Parent Vendors:', 'tourismiq'),
        'not_found'             => __('No vendors found.', 'tourismiq'),
        'not_found_in_trash'    => __('No vendors found in Trash.', 'tourismiq'),
        'featured_image'        => _x('Vendor Logo', 'Overrides the "Featured Image" phrase', 'tourismiq'),
        'set_featured_image'    => _x('Set vendor logo', 'Overrides the "Set featured image" phrase', 'tourismiq'),
        'remove_featured_image' => _x('Remove vendor logo', 'Overrides the "Remove featured image" phrase', 'tourismiq'),
        'use_featured_image'    => _x('Use as vendor logo', 'Overrides the "Use as featured image" phrase', 'tourismiq'),
        'archives'              => _x('Vendor archives', 'The post type archive label', 'tourismiq'),
        'insert_into_item'      => _x('Insert into vendor', 'Overrides the "Insert into post" phrase', 'tourismiq'),
        'uploaded_to_this_item' => _x('Uploaded to this vendor', 'Overrides the "Uploaded to this post" phrase', 'tourismiq'),
        'filter_items_list'     => _x('Filter vendors list', 'Screen reader text for the filter links', 'tourismiq'),
        'items_list_navigation' => _x('Vendors list navigation', 'Screen reader text for the pagination', 'tourismiq'),
        'items_list'            => _x('Vendors list', 'Screen reader text for the items list', 'tourismiq'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'vendor'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 20,
        'menu_icon'          => 'dashicons-building',
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'show_in_rest'       => true,
        'rest_base'          => 'vendors',
        'rest_controller_class' => 'WP_REST_Posts_Controller',
    );

    register_post_type('vendor', $args);
}
add_action('init', 'register_vendor_post_type', 5);

// Register Vendor Categories
function register_vendor_categories() {
    $labels = array(
        'name'              => _x('Vendor Categories', 'taxonomy general name', 'tourismiq'),
        'singular_name'     => _x('Vendor Category', 'taxonomy singular name', 'tourismiq'),
        'search_items'      => __('Search Vendor Categories', 'tourismiq'),
        'all_items'         => __('All Vendor Categories', 'tourismiq'),
        'parent_item'       => __('Parent Vendor Category', 'tourismiq'),
        'parent_item_colon' => __('Parent Vendor Category:', 'tourismiq'),
        'edit_item'         => __('Edit Vendor Category', 'tourismiq'),
        'update_item'       => __('Update Vendor Category', 'tourismiq'),
        'add_new_item'      => __('Add New Vendor Category', 'tourismiq'),
        'new_item_name'     => __('New Vendor Category Name', 'tourismiq'),
        'menu_name'         => __('Categories', 'tourismiq'),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'vendor-category'),
        'show_in_rest'      => true,
        'rest_base'         => 'vendor-categories',
    );

    register_taxonomy('vendor_category', array('vendor'), $args);
}
add_action('init', 'register_vendor_categories', 5);

// Add default vendor categories
function add_default_vendor_categories() {
    $categories = array(
        'Tour Operator',
        'Travel Agency', 
        'Adventure Tours',
        'Luxury Travel',
        'Eco Tourism',
        'Cultural Tourism',
        'Tour Guides',
        'Marine Tourism',
        'Wine Tourism',
        'Culinary Tourism',
        'Historical Tours',
        'Accommodation',
        'Transportation',
        'Equipment Rental'
    );

    foreach ($categories as $category) {
        if (!term_exists($category, 'vendor_category')) {
            wp_insert_term($category, 'vendor_category');
        }
    }
}
add_action('init', 'add_default_vendor_categories', 10);

// Add custom REST API fields for vendors
function add_vendor_rest_fields() {
    // Add vendor meta fields to REST API
    register_rest_field('vendor', 'vendor_meta', array(
        'get_callback' => 'get_vendor_meta_for_api',
        'update_callback' => 'update_vendor_meta_from_api',
        'schema' => array(
            'description' => 'Vendor metadata',
            'type' => 'object',
            'properties' => array(
                'website' => array('type' => 'string'),
                'is_paid' => array('type' => 'boolean'),
                'subscription_status' => array('type' => 'string'),
                'cover_photo' => array('type' => 'string'),
                'social_links' => array('type' => 'object'),
                'assigned_members' => array('type' => 'array'),
                'lead_form_enabled' => array('type' => 'boolean'),
                'resources' => array('type' => 'array'),
            )
        )
    ));

    // Add vendor categories to REST API
    register_rest_field('vendor', 'vendor_categories', array(
        'get_callback' => 'get_vendor_categories_for_api',
        'schema' => array(
            'description' => 'Vendor categories',
            'type' => 'array',
        )
    ));

    // Add vendor slug for easy frontend routing
    register_rest_field('vendor', 'slug', array(
        'get_callback' => function($object) {
            return get_post_field('post_name', $object['id']);
        },
        'schema' => array(
            'description' => 'Vendor slug',
            'type' => 'string',
        )
    ));
}
add_action('rest_api_init', 'add_vendor_rest_fields');

// Get vendor meta for REST API
function get_vendor_meta_for_api($object) {
    $vendor_id = $object['id'];
    
    // Get cover photo - prioritize ACF field, fallback to custom meta
    $cover_photo_url = '';
    
    // First try ACF field
    $acf_cover_photo = get_field('vendor_cover_photo', $vendor_id);
    if ($acf_cover_photo) {
        if (is_array($acf_cover_photo) && isset($acf_cover_photo['url'])) {
            // ACF image field returns array with URL
            $cover_photo_url = $acf_cover_photo['url'];
        } elseif (is_string($acf_cover_photo)) {
            // ACF field might return URL directly
            $cover_photo_url = $acf_cover_photo;
        } elseif (is_numeric($acf_cover_photo)) {
            // ACF field might return attachment ID
            $cover_photo_url = wp_get_attachment_url($acf_cover_photo);
        }
    }
    
    // Fallback to custom meta field if ACF field is empty
    if (empty($cover_photo_url)) {
        $cover_photo = get_post_meta($vendor_id, 'vendor_cover_photo', true);
        if ($cover_photo) {
            // If it's a numeric media ID, get the attachment URL
            if (is_numeric($cover_photo)) {
                $cover_photo_url = wp_get_attachment_url($cover_photo);
            } else {
                // If it's already a URL, use it as-is
                $cover_photo_url = $cover_photo;
            }
        }
    }
    
    // Get assigned members and fetch their complete data
    $assigned_member_ids = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
    $assigned_members_data = array();
    
    if (!empty($assigned_member_ids)) {
        foreach ($assigned_member_ids as $member_id) {
            $user = get_user_by('id', $member_id);
            if ($user) {
                // Get ACF profile picture
                $profile_picture = get_field('profile_picture', 'user_' . $user->ID);
                $avatar_url = null; // Let frontend handle the fallback to placeholder
                
                if ($profile_picture && is_array($profile_picture) && isset($profile_picture['url'])) {
                    $avatar_url = $profile_picture['url'];
                }
                
                // Get additional user meta
                $job_title = get_field('job_title', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'job_title', true);
                $company = get_field('company', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'company', true);
                $bio = get_field('bio', 'user_' . $user->ID) ?: $user->description;
                
                $assigned_members_data[] = array(
                    'id' => $user->ID,
                    'name' => $user->display_name,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'username' => $user->user_login,
                    'slug' => $user->user_nicename,
                    'email' => $user->user_email,
                    'avatar_url' => $avatar_url,
                    'profile_picture' => $profile_picture,
                    'job_title' => $job_title,
                    'company' => $company,
                    'bio' => $bio,
                    'roles' => $user->roles,
                );
            }
        }
    }
    
    return array(
        'website' => get_post_meta($vendor_id, 'vendor_website', true),
        'is_paid' => (bool) get_post_meta($vendor_id, 'vendor_is_paid', true),
        'subscription_status' => get_post_meta($vendor_id, 'vendor_subscription_status', true),
        'cover_photo' => $cover_photo_url,
        'logo_url' => wp_get_attachment_url(get_post_thumbnail_id($vendor_id)), // Add logo URL
        'social_links' => get_post_meta($vendor_id, 'vendor_social_links', true) ?: array(),
        'assigned_members' => $assigned_members_data, // Now contains full user data instead of just IDs
        'lead_form_enabled' => (bool) get_post_meta($vendor_id, 'vendor_lead_form_enabled', true),
        'resources' => get_post_meta($vendor_id, 'vendor_resources', true) ?: array(),
    );
}

// Update vendor meta from REST API
function update_vendor_meta_from_api($value, $object, $field_name) {
    $vendor_id = $object->ID;
    
    if (isset($value['website'])) {
        update_post_meta($vendor_id, 'vendor_website', sanitize_url($value['website']));
    }
    if (isset($value['is_paid'])) {
        update_post_meta($vendor_id, 'vendor_is_paid', (bool) $value['is_paid']);
    }
    if (isset($value['subscription_status'])) {
        update_post_meta($vendor_id, 'vendor_subscription_status', sanitize_text_field($value['subscription_status']));
    }
    if (isset($value['cover_photo'])) {
        update_post_meta($vendor_id, 'vendor_cover_photo', sanitize_url($value['cover_photo']));
    }
    if (isset($value['social_links'])) {
        update_post_meta($vendor_id, 'vendor_social_links', $value['social_links']);
    }
    if (isset($value['assigned_members'])) {
        update_post_meta($vendor_id, 'vendor_assigned_members', $value['assigned_members']);
    }
    if (isset($value['lead_form_enabled'])) {
        update_post_meta($vendor_id, 'vendor_lead_form_enabled', (bool) $value['lead_form_enabled']);
    }
    if (isset($value['resources'])) {
        update_post_meta($vendor_id, 'vendor_resources', $value['resources']);
    }
    
    return true;
}

// Get vendor categories for REST API
function get_vendor_categories_for_api($object) {
    $terms = wp_get_post_terms($object['id'], 'vendor_category');
    if (is_wp_error($terms)) {
        return array();
    }
    
    return array_map(function($term) {
        return array(
            'id' => $term->term_id,
            'name' => $term->name,
            'slug' => $term->slug,
        );
    }, $terms);
}

// Register custom REST endpoints for vendors
function register_vendor_rest_endpoints() {
    // Get paid vendors (featured vendors)
    register_rest_route('tourismiq/v1', '/vendors/paid', array(
        'methods' => 'GET',
        'callback' => 'get_paid_vendors',
        'permission_callback' => '__return_true',
    ));

    // Get vendor by slug
    register_rest_route('tourismiq/v1', '/vendors/slug/(?P<slug>[a-zA-Z0-9-]+)', array(
        'methods' => 'GET',
        'callback' => 'get_vendor_by_slug',
        'permission_callback' => '__return_true',
        'args' => array(
            'slug' => array(
                'required' => true,
                'type' => 'string',
            ),
        ),
    ));

    // Get vendor meta endpoint
    register_rest_route('tourismiq/v1', '/vendor/(?P<id>\d+)/meta', array(
        'methods' => array('GET', 'POST'),
        'callback' => 'handle_vendor_meta_endpoint',
        'permission_callback' => '__return_true',
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'integer',
            ),
        ),
    ));

    // Get vendors by category
    register_rest_route('tourismiq/v1', '/vendors/category/(?P<category>[a-zA-Z0-9-]+)', array(
        'methods' => 'GET',
        'callback' => 'get_vendors_by_category',
        'permission_callback' => '__return_true',
        'args' => array(
            'category' => array(
                'required' => true,
                'type' => 'string',
            ),
        ),
    ));

    // Get vendor posts by ID
    register_rest_route('tourismiq/v1', '/vendors/(?P<id>\d+)/posts', array(
        'methods' => 'GET',
        'callback' => 'get_vendor_posts',
        'permission_callback' => '__return_true',
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'integer',
            ),
        ),
    ));

    // Get vendor posts by slug
    register_rest_route('tourismiq/v1', '/vendors/slug/(?P<slug>[a-zA-Z0-9-]+)/posts', array(
        'methods' => 'GET',
        'callback' => 'get_vendor_posts_by_slug',
        'permission_callback' => '__return_true',
        'args' => array(
            'slug' => array(
                'required' => true,
                'type' => 'string',
            ),
        ),
    ));

    // Assign member to vendor
    register_rest_route('tourismiq/v1', '/vendors/(?P<id>\d+)/assign-member', array(
        'methods' => 'POST',
        'callback' => 'assign_member_to_vendor',
        'permission_callback' => 'vendor_management_permission_check',
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'integer',
            ),
            'member_id' => array(
                'required' => true,
                'type' => 'integer',
            ),
        ),
    ));

    // Update vendor (both post and meta data)
    register_rest_route('tourismiq/v1', '/vendor/(?P<id>\d+)/update', array(
        'methods' => 'POST',
        'callback' => 'update_vendor_complete',
        'permission_callback' => 'vendor_update_permission_check',
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'integer',
            ),
        ),
    ));

    // Get vendors assigned to current user
    register_rest_route('tourismiq/v1', '/user/assigned-vendors', array(
        'methods' => 'GET',
        'callback' => 'get_user_assigned_vendors',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
    ));

    // Vendor upgrade endpoint for Stripe payment processing
    register_rest_route('tourismiq/v1', '/vendors/(?P<id>\d+)/upgrade', array(
        'methods' => 'POST',
        'callback' => 'handle_vendor_upgrade',
        'permission_callback' => '__return_true', // Allow public access for Stripe webhooks
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'integer',
            ),
        ),
    ));

    // Debug endpoint for vendor paid status
    register_rest_route('tourismiq/v1', '/vendors/debug-paid', array(
        'methods' => 'GET',
        'callback' => 'debug_paid_vendors',
        'permission_callback' => function() {
            return current_user_can('manage_options');
        },
    ));

    // Get vendor by ID (for upgrade process)
    register_rest_route('tourismiq/v1', '/vendors/by-id/(?P<id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'get_vendor_by_id',
        'permission_callback' => '__return_true',
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'integer',
            ),
        ),
    ));
    
    // Request vendor upgrade link via email
    register_rest_route('tourismiq/v1', '/vendors/(?P<id>\d+)/request-upgrade-link', array(
        'methods' => 'POST',
        'callback' => 'handle_vendor_upgrade_link_request',
        'permission_callback' => '__return_true',
        'args' => array(
            'id' => array(
                'required' => true,
                'type' => 'integer',
            ),
        ),
    ));
}
add_action('rest_api_init', 'register_vendor_rest_endpoints');

// Filter to exclude paid vendors from the main vendors endpoint
function exclude_paid_vendors_from_rest_query($args, $request) {
    // Only apply to vendor post type
    if ($args['post_type'] !== 'vendor') {
        return $args;
    }
    
    // Don't exclude if this is an admin request or specific vendor request
    if (is_admin() || isset($request['id']) || isset($request['slug'])) {
        return $args;
    }
    
    // Add meta query to exclude paid vendors
    if (!isset($args['meta_query'])) {
        $args['meta_query'] = array();
    }
    
    $args['meta_query'][] = array(
        'relation' => 'OR',
        array(
            'key' => 'vendor_is_paid',
            'value' => '1',
            'compare' => '!='
        ),
        array(
            'key' => 'vendor_is_paid',
            'compare' => 'NOT EXISTS'
        )
    );
    
    return $args;
}
add_filter('rest_vendor_query', 'exclude_paid_vendors_from_rest_query', 10, 2);

// Get paid vendors (featured vendors)
function get_paid_vendors($request) {
    error_log("get_paid_vendors function called");
    
    $vendors = get_posts(array(
        'post_type' => 'vendor',
        'post_status' => 'publish',
        'numberposts' => -1,
        'meta_query' => array(
            array(
                'key' => 'vendor_is_paid',
                'value' => '1',
                'compare' => '='
            )
        ),
        'orderby' => 'title',
        'order' => 'ASC'
    ));

    error_log("Found " . count($vendors) . " paid vendors");

    $controller = new WP_REST_Posts_Controller('vendor');
    $data = array();
    
    foreach ($vendors as $vendor) {
        $vendor_data = $controller->prepare_item_for_response($vendor, $request);
        $data[] = $vendor_data->get_data();
    }
    
    error_log("Returning " . count($data) . " vendors in response");
    return rest_ensure_response($data);
}

// Get vendor by slug
function get_vendor_by_slug($request) {
    $slug = $request['slug'];
    
    $vendors = get_posts(array(
        'post_type' => 'vendor',
        'name' => $slug,
        'post_status' => 'publish',
        'numberposts' => 1
    ));
    
    if (empty($vendors)) {
        return new WP_Error('vendor_not_found', 'Vendor not found', array('status' => 404));
    }
    
    $vendor = $vendors[0];
    $controller = new WP_REST_Posts_Controller('vendor');
    $data = $controller->prepare_item_for_response($vendor, $request);
    
    return rest_ensure_response($data);
}

// Get vendors by category
function get_vendors_by_category($request) {
    $category = $request['category'];
    
    $vendors = get_posts(array(
        'post_type' => 'vendor',
        'post_status' => 'publish',
        'numberposts' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => 'vendor_category',
                'field' => 'slug',
                'terms' => $category,
            ),
        ),
    ));
    
    $controller = new WP_REST_Posts_Controller('vendor');
    $data = array();
    
    foreach ($vendors as $vendor) {
        $vendor_data = $controller->prepare_item_for_response($vendor, $request);
        $data[] = $vendor_data->get_data();
    }
    
    return rest_ensure_response($data);
}

// Get vendor posts by ID
function get_vendor_posts($request) {
    $vendor_id = $request['id'];
    
    // Get posts directly linked to this vendor via ACF relationship field or legacy post_vendor_id
    $posts = get_posts(array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'numberposts' => -1,
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key' => 'post_vendor',
                'value' => '"' . $vendor_id . '"', // ACF stores relationship as serialized array
                'compare' => 'LIKE'
            ),
            array(
                'key' => 'post_vendor_id',
                'value' => $vendor_id,
                'compare' => '='
            )
        ),
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    return prepare_vendor_posts_response($posts, $request);
}

// Get vendor posts by slug
function get_vendor_posts_by_slug($request) {
    $slug = $request['slug'];
    
    // Get vendor by slug
    $vendors = get_posts(array(
        'post_type' => 'vendor',
        'name' => $slug,
        'post_status' => 'publish',
        'numberposts' => 1
    ));
    
    if (empty($vendors)) {
        return new WP_Error('vendor_not_found', 'Vendor not found', array('status' => 404));
    }
    
    $vendor_id = $vendors[0]->ID;
    
    // Get posts directly linked to this vendor via ACF relationship field or legacy post_vendor_id
    $posts = get_posts(array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'numberposts' => -1,
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key' => 'post_vendor',
                'value' => '"' . $vendor_id . '"', // ACF stores relationship as serialized array
                'compare' => 'LIKE'
            ),
            array(
                'key' => 'post_vendor_id',
                'value' => $vendor_id,
                'compare' => '='
            )
        ),
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    return prepare_vendor_posts_response($posts, $request);
}

// Helper function to prepare vendor posts response
function prepare_vendor_posts_response($posts, $request) {
    $controller = new WP_REST_Posts_Controller('post');
    $data = array();
    
    foreach ($posts as $post) {
        $post_data = $controller->prepare_item_for_response($post, $request);
        $response_data = $post_data->get_data();
        
        // Handle _embed parameter manually since we're using a custom endpoint
        if ($request->get_param('_embed')) {
            $response_data['_embedded'] = array();
            
            // Add author data
            $author = get_user_by('id', $post->post_author);
            if ($author) {
                $response_data['_embedded']['author'] = array(array(
                    'id' => $author->ID,
                    'name' => $author->display_name,
                    'avatar_urls' => array(
                        '96' => null // Let frontend handle the fallback to placeholder
                    )
                ));
            }
            
            // Add featured media
            $featured_media_id = get_post_thumbnail_id($post->ID);
            if ($featured_media_id) {
                $featured_media = wp_get_attachment_image_src($featured_media_id, 'medium');
                if ($featured_media) {
                    $response_data['_embedded']['wp:featuredmedia'] = array(array(
                        'id' => $featured_media_id,
                        'source_url' => $featured_media[0],
                        'alt_text' => get_post_meta($featured_media_id, '_wp_attachment_image_alt', true)
                    ));
                }
            }
            
            // Add categories
            $categories = get_the_category($post->ID);
            if ($categories) {
                $response_data['_embedded']['wp:term'] = array(
                    array_map(function($cat) {
                        return array(
                            'id' => $cat->term_id,
                            'name' => $cat->name,
                            'slug' => $cat->slug
                        );
                    }, $categories)
                );
            }
        }
        
        $data[] = $response_data;
    }
    
    return rest_ensure_response($data);
}

// Assign member to vendor
function assign_member_to_vendor($request) {
    $vendor_id = $request['id'];
    $member_id = $request['member_id'];
    
    // Verify vendor exists
    $vendor = get_post($vendor_id);
    if (!$vendor || $vendor->post_type !== 'vendor') {
        return new WP_Error('vendor_not_found', 'Vendor not found', array('status' => 404));
    }
    
    // Verify member exists
    $member = get_user_by('id', $member_id);
    if (!$member) {
        return new WP_Error('invalid_member', 'Member not found', array('status' => 400));
    }
    
    // Get current assigned members
    $assigned_members = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
    
    // Add member if not already assigned
    if (!in_array($member_id, $assigned_members)) {
        $assigned_members[] = $member_id;
        update_post_meta($vendor_id, 'vendor_assigned_members', $assigned_members);
    }
    
    return rest_ensure_response(array(
        'success' => true,
        'message' => 'Member assigned to vendor successfully',
        'assigned_members' => $assigned_members
    ));
}

// Permission check for vendor management
function vendor_management_permission_check($request) {
    if (!is_user_logged_in()) {
        return false;
    }
    
    $current_user = wp_get_current_user();
    
    // Admins can always manage vendors
    if (in_array('administrator', $current_user->roles)) {
        return true;
    }
    
    // For vendor-specific operations, check if user is assigned to this vendor
    $vendor_id = $request['id'];
    if ($vendor_id) {
        $assigned_members = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
        return in_array($current_user->ID, $assigned_members);
    }
    
    return false;
}

// Vendor membership utility functions
function is_user_vendor_member($user_id, $vendor_id = null) {
    if (!$user_id) {
        return false;
    }
    
    if ($vendor_id) {
        // Check if user is assigned to specific vendor
        $assigned_members = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
        return in_array($user_id, $assigned_members);
    }
    
    // Check if user is assigned to any vendor
    $vendors = get_posts(array(
        'post_type' => 'vendor',
        'post_status' => 'publish',
        'numberposts' => -1,
        'fields' => 'ids'
    ));
    
    foreach ($vendors as $vendor_id) {
        $members = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
        if (in_array($user_id, $members)) {
            return true;
        }
    }
    
    return false;
}

function get_user_vendor_assignments($user_id) {
    if (!$user_id) {
        return array();
    }
    
    $vendors = get_posts(array(
        'post_type' => 'vendor',
        'post_status' => 'publish',
        'numberposts' => -1,
        'fields' => 'ids'
    ));
    
    $assigned_vendors = array();
    
    foreach ($vendors as $vendor_id) {
        $members = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
        if (in_array($user_id, $members)) {
            $assigned_vendors[] = $vendor_id;
        }
    }
    
    return $assigned_vendors;
}

// Handle vendor meta endpoint
function handle_vendor_meta_endpoint($request) {
    $vendor_id = $request['id'];
    
    // Check if vendor exists
    $vendor = get_post($vendor_id);
    if (!$vendor || $vendor->post_type !== 'vendor') {
        return new WP_Error('vendor_not_found', 'Vendor not found', array('status' => 404));
    }
    
    if ($request->get_method() === 'GET') {
        // Return vendor meta data
        $vendor_object = array('id' => $vendor_id);
        $meta_data = get_vendor_meta_for_api($vendor_object);
        
        // Ensure cover_photo is always a URL
        if (isset($meta_data['cover_photo']) && is_numeric($meta_data['cover_photo'])) {
            $url = wp_get_attachment_url($meta_data['cover_photo']);
            if ($url) {
                $meta_data['cover_photo'] = $url;
            }
        }
        
        return $meta_data;
    } elseif ($request->get_method() === 'POST') {
        // Update vendor meta data
        $data = $request->get_json_params();
        
        // Check permissions for updating
        if (!current_user_can('edit_post', $vendor_id)) {
            return new WP_Error('insufficient_permissions', 'You do not have permission to edit this vendor', array('status' => 403));
        }
        
        // Update the meta fields
        if (isset($data['website'])) {
            update_post_meta($vendor_id, 'vendor_website', sanitize_url($data['website']));
        }
        if (isset($data['is_paid'])) {
            update_post_meta($vendor_id, 'vendor_is_paid', (bool) $data['is_paid']);
        }
        if (isset($data['subscription_status'])) {
            update_post_meta($vendor_id, 'vendor_subscription_status', sanitize_text_field($data['subscription_status']));
        }
        if (isset($data['cover_photo'])) {
            update_post_meta($vendor_id, 'vendor_cover_photo', sanitize_url($data['cover_photo']));
        }
        if (isset($data['social_links'])) {
            update_post_meta($vendor_id, 'vendor_social_links', $data['social_links']);
        }
        if (isset($data['assigned_members'])) {
            update_post_meta($vendor_id, 'vendor_assigned_members', $data['assigned_members']);
        }
        if (isset($data['lead_form_enabled'])) {
            update_post_meta($vendor_id, 'vendor_lead_form_enabled', (bool) $data['lead_form_enabled']);
        }
        if (isset($data['resources'])) {
            update_post_meta($vendor_id, 'vendor_resources', $data['resources']);
        }
        
        // Return updated meta data
        $vendor_object = array('id' => $vendor_id);
        return get_vendor_meta_for_api($vendor_object);
    }
}

// Update vendor complete (post + meta data)
function update_vendor_complete($request) {
    $vendor_id = $request['id'];
    
    // Check if vendor exists
    $vendor = get_post($vendor_id);
    if (!$vendor || $vendor->post_type !== 'vendor') {
        return new WP_Error('vendor_not_found', 'Vendor not found', array('status' => 404));
    }
    
    $data = $request->get_json_params();
    
    // Update post data if provided
    $post_data = array();
    if (isset($data['title'])) {
        $post_data['post_title'] = sanitize_text_field($data['title']);
    }
    if (isset($data['content'])) {
        $post_data['post_content'] = wp_kses_post($data['content']);
    }
    
    if (!empty($post_data)) {
        $post_data['ID'] = $vendor_id;
        $result = wp_update_post($post_data);
        if (is_wp_error($result)) {
            return new WP_Error('update_failed', 'Failed to update vendor post', array('status' => 500));
        }
    }
    
    // Update meta data
    if (isset($data['website'])) {
        update_post_meta($vendor_id, 'vendor_website', sanitize_url($data['website']));
    }
    if (isset($data['cover_photo'])) {
        // Update both custom meta and ACF field for cover photo
        update_post_meta($vendor_id, 'vendor_cover_photo', sanitize_url($data['cover_photo']));
        
        // Also update the ACF field
        if (!empty($data['cover_photo'])) {
            // Try to get attachment ID from URL for ACF field
            $attachment_id = attachment_url_to_postid($data['cover_photo']);
            if ($attachment_id) {
                // Store as attachment ID for ACF image field
                update_field('vendor_cover_photo', $attachment_id, $vendor_id);
            } else {
                // If we can't get attachment ID, try to find it by filename
                $upload_dir = wp_upload_dir();
                $file_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $data['cover_photo']);
                
                if (file_exists($file_path)) {
                    // Create attachment if file exists but not in database
                    $attachment_id = wp_insert_attachment(array(
                        'post_mime_type' => wp_check_filetype($file_path)['type'],
                        'post_title' => sanitize_file_name(basename($file_path)),
                        'post_content' => '',
                        'post_status' => 'inherit'
                    ), $file_path);
                    
                    if (!is_wp_error($attachment_id)) {
                        require_once(ABSPATH . 'wp-admin/includes/image.php');
                        $attachment_data = wp_generate_attachment_metadata($attachment_id, $file_path);
                        wp_update_attachment_metadata($attachment_id, $attachment_data);
                        update_field('vendor_cover_photo', $attachment_id, $vendor_id);
                    } else {
                        // Fallback: store URL directly
                        update_field('vendor_cover_photo', $data['cover_photo'], $vendor_id);
                    }
                } else {
                    // Fallback: store URL directly
                    update_field('vendor_cover_photo', $data['cover_photo'], $vendor_id);
                }
            }
        }
    }
    if (isset($data['logo_url'])) {
        // For logo, we need to set it as the featured image
        if (!empty($data['logo_url'])) {
            // Try to get attachment ID from URL
            $attachment_id = attachment_url_to_postid($data['logo_url']);
            if ($attachment_id) {
                set_post_thumbnail($vendor_id, $attachment_id);
            }
        }
    }
    if (isset($data['resources'])) {
        update_post_meta($vendor_id, 'vendor_resources', $data['resources']);
    }
    if (isset($data['assigned_members'])) {
        // Validate that all assigned members are valid user IDs
        $member_ids = array_filter(array_map('intval', $data['assigned_members']));
        $valid_member_ids = array();
        
        foreach ($member_ids as $member_id) {
            $user = get_user_by('id', $member_id);
            if ($user) {
                $valid_member_ids[] = $member_id;
            }
        }
        
        update_post_meta($vendor_id, 'vendor_assigned_members', $valid_member_ids);
    }
    
    return rest_ensure_response(array(
        'success' => true,
        'message' => 'Vendor updated successfully'
    ));
}

// Permission check for vendor updates
function vendor_update_permission_check($request) {
    if (!is_user_logged_in()) {
        return false;
    }
    
    $current_user = wp_get_current_user();
    
    // Admins can always update vendors
    if (in_array('administrator', $current_user->roles)) {
        return true;
    }
    
    // Check if user is assigned to this vendor
    $vendor_id = $request['id'];
    if ($vendor_id) {
        $assigned_members = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
        return in_array($current_user->ID, $assigned_members);
    }
    
    return false;
}

// Get vendors assigned to current user
function get_user_assigned_vendors($request) {
    $current_user = wp_get_current_user();
    
    if (!$current_user->exists()) {
        return new WP_Error('not_logged_in', 'User is not logged in', array('status' => 401));
    }
    
    // Get all vendors and check assigned members manually
    // This is more reliable than using meta_query with serialized arrays
    $vendors = get_posts(array(
        'post_type' => 'vendor',
        'post_status' => 'publish',
        'numberposts' => -1,
    ));
    
    $assigned_vendors = array();
    
    foreach ($vendors as $vendor) {
        $assigned_members = get_post_meta($vendor->ID, 'vendor_assigned_members', true);
        
        // Ensure assigned_members is an array
        if (!is_array($assigned_members)) {
            $assigned_members = array();
        }
        
        // Check if current user is in the assigned members array
        if (in_array($current_user->ID, $assigned_members)) {
            $logo_url = wp_get_attachment_url(get_post_thumbnail_id($vendor->ID));
            
            $assigned_vendors[] = array(
                'id' => $vendor->ID,
                'name' => $vendor->post_title,
                'slug' => $vendor->post_name,
                'logo_url' => $logo_url ?: null,
                'is_paid' => (bool) get_post_meta($vendor->ID, 'vendor_is_paid', true),
            );
        }
    }
    
    return rest_ensure_response($assigned_vendors);
}

/**
 * Helper function to get vendor data for REST API
 */
function get_vendor_data_for_rest($vendor_id) {
    $vendor = get_post($vendor_id);
    
    // Get basic data
    $featured_image_id = get_post_thumbnail_id($vendor_id);
    $featured_image = $featured_image_id ? wp_get_attachment_image_url($featured_image_id, 'full') : null;
    
    // Get categories
    $categories = get_the_terms($vendor_id, 'vendor_category');
    $category_names = $categories && !is_wp_error($categories) ? wp_list_pluck($categories, 'name') : array();
    
    return array(
        'id' => $vendor_id,
        'title' => array('rendered' => $vendor->post_title),
        'content' => array('rendered' => $vendor->post_content),
        'excerpt' => array('rendered' => get_the_excerpt($vendor_id)),
        'slug' => $vendor->post_name,
        'featured_media' => $featured_image_id ?: 0,
        'vendor_categories' => $category_names,
        'vendor_meta' => array(
            'website' => get_field('vendor_website', $vendor_id) ?: '',
            'is_paid' => (bool) get_field('vendor_is_paid', $vendor_id),
            'cover_photo' => get_field('vendor_cover_photo', $vendor_id) ?: '',
            'logo_url' => $featured_image,
        ),
    );
}

/**
 * Get vendor by ID for upgrade process
 */
function get_vendor_by_id($request) {
    $vendor_id = $request['id'];
    
    $vendor = get_post($vendor_id);
    
    if (!$vendor || $vendor->post_type !== 'vendor') {
        return new WP_Error('vendor_not_found', 'Vendor not found', array('status' => 404));
    }
    
    $controller = new WP_REST_Posts_Controller('vendor');
    $data = $controller->prepare_item_for_response($vendor, $request);
    
    return rest_ensure_response($data);
}

/**
 * Handle vendor upgrade from Stripe payment
 */
function handle_vendor_upgrade($request) {
    $vendor_id = $request['id'];
    
    // Verify vendor exists
    $vendor = get_post($vendor_id);
    if (!$vendor || $vendor->post_type !== 'vendor') {
        return new WP_Error('vendor_not_found', 'Vendor not found', array('status' => 404));
    }
    
    // Get request data
    $body = $request->get_json_params();
    
    // Validate required fields
    if (empty($body['session_id']) || empty($body['payment_status'])) {
        return new WP_Error('missing_data', 'Session ID and payment status are required', array('status' => 400));
    }
    
    // Validate token if provided (from email verification flow)
    if (!empty($body['token'])) {
        $transient_key = 'vendor_upgrade_' . $body['token'];
        $token_data = get_transient($transient_key);
        
        if (!$token_data) {
            return new WP_Error('invalid_token', 'Invalid or expired upgrade link', array('status' => 401));
        }
        
        // Verify token matches this vendor
        if ($token_data['vendor_id'] != $vendor_id) {
            return new WP_Error('token_mismatch', 'Token does not match vendor', array('status' => 401));
        }
        
        // Delete token after use (one-time use)
        delete_transient($transient_key);
        
        // Log token validation
        error_log("Token validated for vendor {$vendor_id}, email: {$token_data['email']}");
    }
    
    // Only process if payment was successful
    if ($body['payment_status'] !== 'paid') {
        return new WP_Error('payment_not_completed', 'Payment was not completed successfully', array('status' => 400));
    }
    
    try {
        // Log the upgrade attempt
        error_log("Processing vendor upgrade for vendor ID: {$vendor_id}, session: {$body['session_id']}");
        
        // Update vendor to paid status
        // First delete the old value to force update
        delete_post_meta($vendor_id, 'vendor_is_paid');
        $update_result = update_post_meta($vendor_id, 'vendor_is_paid', '1');  // ACF true_false fields store as string '1'
        
        // Log the actual result
        $verify_paid = get_post_meta($vendor_id, 'vendor_is_paid', true);
        error_log("Vendor {$vendor_id} upgrade: update_result={$update_result}, verified_value={$verify_paid}");
        
        
        // Update subscription status
        update_post_meta($vendor_id, 'vendor_subscription_status', 'active');
        
        
        // Store Stripe session information for tracking
        if (!empty($body['session_id'])) {
            update_post_meta($vendor_id, 'vendor_stripe_session_id', sanitize_text_field($body['session_id']));
        }
        
        if (!empty($body['stripe_customer_id'])) {
            update_post_meta($vendor_id, 'vendor_stripe_customer_id', sanitize_text_field($body['stripe_customer_id']));
        }
        
        if (!empty($body['stripe_subscription_id'])) {
            update_post_meta($vendor_id, 'vendor_stripe_subscription_id', sanitize_text_field($body['stripe_subscription_id']));
        }
        
        // Store subscription start date
        if (!empty($body['subscription_start'])) {
            update_post_meta($vendor_id, 'vendor_subscription_start', sanitize_text_field($body['subscription_start']));
        } else {
            // Use current date as fallback
            update_post_meta($vendor_id, 'vendor_subscription_start', current_time('mysql'));
        }
        
        // Store upgrade timestamp
        update_post_meta($vendor_id, 'vendor_upgraded_at', current_time('mysql'));
        
        // Store payment amount and currency for record keeping
        if (!empty($body['amount_total'])) {
            update_post_meta($vendor_id, 'vendor_last_payment_amount', intval($body['amount_total']));
        }
        
        if (!empty($body['currency'])) {
            update_post_meta($vendor_id, 'vendor_currency', sanitize_text_field($body['currency']));
        }
        
        // Update ACF fields if ACF is available
        if (function_exists('update_field')) {
            // Update the main 'paid vendor' ACF field - use string '1' for ACF true_false fields
            $acf_update_result = update_field('vendor_is_paid', '1', $vendor_id);
            
            // Verify ACF update
            $acf_verify = get_field('vendor_is_paid', $vendor_id);
            error_log("Vendor {$vendor_id} ACF update: result={$acf_update_result}, verified_value={$acf_verify}");
            
            // Update other ACF fields
            update_field('vendor_subscription_status', 'active', $vendor_id);
            
            
            if (!empty($body['stripe_customer_id'])) {
                update_field('vendor_stripe_customer_id', sanitize_text_field($body['stripe_customer_id']), $vendor_id);
            }
            
            if (!empty($body['stripe_subscription_id'])) {
                update_field('vendor_stripe_subscription_id', sanitize_text_field($body['stripe_subscription_id']), $vendor_id);
            }
            
            // Update timestamp fields
            update_field('vendor_upgraded_at', current_time('mysql'), $vendor_id);
            if (!empty($body['subscription_start'])) {
                update_field('vendor_subscription_start', sanitize_text_field($body['subscription_start']), $vendor_id);
            } else {
                update_field('vendor_subscription_start', current_time('mysql'), $vendor_id);
            }
        }
        
        // Assign paying user to vendor team
        if (!empty($body['paying_user_id'])) {
            $paying_user_id = intval($body['paying_user_id']);
            
            // Verify the user exists
            $user = get_user_by('ID', $paying_user_id);
            if ($user) {
                // Get current assigned members
                $assigned_members = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
                
                // Add paying user if not already assigned
                if (!in_array($paying_user_id, $assigned_members)) {
                    $assigned_members[] = $paying_user_id;
                    update_post_meta($vendor_id, 'vendor_assigned_members', $assigned_members);
                    
                    // Update ACF field if available
                    if (function_exists('update_field')) {
                        update_field('vendor_assigned_members', $assigned_members, $vendor_id);
                    }
                    
                    error_log("Assigned paying user {$paying_user_id} to vendor {$vendor_id}");
                } else {
                    error_log("Paying user {$paying_user_id} already assigned to vendor {$vendor_id}");
                }
            } else {
                error_log("Warning: Paying user ID {$paying_user_id} not found when upgrading vendor {$vendor_id}");
            }
        }
        
        // Log successful upgrade
        error_log("Successfully upgraded vendor {$vendor_id} to paid status");
        
        // Send vendor upgrade email notification
        if (!empty($body['paying_user_id'])) {
            tourismiq_send_vendor_upgrade_email($vendor_id, $body['paying_user_id']);
        } else {
            // If no paying user ID, send to vendor author
            tourismiq_send_vendor_upgrade_email($vendor_id, $vendor->post_author);
        }
        
        // Return success response
        return rest_ensure_response(array(
            'success' => true,
            'message' => 'Vendor successfully upgraded to paid status',
            'vendor_id' => $vendor_id,
            'vendor_name' => $vendor->post_title,
            'vendor_slug' => $vendor->post_name,
            'is_paid' => true,
            'subscription_status' => 'active',
            'upgraded_at' => current_time('mysql')
        ));
        
    } catch (Exception $e) {
        error_log("Error upgrading vendor {$vendor_id}: " . $e->getMessage());
        
        return new WP_Error('upgrade_failed', 'Failed to upgrade vendor: ' . $e->getMessage(), array('status' => 500));
    }
}

/**
 * Debug function to check paid vendor status
 */
function debug_paid_vendors($request) {
    $debug_info = array();
    
    // Get all vendors
    $all_vendors = get_posts(array(
        'post_type' => 'vendor',
        'post_status' => 'publish',
        'numberposts' => -1,
        'orderby' => 'modified',
        'order' => 'DESC'
    ));
    
    foreach ($all_vendors as $vendor) {
        $vendor_id = $vendor->ID;
        
        // Get all possible paid status values
        $post_meta_paid = get_post_meta($vendor_id, 'vendor_is_paid', true);
        $acf_field_paid = get_field('vendor_is_paid', $vendor_id);
        
        
        $debug_info[] = array(
            'vendor_id' => $vendor_id,
            'vendor_name' => $vendor->post_title,
            'post_meta_vendor_is_paid' => $post_meta_paid,
            'post_meta_vendor_is_paid_type' => gettype($post_meta_paid),
            'acf_vendor_is_paid' => $acf_field_paid,
            'acf_vendor_is_paid_type' => gettype($acf_field_paid),
            'is_paid_evaluated' => !empty($post_meta_paid) || !empty($acf_field_paid),
            'modified' => $vendor->post_modified
        );
    }
    
    // Also run test queries
    $test_queries = array();
    
    // Test query 1: Standard meta query with string '1'
    $query1 = new WP_Query(array(
        'post_type' => 'vendor',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => 'vendor_is_paid',
                'value' => '1',
                'compare' => '='
            )
        )
    ));
    $test_queries['string_1_equals'] = $query1->found_posts;
    
    // Test query 2: Numeric 1 equals
    $query2 = new WP_Query(array(
        'post_type' => 'vendor',
        'post_status' => 'publish', 
        'posts_per_page' => -1,
        'meta_query' => array(
            array(
                'key' => 'vendor_is_paid',
                'value' => '1',
                'compare' => '='
            )
        )
    ));
    $test_queries['numeric_1_equals'] = $query2->found_posts;
    
    // Test query 3: NOT paid query
    $query3 = new WP_Query(array(
        'post_type' => 'vendor',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key' => 'vendor_is_paid',
                'value' => '1',
                'compare' => '!='
            ),
            array(
                'key' => 'vendor_is_paid',
                'compare' => 'NOT EXISTS'
            )
        )
    ));
    $test_queries['not_paid_query'] = $query3->found_posts;
    
    return rest_ensure_response(array(
        'vendors' => $debug_info,
        'test_queries' => $test_queries,
        'total_vendors' => count($all_vendors)
    ));
}

/**
 * Handle vendor upgrade link request
 * Validates email and sends secure upgrade link
 */
function handle_vendor_upgrade_link_request($request) {
    $vendor_id = $request->get_param('id');
    $body = $request->get_json_params();
    $email = isset($body['email']) ? sanitize_email($body['email']) : '';
    
    // Validate email
    if (empty($email) || !is_email($email)) {
        return new WP_Error('invalid_email', 'Please provide a valid email address', array('status' => 400));
    }
    
    // Rate limiting: Check if email was requested recently (within 5 minutes)
    $rate_limit_key = 'vendor_upgrade_rate_' . md5($email . $vendor_id);
    $last_request = get_transient($rate_limit_key);
    
    if ($last_request) {
        $time_remaining = 5 - ((time() - $last_request) / 60);
        return new WP_Error('rate_limited', 
            sprintf('Please wait %d more minutes before requesting another link', ceil($time_remaining)), 
            array('status' => 429)
        );
    }
    
    // Set rate limit transient
    set_transient($rate_limit_key, time(), 5 * 60); // 5 minutes
    
    // Get vendor
    $vendor = get_post($vendor_id);
    if (!$vendor || $vendor->post_type !== 'vendor') {
        return new WP_Error('vendor_not_found', 'Vendor not found', array('status' => 404));
    }
    
    // Check if vendor is already paid
    $is_paid = get_post_meta($vendor_id, 'vendor_is_paid', true);
    if ($is_paid === '1') {
        return new WP_Error('already_paid', 'This vendor is already upgraded to premium', array('status' => 400));
    }
    
    // Generate secure token (valid for 24 hours)
    $token = wp_generate_password(32, false);
    $expiry = time() + (24 * 60 * 60); // 24 hours from now
    
    // Store token in transient (auto-expires)
    $transient_key = 'vendor_upgrade_' . $token;
    $transient_data = array(
        'vendor_id' => $vendor_id,
        'email' => $email,
        'created' => time(),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
    );
    set_transient($transient_key, $transient_data, 24 * 60 * 60);
    
    // Generate Stripe payment link with token metadata
    $stripe_payment_link = defined('STRIPE_PAYMENT_LINK') ? STRIPE_PAYMENT_LINK : 'https://buy.stripe.com/test_dRm5kFgeB9SU6z72gr2cg00';
    $upgrade_link = add_query_arg(array(
        'client_reference_id' => $vendor_id,
        'prefilled_email' => urlencode($email),
    ), $stripe_payment_link);
    
    // Send upgrade link email
    if (function_exists('tourismiq_send_vendor_upgrade_link_email')) {
        $email_sent = tourismiq_send_vendor_upgrade_link_email(array(
            'vendor_id' => $vendor_id,
            'vendor_name' => get_the_title($vendor_id),
            'email' => $email,
            'upgrade_link' => $upgrade_link,
        ));
        
        if (!$email_sent) {
            return new WP_Error('email_failed', 'Failed to send upgrade link email', array('status' => 500));
        }
    } else {
        return new WP_Error('email_handler_missing', 'Email handler not available', array('status' => 500));
    }
    
    // Log the request for security
    error_log("Vendor upgrade link requested - Vendor: {$vendor_id}, Email: {$email}, IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
    
    return rest_ensure_response(array(
        'success' => true,
        'message' => 'Upgrade link has been sent to ' . $email,
        'vendor_id' => $vendor_id,
    ));
}

 