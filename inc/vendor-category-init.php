<?php
/**
 * Initialize vendor categories on theme activation
 */

// Function to ensure vendor categories are created
function tourismiq_ensure_vendor_categories() {
    // Check if we've already initialized categories
    $initialized = get_option('tourismiq_vendor_categories_initialized');
    
    if (!$initialized) {
        // Default categories to create
        $categories = array(
            'Tour Operator',
            'Travel Agency', 
            'Adventure Tours',
            'Luxury Travel',
            'Eco Tourism',
            'Cultural Tourism',
            'Tour Guides',
            'Marine Tourism',
            'Wine Tourism',
            'Culinary Tourism',
            'Historical Tours',
            'Accommodation',
            'Transportation',
            'Equipment Rental',
            'Technology',
            'Marketing & PR',
            'Events & Conferences',
            'Education & Training',
            'Consulting Services',
            'Insurance & Finance'
        );

        foreach ($categories as $category) {
            $term = term_exists($category, 'vendor_category');
            if (!$term) {
                $result = wp_insert_term($category, 'vendor_category');
                if (!is_wp_error($result)) {
                    error_log("Created vendor category: $category");
                } else {
                    error_log("Failed to create vendor category $category: " . $result->get_error_message());
                }
            }
        }
        
        // Mark as initialized
        update_option('tourismiq_vendor_categories_initialized', true);
        error_log("Vendor categories initialization completed");
    }
}

// Run on admin init to ensure categories exist
add_action('admin_init', 'tourismiq_ensure_vendor_categories');

// Also run when taxonomy is registered
add_action('registered_taxonomy', function($taxonomy) {
    if ($taxonomy === 'vendor_category') {
        tourismiq_ensure_vendor_categories();
    }
});