<?php
/**
 * Helper Functions
 * 
 * Utility and helper functions for the theme.
 */

if (!function_exists('tourismiq_include_php_files_in_directory')) {
    /**
     * Include all PHP files in a directory
     * 
     * @param string $directory Path to the directory
     */
    function tourismiq_include_php_files_in_directory($directory) {
        $directory = trailingslashit($directory);
        
        if (!is_dir($directory)) {
            return;
        }
        
        $files = glob($directory . '*.php');
        
        if (!$files) {
            return;
        }
        
        foreach ($files as $file) {
            if (is_file($file) && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                require_once $file;
            }
        }
    }
}

/**
 * Load all PHP files from the inc directory
 */
function tourismiq_load_inc_files() {
    $inc_dir = get_template_directory() . '/inc';
    
    // Include all PHP files in the inc directory
    if (is_dir($inc_dir)) {
        tourismiq_include_php_files_in_directory($inc_dir);
    }
}
add_action('after_setup_theme', 'tourismiq_load_inc_files');
