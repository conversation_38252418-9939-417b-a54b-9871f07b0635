<?php
/**
 * RFP Custom Post Type and Related Functionality
 */

// Register RFP Custom Post Type
function register_rfp_post_type() {
    $labels = array(
        'name'                  => _x('RFPs', 'Post type general name', 'tourismiq'),
        'singular_name'         => _x('RFP', 'Post type singular name', 'tourismiq'),
        'menu_name'             => _x('RFPs', 'Admin Menu text', 'tourismiq'),
        'name_admin_bar'        => _x('RFP', 'Add New on Toolbar', 'tourismiq'),
        'add_new'               => __('Add New', 'tourismiq'),
        'add_new_item'          => __('Add New RFP', 'tourismiq'),
        'new_item'              => __('New RFP', 'tourismiq'),
        'edit_item'             => __('Edit RFP', 'tourismiq'),
        'view_item'             => __('View RFP', 'tourismiq'),
        'all_items'             => __('All RFPs', 'tourismiq'),
        'search_items'          => __('Search RFPs', 'tourismiq'),
        'parent_item_colon'     => __('Parent RFPs:', 'tourismiq'),
        'not_found'             => __('No RFPs found.', 'tourismiq'),
        'not_found_in_trash'    => __('No RFPs found in Trash.', 'tourismiq'),
        'featured_image'        => _x('RFP Featured Image', 'Overrides the "Featured Image" phrase', 'tourismiq'),
        'set_featured_image'    => _x('Set featured image', 'Overrides the "Set featured image" phrase', 'tourismiq'),
        'remove_featured_image' => _x('Remove featured image', 'Overrides the "Remove featured image" phrase', 'tourismiq'),
        'use_featured_image'    => _x('Use as featured image', 'Overrides the "Use as featured image" phrase', 'tourismiq'),
        'archives'              => _x('RFP archives', 'The post type archive label', 'tourismiq'),
        'insert_into_item'      => _x('Insert into RFP', 'Overrides the "Insert into post" phrase', 'tourismiq'),
        'uploaded_to_this_item' => _x('Uploaded to this RFP', 'Overrides the "Uploaded to this post" phrase', 'tourismiq'),
        'filter_items_list'     => _x('Filter RFPs list', 'Screen reader text for the filter links', 'tourismiq'),
        'items_list_navigation' => _x('RFPs list navigation', 'Screen reader text for the pagination', 'tourismiq'),
        'items_list'            => _x('RFPs list', 'Screen reader text for the items list', 'tourismiq'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'rfps'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 22,
        'menu_icon'          => 'dashicons-media-document',
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields', 'author'),
        'show_in_rest'       => true,
        'rest_base'          => 'rfps',
        'rest_controller_class' => 'WP_REST_Posts_Controller',
    );

    register_post_type('rfp', $args);
}
add_action('init', 'register_rfp_post_type');

// Register RFP Category Taxonomy
function register_rfp_category_taxonomy() {
    $labels = array(
        'name'              => _x('RFP Categories', 'taxonomy general name', 'tourismiq'),
        'singular_name'     => _x('RFP Category', 'taxonomy singular name', 'tourismiq'),
        'search_items'      => __('Search RFP Categories', 'tourismiq'),
        'all_items'         => __('All RFP Categories', 'tourismiq'),
        'parent_item'       => __('Parent RFP Category', 'tourismiq'),
        'parent_item_colon' => __('Parent RFP Category:', 'tourismiq'),
        'edit_item'         => __('Edit RFP Category', 'tourismiq'),
        'update_item'       => __('Update RFP Category', 'tourismiq'),
        'add_new_item'      => __('Add New RFP Category', 'tourismiq'),
        'new_item_name'     => __('New RFP Category Name', 'tourismiq'),
        'menu_name'         => __('RFP Categories', 'tourismiq'),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'rfp-category'),
        'show_in_rest'      => true,
        'rest_base'         => 'rfp-categories',
    );

    register_taxonomy('rfp_category', array('rfp'), $args);
}
add_action('init', 'register_rfp_category_taxonomy');

// Register RFP State Taxonomy
function register_rfp_state_taxonomy() {
    $labels = array(
        'name'              => _x('RFP States', 'taxonomy general name', 'tourismiq'),
        'singular_name'     => _x('RFP State', 'taxonomy singular name', 'tourismiq'),
        'search_items'      => __('Search RFP States', 'tourismiq'),
        'all_items'         => __('All RFP States', 'tourismiq'),
        'parent_item'       => __('Parent RFP State', 'tourismiq'),
        'parent_item_colon' => __('Parent RFP State:', 'tourismiq'),
        'edit_item'         => __('Edit RFP State', 'tourismiq'),
        'update_item'       => __('Update RFP State', 'tourismiq'),
        'add_new_item'      => __('Add New RFP State', 'tourismiq'),
        'new_item_name'     => __('New RFP State Name', 'tourismiq'),
        'menu_name'         => __('RFP States', 'tourismiq'),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'rfp-state'),
        'show_in_rest'      => true,
        'rest_base'         => 'rfp-states',
    );

    register_taxonomy('rfp_state', array('rfp'), $args);
}
add_action('init', 'register_rfp_state_taxonomy');

// Register RFP Country Taxonomy
function register_rfp_country_taxonomy() {
    $labels = array(
        'name'              => _x('RFP Countries', 'taxonomy general name', 'tourismiq'),
        'singular_name'     => _x('RFP Country', 'taxonomy singular name', 'tourismiq'),
        'search_items'      => __('Search RFP Countries', 'tourismiq'),
        'all_items'         => __('All RFP Countries', 'tourismiq'),
        'parent_item'       => __('Parent RFP Country', 'tourismiq'),
        'parent_item_colon' => __('Parent RFP Country:', 'tourismiq'),
        'edit_item'         => __('Edit RFP Country', 'tourismiq'),
        'update_item'       => __('Update RFP Country', 'tourismiq'),
        'add_new_item'      => __('Add New RFP Country', 'tourismiq'),
        'new_item_name'     => __('New RFP Country Name', 'tourismiq'),
        'menu_name'         => __('RFP Countries', 'tourismiq'),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'rfp-country'),
        'show_in_rest'      => true,
        'rest_base'         => 'rfp-countries',
    );

    register_taxonomy('rfp_country', array('rfp'), $args);
}
add_action('init', 'register_rfp_country_taxonomy');

// Add custom REST API fields for RFPs
function add_rfp_rest_fields() {
    // Add RFP meta fields to REST API
    register_rest_field('rfp', 'rfp_meta', array(
        'get_callback' => 'get_rfp_meta_for_api',
        'update_callback' => 'update_rfp_meta_from_api',
        'schema' => array(
            'description' => 'RFP metadata',
            'type' => 'object',
            'properties' => array(
                'rfp_status' => array('type' => 'string'),
                'rfp_organization' => array('type' => 'string'),
                'rfp_source_url' => array('type' => 'string'),
                'rfp_deadline' => array('type' => 'string'),
            )
        )
    ));

    // Add RFP categories to REST API
    register_rest_field('rfp', 'rfp_categories', array(
        'get_callback' => function($object) {
            $terms = wp_get_post_terms($object['id'], 'rfp_category');
            return wp_list_pluck($terms, 'name');
        },
        'schema' => array(
            'description' => 'RFP categories',
            'type' => 'array',
        )
    ));

    // Add RFP states to REST API
    register_rest_field('rfp', 'rfp_states', array(
        'get_callback' => function($object) {
            $terms = wp_get_post_terms($object['id'], 'rfp_state');
            return wp_list_pluck($terms, 'name');
        },
        'schema' => array(
            'description' => 'RFP states',
            'type' => 'array',
        )
    ));

    // Add RFP countries to REST API
    register_rest_field('rfp', 'rfp_countries', array(
        'get_callback' => function($object) {
            $terms = wp_get_post_terms($object['id'], 'rfp_country');
            return wp_list_pluck($terms, 'name');
        },
        'schema' => array(
            'description' => 'RFP countries',
            'type' => 'array',
        )
    ));

    // Add RFP slug for easy frontend routing
    register_rest_field('rfp', 'slug', array(
        'get_callback' => function($object) {
            return get_post_field('post_name', $object['id']);
        },
        'schema' => array(
            'description' => 'RFP slug',
            'type' => 'string',
        )
    ));
}
add_action('rest_api_init', 'add_rfp_rest_fields');

// Get RFP meta for REST API
function get_rfp_meta_for_api($object) {
    $rfp_id = $object['id'];

    // Get ACF fields
    $rfp_status = get_field('rfp_status', $rfp_id);
    $rfp_organization = get_field('rfp_organization', $rfp_id);
    $rfp_source_url = get_field('rfp_source_url', $rfp_id);
    $rfp_deadline = get_field('rfp_deadline', $rfp_id);

    return array(
        'rfp_status' => $rfp_status ?: '',
        'rfp_organization' => $rfp_organization ?: '',
        'rfp_source_url' => $rfp_source_url ?: '',
        'rfp_deadline' => $rfp_deadline ?: '',
    );
}

// Update RFP meta from REST API
function update_rfp_meta_from_api($value, $object, $field_name) {
    $rfp_id = $object->ID;

    if (isset($value['rfp_status'])) {
        update_field('rfp_status', sanitize_text_field($value['rfp_status']), $rfp_id);
    }
    if (isset($value['rfp_organization'])) {
        update_field('rfp_organization', sanitize_text_field($value['rfp_organization']), $rfp_id);
    }
    if (isset($value['rfp_source_url'])) {
        update_field('rfp_source_url', esc_url_raw($value['rfp_source_url']), $rfp_id);
    }
    if (isset($value['rfp_deadline'])) {
        update_field('rfp_deadline', sanitize_text_field($value['rfp_deadline']), $rfp_id);
    }

    return true;
}

// Register custom REST endpoints for RFPs
function register_rfp_rest_endpoints() {
    // Enhanced RFP search endpoint
    register_rest_route('tourismiq/v1', '/rfps/search', array(
        'methods' => 'GET',
        'callback' => 'search_rfps_enhanced',
        'permission_callback' => '__return_true',
        'args' => array(
            'search' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'per_page' => array(
                'required' => false,
                'type' => 'integer',
                'default' => 2000,
            ),
            'page' => array(
                'required' => false,
                'type' => 'integer',
                'default' => 1,
            ),
            'category' => array(
                'required' => false,
                'type' => 'string',
            ),
            'status' => array(
                'required' => false,
                'type' => 'string',
            ),
            'organization' => array(
                'required' => false,
                'type' => 'string',
            ),
            'state' => array(
                'required' => false,
                'type' => 'string',
            ),
            'country' => array(
                'required' => false,
                'type' => 'string',
            ),
            'orderby' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'status_then_date',
            ),
            'order' => array(
                'required' => false,
                'type' => 'string',
                'default' => 'desc',
            ),
        ),
    ));

    // Get RFP by slug
    register_rest_route('tourismiq/v1', '/rfps/slug/(?P<slug>[a-zA-Z0-9-]+)', array(
        'methods' => 'GET',
        'callback' => 'get_rfp_by_slug',
        'permission_callback' => '__return_true',
        'args' => array(
            'slug' => array(
                'required' => true,
                'type' => 'string',
            ),
        ),
    ));
}
add_action('rest_api_init', 'register_rfp_rest_endpoints');

// Get RFP by slug
function get_rfp_by_slug($request) {
    $slug = $request['slug'];

    $rfps = get_posts(array(
        'post_type' => 'rfp',
        'name' => $slug,
        'post_status' => 'publish',
        'numberposts' => 1
    ));

    if (empty($rfps)) {
        return new WP_Error('rfp_not_found', 'RFP not found', array('status' => 404));
    }

    $rfp = $rfps[0];
    $controller = new WP_REST_Posts_Controller('rfp');
    $data = $controller->prepare_item_for_response($rfp, $request);

    return rest_ensure_response($data);
}

// State abbreviation mapping (reusing the same mapping as jobs)
function get_rfp_state_mapping() {
    return array(
        'AL' => 'Alabama', 'AK' => 'Alaska', 'AZ' => 'Arizona', 'AR' => 'Arkansas', 'CA' => 'California',
        'CO' => 'Colorado', 'CT' => 'Connecticut', 'DE' => 'Delaware', 'FL' => 'Florida', 'GA' => 'Georgia',
        'HI' => 'Hawaii', 'ID' => 'Idaho', 'IL' => 'Illinois', 'IN' => 'Indiana', 'IA' => 'Iowa',
        'KS' => 'Kansas', 'KY' => 'Kentucky', 'LA' => 'Louisiana', 'ME' => 'Maine', 'MD' => 'Maryland',
        'MA' => 'Massachusetts', 'MI' => 'Michigan', 'MN' => 'Minnesota', 'MS' => 'Mississippi', 'MO' => 'Missouri',
        'MT' => 'Montana', 'NE' => 'Nebraska', 'NV' => 'Nevada', 'NH' => 'New Hampshire', 'NJ' => 'New Jersey',
        'NM' => 'New Mexico', 'NY' => 'New York', 'NC' => 'North Carolina', 'ND' => 'North Dakota', 'OH' => 'Ohio',
        'OK' => 'Oklahoma', 'OR' => 'Oregon', 'PA' => 'Pennsylvania', 'RI' => 'Rhode Island', 'SC' => 'South Carolina',
        'SD' => 'South Dakota', 'TN' => 'Tennessee', 'TX' => 'Texas', 'UT' => 'Utah', 'VT' => 'Vermont',
        'VA' => 'Virginia', 'WA' => 'Washington', 'WV' => 'West Virginia', 'WI' => 'Wisconsin', 'WY' => 'Wyoming',
        'DC' => 'District of Columbia'
    );
}

// Get expanded search terms for states (abbreviation <-> full name)
function get_rfp_state_search_terms($search_term) {
    $state_mapping = get_rfp_state_mapping();
    $search_terms = array($search_term);

    $upper_search = strtoupper($search_term);
    $lower_search = strtolower($search_term);

    // If search term is an abbreviation, add full name
    if (isset($state_mapping[$upper_search])) {
        $search_terms[] = $state_mapping[$upper_search];
    }

    // If search term is a full name, add abbreviation
    foreach ($state_mapping as $abbrev => $full_name) {
        if (strtolower($full_name) === $lower_search) {
            $search_terms[] = $abbrev;
            break;
        }
    }

    return array_unique($search_terms);
}

// Increase query limits for RFP searches
add_filter('post_limits', function($limits, $query) {
    if (!is_admin() && $query->get('post_type') === 'rfp' && $query->get('posts_per_page') > 100) {
        // Remove limits for large RFP queries
        return '';
    }
    return $limits;
}, 10, 2);

// Enhanced RFP search function
function search_rfps_enhanced($request) {
    $search = $request->get_param('search') ? sanitize_text_field($request->get_param('search')) : '';
    $per_page = $request->get_param('per_page') ? intval($request->get_param('per_page')) : 2000;
    $page = $request->get_param('page') ? intval($request->get_param('page')) : 1;
    $category = $request->get_param('category') ? sanitize_text_field($request->get_param('category')) : '';
    $status = $request->get_param('status') ? sanitize_text_field($request->get_param('status')) : '';
    $organization = $request->get_param('organization') ? sanitize_text_field($request->get_param('organization')) : '';
    $state = $request->get_param('state') ? sanitize_text_field($request->get_param('state')) : '';
    $country = $request->get_param('country') ? sanitize_text_field($request->get_param('country')) : '';
    $orderby = $request->get_param('orderby') ? sanitize_text_field($request->get_param('orderby')) : 'status_then_date';
    $order = $request->get_param('order') ? sanitize_text_field($request->get_param('order')) : 'desc';

    // For large requests, use WordPress convention for "all posts"
    if ($per_page >= 1000) {
        $per_page = -1; // WordPress convention for "all posts"
        $offset = 0;
    } else {
        $offset = ($page - 1) * $per_page;
    }

    // Build query arguments
    $args = array(
        'post_type' => 'rfp',
        'post_status' => 'publish',
        'posts_per_page' => $per_page,
        'offset' => $offset,
        'no_found_rows' => false, // Ensure found_posts is calculated
        'cache_results' => false, // Don't cache for fresh results
    );

    // Initialize meta_query array
    $meta_query = array();
    
    // When search is active, prioritize relevance-based sorting
    $search_active = !empty($search);

    // Handle custom ordering for status-based sorting
    if ($search_active) {
        // When search is active, prioritize relevance first
        if ($orderby === 'status_then_date') {
            $args['orderby'] = array(
                'relevance_score' => 'DESC',
                'meta_value' => 'DESC', // NEW comes after EXPIRED alphabetically, so DESC puts NEW first
                'date' => $order
            );
            $args['meta_key'] = 'rfp_status';
            $args['meta_type'] = 'CHAR';
            // Include posts without the meta key (empty status) by adding to meta_query
            $meta_query[] = array(
                'relation' => 'OR',
                array(
                    'key' => 'rfp_status',
                    'compare' => 'EXISTS'
                ),
                array(
                    'key' => 'rfp_status',
                    'compare' => 'NOT EXISTS'
                )
            );
        } else {
            // For other orderby values when search is active, still prioritize relevance
            $args['orderby'] = array(
                'relevance_score' => 'DESC',
                $orderby => $order
            );
        }
    } else {
        // No search active, use standard ordering
        if ($orderby === 'status_then_date') {
            // Custom sorting: NEW status first, then empty/null, then others, all by date within groups
            $args['orderby'] = array(
                'meta_value' => 'DESC', // NEW comes after EXPIRED alphabetically, so DESC puts NEW first
                'date' => $order
            );
            $args['meta_key'] = 'rfp_status';
            $args['meta_type'] = 'CHAR';
            // Include posts without the meta key (empty status) by adding to meta_query
            $meta_query[] = array(
                'relation' => 'OR',
                array(
                    'key' => 'rfp_status',
                    'compare' => 'EXISTS'
                ),
                array(
                    'key' => 'rfp_status',
                    'compare' => 'NOT EXISTS'
                )
            );
        } elseif ($orderby === 'date') {
            // Sort by date only
            $args['orderby'] = 'date';
            $args['order'] = $order;
        } else {
            // Standard ordering for other orderby values
            $args['orderby'] = $orderby;
            $args['order'] = $order;
        }
    }

    // Add comprehensive search functionality with relevance scoring
    if (!empty($search)) {
        // Add relevance scoring to the SELECT clause
        add_filter('posts_fields', function($fields, $query) use ($search) {
            global $wpdb;
            if ($query->get('post_type') === 'rfp' && !empty($search)) {
                // Escape the search term
                $search_term = '%' . $wpdb->esc_like($search) . '%';
                
                // Get state variations for location search
                $state_terms = get_state_search_terms($search);
                $state_search_parts = array();
                
                foreach ($state_terms as $state_term) {
                    $escaped_state = '%' . $wpdb->esc_like($state_term) . '%';
                    $state_search_parts[] = "pm_score.meta_value LIKE '$escaped_state'";
                }
                
                $state_search_clause = !empty($state_search_parts) ? '(' . implode(' OR ', $state_search_parts) . ')' : "pm_score.meta_value LIKE '$search_term'";
                
                // Add relevance scoring to SELECT
                $relevance_score = ", (
                    CASE 
                        WHEN EXISTS (
                            SELECT 1 FROM {$wpdb->term_relationships} tr_score
                            INNER JOIN {$wpdb->term_taxonomy} tt_score ON tr_score.term_taxonomy_id = tt_score.term_taxonomy_id
                            INNER JOIN {$wpdb->terms} t_score ON tt_score.term_id = t_score.term_id
                            WHERE tr_score.object_id = {$wpdb->posts}.ID
                            AND tt_score.taxonomy = 'rfp_state'
                            AND (t_score.name LIKE '$search_term' OR t_score.slug LIKE '$search_term')
                        ) THEN 100
                        WHEN {$wpdb->posts}.post_title LIKE '$search_term' THEN 80
                        WHEN EXISTS (
                            SELECT 1 FROM {$wpdb->postmeta} pm_score
                            WHERE pm_score.post_id = {$wpdb->posts}.ID 
                            AND pm_score.meta_key = 'rfp_organization'
                            AND pm_score.meta_value LIKE '$search_term'
                        ) THEN 70
                        WHEN EXISTS (
                            SELECT 1 FROM {$wpdb->postmeta} pm_score
                            WHERE pm_score.post_id = {$wpdb->posts}.ID 
                            AND ($state_search_clause)
                            AND pm_score.meta_key NOT LIKE '\_%'
                        ) THEN 60
                        WHEN {$wpdb->posts}.post_content LIKE '$search_term' THEN 50
                        WHEN {$wpdb->posts}.post_excerpt LIKE '$search_term' THEN 30
                        ELSE 10
                    END
                ) AS relevance_score";
                
                $fields .= $relevance_score;
            }
            return $fields;
        }, 10, 2);
        
        // Use a custom posts_where filter for comprehensive search
        add_filter('posts_where', function($where, $query) use ($search) {
            global $wpdb;
            if ($query->get('post_type') === 'rfp' && !empty($search)) {
                // Remove any existing search WHERE clause
                $where = preg_replace('/AND \(.*post_title LIKE.*\)/', '', $where);
                
                // Escape the search term
                $search_term = '%' . $wpdb->esc_like($search) . '%';
                
                // Get state variations for location search
                $state_terms = get_rfp_state_search_terms($search);
                $state_search_parts = array();
                
                foreach ($state_terms as $state_term) {
                    $escaped_state = '%' . $wpdb->esc_like($state_term) . '%';
                    $state_search_parts[] = "pm.meta_value LIKE '$escaped_state'";
                }
                
                $state_search_clause = !empty($state_search_parts) ? '(' . implode(' OR ', $state_search_parts) . ')' : "pm.meta_value LIKE '$search_term'";
                
                // Create comprehensive search across all relevant fields
                $custom_where = " AND (
                    {$wpdb->posts}.post_title LIKE '$search_term' 
                    OR {$wpdb->posts}.post_content LIKE '$search_term'
                    OR {$wpdb->posts}.post_excerpt LIKE '$search_term'
                    OR EXISTS (
                        SELECT 1 FROM {$wpdb->postmeta} pm
                        WHERE pm.post_id = {$wpdb->posts}.ID 
                        AND ($state_search_clause)
                        AND pm.meta_key NOT LIKE '\_%'
                    )
                    OR EXISTS (
                        SELECT 1 FROM {$wpdb->term_relationships} tr
                        INNER JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
                        INNER JOIN {$wpdb->terms} t ON tt.term_id = t.term_id
                        WHERE tr.object_id = {$wpdb->posts}.ID
                        AND tt.taxonomy IN ('rfp_state', 'rfp_country', 'rfp_category')
                        AND (t.name LIKE '$search_term' OR t.slug LIKE '$search_term')
                    )
                )";
                $where .= $custom_where;
            }
            return $where;
        }, 10, 2);
        
        // Clear other search-related queries to avoid conflicts
        $tax_query = array();
        $meta_query = array();
    }

    // Handle filter tax queries
    $filter_tax_query = array();

    if (!empty($category)) {
        $filter_tax_query[] = array(
            'taxonomy' => 'rfp_category',
            'field'    => 'slug',
            'terms'    => $category,
        );
    }

    if (!empty($state)) {
        $filter_tax_query[] = array(
            'taxonomy' => 'rfp_state',
            'field'    => 'name',
            'terms'    => $state,
        );
    }

    if (!empty($country)) {
        $filter_tax_query[] = array(
            'taxonomy' => 'rfp_country',
            'field'    => 'name',
            'terms'    => $country,
        );
    }

    // Apply filter tax queries only if there's no search term (search uses custom WHERE clause)
    if (empty($search) && !empty($filter_tax_query)) {
        // Only filters when no search
        if (count($filter_tax_query) > 1) {
            $filter_tax_query['relation'] = 'AND';
        }
        $args['tax_query'] = $filter_tax_query;
    }

    // Add filter meta queries only if there's no search term (search uses custom WHERE clause)
    if (empty($search)) {
        if (!empty($status)) {
            $meta_query[] = array(
                'key' => 'rfp_status',
                'value' => $status,
                'compare' => '='
            );
        }

        if (!empty($organization)) {
            $meta_query[] = array(
                'key' => 'rfp_organization',
                'value' => $organization,
                'compare' => '='
            );
        }

        // Apply meta queries if any exist
        if (!empty($meta_query)) {
            if (count($meta_query) > 1) {
                $meta_query['relation'] = 'AND';
            }
            $args['meta_query'] = $meta_query;
        }
    }

    // Execute query
    $rfps_query = new WP_Query($args);

    // Remove the search filter after our query
    if (!empty($search)) {
        remove_all_filters('posts_search');
    }

    $rfps = array();

    if ($rfps_query->have_posts()) {
        $controller = new WP_REST_Posts_Controller('rfp');

        while ($rfps_query->have_posts()) {
            $rfps_query->the_post();
            $rfp = get_post();
            $rfp_data = $controller->prepare_item_for_response($rfp, $request);
            $rfps[] = $rfp_data->get_data();
        }
        wp_reset_postdata();
    }

    // Get total count for pagination
    $total = $rfps_query->found_posts;

    // Debug logging
    error_log("RFP search debug: requested per_page = $per_page, found_posts = $total, returned RFPs = " . count($rfps));
    error_log("RFP search args: " . print_r($args, true));

    $response = rest_ensure_response(array(
        'rfps' => $rfps,
        'total' => $total,
        'pages' => $per_page > 0 ? ceil($total / $per_page) : 1,
        'page' => $page,
        'per_page' => $per_page,
        'debug' => array(
            'requested_per_page' => $request->get_param('per_page'),
            'actual_per_page' => $per_page,
            'found_posts' => $total,
            'returned_count' => count($rfps)
        )
    ));

    $response->header('X-WP-Total', $total);
    $response->header('X-WP-TotalPages', ceil($total / $per_page));

    return $response;
}

// Add support for RFP post type in author archives
function add_rfp_to_author_archives($query) {
    if (!is_admin() && $query->is_author() && $query->is_main_query()) {
        $current_types = $query->get('post_type');
        if (empty($current_types)) {
            $current_types = array('post');
        } elseif (!is_array($current_types)) {
            $current_types = array($current_types);
        }
        if (!in_array('rfp', $current_types)) {
            $current_types[] = 'rfp';
        }
        $query->set('post_type', $current_types);
    }
}
add_action('pre_get_posts', 'add_rfp_to_author_archives');

// Flush rewrite rules on theme activation
function rfp_post_type_rewrite_flush() {
    register_rfp_post_type();
    register_rfp_category_taxonomy();
    register_rfp_state_taxonomy();
    register_rfp_country_taxonomy();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'rfp_post_type_rewrite_flush');