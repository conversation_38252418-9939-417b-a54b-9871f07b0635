<?php
/**
 * Legacy Messages API Endpoints
 * Handles read-only access to old Ultimate Member messaging system
 */

add_action('rest_api_init', function() {
    // Get legacy conversations for current user
    register_rest_route('tourismiq/v1', '/legacy-messages/conversations', array(
        'methods' => 'GET',
        'callback' => 'get_legacy_conversations',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));

    // Get messages for a specific conversation
    register_rest_route('tourismiq/v1', '/legacy-messages/conversation/(?P<id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'get_legacy_conversation_messages',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
});

function get_legacy_conversations($request) {
    global $wpdb;
    $current_user_id = get_current_user_id();
    
    if (!$current_user_id) {
        return new WP_Error('not_authenticated', 'User not authenticated', array('status' => 401));
    }
    

    // Get all conversations where user is either user_a or user_b (simplified query)
    $conversations = $wpdb->get_results($wpdb->prepare("
        SELECT 
            c.conversation_id,
            c.user_a,
            c.user_b,
            c.last_updated,
            CASE 
                WHEN c.user_a = %d THEN c.user_b 
                ELSE c.user_a 
            END as other_user_id,
            u.display_name as other_user_name,
            (SELECT COUNT(*) FROM {$wpdb->prefix}um_messages m 
             WHERE m.conversation_id = c.conversation_id 
             AND m.recipient = %d 
             AND m.status = 0) as unread_count,
            (SELECT content FROM {$wpdb->prefix}um_messages m2 
             WHERE m2.conversation_id = c.conversation_id 
             ORDER BY m2.time DESC LIMIT 1) as last_message,
            (SELECT time FROM {$wpdb->prefix}um_messages m3 
             WHERE m3.conversation_id = c.conversation_id 
             ORDER BY m3.time DESC LIMIT 1) as last_message_time
        FROM {$wpdb->prefix}um_conversations c
        LEFT JOIN {$wpdb->users} u ON u.ID = CASE 
            WHEN c.user_a = %d THEN c.user_b 
            ELSE c.user_a 
        END
        WHERE c.user_a = %d OR c.user_b = %d
        ORDER BY c.last_updated DESC
    ", $current_user_id, $current_user_id, $current_user_id, $current_user_id, $current_user_id));

    // Process conversations
    $processed_conversations = array();
    foreach ($conversations as $conversation) {
        // Clean up the message content (remove escaped quotes)
        $last_message = $conversation->last_message;
        if ($last_message) {
            $last_message = stripslashes($last_message);
            // Truncate long messages
            if (strlen($last_message) > 100) {
                $last_message = substr($last_message, 0, 100) . '...';
            }
        }

        $processed_conversations[] = array(
            'conversation_id' => $conversation->conversation_id,
            'other_user' => array(
                'id' => $conversation->other_user_id,
                'name' => $conversation->other_user_name ?: 'Unknown User',
                'avatar' => '' // Skip avatar for now
            ),
            'last_message' => $last_message,
            'last_message_time' => $conversation->last_message_time,
            'unread_count' => intval($conversation->unread_count)
        );
    }

    return rest_ensure_response($processed_conversations);
}

function get_legacy_conversation_messages($request) {
    global $wpdb;
    $current_user_id = get_current_user_id();
    $conversation_id = intval($request->get_param('id'));
    
    if (!$current_user_id) {
        return new WP_Error('not_authenticated', 'User not authenticated', array('status' => 401));
    }

    // Verify user is part of this conversation
    $conversation = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM {$wpdb->prefix}um_conversations 
        WHERE conversation_id = %d 
        AND (user_a = %d OR user_b = %d)
    ", $conversation_id, $current_user_id, $current_user_id));

    if (!$conversation) {
        return new WP_Error('unauthorized', 'You do not have access to this conversation', array('status' => 403));
    }

    // Get the other user
    $other_user_id = ($conversation->user_a == $current_user_id) ? $conversation->user_b : $conversation->user_a;
    $other_user = get_userdata($other_user_id);

    // Get messages (simplified query)
    $messages = $wpdb->get_results($wpdb->prepare("
        SELECT 
            m.message_id,
            m.content,
            m.time,
            m.author,
            m.recipient,
            m.status,
            u.display_name as author_name
        FROM {$wpdb->prefix}um_messages m
        LEFT JOIN {$wpdb->users} u ON u.ID = m.author
        WHERE m.conversation_id = %d
        ORDER BY m.time ASC
    ", $conversation_id));

    // Process messages
    $processed_messages = array();
    foreach ($messages as $message) {
        // Clean up the message content
        $content = stripslashes($message->content);

        $processed_messages[] = array(
            'id' => $message->message_id,
            'content' => $content,
            'time' => $message->time,
            'is_from_me' => ($message->author == $current_user_id),
            'author' => array(
                'id' => $message->author,
                'name' => $message->author_name ?: 'Unknown User',
                'avatar' => '' // Skip avatar for now
            ),
            'status' => $message->status
        );
    }

    return rest_ensure_response(array(
        'conversation_id' => $conversation_id,
        'other_user' => array(
            'id' => $other_user_id,
            'name' => $other_user ? $other_user->display_name : 'Unknown User',
            'avatar' => '' // Skip avatar for now
        ),
        'messages' => $processed_messages
    ));
}