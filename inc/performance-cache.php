<?php
/**
 * Performance and Caching Utilities for TourismIQ
 * Optimizes database queries and adds caching layers
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Performance {
    private static $instance = null;
    private static $cache = [];

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('init', [$this, 'init_performance_optimizations']);
        add_action('wp_footer', [$this, 'cleanup_cache']);
    }

    /**
     * Initialize performance optimizations
     */
    public function init_performance_optimizations() {
        // Add object caching for expensive queries
        add_filter('pre_get_users', [$this, 'optimize_user_queries']);
        
        // Cache ACF field calls
        add_filter('acf/load_value', [$this, 'cache_acf_values'], 10, 3);
        
        // Optimize post queries
        add_action('pre_get_posts', [$this, 'optimize_post_queries']);
    }

    /**
     * Cache expensive user data
     */
    public static function cache_user_data($user_id, $data, $expiration = 300) {
        $cache_key = "user_data_{$user_id}";
        wp_cache_set($cache_key, $data, 'tourismiq_users', $expiration);
        self::$cache[$cache_key] = $data;
    }

    /**
     * Get cached user data
     */
    public static function get_cached_user_data($user_id) {
        $cache_key = "user_data_{$user_id}";
        
        // Check in-memory cache first
        if (isset(self::$cache[$cache_key])) {
            return self::$cache[$cache_key];
        }
        
        // Check WordPress object cache
        $cached = wp_cache_get($cache_key, 'tourismiq_users');
        if ($cached !== false) {
            self::$cache[$cache_key] = $cached;
            return $cached;
        }
        
        return false;
    }

    /**
     * Cache member directory data
     */
    public static function cache_members_data($data, $page = 1, $per_page = 20) {
        $cache_key = "members_data_{$page}_{$per_page}";
        wp_cache_set($cache_key, $data, 'tourismiq_members', 300); // 5 minutes
        self::$cache[$cache_key] = $data;
    }

    /**
     * Get cached members data
     */
    public static function get_cached_members_data($page = 1, $per_page = 20) {
        $cache_key = "members_data_{$page}_{$per_page}";
        
        if (isset(self::$cache[$cache_key])) {
            return self::$cache[$cache_key];
        }
        
        $cached = wp_cache_get($cache_key, 'tourismiq_members');
        if ($cached !== false) {
            self::$cache[$cache_key] = $cached;
            return $cached;
        }
        
        return false;
    }

    /**
     * Cache IQ score rankings
     */
    public static function cache_leaderboard($data, $limit = 20) {
        $cache_key = "leaderboard_{$limit}";
        wp_cache_set($cache_key, $data, 'tourismiq_scores', 600); // 10 minutes
        self::$cache[$cache_key] = $data;
    }

    /**
     * Get cached leaderboard
     */
    public static function get_cached_leaderboard($limit = 20) {
        $cache_key = "leaderboard_{$limit}";
        
        if (isset(self::$cache[$cache_key])) {
            return self::$cache[$cache_key];
        }
        
        $cached = wp_cache_get($cache_key, 'tourismiq_scores');
        if ($cached !== false) {
            self::$cache[$cache_key] = $cached;
            return $cached;
        }
        
        return false;
    }

    /**
     * Optimize user queries
     */
    public function optimize_user_queries($query) {
        // Add meta query optimization
        if (isset($query->query_vars['meta_query'])) {
            // Ensure proper indexing hints
            add_filter('users_pre_query', [$this, 'add_user_query_indexes'], 10, 2);
        }
        
        return $query;
    }

    /**
     * Add database indexes for user queries
     */
    public function add_user_query_indexes($results, $query) {
        global $wpdb;
        
        // Check if we need to add indexes for better performance
        $this->ensure_user_meta_indexes();
        
        return $results;
    }

    /**
     * Ensure proper database indexes exist
     */
    private function ensure_user_meta_indexes() {
        global $wpdb;
        
        // Check if custom indexes exist
        $indexes = $wpdb->get_results("SHOW INDEX FROM {$wpdb->usermeta}");
        $existing_indexes = array_column($indexes, 'Key_name');
        
        // Add index for IQ score queries if not exists
        if (!in_array('meta_key_value_idx', $existing_indexes)) {
            $wpdb->query("ALTER TABLE {$wpdb->usermeta} ADD INDEX meta_key_value_idx (meta_key, meta_value(20))");
        }
    }

    /**
     * Cache ACF field values
     */
    public function cache_acf_values($value, $post_id, $field) {
        if ($value !== null) {
            $cache_key = "acf_{$field['name']}_{$post_id}";
            wp_cache_set($cache_key, $value, 'tourismiq_acf', 300);
        }
        
        return $value;
    }

    /**
     * Optimize post queries
     */
    public function optimize_post_queries($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Limit posts per page for performance
            if (!$query->get('posts_per_page')) {
                $query->set('posts_per_page', 20);
            }
            
            // Add caching for expensive meta queries
            if ($query->get('meta_query')) {
                add_filter('posts_pre_query', [$this, 'cache_post_queries'], 10, 2);
            }
        }
    }

    /**
     * Cache post query results
     */
    public function cache_post_queries($posts, $query) {
        if ($posts !== null) {
            return $posts;
        }
        
        $cache_key = 'posts_' . md5(serialize($query->query_vars));
        $cached_posts = wp_cache_get($cache_key, 'tourismiq_posts');
        
        if ($cached_posts !== false) {
            return $cached_posts;
        }
        
        return $posts;
    }

    /**
     * Clear cache for specific user
     */
    public static function clear_user_cache($user_id) {
        $cache_keys = [
            "user_data_{$user_id}",
            "members_data_*", // Clear all member pages
            "leaderboard_*"   // Clear all leaderboard caches
        ];
        
        foreach ($cache_keys as $pattern) {
            if (strpos($pattern, '*') !== false) {
                // Clear pattern-based cache
                wp_cache_flush_group(str_replace('_*', '', $pattern));
            } else {
                wp_cache_delete($pattern, 'tourismiq_users');
                unset(self::$cache[$pattern]);
            }
        }
    }

    /**
     * Clear all TourismIQ caches
     */
    public static function clear_all_cache() {
        wp_cache_flush_group('tourismiq_users');
        wp_cache_flush_group('tourismiq_members');
        wp_cache_flush_group('tourismiq_scores');
        wp_cache_flush_group('tourismiq_posts');
        wp_cache_flush_group('tourismiq_acf');
        self::$cache = [];
    }

    /**
     * Cleanup cache on page load end
     */
    public function cleanup_cache() {
        // Clear in-memory cache to prevent memory leaks
        if (count(self::$cache) > 100) {
            self::$cache = array_slice(self::$cache, -50, 50, true);
        }
    }

    /**
     * Get database query statistics
     */
    public static function get_query_stats() {
        global $wpdb;
        
        return [
            'num_queries' => $wpdb->num_queries,
            'queries' => defined('WP_DEBUG') && WP_DEBUG ? $wpdb->queries : [],
            'cache_hits' => wp_cache_get_stats(),
        ];
    }
}

// Initialize performance optimizations
TourismIQ_Performance::get_instance();

/**
 * Helper functions for easy cache access
 */
function tourismiq_cache_user_data($user_id, $data, $expiration = 300) {
    return TourismIQ_Performance::cache_user_data($user_id, $data, $expiration);
}

function tourismiq_get_cached_user_data($user_id) {
    return TourismIQ_Performance::get_cached_user_data($user_id);
}

function tourismiq_cache_members_data($data, $page = 1, $per_page = 20) {
    return TourismIQ_Performance::cache_members_data($data, $page, $per_page);
}

function tourismiq_get_cached_members_data($page = 1, $per_page = 20) {
    return TourismIQ_Performance::get_cached_members_data($page, $per_page);
}

function tourismiq_clear_user_cache($user_id) {
    return TourismIQ_Performance::clear_user_cache($user_id);
}

function tourismiq_clear_all_cache() {
    return TourismIQ_Performance::clear_all_cache();
} 