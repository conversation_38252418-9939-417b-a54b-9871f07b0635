<?php
/**
 * Forum Custom Post Type and Related Functionality
 */

// Register Forum Question Custom Post Type
function register_forum_question_post_type() {
    $labels = array(
        'name'                  => _x('Forum Questions', 'Post type general name', 'tourismiq'),
        'singular_name'         => _x('Forum Question', 'Post type singular name', 'tourismiq'),
        'menu_name'             => _x('Forum', 'Admin Menu text', 'tourismiq'),
        'name_admin_bar'        => _x('Forum Question', 'Add New on Toolbar', 'tourismiq'),
        'add_new'               => __('Add New', 'tourismiq'),
        'add_new_item'          => __('Add New Question', 'tourismiq'),
        'new_item'              => __('New Question', 'tourismiq'),
        'edit_item'             => __('Edit Question', 'tourismiq'),
        'view_item'             => __('View Question', 'tourismiq'),
        'all_items'             => __('All Questions', 'tourismiq'),
        'search_items'          => __('Search Questions', 'tourismiq'),
        'parent_item_colon'     => __('Parent Questions:', 'tourismiq'),
        'not_found'             => __('No questions found.', 'tourismiq'),
        'not_found_in_trash'    => __('No questions found in Trash.', 'tourismiq'),
        'featured_image'        => _x('Question Image', 'Overrides the "Featured Image" phrase', 'tourismiq'),
        'set_featured_image'    => _x('Set question image', 'Overrides the "Set featured image" phrase', 'tourismiq'),
        'remove_featured_image' => _x('Remove question image', 'Overrides the "Remove featured image" phrase', 'tourismiq'),
        'use_featured_image'    => _x('Use as question image', 'Overrides the "Use as featured image" phrase', 'tourismiq'),
        'archives'              => _x('Forum archives', 'The post type archive label', 'tourismiq'),
        'insert_into_item'      => _x('Insert into question', 'Overrides the "Insert into post" phrase', 'tourismiq'),
        'uploaded_to_this_item' => _x('Uploaded to this question', 'Overrides the "Uploaded to this post" phrase', 'tourismiq'),
        'filter_items_list'     => _x('Filter questions list', 'Screen reader text for the filter links', 'tourismiq'),
        'items_list_navigation' => _x('Questions list navigation', 'Screen reader text for the pagination', 'tourismiq'),
        'items_list'            => _x('Questions list', 'Screen reader text for the items list', 'tourismiq'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'forum'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 25,
        'menu_icon'          => 'dashicons-format-chat',
        'supports'           => array('title', 'editor', 'author', 'comments', 'custom-fields'),
        'show_in_rest'       => true,
        'rest_base'          => 'forum-questions',
        'rest_controller_class' => 'WP_REST_Posts_Controller',
    );

    register_post_type('forum_question', $args);
}
add_action('init', 'register_forum_question_post_type');

// Add custom REST API fields for forum questions
function add_forum_rest_fields() {
    // Add forum meta fields to REST API
    register_rest_field('forum_question', 'forum_meta', array(
        'get_callback' => 'get_forum_meta_for_api',
        'update_callback' => 'update_forum_meta_from_api',
        'schema' => array(
            'description' => 'Forum question metadata',
            'type' => 'object',
            'properties' => array(
                'is_resolved' => array('type' => 'boolean'),
                'resolved_at' => array('type' => 'string'),
                'resolved_by' => array('type' => 'integer'),
                'view_count' => array('type' => 'integer'),
                'last_activity' => array('type' => 'string'),
            )
        )
    ));

    // Add total comments count
    register_rest_field('forum_question', 'total_comments', array(
        'get_callback' => function($object) {
            return get_comments_number($object['id']);
        },
        'schema' => array(
            'description' => 'Total number of comments',
            'type' => 'integer',
        )
    ));

    // Add forum question slug for easy frontend routing
    register_rest_field('forum_question', 'slug', array(
        'get_callback' => function($object) {
            return get_post_field('post_name', $object['id']);
        },
        'schema' => array(
            'description' => 'Forum question slug',
            'type' => 'string',
        )
    ));

    // Add author information
    register_rest_field('forum_question', 'author_info', array(
        'get_callback' => 'get_forum_author_info',
        'schema' => array(
            'description' => 'Author information',
            'type' => 'object',
        )
    ));
}
add_action('rest_api_init', 'add_forum_rest_fields');

// Get forum meta for REST API
function get_forum_meta_for_api($object) {
    $question_id = $object['id'];
    
    return array(
        'is_resolved' => (bool) get_post_meta($question_id, 'forum_is_resolved', true),
        'resolved_at' => get_post_meta($question_id, 'forum_resolved_at', true),
        'resolved_by' => (int) get_post_meta($question_id, 'forum_resolved_by', true),
        'view_count' => (int) get_post_meta($question_id, 'forum_view_count', true),
        'last_activity' => get_post_meta($question_id, 'forum_last_activity', true) ?: get_the_modified_time('c', $question_id),
    );
}

// Update forum meta from REST API
function update_forum_meta_from_api($value, $object, $field_name) {
    $question_id = $object->ID;
    
    if (isset($value['is_resolved'])) {
        update_post_meta($question_id, 'forum_is_resolved', (bool) $value['is_resolved']);
        
        if ($value['is_resolved']) {
            update_post_meta($question_id, 'forum_resolved_at', current_time('mysql'));
            update_post_meta($question_id, 'forum_resolved_by', get_current_user_id());
        } else {
            delete_post_meta($question_id, 'forum_resolved_at');
            delete_post_meta($question_id, 'forum_resolved_by');
        }
    }
    
    if (isset($value['view_count'])) {
        update_post_meta($question_id, 'forum_view_count', (int) $value['view_count']);
    }
    
    return true;
}

// Get author information for forum questions
function get_forum_author_info($object) {
    $author_id = $object['author'];
    $author = get_userdata($author_id);
    
    if (!$author) {
        return null;
    }
    
    // Get ACF profile picture
    $profile_picture = get_field('profile_picture', 'user_' . $author_id);
    $avatar_url = '';
    
    if ($profile_picture && is_array($profile_picture)) {
        // If profile_picture is an ACF image array
        $avatar_url = $profile_picture['url'] ?? '';
    } elseif ($profile_picture && is_string($profile_picture)) {
        // If profile_picture is just a URL string
        $avatar_url = $profile_picture;
    } elseif ($profile_picture && is_numeric($profile_picture)) {
        // If profile_picture is an attachment ID
        $avatar_url = wp_get_attachment_url($profile_picture);
    }
    
    // Let frontend handle the fallback to placeholder if no profile picture
    if (empty($avatar_url)) {
        $avatar_url = null; // Let frontend handle the fallback to placeholder
    }
    
    // Get user roles as array values (not associative array)
    $roles = array_values($author->roles);
    
    return array(
        'id' => $author_id,
        'name' => $author->display_name,
        'username' => $author->user_login,
        'profile_picture' => $avatar_url,
        'avatar_urls' => array(
            '24' => $avatar_url,
            '48' => $avatar_url,
            '96' => $avatar_url,
        ),
        'url' => get_author_posts_url($author_id),
        'roles' => $roles,
    );
}

// Register forum REST endpoints
function register_forum_rest_endpoints() {
    // Get forum questions with custom filtering
    register_rest_route('tourismiq/v1', '/forum', array(
        'methods' => 'GET',
        'callback' => 'get_forum_questions',
        'permission_callback' => '__return_true', // Allow public access to read forum questions
        'args' => array(
            'page' => array(
                'default' => 1,
                'sanitize_callback' => 'absint',
            ),
            'per_page' => array(
                'default' => 10,
                'sanitize_callback' => 'absint',
            ),
            'sort' => array(
                'default' => 'new',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'resolved' => array(
                'default' => null,
                'sanitize_callback' => function($param) {
                    if ($param === 'true') return true;
                    if ($param === 'false') return false;
                    return null;
                }
            ),
            'search' => array(
                'default' => '',
                'sanitize_callback' => 'sanitize_text_field',
            ),
        ),
    ));

    // Create forum question
    register_rest_route('tourismiq/v1', '/forum/create', array(
        'methods' => 'POST',
        'callback' => 'create_forum_question',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
        'args' => array(
            'title' => array(
                'required' => true,
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'content' => array(
                'required' => true,
                'sanitize_callback' => 'wp_kses_post',
            ),
        ),
    ));

    // Get single forum question
    register_rest_route('tourismiq/v1', '/forum/(?P<id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'get_forum_question_by_id',
        'permission_callback' => '__return_true', // Allow public access to read individual forum questions
        'args' => array(
            'id' => array(
                'validate_callback' => function($param, $request, $key) {
                    return is_numeric($param);
                }
            ),
        ),
    ));

    // Mark question as resolved
    register_rest_route('tourismiq/v1', '/forum/(?P<id>\d+)/resolve', array(
        'methods' => 'POST',
        'callback' => 'resolve_forum_question',
        'permission_callback' => 'forum_question_edit_permission_check',
        'args' => array(
            'id' => array(
                'validate_callback' => function($param, $request, $key) {
                    return is_numeric($param);
                }
            ),
            'resolved' => array(
                'default' => true,
                'sanitize_callback' => function($param) {
                    return (bool) $param;
                }
            ),
        ),
    ));

    // Get forum question comments with custom author info
    register_rest_route('tourismiq/v1', '/forum/(?P<id>\d+)/comments', array(
        'methods' => 'GET',
        'callback' => 'get_forum_question_comments',
        'permission_callback' => '__return_true', // Allow public access to read comments
        'args' => array(
            'id' => array(
                'validate_callback' => function($param, $request, $key) {
                    return is_numeric($param);
                }
            ),
        ),
    ));

    // Get forum questions by user ID
    register_rest_route('tourismiq/v1', '/forum/user/(?P<user_id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'get_user_forum_questions',
        'permission_callback' => '__return_true', // Allow public access to read user questions
        'args' => array(
            'user_id' => array(
                'validate_callback' => function($param, $request, $key) {
                    return is_numeric($param);
                }
            ),
            'page' => array(
                'default' => 1,
                'sanitize_callback' => 'absint',
            ),
            'per_page' => array(
                'default' => 10,
                'sanitize_callback' => 'absint',
            ),
        ),
    ));
}
add_action('rest_api_init', 'register_forum_rest_endpoints');

// Get forum questions with filtering and sorting
function get_forum_questions($request) {
    $page = $request->get_param('page');
    $per_page = min($request->get_param('per_page'), 50); // Max 50 per page
    $sort = $request->get_param('sort');
    $resolved = $request->get_param('resolved');
    $search = $request->get_param('search');

    $args = array(
        'post_type' => 'forum_question',
        'post_status' => 'publish',
        'posts_per_page' => $per_page,
        'paged' => $page,
    );

    // Handle search
    if (!empty($search)) {
        $args['s'] = $search;
    }

    // Handle sorting
    switch ($sort) {
        case 'trending':
            $args['orderby'] = 'comment_count';
            $args['order'] = 'DESC';
            break;
        case 'new':
        default:
            $args['orderby'] = 'date';
            $args['order'] = 'DESC';
            break;
    }

    // Filter by resolved status
    if ($resolved !== null) {
        $args['meta_query'] = array(
            array(
                'key' => 'forum_is_resolved',
                'value' => $resolved ? '1' : '0',
                'compare' => '='
            )
        );
    }

    $query = new WP_Query($args);
    $questions = array();

    foreach ($query->posts as $post) {
        $question_data = array(
            'id' => $post->ID,
            'title' => array('rendered' => $post->post_title),
            'content' => array('rendered' => apply_filters('the_content', $post->post_content)),
            'date' => $post->post_date,
            'modified' => $post->post_modified,
            'author' => $post->post_author,
            'slug' => $post->post_name,
            'status' => $post->post_status,
            'total_comments' => get_comments_number($post->ID),
            'forum_meta' => get_forum_meta_for_api(array('id' => $post->ID)),
            'author_info' => get_forum_author_info(array('author' => $post->post_author)),
            '_embedded' => array(
                'author' => array(get_forum_author_info(array('author' => $post->post_author)))
            )
        );
        
        $questions[] = $question_data;
    }

    $response = array(
        'questions' => $questions,
        'pagination' => array(
            'page' => $page,
            'per_page' => $per_page,
            'total' => $query->found_posts,
            'total_pages' => $query->max_num_pages,
        )
    );

    return new WP_REST_Response($response, 200);
}

// Create a new forum question
function create_forum_question($request) {
    $title = $request->get_param('title');
    $content = $request->get_param('content');
    $current_user_id = get_current_user_id();

    if (empty($title) || empty($content)) {
        return new WP_Error('missing_data', 'Title and content are required', array('status' => 400));
    }

    $post_data = array(
        'post_title' => $title,
        'post_content' => $content,
        'post_status' => 'publish',
        'post_type' => 'forum_question',
        'post_author' => $current_user_id,
        'meta_input' => array(
            'forum_is_resolved' => false,
            'forum_view_count' => 0,
            'forum_last_activity' => current_time('mysql'),
        )
    );

    $post_id = wp_insert_post($post_data);

    if (is_wp_error($post_id)) {
        return new WP_Error('creation_failed', 'Failed to create forum question', array('status' => 500));
    }

    // Award IQ points for creating a forum question (3 points)
    if (function_exists('award_iq_points')) {
        award_iq_points($current_user_id, 3, 'forum_question', $post_id);
    }

    // Get the created post data
    $post = get_post($post_id);
    $question_data = array(
        'id' => $post->ID,
        'title' => $post->post_title,
        'content' => $post->post_content,
        'date' => $post->post_date,
        'author' => $post->post_author,
        'slug' => $post->post_name,
        'forum_meta' => get_forum_meta_for_api(array('id' => $post->ID)),
        'author_info' => get_forum_author_info(array('author' => $post->post_author)),
    );

    return new WP_REST_Response(array(
        'success' => true,
        'question' => $question_data
    ), 201);
}

// Get single forum question by ID
function get_forum_question_by_id($request) {
    $question_id = $request->get_param('id');
    
    $post = get_post($question_id);
    
    if (!$post || $post->post_type !== 'forum_question') {
        return new WP_Error('not_found', 'Forum question not found', array('status' => 404));
    }

    // Increment view count
    $view_count = (int) get_post_meta($question_id, 'forum_view_count', true);
    update_post_meta($question_id, 'forum_view_count', $view_count + 1);

    // Get comments with proper formatting (same logic as get_forum_question_comments)
    $comments = get_comments(array(
        'post_id' => $question_id,
        'status' => 'approve',
        'order' => 'ASC',
    ));

    $formatted_comments = array();
    
    foreach ($comments as $comment) {
        $author_info = null;
        
        if ($comment->user_id > 0) {
            // Registered user - get custom author info
            $author_info = get_forum_author_info(array('author' => $comment->user_id));
        } else {
            // Guest comment - use basic info
            $author_info = array(
                'id' => '0',
                'name' => $comment->comment_author,
                'username' => '',
                'profile_picture' => '/images/avatar-placeholder.svg',
                'avatar_urls' => array(
                    '24' => '/images/avatar-placeholder.svg',
                    '48' => '/images/avatar-placeholder.svg',
                    '96' => '/images/avatar-placeholder.svg',
                ),
                'url' => '',
                'roles' => array(), // Empty roles array for guests
            );
        }
        
        $formatted_comments[] = array(
            'id' => $comment->comment_ID,
            'content' => $comment->comment_content,
            'date' => $comment->comment_date,
            'author_id' => $comment->user_id,
            'author_info' => $author_info,
            'parent' => $comment->comment_parent,
        );
    }

    $question_data = array(
        'id' => $post->ID,
        'title' => $post->post_title,
        'content' => apply_filters('the_content', $post->post_content),
        'date' => $post->post_date,
        'modified' => $post->post_modified,
        'author' => $post->post_author,
        'slug' => $post->post_name,
        'status' => $post->post_status,
        'total_comments' => count($formatted_comments),
        'forum_meta' => get_forum_meta_for_api(array('id' => $post->ID)),
        'author_info' => get_forum_author_info(array('author' => $post->post_author)),
        'comments' => $formatted_comments,
    );

    return new WP_REST_Response($question_data, 200);
}

// Resolve/unresolve forum question
function resolve_forum_question($request) {
    $question_id = $request->get_param('id');
    $resolved = $request->get_param('resolved');
    
    $post = get_post($question_id);
    
    if (!$post || $post->post_type !== 'forum_question') {
        return new WP_Error('not_found', 'Forum question not found', array('status' => 404));
    }

    update_post_meta($question_id, 'forum_is_resolved', $resolved);
    
    if ($resolved) {
        update_post_meta($question_id, 'forum_resolved_at', current_time('mysql'));
        update_post_meta($question_id, 'forum_resolved_by', get_current_user_id());
    } else {
        delete_post_meta($question_id, 'forum_resolved_at');
        delete_post_meta($question_id, 'forum_resolved_by');
    }

    return new WP_REST_Response(array(
        'success' => true,
        'resolved' => $resolved,
        'forum_meta' => get_forum_meta_for_api(array('id' => $question_id))
    ), 200);
}

// Get forum question comments with custom author info
function get_forum_question_comments($request) {
    $question_id = $request->get_param('id');
    
    $post = get_post($question_id);
    
    if (!$post || $post->post_type !== 'forum_question') {
        return new WP_Error('not_found', 'Forum question not found', array('status' => 404));
    }

    // Get comments
    $comments = get_comments(array(
        'post_id' => $question_id,
        'status' => 'approve',
        'order' => 'ASC',
    ));

    $formatted_comments = array();
    
    foreach ($comments as $comment) {
        $author_info = null;
        
        if ($comment->user_id > 0) {
            // Registered user - get custom author info
            $author_info = get_forum_author_info(array('author' => $comment->user_id));
        } else {
            // Guest comment - use basic info
            $author_info = array(
                'id' => '0',
                'name' => $comment->comment_author,
                'username' => '',
                'profile_picture' => '/images/avatar-placeholder.svg',
                'avatar_urls' => array(
                    '24' => '/images/avatar-placeholder.svg',
                    '48' => '/images/avatar-placeholder.svg',
                    '96' => '/images/avatar-placeholder.svg',
                ),
                'url' => '',
                'roles' => array(), // Empty roles array for guests
            );
        }
        
        $formatted_comment = array(
            'id' => $comment->comment_ID,
            'content' => $comment->comment_content,
            'date' => $comment->comment_date,
            'author_id' => $comment->user_id,
            'author_info' => $author_info,
            'parent' => $comment->comment_parent,
        );
        
        $formatted_comments[] = $formatted_comment;
    }

    $response = array(
        'success' => true,
        'comments' => $formatted_comments,
    );

    return new WP_REST_Response($response, 200);
}

// Permission check for editing forum questions
function forum_question_edit_permission_check($request) {
    if (!is_user_logged_in()) {
        return false;
    }

    $question_id = $request->get_param('id');
    $post = get_post($question_id);
    
    if (!$post || $post->post_type !== 'forum_question') {
        return false;
    }

    $current_user_id = get_current_user_id();
    
    // Allow question author or users with edit_others_posts capability
    return ($post->post_author == $current_user_id) || current_user_can('edit_others_posts');
}

// Update last activity when comments are added
function update_forum_last_activity($comment_id, $comment_approved, $commentdata) {
    if ($comment_approved === 1) {
        $post_id = $commentdata['comment_post_ID'];
        $post = get_post($post_id);
        
        if ($post && $post->post_type === 'forum_question') {
            update_post_meta($post_id, 'forum_last_activity', current_time('mysql'));
        }
    }
}
add_action('comment_post', 'update_forum_last_activity', 10, 3);

// Add forum capabilities to roles
function add_forum_capabilities() {
    $roles = array('founder', 'member', 'administrator', 'editor');
    
    foreach ($roles as $role_name) {
        $role = get_role($role_name);
        if ($role) {
            $role->add_cap('read_forum_question');
            $role->add_cap('edit_forum_question');
            $role->add_cap('edit_forum_questions');
            $role->add_cap('publish_forum_questions');
            $role->add_cap('delete_forum_question');
        }
    }
}
add_action('init', 'add_forum_capabilities');

// Get forum questions by user ID
function get_user_forum_questions($request) {
    $user_id = $request->get_param('user_id');
    $page = $request->get_param('page');
    $per_page = min($request->get_param('per_page'), 50); // Max 50 per page

    // Verify user exists
    $user = get_userdata($user_id);
    if (!$user) {
        return new WP_Error('user_not_found', 'User not found', array('status' => 404));
    }

    $args = array(
        'post_type' => 'forum_question',
        'post_status' => 'publish',
        'author' => $user_id,
        'posts_per_page' => $per_page,
        'paged' => $page,
        'orderby' => 'date',
        'order' => 'DESC',
    );

    $query = new WP_Query($args);
    $questions = array();

    foreach ($query->posts as $post) {
        $question_data = array(
            'id' => $post->ID,
            'title' => array('rendered' => $post->post_title),
            'content' => array('rendered' => apply_filters('the_content', $post->post_content)),
            'date' => $post->post_date,
            'modified' => $post->post_modified,
            'author' => $post->post_author,
            'slug' => $post->post_name,
            'status' => $post->post_status,
            'total_comments' => get_comments_number($post->ID),
            'forum_meta' => get_forum_meta_for_api(array('id' => $post->ID)),
            'author_info' => get_forum_author_info(array('author' => $post->post_author)),
            '_embedded' => array(
                'author' => array(get_forum_author_info(array('author' => $post->post_author)))
            )
        );
        
        $questions[] = $question_data;
    }

    $response = array(
        'questions' => $questions,
        'pagination' => array(
            'page' => $page,
            'per_page' => $per_page,
            'total' => $query->found_posts,
            'total_pages' => $query->max_num_pages,
        )
    );

    return new WP_REST_Response($response, 200);
} 