<?php
/**
 * Mailchimp Settings Page
 * 
 * Adds a settings page for Mailchimp configuration in WordPress admin
 */

if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Mailchimp_Settings {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_settings_page'));
        add_action('admin_init', array($this, 'register_settings'));
    }
    
    /**
     * Add settings page to admin menu
     */
    public function add_settings_page() {
        add_options_page(
            'Mailchimp Settings',
            'Mailchimp',
            'manage_options',
            'tourismiq-mailchimp',
            array($this, 'render_settings_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting('tourismiq_mailchimp_settings', 'tourismiq_mailchimp_api_key');
        register_setting('tourismiq_mailchimp_settings', 'tourismiq_mailchimp_list_id');
        
        add_settings_section(
            'tourismiq_mailchimp_main',
            'Mailchimp API Configuration',
            array($this, 'section_callback'),
            'tourismiq-mailchimp'
        );
        
        add_settings_field(
            'tourismiq_mailchimp_api_key',
            'API Key',
            array($this, 'api_key_callback'),
            'tourismiq-mailchimp',
            'tourismiq_mailchimp_main'
        );
        
        add_settings_field(
            'tourismiq_mailchimp_list_id',
            'List/Audience ID',
            array($this, 'list_id_callback'),
            'tourismiq-mailchimp',
            'tourismiq_mailchimp_main'
        );
    }
    
    /**
     * Section callback
     */
    public function section_callback() {
        echo '<p>Enter your Mailchimp API credentials below. You can find these in your Mailchimp account.</p>';
    }
    
    /**
     * API Key field callback
     */
    public function api_key_callback() {
        $api_key = get_option('tourismiq_mailchimp_api_key');
        ?>
        <input type="text" 
               name="tourismiq_mailchimp_api_key" 
               value="<?php echo esc_attr($api_key); ?>" 
               class="regular-text" 
               placeholder="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx-us1" />
        <p class="description">
            Get your API key from Mailchimp: Account → Extras → API keys
        </p>
        <?php
    }
    
    /**
     * List ID field callback
     */
    public function list_id_callback() {
        $list_id = get_option('tourismiq_mailchimp_list_id');
        ?>
        <input type="text" 
               name="tourismiq_mailchimp_list_id" 
               value="<?php echo esc_attr($list_id); ?>" 
               class="regular-text" 
               placeholder="xxxxxxxxxx" />
        <p class="description">
            Find your List ID in Mailchimp: Audience → All contacts → Settings → Audience name and defaults
        </p>
        <?php
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Check if settings were updated
        if (isset($_GET['settings-updated'])) {
            add_settings_error('tourismiq_mailchimp_messages', 'tourismiq_mailchimp_message', 
                'Settings Saved. You can test the connection below.', 'updated');
        }
        
        settings_errors('tourismiq_mailchimp_messages');
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <form action="options.php" method="post">
                <?php
                settings_fields('tourismiq_mailchimp_settings');
                do_settings_sections('tourismiq-mailchimp');
                submit_button('Save Settings');
                ?>
            </form>
            
            <?php if (get_option('tourismiq_mailchimp_api_key') && get_option('tourismiq_mailchimp_list_id')): ?>
            <hr />
            <h2>Test Connection</h2>
            <p>Click the button below to test your Mailchimp connection.</p>
            <button type="button" class="button button-secondary" id="test-mailchimp-connection">
                Test Connection
            </button>
            <div id="test-results" style="margin-top: 20px;"></div>
            
            <script type="text/javascript">
            (function($) {
                $(document).ready(function() {
                    $('#test-mailchimp-connection').on('click', function() {
                        var $button = $(this);
                        var $results = $('#test-results');
                        
                        $button.prop('disabled', true).text('Testing...');
                        $results.html('');
                        
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'test_mailchimp_connection',
                                nonce: '<?php echo wp_create_nonce('test_mailchimp_connection'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $results.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                                } else {
                                    $results.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                                }
                            },
                            error: function() {
                                $results.html('<div class="notice notice-error"><p>An error occurred while testing the connection.</p></div>');
                            },
                            complete: function() {
                                $button.prop('disabled', false).text('Test Connection');
                            }
                        });
                    });
                });
            })(jQuery);
            </script>
            <?php endif; ?>
        </div>
        <?php
    }
}

// Initialize settings page
new TourismIQ_Mailchimp_Settings();

// AJAX handler for testing connection
add_action('wp_ajax_test_mailchimp_connection', 'tourismiq_test_mailchimp_connection');
function tourismiq_test_mailchimp_connection() {
    if (!current_user_can('manage_options')) {
        wp_die();
    }
    
    if (!wp_verify_nonce($_POST['nonce'], 'test_mailchimp_connection')) {
        wp_die();
    }
    
    $api_key = get_option('tourismiq_mailchimp_api_key');
    $list_id = get_option('tourismiq_mailchimp_list_id');
    
    if (!$api_key || !$list_id) {
        wp_send_json_error(array('message' => 'API credentials not configured.'));
    }
    
    // Extract server prefix
    $key_parts = explode('-', $api_key);
    $server_prefix = end($key_parts);
    
    // Test API connection by getting list info
    $api_url = "https://{$server_prefix}.api.mailchimp.com/3.0/lists/{$list_id}";
    
    $response = wp_remote_get($api_url, array(
        'headers' => array(
            'Authorization' => 'Basic ' . base64_encode('user:' . $api_key),
        ),
        'timeout' => 10,
    ));
    
    if (is_wp_error($response)) {
        wp_send_json_error(array('message' => 'Connection failed: ' . $response->get_error_message()));
    }
    
    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = json_decode(wp_remote_retrieve_body($response), true);
    
    if ($response_code === 200) {
        $list_name = isset($response_body['name']) ? $response_body['name'] : 'Unknown';
        $member_count = isset($response_body['stats']['member_count']) ? $response_body['stats']['member_count'] : 0;
        
        wp_send_json_success(array(
            'message' => sprintf(
                'Connection successful! Connected to list "%s" with %d members.',
                $list_name,
                $member_count
            )
        ));
    } else {
        $error_message = isset($response_body['detail']) ? $response_body['detail'] : 'Unknown error';
        wp_send_json_error(array('message' => 'Connection failed: ' . $error_message));
    }
}