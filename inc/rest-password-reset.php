<?php
/**
 * REST API endpoint for password reset
 * Handles password reset requests from the frontend
 */

// Register password reset endpoint
add_action('rest_api_init', function () {
    register_rest_route('tourismiq/v1', '/auth/reset-password', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_handle_password_reset',
        'permission_callback' => '__return_true', // Public endpoint
        'args' => array(
            'email' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return is_email($param);
                }
            ),
        ),
    ));
});

/**
 * Handle password reset request
 */
function tourismiq_handle_password_reset($request) {
    $email = sanitize_email($request->get_param('email'));
    
    // Check if user exists
    $user = get_user_by('email', $email);
    
    if (!$user) {
        // Don't reveal if email exists or not for security
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'If an account exists with this email address, you will receive a password reset link shortly.'
        ), 200);
    }
    
    // Trigger WordPress password reset
    $result = retrieve_password($user->user_login);
    
    if (is_wp_error($result)) {
        error_log('Password reset error for ' . $email . ': ' . $result->get_error_message());
        
        return new WP_Error(
            'reset_failed',
            'Unable to process password reset request. Please try again later.',
            array('status' => 500)
        );
    }
    
    return new WP_REST_Response(array(
        'success' => true,
        'message' => 'If an account exists with this email address, you will receive a password reset link shortly.'
    ), 200);
}

// Add CORS headers for password reset endpoint
add_filter('rest_pre_serve_request', function($served, $result, $request, $server) {
    $route = $request->get_route();
    
    if (strpos($route, '/tourismiq/v1/auth/reset-password') !== false) {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            exit;
        }
    }
    
    return $served;
}, 10, 4);