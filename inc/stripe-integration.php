<?php
/**
 * Stripe Integration for Vendor Subscriptions
 * Handles webhook events and subscription management
 */

class TourismIQ_Stripe_Integration {
    
    private $stripe_secret_key;
    private $stripe_webhook_secret;
    
    public function __construct() {
        $this->stripe_secret_key = defined('STRIPE_SECRET_KEY') ? STRIPE_SECRET_KEY : '';
        $this->stripe_webhook_secret = defined('STRIPE_WEBHOOK_SECRET') ? STRIPE_WEBHOOK_SECRET : '';
        
        add_action('rest_api_init', array($this, 'register_endpoints'));
        add_action('init', array($this, 'init_stripe'));
    }
    
    /**
     * Initialize Stripe (assuming Stripe PHP library is loaded)
     */
    public function init_stripe() {
        if (!empty($this->stripe_secret_key) && class_exists('\Stripe\Stripe')) {
            \Stripe\Stripe::setApiKey($this->stripe_secret_key);
        }
    }
    
    /**
     * Register all REST API endpoints
     */
    public function register_endpoints() {
        // Webhook endpoint
        register_rest_route('tourismiq/v1', '/stripe/webhook', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_webhook'),
            'permission_callback' => '__return_true', // Stripe webhook validation handles security
        ));
        
        // Subscription status endpoint
        register_rest_route('tourismiq/v1', '/vendors/(?P<vendor_id>\d+)/subscription-status', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_subscription_status'),
            'permission_callback' => array($this, 'check_vendor_permissions'),
            'args' => array(
                'vendor_id' => array(
                    'validate_callback' => function($param) {
                        return is_numeric($param);
                    }
                ),
            ),
        ));
        
        // Customer portal endpoint
        register_rest_route('tourismiq/v1', '/vendors/(?P<vendor_id>\d+)/customer-portal', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_customer_portal_session'),
            'permission_callback' => array($this, 'check_vendor_permissions'),
            'args' => array(
                'vendor_id' => array(
                    'validate_callback' => function($param) {
                        return is_numeric($param);
                    }
                ),
            ),
        ));
    }
    
    /**
     * Check if user has permission to manage vendor
     */
    public function check_vendor_permissions($request) {
        if (!is_user_logged_in()) {
            return false;
        }
        
        $vendor_id = $request->get_param('vendor_id');
        $vendor = get_post($vendor_id);
        
        if (!$vendor || $vendor->post_type !== 'vendor') {
            return false;
        }
        
        $current_user_id = get_current_user_id();
        
        // Check if user is the vendor author or assigned member
        if ($vendor->post_author == $current_user_id) {
            return true;
        }
        
        $assigned_members = get_post_meta($vendor_id, 'vendor_assigned_members', true) ?: array();
        if (in_array($current_user_id, $assigned_members)) {
            return true;
        }
        
        // Check if user is admin
        if (current_user_can('manage_options')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Get subscription status for a vendor
     */
    public function get_subscription_status($request) {
        $vendor_id = $request->get_param('vendor_id');
        $status = self::get_vendor_subscription_status($vendor_id);
        
        if ($status === false) {
            return new WP_Error('vendor_not_found', 'Vendor not found', array('status' => 404));
        }
        
        return rest_ensure_response($status);
    }
    
    /**
     * Create customer portal session
     */
    public function create_customer_portal_session($request) {
        $vendor_id = $request->get_param('vendor_id');
        $body = $request->get_json_params();
        $return_url = $body['return_url'] ?? home_url('/dashboard/vendor-settings');
        
        $portal_url = self::create_customer_portal_link($vendor_id, $return_url);
        
        if (!$portal_url) {
            return new WP_Error('portal_failed', 'Failed to create customer portal session', array('status' => 500));
        }
        
        return rest_ensure_response(array(
            'portal_url' => $portal_url
        ));
    }
    
    /**
     * Handle Stripe webhook events
     */
    public function handle_webhook($request) {
        $payload = $request->get_body();
        $sig_header = $request->get_header('stripe-signature');
        
        if (empty($this->stripe_webhook_secret)) {
            error_log('TourismIQ Stripe: Webhook secret not configured');
            return new WP_Error('webhook_error', 'Webhook secret not configured', array('status' => 400));
        }
        
        try {
            // Verify webhook signature
            if (class_exists('\Stripe\Webhook')) {
                $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $this->stripe_webhook_secret);
            } else {
                error_log('TourismIQ Stripe: Stripe PHP library not loaded');
                return new WP_Error('webhook_error', 'Stripe library not loaded', array('status' => 500));
            }
        } catch(\UnexpectedValueException $e) {
            error_log('TourismIQ Stripe: Invalid payload - ' . $e->getMessage());
            return new WP_Error('webhook_error', 'Invalid payload', array('status' => 400));
        } catch(\Stripe\Exception\SignatureVerificationException $e) {
            error_log('TourismIQ Stripe: Invalid signature - ' . $e->getMessage());
            return new WP_Error('webhook_error', 'Invalid signature', array('status' => 400));
        }
        
        // Handle the event
        switch ($event['type']) {
            case 'customer.subscription.created':
                $this->handle_subscription_created($event['data']['object']);
                break;
            case 'customer.subscription.updated':
                $this->handle_subscription_updated($event['data']['object']);
                break;
            case 'customer.subscription.deleted':
                $this->handle_subscription_deleted($event['data']['object']);
                break;
            case 'invoice.payment_succeeded':
                $this->handle_payment_succeeded($event['data']['object']);
                break;
            case 'invoice.payment_failed':
                $this->handle_payment_failed($event['data']['object']);
                break;
            default:
                error_log('TourismIQ Stripe: Unhandled event type - ' . $event['type']);
        }
        
        return rest_ensure_response(array('received' => true));
    }
    
    /**
     * Handle subscription created event
     */
    private function handle_subscription_created($subscription) {
        $customer_id = $subscription['customer'];
        $subscription_id = $subscription['id'];
        $status = $subscription['status'];
        
        // Get vendor ID from subscription metadata
        $vendor_id = isset($subscription['metadata']['vendor_id']) ? intval($subscription['metadata']['vendor_id']) : null;
        
        if (!$vendor_id) {
            error_log('TourismIQ Stripe: No vendor_id in subscription metadata');
            return;
        }
        
        // Get the vendor post
        $vendor = get_post($vendor_id);
        if (!$vendor || $vendor->post_type !== 'vendor') {
            error_log('TourismIQ Stripe: Invalid vendor ID - ' . $vendor_id);
            return;
        }
        
        // Get vendor author (the user who created the vendor)
        $user_id = $vendor->post_author;
        
        // Store Stripe data in user meta
        update_user_meta($user_id, 'stripe_customer_id', $customer_id);
        update_user_meta($user_id, 'stripe_subscription_id', $subscription_id);
        update_user_meta($user_id, 'stripe_subscription_status', $status);
        
        // Update vendor ACF fields
        if ($status === 'active') {
            update_field('vendor_is_paid', true, $vendor_id);
            update_field('vendor_subscription_status', 'active', $vendor_id);
            
            // Add user as team member if not already assigned
            $assigned_members = get_field('vendor_assigned_members', $vendor_id) ?: array();
            if (!in_array($user_id, $assigned_members)) {
                $assigned_members[] = $user_id;
                update_field('vendor_assigned_members', $assigned_members, $vendor_id);
                error_log('TourismIQ Stripe: Added user ' . $user_id . ' as team member to vendor ' . $vendor_id);
            }
            
            // Send vendor upgrade email notification
            if (function_exists('tourismiq_send_vendor_upgrade_email')) {
                tourismiq_send_vendor_upgrade_email($vendor_id, $user_id);
                error_log('TourismIQ Stripe: Sent upgrade email for vendor ' . $vendor_id . ' to user ' . $user_id);
            }
        }
        
        error_log('TourismIQ Stripe: Subscription created for vendor ' . $vendor_id . ' - Status: ' . $status);
    }
    
    /**
     * Handle subscription updated event
     */
    private function handle_subscription_updated($subscription) {
        $subscription_id = $subscription['id'];
        $status = $subscription['status'];
        
        // Find user by subscription ID
        $users = get_users(array(
            'meta_key' => 'stripe_subscription_id',
            'meta_value' => $subscription_id,
            'number' => 1
        ));
        
        if (empty($users)) {
            error_log('TourismIQ Stripe: No user found for subscription ' . $subscription_id);
            return;
        }
        
        $user = $users[0];
        $user_id = $user->ID;
        
        // Update subscription status
        update_user_meta($user_id, 'stripe_subscription_status', $status);
        
        // Get user's vendor(s)
        $vendors = get_posts(array(
            'post_type' => 'vendor',
            'author' => $user_id,
            'posts_per_page' => -1
        ));
        
        foreach ($vendors as $vendor) {
            // Check if this vendor has the subscription (from metadata)
            $vendor_subscription_id = get_post_meta($vendor->ID, '_stripe_subscription_id', true);
            if ($vendor_subscription_id === $subscription_id) {
                
                update_field('vendor_subscription_status', $status, $vendor->ID);
                
                // Update paid status based on subscription status
                if (in_array($status, ['active', 'trialing'])) {
                    update_field('vendor_is_paid', true, $vendor->ID);
                } else {
                    update_field('vendor_is_paid', false, $vendor->ID);
                }
                
                // Store subscription ID for future reference
                update_post_meta($vendor->ID, '_stripe_subscription_id', $subscription_id);
                
                error_log('TourismIQ Stripe: Subscription updated for vendor ' . $vendor->ID . ' - Status: ' . $status);
            }
        }
    }
    
    /**
     * Handle subscription deleted/cancelled event
     */
    private function handle_subscription_deleted($subscription) {
        $subscription_id = $subscription['id'];
        
        // Find user by subscription ID
        $users = get_users(array(
            'meta_key' => 'stripe_subscription_id',
            'meta_value' => $subscription_id,
            'number' => 1
        ));
        
        if (empty($users)) {
            error_log('TourismIQ Stripe: No user found for cancelled subscription ' . $subscription_id);
            return;
        }
        
        $user = $users[0];
        $user_id = $user->ID;
        
        // Update subscription status
        update_user_meta($user_id, 'stripe_subscription_status', 'cancelled');
        
        // Get user's vendor(s) and disable paid features
        $vendors = get_posts(array(
            'post_type' => 'vendor',
            'author' => $user_id,
            'posts_per_page' => -1
        ));
        
        foreach ($vendors as $vendor) {
            $vendor_subscription_id = get_post_meta($vendor->ID, '_stripe_subscription_id', true);
            if ($vendor_subscription_id === $subscription_id) {
                
                update_field('vendor_is_paid', false, $vendor->ID);
                update_field('vendor_subscription_status', 'cancelled', $vendor->ID);
                
                error_log('TourismIQ Stripe: Subscription cancelled for vendor ' . $vendor->ID);
            }
        }
    }
    
    /**
     * Handle successful payment
     */
    private function handle_payment_succeeded($invoice) {
        $subscription_id = $invoice['subscription'];
        
        if (!$subscription_id) {
            return; // Not a subscription invoice
        }
        
        // Find user by subscription ID
        $users = get_users(array(
            'meta_key' => 'stripe_subscription_id',
            'meta_value' => $subscription_id,
            'number' => 1
        ));
        
        if (empty($users)) {
            return;
        }
        
        $user = $users[0];
        $user_id = $user->ID;
        
        // Ensure subscription is active after successful payment
        update_user_meta($user_id, 'stripe_subscription_status', 'active');
        
        // Get user's vendor(s) and ensure paid status is enabled
        $vendors = get_posts(array(
            'post_type' => 'vendor',
            'author' => $user_id,
            'posts_per_page' => -1
        ));
        
        foreach ($vendors as $vendor) {
            $vendor_subscription_id = get_post_meta($vendor->ID, '_stripe_subscription_id', true);
            if ($vendor_subscription_id === $subscription_id) {
                
                update_field('vendor_is_paid', true, $vendor->ID);
                update_field('vendor_subscription_status', 'active', $vendor->ID);
                
                error_log('TourismIQ Stripe: Payment succeeded for vendor ' . $vendor->ID);
            }
        }
    }
    
    /**
     * Handle failed payment
     */
    private function handle_payment_failed($invoice) {
        $subscription_id = $invoice['subscription'];
        
        if (!$subscription_id) {
            return; // Not a subscription invoice
        }
        
        // Find user by subscription ID
        $users = get_users(array(
            'meta_key' => 'stripe_subscription_id',
            'meta_value' => $subscription_id,
            'number' => 1
        ));
        
        if (empty($users)) {
            return;
        }
        
        $user = $users[0];
        $user_id = $user->ID;
        
        // Update subscription status to past_due
        update_user_meta($user_id, 'stripe_subscription_status', 'past_due');
        
        // Get user's vendor(s) and update status (but don't disable immediately - give grace period)
        $vendors = get_posts(array(
            'post_type' => 'vendor',
            'author' => $user_id,
            'posts_per_page' => -1
        ));
        
        foreach ($vendors as $vendor) {
            $vendor_subscription_id = get_post_meta($vendor->ID, '_stripe_subscription_id', true);
            if ($vendor_subscription_id === $subscription_id) {
                
                update_field('vendor_subscription_status', 'past_due', $vendor->ID);
                // Note: We keep vendor_is_paid as true during grace period
                
                error_log('TourismIQ Stripe: Payment failed for vendor ' . $vendor->ID . ' - Grace period active');
            }
        }
    }
    
    /**
     * Get subscription status for a vendor
     */
    public static function get_vendor_subscription_status($vendor_id) {
        $vendor = get_post($vendor_id);
        if (!$vendor) {
            return false;
        }
        
        $user_id = $vendor->post_author;
        $subscription_status = get_user_meta($user_id, 'stripe_subscription_status', true);
        $is_paid = get_field('vendor_is_paid', $vendor_id);
        
        return array(
            'is_paid' => $is_paid,
            'subscription_status' => $subscription_status,
            'subscription_id' => get_user_meta($user_id, 'stripe_subscription_id', true),
            'customer_id' => get_user_meta($user_id, 'stripe_customer_id', true)
        );
    }
    
    /**
     * Create customer portal link for subscription management
     */
    public static function create_customer_portal_link($vendor_id, $return_url = '') {
        $vendor = get_post($vendor_id);
        if (!$vendor) {
            return false;
        }
        
        $user_id = $vendor->post_author;
        $customer_id = get_user_meta($user_id, 'stripe_customer_id', true);
        
        if (!$customer_id) {
            return false;
        }
        
        if (empty($return_url)) {
            $return_url = home_url('/dashboard/vendor-settings');
        }
        
        try {
            if (class_exists('\Stripe\BillingPortal\Session')) {
                $session = \Stripe\BillingPortal\Session::create([
                    'customer' => $customer_id,
                    'return_url' => $return_url,
                ]);
                
                return $session->url;
            }
        } catch (Exception $e) {
            error_log('TourismIQ Stripe: Error creating portal link - ' . $e->getMessage());
        }
        
        return false;
    }
}

// Initialize the integration
new TourismIQ_Stripe_Integration();