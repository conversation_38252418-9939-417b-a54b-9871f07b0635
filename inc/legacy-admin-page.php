<?php
/**
 * Simple admin page to install legacy tables
 */

add_action('admin_menu', 'legacy_install_admin_menu');

function legacy_install_admin_menu() {
    add_management_page(
        'Install Legacy Tables',
        'Legacy Messages Install',
        'manage_options',
        'legacy-install',
        'legacy_install_page'
    );
}

function legacy_install_page() {
    global $wpdb;
    
    if (isset($_POST['install_tables'])) {
        $result = install_legacy_tables_now();
        echo '<div class="notice notice-success"><p>Legacy tables installed! ' . $result . '</p></div>';
    }
    
    ?>
    <div class="wrap">
        <h1>Legacy Message Tables Manager</h1>
        
        <h2>Install Tables</h2>
        <p>This will create the wp_um_conversations and wp_um_messages tables with sample data for user ID 1.</p>
        
        <form method="post">
            <?php wp_nonce_field('legacy_install'); ?>
            <input type="submit" name="install_tables" class="button button-primary" value="Install Legacy Tables">
        </form>
        
        <hr>
        
        <h2>Data Status</h2>
        <?php
        // Check table status
        $conversations_table = $wpdb->prefix . 'um_conversations';
        $messages_table = $wpdb->prefix . 'um_messages';
        
        $conv_exists = $wpdb->get_var("SHOW TABLES LIKE '$conversations_table'");
        $msg_exists = $wpdb->get_var("SHOW TABLES LIKE '$messages_table'");
        
        echo "<p><strong>Tables Status:</strong><br>";
        echo "Conversations table exists: " . ($conv_exists ? 'YES' : 'NO') . "<br>";
        echo "Messages table exists: " . ($msg_exists ? 'YES' : 'NO') . "</p>";
        
        if ($conv_exists) {
            $total_conv = $wpdb->get_var("SELECT COUNT(*) FROM $conversations_table");
            $user1_conv = $wpdb->get_var("SELECT COUNT(*) FROM $conversations_table WHERE user_a = 1 OR user_b = 1");
            echo "<p><strong>Conversations:</strong><br>";
            echo "Total conversations: $total_conv<br>";
            echo "Conversations for user 1: $user1_conv</p>";
            
            if ($user1_conv > 0) {
                $user1_conversations = $wpdb->get_results("SELECT * FROM $conversations_table WHERE user_a = 1 OR user_b = 1");
                echo "<h3>User 1 Conversations:</h3>";
                echo "<table class='wp-list-table widefat fixed striped'>";
                echo "<thead><tr><th>ID</th><th>User A</th><th>User B</th><th>Last Updated</th></tr></thead><tbody>";
                foreach ($user1_conversations as $conv) {
                    echo "<tr><td>{$conv->conversation_id}</td><td>{$conv->user_a}</td><td>{$conv->user_b}</td><td>{$conv->last_updated}</td></tr>";
                }
                echo "</tbody></table>";
            }
        }
        
        if ($msg_exists) {
            $total_msg = $wpdb->get_var("SELECT COUNT(*) FROM $messages_table");
            $user1_msg = $wpdb->get_var("SELECT COUNT(*) FROM $messages_table WHERE author = 1 OR recipient = 1");
            echo "<p><strong>Messages:</strong><br>";
            echo "Total messages: $total_msg<br>";
            echo "Messages for user 1: $user1_msg</p>";
            
            if ($user1_msg > 0) {
                $user1_messages = $wpdb->get_results("SELECT * FROM $messages_table WHERE author = 1 OR recipient = 1 LIMIT 5");
                echo "<h3>User 1 Messages (first 5):</h3>";
                echo "<table class='wp-list-table widefat fixed striped'>";
                echo "<thead><tr><th>ID</th><th>Conv ID</th><th>Time</th><th>Content</th><th>Author</th><th>Recipient</th></tr></thead><tbody>";
                foreach ($user1_messages as $msg) {
                    $content = substr($msg->content, 0, 50) . '...';
                    echo "<tr><td>{$msg->message_id}</td><td>{$msg->conversation_id}</td><td>{$msg->time}</td><td>{$content}</td><td>{$msg->author}</td><td>{$msg->recipient}</td></tr>";
                }
                echo "</tbody></table>";
            }
        }
        ?>
        
        
    </div>
    <?php
}

function install_legacy_tables_now() {
    global $wpdb;
    
    // Create conversations table
    $wpdb->query("
        CREATE TABLE IF NOT EXISTS {$wpdb->prefix}um_conversations (
            conversation_id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            user_a bigint(20) UNSIGNED NOT NULL DEFAULT 0,
            user_b bigint(20) UNSIGNED NOT NULL DEFAULT 0,
            last_updated datetime NOT NULL DEFAULT '1970-01-01 00:00:00',
            PRIMARY KEY (conversation_id),
            KEY user_a_user_b (user_a,user_b),
            KEY user_a (user_a),
            KEY user_b (user_b)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create messages table
    $wpdb->query("
        CREATE TABLE IF NOT EXISTS {$wpdb->prefix}um_messages (
            message_id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            conversation_id bigint(20) UNSIGNED NOT NULL DEFAULT 0,
            time datetime NOT NULL DEFAULT '1970-01-01 00:00:00',
            content longtext NOT NULL,
            status int(11) NOT NULL DEFAULT 0,
            reminded tinyint(2) NOT NULL DEFAULT 0,
            author bigint(20) UNSIGNED NOT NULL DEFAULT 0,
            recipient bigint(20) UNSIGNED NOT NULL DEFAULT 0,
            PRIMARY KEY (message_id),
            KEY recipient_author_status (recipient,author,status),
            KEY conversation_id_author (conversation_id,author),
            KEY recipient_author (recipient,author),
            KEY recipient_status (recipient,status),
            KEY conversation_id (conversation_id),
            KEY recipient (recipient),
            KEY status (status),
            KEY author (author)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Import data from SQL files
    $theme_dir = get_template_directory();
    $conversations_file = $theme_dir . '/backups/wp_um_conversations.sql';
    $messages_file = $theme_dir . '/backups/wp_um_messages.sql';
    
    $imported_conversations = 0;
    $imported_messages = 0;
    
    // Import conversations
    if (file_exists($conversations_file)) {
        $conversations_sql = file_get_contents($conversations_file);
        if ($conversations_sql) {
            // Clear existing data first
            $wpdb->query("TRUNCATE TABLE {$wpdb->prefix}um_conversations");
            
            // Extract and execute INSERT statements only
            $imported_conversations = execute_sql_inserts($conversations_sql, $wpdb->prefix . 'um_conversations');
        }
    }
    
    // Import messages  
    if (file_exists($messages_file)) {
        $messages_sql = file_get_contents($messages_file);
        if ($messages_sql) {
            // Clear existing data first
            $wpdb->query("TRUNCATE TABLE {$wpdb->prefix}um_messages");
            
            // Extract and execute INSERT statements only
            $imported_messages = execute_sql_inserts($messages_sql, $wpdb->prefix . 'um_messages');
        }
    }
    
    if ($imported_conversations > 0 && $imported_messages > 0) {
        return "Tables created and imported $imported_conversations conversations and $imported_messages messages from backup files!";
    } else {
        return "Tables created but backup files not found. Please upload the backup files to /backups/ folder.";
    }
}

/**
 * Extract and execute INSERT statements from SQL dump file
 */
function execute_sql_inserts($sql_content, $table_name) {
    global $wpdb;
    
    $imported_count = 0;
    $old_table_name = str_replace($wpdb->prefix, 'wp_', $table_name);
    
    // Find all INSERT statements for the specific table (multi-line support)
    $pattern = '/INSERT INTO `?' . preg_quote($old_table_name, '/') . '`?\s*\([^)]+\)\s*VALUES\s*(.*?)(?=INSERT INTO|ALTER TABLE|--.*AUTO_INCREMENT|\z)/is';
    
    if (preg_match_all($pattern, $sql_content, $matches, PREG_SET_ORDER)) {
        foreach ($matches as $match) {
            $values_section = trim($match[1]);
            
            // Remove trailing semicolon and whitespace
            $values_section = rtrim($values_section, "; \n\r\t");
            
            // Get column names from the first INSERT statement
            $column_pattern = '/INSERT INTO `?' . preg_quote($old_table_name, '/') . '`?\s*\(([^)]+)\)/i';
            if (preg_match($column_pattern, $sql_content, $col_match)) {
                $columns = trim($col_match[1]);
                
                // Split values into individual rows - handle complex cases with nested parentheses
                $values_array = array();
                $current_row = '';
                $paren_count = 0;
                $in_quotes = false;
                $quote_char = '';
                
                for ($i = 0; $i < strlen($values_section); $i++) {
                    $char = $values_section[$i];
                    
                    if (!$in_quotes && ($char == '"' || $char == "'")) {
                        $in_quotes = true;
                        $quote_char = $char;
                    } elseif ($in_quotes && $char == $quote_char && ($i == 0 || $values_section[$i-1] != '\\')) {
                        $in_quotes = false;
                        $quote_char = '';
                    }
                    
                    if (!$in_quotes) {
                        if ($char == '(') {
                            $paren_count++;
                        } elseif ($char == ')') {
                            $paren_count--;
                        }
                    }
                    
                    $current_row .= $char;
                    
                    // End of a complete row
                    if (!$in_quotes && $paren_count == 0 && $char == ')') {
                        $values_array[] = trim($current_row);
                        $current_row = '';
                        
                        // Skip comma and whitespace
                        while ($i + 1 < strlen($values_section) && in_array($values_section[$i + 1], [',', ' ', "\n", "\r", "\t"])) {
                            $i++;
                        }
                    }
                }
                
                // Process in batches to avoid memory issues and SQL limits
                $batch_size = 100;
                for ($j = 0; $j < count($values_array); $j += $batch_size) {
                    $batch = array_slice($values_array, $j, $batch_size);
                    $values_string = implode(',', $batch);
                    
                    $insert_query = "INSERT INTO `{$table_name}` ({$columns}) VALUES {$values_string}";
                    
                    $result = $wpdb->query($insert_query);
                    if ($result !== false) {
                        $imported_count += $result;
                    }
                }
            }
        }
    }
    
    return $imported_count;
}