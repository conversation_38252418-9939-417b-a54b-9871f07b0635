<?php
/**
 * Security Utilities for TourismIQ
 * Centralized security functions and helpers
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Security {
    private static $instance = null;
    private static $rate_limits = [];

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('init', [$this, 'init_security_headers']);
        add_action('rest_api_init', [$this, 'add_rate_limiting']);
    }

    /**
     * Initialize security headers
     */
    public function init_security_headers() {
        // Add security headers
        if (!headers_sent()) {
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
            header('X-XSS-Protection: 1; mode=block');
            header('Referrer-Policy: strict-origin-when-cross-origin');
        }
    }

    /**
     * Validate and sanitize user input
     */
    public static function validate_input($input, $type = 'text', $max_length = null) {
        if (empty($input)) {
            return '';
        }

        switch ($type) {
            case 'email':
                return sanitize_email($input);
            case 'url':
                return esc_url_raw($input);
            case 'int':
                return intval($input);
            case 'float':
                return floatval($input);
            case 'html':
                return wp_kses_post($input);
            case 'textarea':
                $sanitized = sanitize_textarea_field($input);
                break;
            case 'text':
            default:
                $sanitized = sanitize_text_field($input);
                break;
        }

        // Apply length limit if specified
        if ($max_length && strlen($sanitized) > $max_length) {
            $sanitized = substr($sanitized, 0, $max_length);
        }

        return $sanitized;
    }

    /**
     * Rate limiting for API endpoints
     * TEMPORARILY DISABLED FOR DEBUGGING 403 ISSUES
     */
    public function add_rate_limiting() {
        // add_filter('rest_pre_dispatch', [$this, 'check_rate_limit'], 10, 3);
    }

    /**
     * Check rate limit for requests
     */
    public function check_rate_limit($result, $server, $request) {
        $route = $request->get_route();
        $user_id = get_current_user_id();
        $ip = $this->get_client_ip();
        
        // Define rate limits for different endpoints
        $limits = [
            '/tourismiq/v1/messages/send' => ['limit' => 30, 'window' => 300], // 30 messages per 5 minutes
            '/tourismiq/v1/connections/request' => ['limit' => 10, 'window' => 300], // 10 requests per 5 minutes
            '/tourismiq/v1/notifications/send' => ['limit' => 20, 'window' => 300], // 20 notifications per 5 minutes
            '/tourismiq/v1/auth/login' => ['limit' => 5, 'window' => 300], // 5 login attempts per 5 minutes
        ];

        foreach ($limits as $pattern => $config) {
            if (strpos($route, $pattern) !== false) {
                $key = $user_id ? "user_{$user_id}_{$pattern}" : "ip_{$ip}_{$pattern}";
                
                if (!$this->is_within_rate_limit($key, $config['limit'], $config['window'])) {
                    return new WP_Error(
                        'rate_limit_exceeded',
                        'Rate limit exceeded. Please try again later.',
                        ['status' => 429]
                    );
                }
                break;
            }
        }

        return $result;
    }

    /**
     * Check if request is within rate limit
     */
    private function is_within_rate_limit($key, $limit, $window) {
        $current_time = time();
        $requests = get_transient("rate_limit_{$key}") ?: [];
        
        // Remove old requests outside the window
        $requests = array_filter($requests, function($timestamp) use ($current_time, $window) {
            return ($current_time - $timestamp) < $window;
        });

        // Check if limit exceeded
        if (count($requests) >= $limit) {
            return false;
        }

        // Add current request
        $requests[] = $current_time;
        set_transient("rate_limit_{$key}", $requests, $window);
        
        return true;
    }

    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Verify nonce for CSRF protection
     */
    public static function verify_nonce($nonce, $action = 'wp_rest') {
        return wp_verify_nonce($nonce, $action);
    }

    /**
     * Secure database query preparation
     */
    public static function prepare_query($query, $args = []) {
        global $wpdb;
        
        if (empty($args)) {
            return $query;
        }
        
        return $wpdb->prepare($query, $args);
    }

    /**
     * Log security events
     */
    public static function log_security_event($event, $details = []) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log(sprintf(
                '[TourismIQ Security] %s: %s',
                $event,
                json_encode($details)
            ));
        }
    }

    /**
     * Check if user has permission for action
     */
    public static function check_permission($action, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        if (!$user_id) {
            return false;
        }

        $permissions = [
            'send_message' => 'read',
            'create_connection' => 'read',
            'send_notification' => 'read',
            'update_profile' => 'read',
            'admin_action' => 'manage_options',
        ];

        $required_cap = $permissions[$action] ?? 'read';
        return user_can($user_id, $required_cap);
    }

    /**
     * Sanitize array recursively
     */
    public static function sanitize_array($array, $type = 'text') {
        if (!is_array($array)) {
            return self::validate_input($array, $type);
        }

        $sanitized = [];
        foreach ($array as $key => $value) {
            $sanitized_key = sanitize_key($key);
            $sanitized[$sanitized_key] = is_array($value) 
                ? self::sanitize_array($value, $type)
                : self::validate_input($value, $type);
        }

        return $sanitized;
    }
}

// Initialize security
TourismIQ_Security::get_instance();

/**
 * Helper functions for easy access
 */
function tourismiq_validate_input($input, $type = 'text', $max_length = null) {
    return TourismIQ_Security::validate_input($input, $type, $max_length);
}

function tourismiq_check_permission($action, $user_id = null) {
    return TourismIQ_Security::check_permission($action, $user_id);
}

function tourismiq_log_security($event, $details = []) {
    return TourismIQ_Security::log_security_event($event, $details);
}

function tourismiq_sanitize_array($array, $type = 'text') {
    return TourismIQ_Security::sanitize_array($array, $type);
} 