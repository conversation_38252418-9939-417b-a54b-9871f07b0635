<?php
/**
 * Yoast SEO Integration for Headless WordPress
 * 
 * This file ensures Yoast SEO data is properly exposed through the REST API
 * for consumption by the Next.js frontend.
 */

/**
 * Ensure Yoast SEO data is available in REST API for all post types
 */
function tourismiq_add_yoast_to_rest_api() {
    // Get all public post types
    $post_types = get_post_types(['public' => true], 'names');
    
    foreach ($post_types as $post_type) {
        // Add Yoast head data to REST API
        register_rest_field($post_type, 'yoast_head', [
            'get_callback' => function($post) {
                if (!function_exists('YoastSEO')) {
                    return '';
                }
                
                // Switch to the post context
                $GLOBALS['post'] = get_post($post['id']);
                setup_postdata($GLOBALS['post']);
                
                // Get Yoast generated head HTML
                ob_start();
                do_action('wpseo_head');
                $yoast_head = ob_get_clean();
                
                wp_reset_postdata();
                
                return $yoast_head;
            },
            'schema' => [
                'description' => 'Yoast SEO head HTML',
                'type' => 'string',
                'context' => ['view', 'edit']
            ]
        ]);
        
        // Add Yoast head JSON data for easier parsing
        register_rest_field($post_type, 'yoast_head_json', [
            'get_callback' => function($post) {
                if (!class_exists('WPSEO_Frontend')) {
                    return null;
                }
                
                // Get the post object
                $post_obj = get_post($post['id']);
                
                // Build Yoast meta data array
                $wpseo_frontend = WPSEO_Frontend::get_instance();
                $wpseo_meta = WPSEO_Meta::get_value('', $post_obj->ID);
                
                $meta_data = [
                    'title' => $wpseo_frontend->get_content_title($post_obj),
                    'description' => $wpseo_meta ? WPSEO_Meta::get_value('metadesc', $post_obj->ID) : '',
                    'robots' => [
                        'index' => WPSEO_Meta::get_value('meta-robots-noindex', $post_obj->ID) ? 'noindex' : 'index',
                        'follow' => WPSEO_Meta::get_value('meta-robots-nofollow', $post_obj->ID) ? 'nofollow' : 'follow',
                    ],
                    'canonical' => get_permalink($post_obj->ID),
                    'og_locale' => get_locale(),
                    'og_type' => 'article',
                    'og_title' => $wpseo_frontend->get_content_title($post_obj),
                    'og_description' => WPSEO_Meta::get_value('opengraph-description', $post_obj->ID) ?: WPSEO_Meta::get_value('metadesc', $post_obj->ID),
                    'og_url' => get_permalink($post_obj->ID),
                    'og_site_name' => get_bloginfo('name'),
                    'article_publisher' => get_option('wpseo_social')['facebook_site'] ?? '',
                    'article_published_time' => get_the_date('c', $post_obj),
                    'article_modified_time' => get_the_modified_date('c', $post_obj),
                    'og_image' => [],
                    'twitter_card' => 'summary_large_image',
                    'twitter_site' => get_option('wpseo_social')['twitter_site'] ?? '',
                    'schema' => [] // Would need additional processing for full schema
                ];
                
                // Add Open Graph image if available
                $og_image_id = WPSEO_Meta::get_value('opengraph-image-id', $post_obj->ID);
                if (!$og_image_id && has_post_thumbnail($post_obj->ID)) {
                    $og_image_id = get_post_thumbnail_id($post_obj->ID);
                }
                
                if ($og_image_id) {
                    $image_data = wp_get_attachment_image_src($og_image_id, 'full');
                    if ($image_data) {
                        $meta_data['og_image'][] = [
                            'url' => $image_data[0],
                            'width' => $image_data[1],
                            'height' => $image_data[2],
                            'type' => get_post_mime_type($og_image_id)
                        ];
                    }
                }
                
                return $meta_data;
            },
            'schema' => [
                'description' => 'Yoast SEO structured data',
                'type' => 'object',
                'context' => ['view', 'edit']
            ]
        ]);
    }
}
add_action('rest_api_init', 'tourismiq_add_yoast_to_rest_api');

/**
 * Add Yoast data to user REST API responses
 */
function tourismiq_add_yoast_to_users() {
    register_rest_field('user', 'yoast_head_json', [
        'get_callback' => function($user) {
            $author_url = get_author_posts_url($user['id']);
            
            return [
                'title' => $user['name'] . ' - ' . get_bloginfo('name'),
                'description' => get_the_author_meta('description', $user['id']),
                'canonical' => $author_url,
                'og_type' => 'profile',
                'og_title' => $user['name'],
                'og_url' => $author_url,
                'og_site_name' => get_bloginfo('name'),
            ];
        },
        'schema' => [
            'description' => 'Yoast SEO structured data for users',
            'type' => 'object',
            'context' => ['view', 'edit']
        ]
    ]);
}
add_action('rest_api_init', 'tourismiq_add_yoast_to_users');

/**
 * Add Yoast data to category/taxonomy REST API responses
 */
function tourismiq_add_yoast_to_taxonomies() {
    $taxonomies = get_taxonomies(['public' => true], 'names');
    
    foreach ($taxonomies as $taxonomy) {
        register_rest_field($taxonomy, 'yoast_head_json', [
            'get_callback' => function($term) {
                $term_obj = get_term($term['id']);
                $term_url = get_term_link($term_obj);
                
                return [
                    'title' => $term['name'] . ' - ' . get_bloginfo('name'),
                    'description' => $term['description'] ?: 'Browse all posts in ' . $term['name'],
                    'canonical' => $term_url,
                    'og_type' => 'website',
                    'og_title' => $term['name'],
                    'og_url' => $term_url,
                    'og_site_name' => get_bloginfo('name'),
                ];
            },
            'schema' => [
                'description' => 'Yoast SEO structured data for taxonomies',
                'type' => 'object',
                'context' => ['view', 'edit']
            ]
        ]);
    }
}
add_action('rest_api_init', 'tourismiq_add_yoast_to_taxonomies');