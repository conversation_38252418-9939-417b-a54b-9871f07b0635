<?php
/**
 * Member Directory Functionality
 * 
 * Handles the member directory features and shortcodes.
 */

// Register member directory shortcode
add_shortcode('member_directory', 'tourismiq_member_directory_shortcode');

function tourismiq_member_directory_shortcode($atts) {
    // Enqueue the member directory script
    wp_enqueue_script('tourismiq-member-directory');
    
    // Output the container for the member directory
    ob_start();
    ?>
    <div id="member-directory-app">
        <!-- React will render here -->
    </div>
    <?php
    return ob_get_clean();
}

// Enqueue member directory scripts and styles
add_action('wp_enqueue_scripts', function() {
    if (!is_admin()) {
        $asset_file = include get_template_directory() . '/frontend/build/member-directory.asset.php';
        
        wp_register_script(
            'tourismiq-member-directory',
            get_template_directory_uri() . '/frontend/build/member-directory.js',
            $asset_file['dependencies'],
            $asset_file['version'],
            true
        );
        
        wp_localize_script('tourismiq-member-directory', 'tourismiqMemberDirectory', [
            'restUrl' => rest_url('tourismiq/v1/members'),
            'nonce' => wp_create_nonce('wp_rest')
        ]);
    }
});
