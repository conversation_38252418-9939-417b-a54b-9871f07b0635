<?php
/**
 * TourismIQ User Connections API
 *
 * Expected database table schema:
 * CREATE TABLE wp_user_connections (
 *   id int(11) NOT NULL AUTO_INCREMENT,
 *   requester_id bigint(20) UNSIGNED NOT NULL,
 *   requested_id bigint(20) UNSIGNED NOT NULL,
 *   status varchar(20) NOT NULL DEFAULT 'pending',
 *   created_at datetime NOT NULL,
 *   updated_at datetime NOT NULL,
 *   PRIMARY KEY (id),
 *   KEY requester_id (requester_id),
 *   KEY requested_id (requested_id),
 *   KEY status (status),
 *   UNIQUE KEY unique_connection (requester_id, requested_id)
 * );
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Connections {
    private static $instance = null;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        add_action('init', array($this, 'maybe_create_connections_table'));
    }

    /**
     * Register REST API endpoints for connections
     */
    public function register_rest_routes() {
        // Send connection request
        register_rest_route('tourismiq/v1', '/connections/request', array(
            'methods' => 'POST',
            'callback' => array($this, 'send_connection_request'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'user_id' => array(
                    'required' => true,
                    'type' => 'integer',
                    'validate_callback' => function($param) {
                        return is_numeric($param) && $param > 0;
                    }
                ),
            ),
        ));

        // Accept connection request
        register_rest_route('tourismiq/v1', '/connections/accept', array(
            'methods' => 'POST',
            'callback' => array($this, 'accept_connection_request'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'user_id' => array(
                    'required' => true,
                    'type' => 'integer',
                    'validate_callback' => function($param) {
                        return is_numeric($param) && $param > 0;
                    }
                ),
            ),
        ));

        // Decline connection request
        register_rest_route('tourismiq/v1', '/connections/decline', array(
            'methods' => 'POST',
            'callback' => array($this, 'decline_connection_request'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'user_id' => array(
                    'required' => true,
                    'type' => 'integer',
                    'validate_callback' => function($param) {
                        return is_numeric($param) && $param > 0;
                    }
                ),
            ),
        ));

        // Remove connection
        register_rest_route('tourismiq/v1', '/connections/remove', array(
            'methods' => 'POST',
            'callback' => array($this, 'remove_connection'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'user_id' => array(
                    'required' => true,
                    'type' => 'integer',
                    'validate_callback' => function($param) {
                        return is_numeric($param) && $param > 0;
                    }
                ),
            ),
        ));

        // Get connection status between two users
        register_rest_route('tourismiq/v1', '/connections/status/(?P<user_id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_connection_status'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
        ));

        // Get user's connections
        register_rest_route('tourismiq/v1', '/connections', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_user_connections'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'userId' => array(
                    'required' => false,
                    'type' => 'integer',
                    'validate_callback' => function($param) {
                        return is_numeric($param) && $param > 0;
                    }
                ),
            ),
        ));

        // Get pending requests (both sent and received)
        register_rest_route('tourismiq/v1', '/connections/pending', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_pending_requests'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
        ));

        // Bulk connection status endpoint
        register_rest_route('tourismiq/v1', '/connections/bulk-status', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_bulk_connection_status'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'userIds' => array(
                    'required' => true,
                    'type' => 'array',
                    'validate_callback' => function($param) {
                        return is_array($param) && !empty($param) && array_filter($param, 'is_numeric') === $param;
                    }
                ),
            ),
        ));
    }

    /**
     * Send a connection request
     */
    public function send_connection_request($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $target_user_id = tourismiq_validate_input($request->get_param('user_id'), 'int');

        // Check permissions
        if (!tourismiq_check_permission('create_connection', $current_user_id)) {
            return new WP_Error(
                'insufficient_permissions',
                'You do not have permission to create connections',
                array('status' => 403)
            );
        }

        // Validate input
        if (!$target_user_id || $target_user_id <= 0) {
            return new WP_Error(
                'invalid_user_id',
                'Invalid user ID',
                array('status' => 400)
            );
        }

        // Validate target user exists
        if (!get_user_by('id', $target_user_id)) {
            return new WP_Error(
                'invalid_user',
                'Target user does not exist',
                array('status' => 404)
            );
        }

        // Don't allow self-connections
        if ($current_user_id == $target_user_id) {
            return new WP_Error(
                'self_connection',
                'Cannot send connection request to yourself',
                array('status' => 400)
            );
        }

        // Check if table exists and create if not
        $table_name = $wpdb->prefix . 'user_connections';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            $this->maybe_create_connections_table();
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        }

        // Check if connection already exists
        $table_name = $wpdb->prefix . 'user_connections';
        $existing_connection = $wpdb->get_row(
            TourismIQ_Security::prepare_query(
                "SELECT * FROM {$table_name}
                 WHERE (requester_id = %d AND requested_id = %d)
                 OR (requester_id = %d AND requested_id = %d)",
                [$current_user_id, $target_user_id, $target_user_id, $current_user_id]
            )
        );

        if ($existing_connection) {
            if ($existing_connection->status === 'pending') {
                return new WP_Error(
                    'request_exists',
                    'Connection request already exists',
                    array('status' => 409)
                );
            } elseif ($existing_connection->status === 'accepted') {
                return new WP_Error(
                    'already_connected',
                    'Users are already connected',
                    array('status' => 409)
                );
            }
        }

        // Insert connection request
        $result = $wpdb->insert(
            $wpdb->prefix . 'user_connections',
            array(
                'requester_id' => $current_user_id,
                'requested_id' => $target_user_id,
                'status' => 'pending',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s')
        );

        if ($result === false) {
            return new WP_Error(
                'database_error',
                'Failed to create connection request: ' . $wpdb->last_error,
                array('status' => 500)
            );
        }

        // Get user data for notification
        $requester = get_user_by('id', $current_user_id);
        $target_user = get_user_by('id', $target_user_id);

        // Get profile picture from ACF instead of Gravatar
        $profile_picture = get_field('profile_picture', 'user_' . $current_user_id);
        $avatar_url = null;

        if ($profile_picture && is_array($profile_picture) && isset($profile_picture['url'])) {
            $avatar_url = $profile_picture['url'];
        }

        // Send notification
        tourismiq_store_and_send_notification(array(
            'recipient_id' => $target_user_id,
            'sender_id' => $current_user_id,
            'type' => 'connection_request',
            'content' => sprintf('%s sent you a connection request', $requester->display_name),
            'reference_id' => $current_user_id,
            'reference_type' => 'user',
            'sender_name' => $requester->display_name,
            'sender_avatar' => $avatar_url,
        ));

        return array(
            'success' => true,
            'message' => 'Connection request sent successfully',
            'data' => array(
                'status' => 'pending',
                'requester_id' => $current_user_id,
                'requested_id' => $target_user_id,
            )
        );
    }

    /**
     * Accept a connection request
     */
    public function accept_connection_request($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $requester_id = $request->get_param('user_id');

        // Find the pending request
        $connection = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}user_connections
             WHERE requester_id = %d AND requested_id = %d AND status = 'pending'",
            $requester_id, $current_user_id
        ));

        if (!$connection) {
            return new WP_Error(
                'request_not_found',
                'Connection request not found',
                array('status' => 404)
            );
        }

        // Update connection status
        $result = $wpdb->update(
            $wpdb->prefix . 'user_connections',
            array(
                'status' => 'accepted',
                'updated_at' => current_time('mysql')
            ),
            array('id' => $connection->id),
            array('%s', '%s'),
            array('%d')
        );

        if ($result === false) {
            return new WP_Error(
                'database_error',
                'Failed to accept connection request',
                array('status' => 500)
            );
        }

        // Get user data for notification
        $accepter = get_user_by('id', $current_user_id);

        // Get profile picture from ACF instead of Gravatar
        $profile_picture = get_field('profile_picture', 'user_' . $current_user_id);
        $avatar_url = null;

        if ($profile_picture && is_array($profile_picture) && isset($profile_picture['url'])) {
            $avatar_url = $profile_picture['url'];
        }

        // Send notification to requester
        tourismiq_store_and_send_notification(array(
            'recipient_id' => $requester_id,
            'sender_id' => $current_user_id,
            'type' => 'connection_accepted',
            'content' => sprintf('%s accepted your connection request', $accepter->display_name),
            'reference_id' => $current_user_id,
            'reference_type' => 'user',
            'sender_name' => $accepter->display_name,
            'sender_avatar' => $avatar_url,
        ));

        // Remove the original connection request notification from the recipient's notifications
        $this->remove_connection_notification($current_user_id, $requester_id, 'connection_request');

        // Trigger IQ points for both users
        do_action('tourismiq_connection_accepted', $current_user_id, $requester_id);

        return array(
            'success' => true,
            'message' => 'Connection request accepted',
            'data' => array(
                'status' => 'accepted',
                'requester_id' => $requester_id,
                'requested_id' => $current_user_id,
            )
        );
    }

    /**
     * Decline a connection request
     */
    public function decline_connection_request($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $requester_id = $request->get_param('user_id');

        // Find the pending request
        $connection = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}user_connections
             WHERE requester_id = %d AND requested_id = %d AND status = 'pending'",
            $requester_id, $current_user_id
        ));

        if (!$connection) {
            // If no pending connection found, return success anyway
            // This handles the case where the request was already declined
            return array(
                'success' => true,
                'message' => 'Connection request already handled',
            );
        }

        // Delete the connection request (or update to 'declined' if you want to keep the record)
        $result = $wpdb->delete(
            $wpdb->prefix . 'user_connections',
            array('id' => $connection->id),
            array('%d')
        );

        if ($result === false) {
            return new WP_Error(
                'database_error',
                'Failed to decline connection request',
                array('status' => 500)
            );
        }

        // Get user data for notification
        $decliner = get_user_by('id', $current_user_id);

        // Get profile picture from ACF instead of Gravatar
        $profile_picture = get_field('profile_picture', 'user_' . $current_user_id);
        $avatar_url = null;

        if ($profile_picture && is_array($profile_picture) && isset($profile_picture['url'])) {
            $avatar_url = $profile_picture['url'];
        }

        // Send notification to requester that their request was declined
        tourismiq_store_and_send_notification(array(
            'recipient_id' => $requester_id,
            'sender_id' => $current_user_id,
            'type' => 'connection_declined',
            'content' => sprintf('%s declined your connection request', $decliner->display_name),
            'reference_id' => $current_user_id,
            'reference_type' => 'user',
            'sender_name' => $decliner->display_name,
            'sender_avatar' => $avatar_url,
        ));

        // Remove the original connection request notification from the recipient's notifications
        $this->remove_connection_notification($current_user_id, $requester_id, 'connection_request');

        return array(
            'success' => true,
            'message' => 'Connection request declined',
        );
    }

    /**
     * Remove an existing connection
     */
    public function remove_connection($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $other_user_id = $request->get_param('user_id');

        // Find the accepted connection
        $connection = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}user_connections
             WHERE ((requester_id = %d AND requested_id = %d) OR (requester_id = %d AND requested_id = %d))
             AND status = 'accepted'",
            $current_user_id, $other_user_id, $other_user_id, $current_user_id
        ));

        if (!$connection) {
            return new WP_Error(
                'connection_not_found',
                'Connection not found',
                array('status' => 404)
            );
        }

        // Delete the connection
        $result = $wpdb->delete(
            $wpdb->prefix . 'user_connections',
            array('id' => $connection->id),
            array('%d')
        );

        if ($result === false) {
            return new WP_Error(
                'database_error',
                'Failed to remove connection',
                array('status' => 500)
            );
        }

        // Get user data for notification
        $disconnector = get_user_by('id', $current_user_id);

        // Get profile picture from ACF instead of Gravatar
        $profile_picture = get_field('profile_picture', 'user_' . $current_user_id);
        $avatar_url = null;

        if ($profile_picture && is_array($profile_picture) && isset($profile_picture['url'])) {
            $avatar_url = $profile_picture['url'];
        }

        // Send notification to the other user that they've been disconnected
        tourismiq_store_and_send_notification(array(
            'recipient_id' => $other_user_id,
            'sender_id' => $current_user_id,
            'type' => 'connection_removed',
            'content' => sprintf('%s disconnected from you', $disconnector->display_name),
            'reference_id' => $current_user_id,
            'reference_type' => 'user',
            'sender_name' => $disconnector->display_name,
            'sender_avatar' => $avatar_url,
        ));

        return array(
            'success' => true,
            'message' => 'Connection removed successfully',
        );
    }

    /**
     * Get connection status between current user and target user
     */
    public function get_connection_status($request) {
        global $wpdb;

        $start_time = microtime(true);
        $current_user_id = get_current_user_id();
        $target_user_id = $request->get_param('user_id');

        try {
            // Validate input
            if (!$target_user_id || !is_numeric($target_user_id) || $target_user_id <= 0) {
                error_log("[Connections Status] Invalid target user ID: " . ($target_user_id ?? 'null'));
                return new WP_Error(
                    'invalid_user_id',
                    'Valid user ID is required',
                    array('status' => 400)
                );
            }

            if (!$current_user_id) {
                error_log("[Connections Status] No current user found");
                return new WP_Error(
                    'not_authenticated',
                    'User must be logged in',
                    array('status' => 401)
                );
            }

            // Check if table exists and create if not
            $table_name = $wpdb->prefix . 'user_connections';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

            if (!$table_exists) {
                error_log("[Connections Status] Table doesn't exist, creating...");
                $this->maybe_create_connections_table();
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
                
                if (!$table_exists) {
                    error_log("[Connections Status] Failed to create connections table");
                    return new WP_Error(
                        'table_error',
                        'Database table unavailable',
                        array('status' => 500)
                    );
                }
            }

            error_log("[Connections Status] Checking connection between users {$current_user_id} and {$target_user_id}");

            // Check for any connection between the users with timeout protection
            $query = $wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}user_connections
                 WHERE (requester_id = %d AND requested_id = %d)
                 OR (requester_id = %d AND requested_id = %d)
                 LIMIT 1",
                $current_user_id, $target_user_id, $target_user_id, $current_user_id
            );

            $connection = $wpdb->get_row($query);

            // Check for database errors
            if ($wpdb->last_error) {
                error_log("[Connections Status] Database error: " . $wpdb->last_error);
                return new WP_Error(
                    'database_error',
                    'Database query failed',
                    array('status' => 500)
                );
            }

            $duration = round((microtime(true) - $start_time) * 1000, 2);
            error_log("[Connections Status] Query completed in {$duration}ms for users {$current_user_id} and {$target_user_id}");

            if (!$connection) {
                $response = array(
                    'status' => 'none',
                    'can_request' => true,
                );
                
                // Add cache headers
                $this->add_cache_headers();
                return $response;
            }

            $response = array(
                'status' => $connection->status,
                'can_request' => false,
            );

            // Add additional context based on status and user role in the connection
            if ($connection->status === 'pending') {
                if ($connection->requester_id == $current_user_id) {
                    $response['pending_type'] = 'sent';
                    $response['message'] = 'Request sent';
                } else {
                    $response['pending_type'] = 'received';
                    $response['message'] = 'Request received';
                    $response['can_accept'] = true;
                    $response['can_decline'] = true;
                }
            } elseif ($connection->status === 'accepted') {
                $response['message'] = 'Connected';
                $response['can_remove'] = true;
            }

            // Add cache headers for successful responses
            $this->add_cache_headers();
            
            error_log("[Connections Status] Successfully returned status '{$connection->status}' for users {$current_user_id} and {$target_user_id}");
            return $response;

        } catch (Exception $e) {
            $duration = round((microtime(true) - $start_time) * 1000, 2);
            error_log("[Connections Status] Exception after {$duration}ms for users {$current_user_id} and {$target_user_id}: " . $e->getMessage());
            
            return new WP_Error(
                'server_error',
                'Internal server error',
                array('status' => 500)
            );
        }
    }

    /**
     * Get bulk connection status for multiple users
     */
    public function get_bulk_connection_status($request) {
        global $wpdb;

        $start_time = microtime(true);
        $current_user_id = get_current_user_id();
        $user_ids = $request->get_param('userIds');

        try {
            if (!$current_user_id) {
                return new WP_Error(
                    'not_authenticated',
                    'User must be logged in',
                    array('status' => 401)
                );
            }

            if (!is_array($user_ids) || empty($user_ids)) {
                return new WP_Error(
                    'invalid_user_ids',
                    'Valid user IDs array is required',
                    array('status' => 400)
                );
            }

            // Limit to prevent abuse
            if (count($user_ids) > 100) {
                return new WP_Error(
                    'too_many_users',
                    'Maximum 100 users allowed per request',
                    array('status' => 400)
                );
            }

            // Check if table exists and create if not
            $table_name = $wpdb->prefix . 'user_connections';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

            if (!$table_exists) {
                $this->maybe_create_connections_table();
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
                
                if (!$table_exists) {
                    return new WP_Error(
                        'table_error',
                        'Database table unavailable',
                        array('status' => 500)
                    );
                }
            }

            // Sanitize user IDs
            $sanitized_user_ids = array_map('intval', $user_ids);
            $sanitized_user_ids = array_filter($sanitized_user_ids, function($id) { return $id > 0; });

            if (empty($sanitized_user_ids)) {
                return array('statuses' => array());
            }

            // Build placeholders for IN clause
            $placeholders = implode(',', array_fill(0, count($sanitized_user_ids), '%d'));
            
            // Query to get all connections for these users
            $query = $wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}user_connections
                 WHERE ((requester_id = %d AND requested_id IN ($placeholders))
                 OR (requester_id IN ($placeholders) AND requested_id = %d))",
                array_merge(
                    [$current_user_id],
                    $sanitized_user_ids,
                    $sanitized_user_ids,
                    [$current_user_id]
                )
            );

            $connections = $wpdb->get_results($query);

            // Check for database errors
            if ($wpdb->last_error) {
                error_log("[Bulk Connections Status] Database error: " . $wpdb->last_error);
                return new WP_Error(
                    'database_error',
                    'Database query failed',
                    array('status' => 500)
                );
            }

            // Build response array
            $statuses = array();
            
            // Initialize all users with 'none' status
            foreach ($sanitized_user_ids as $user_id) {
                $statuses[$user_id] = array(
                    'status' => 'none',
                    'can_request' => true,
                );
            }

            // Update with actual connection data
            foreach ($connections as $connection) {
                $other_user_id = null;
                
                if ($connection->requester_id == $current_user_id) {
                    $other_user_id = $connection->requested_id;
                } elseif ($connection->requested_id == $current_user_id) {
                    $other_user_id = $connection->requester_id;
                }

                if ($other_user_id && isset($statuses[$other_user_id])) {
                    $status_data = array(
                        'status' => $connection->status,
                        'can_request' => false,
                    );

                    // Add additional context based on status and user role
                    if ($connection->status === 'pending') {
                        if ($connection->requester_id == $current_user_id) {
                            $status_data['pending_type'] = 'sent';
                            $status_data['message'] = 'Request sent';
                        } else {
                            $status_data['pending_type'] = 'received';
                            $status_data['message'] = 'Request received';
                            $status_data['can_accept'] = true;
                            $status_data['can_decline'] = true;
                        }
                    } elseif ($connection->status === 'accepted') {
                        $status_data['message'] = 'Connected';
                        $status_data['can_remove'] = true;
                    }

                    $statuses[$other_user_id] = $status_data;
                }
            }

            $duration = round((microtime(true) - $start_time) * 1000, 2);
            error_log("[Bulk Connections Status] Query completed in {$duration}ms for " . count($sanitized_user_ids) . " users");

            // Add cache headers
            $this->add_cache_headers();
            
            return array('statuses' => $statuses);

        } catch (Exception $e) {
            $duration = round((microtime(true) - $start_time) * 1000, 2);
            error_log("[Bulk Connections Status] Exception after {$duration}ms: " . $e->getMessage());
            
            return new WP_Error(
                'server_error',
                'Internal server error',
                array('status' => 500)
            );
        }
    }

    /**
     * Add cache headers for better performance
     */
    private function add_cache_headers() {
        if (!headers_sent()) {
            header('Cache-Control: private, max-age=60');
            header('Pragma: cache');
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 60) . ' GMT');
        }
    }

    /**
     * Get user's connections
     */
    public function get_user_connections($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $userId = $request->get_param('userId');
        
        // Use the provided userId if available, otherwise use current user's ID
        $target_user_id = $userId ? $userId : $current_user_id;

        // Get all accepted connections for the target user
        $connections = $wpdb->get_results($wpdb->prepare(
            "SELECT uc.*,
                    CASE
                        WHEN uc.requester_id = %d THEN uc.requested_id
                        ELSE uc.requester_id
                    END as connected_user_id
             FROM {$wpdb->prefix}user_connections uc
             WHERE (uc.requester_id = %d OR uc.requested_id = %d)
             AND uc.status = 'accepted'
             ORDER BY uc.updated_at DESC",
            $target_user_id, $target_user_id, $target_user_id
        ));

        $connections_data = array();
        foreach ($connections as $connection) {
            $user = get_user_by('id', $connection->connected_user_id);
            if ($user) {
                // Get additional user meta
                $profile_picture = get_field('profile_picture', 'user_' . $user->ID);
                $bio = get_user_meta($user->ID, 'bio', true);
                $location = get_user_meta($user->ID, 'location', true);
                $job_title = get_user_meta($user->ID, 'job_title', true);
                $company = get_user_meta($user->ID, 'company', true);
                
                // Handle profile picture format
                $avatar_url = '';
                if ($profile_picture) {
                    if (is_array($profile_picture) && isset($profile_picture['url'])) {
                        $avatar_url = $profile_picture['url'];
                    } elseif (is_string($profile_picture)) {
                        $avatar_url = $profile_picture;
                    }
                }
                
                // Let frontend handle the fallback to placeholder if no profile picture
                if (empty($avatar_url)) {
                    $avatar_url = null; // Let frontend handle the fallback to placeholder
                }

                $connections_data[] = array(
                    'user_id' => $user->ID,
                    'username' => $user->user_login,
                    'display_name' => $user->display_name,
                    'name' => $user->display_name,
                    'avatar' => $avatar_url,
                    'profile_picture' => $profile_picture,
                    'bio' => $bio,
                    'location' => $location,
                    'job_title' => $job_title,
                    'company' => $company,
                    'connected_at' => $connection->updated_at,
                    'slug' => $user->user_login, // Using username as slug
                    'roles' => $user->roles, // Add user roles for badge display
                );
            }
        }

        return array(
            'connections' => $connections_data,
            'total' => count($connections_data),
        );
    }

    /**
     * Get pending connection requests
     */
    public function get_pending_requests($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();

        // Get pending requests sent by current user
        $sent_requests = $wpdb->get_results($wpdb->prepare(
            "SELECT uc.*, u.user_login, u.display_name
             FROM {$wpdb->prefix}user_connections uc
             JOIN {$wpdb->users} u ON u.ID = uc.requested_id
             WHERE uc.requester_id = %d AND uc.status = 'pending'
             ORDER BY uc.created_at DESC",
            $current_user_id
        ));

        // Get pending requests received by current user
        $received_requests = $wpdb->get_results($wpdb->prepare(
            "SELECT uc.*, u.user_login, u.display_name
             FROM {$wpdb->prefix}user_connections uc
             JOIN {$wpdb->users} u ON u.ID = uc.requester_id
             WHERE uc.requested_id = %d AND uc.status = 'pending'
             ORDER BY uc.created_at DESC",
            $current_user_id
        ));

        return array(
            'sent' => array_map(function($req) {
                return array(
                    'user_id' => $req->requested_id,
                    'username' => $req->user_login,
                    'display_name' => $req->display_name,
                    'avatar' => null, // Let frontend handle the fallback to placeholder
                    'sent_at' => $req->created_at,
                );
            }, $sent_requests),
            'received' => array_map(function($req) {
                return array(
                    'user_id' => $req->requester_id,
                    'username' => $req->user_login,
                    'display_name' => $req->display_name,
                    'avatar' => null, // Let frontend handle the fallback to placeholder
                    'received_at' => $req->created_at,
                );
            }, $received_requests),
        );
    }

    /**
     * Create the connections table if it doesn't exist
     */
    public function maybe_create_connections_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'user_connections';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                requester_id bigint(20) UNSIGNED NOT NULL,
                requested_id bigint(20) UNSIGNED NOT NULL,
                status varchar(20) NOT NULL DEFAULT 'pending',
                created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY requester_id (requester_id),
                KEY requested_id (requested_id),
                KEY status (status),
                UNIQUE KEY unique_connection (requester_id, requested_id)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }

    /**
     * Remove a specific notification from the user's notifications
     */
    private function remove_connection_notification($user_id, $reference_id, $type) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'user_notifications';

        // Delete the notification from the user's notifications table
        $result = $wpdb->delete(
            $table_name,
            array(
                'recipient_id' => $user_id,
                'type' => $type,
                'reference_id' => $reference_id,
            ),
            array('%d', '%s', '%d')
        );

        if ($result === false) {
            error_log("Failed to remove connection notification: " . $wpdb->last_error);
        } 
    }
}

// Initialize the connections API
TourismIQ_Connections::get_instance();
