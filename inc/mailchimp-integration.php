<?php
/**
 * Mailchimp Integration
 * 
 * Handles newsletter subscriptions and email list management
 */

if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Mailchimp_Integration {
    
    private $api_key;
    private $list_id;
    private $server_prefix;
    
    public function __construct() {
        // Get Mailchimp credentials from WordPress options or constants
        $this->api_key = defined('MAILCHIMP_API_KEY') ? MAILCHIMP_API_KEY : get_option('tourismiq_mailchimp_api_key');
        $this->list_id = defined('MAILCHIMP_LIST_ID') ? MAILCHIMP_LIST_ID : get_option('tourismiq_mailchimp_list_id');
        
        // Extract server prefix from API key (e.g., us1, us2, etc.)
        if ($this->api_key) {
            $key_parts = explode('-', $this->api_key);
            $this->server_prefix = end($key_parts);
        }
        
        // Register REST API endpoint
        add_action('rest_api_init', array($this, 'register_endpoints'));
    }
    
    /**
     * Register REST API endpoints
     */
    public function register_endpoints() {
        register_rest_route('tourismiq/v1', '/mailchimp/subscribe', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_subscription'),
            'permission_callback' => '__return_true',
            'args' => array(
                'email' => array(
                    'required' => true,
                    'type' => 'string',
                    'format' => 'email',
                ),
                'firstName' => array(
                    'required' => false,
                    'type' => 'string',
                ),
                'lastName' => array(
                    'required' => false,
                    'type' => 'string',
                ),
            ),
        ));
    }
    
    /**
     * Handle subscription request
     */
    public function handle_subscription($request) {
        $email = sanitize_email($request->get_param('email'));
        $first_name = sanitize_text_field($request->get_param('firstName'));
        $last_name = sanitize_text_field($request->get_param('lastName'));
        
        if (!$email) {
            return new WP_Error('invalid_email', 'Invalid email address', array('status' => 400));
        }
        
        $result = $this->subscribe_to_list($email, $first_name, $last_name);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        return rest_ensure_response(array(
            'success' => true,
            'message' => 'Successfully subscribed to newsletter',
        ));
    }
    
    /**
     * Subscribe a user to the Mailchimp list
     */
    public function subscribe_to_list($email, $first_name = '', $last_name = '', $tags = array()) {
        error_log('Mailchimp subscribe_to_list called with email: ' . $email . ', tags: ' . implode(', ', $tags));
        
        if (!$this->api_key || !$this->list_id || !$this->server_prefix) {
            error_log('Mailchimp Integration Error: Missing API credentials - API Key: ' . (!empty($this->api_key) ? 'set' : 'missing') . ', List ID: ' . (!empty($this->list_id) ? 'set' : 'missing') . ', Server: ' . (!empty($this->server_prefix) ? $this->server_prefix : 'missing'));
            return new WP_Error('mailchimp_not_configured', 'Mailchimp integration is not properly configured', array('status' => 500));
        }
        
        $subscriber_hash = md5(strtolower($email));
        $api_url = "https://{$this->server_prefix}.api.mailchimp.com/3.0/lists/{$this->list_id}/members/{$subscriber_hash}";
        
        $merge_fields = array();
        if ($first_name) {
            $merge_fields['FNAME'] = $first_name;
        }
        if ($last_name) {
            $merge_fields['LNAME'] = $last_name;
        }
        
        $data = array(
            'email_address' => $email,
            'status_if_new' => 'subscribed',
            'status' => 'subscribed',
        );
        
        if (!empty($merge_fields)) {
            $data['merge_fields'] = $merge_fields;
        }
        
        if (!empty($tags)) {
            $data['tags'] = $tags;
        }
        
        $args = array(
            'method' => 'PUT',
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode('user:' . $this->api_key),
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($data),
            'timeout' => 10,
        );
        
        $response = wp_remote_request($api_url, $args);
        
        if (is_wp_error($response)) {
            error_log('Mailchimp API Error: ' . $response->get_error_message());
            return new WP_Error('mailchimp_api_error', 'Failed to connect to Mailchimp', array('status' => 500));
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        
        if ($response_code >= 400) {
            $error_message = isset($response_body['detail']) ? $response_body['detail'] : 'Unknown error occurred';
            error_log('Mailchimp API Error (' . $response_code . '): ' . $error_message);
            
            // Handle specific error cases
            if ($response_code === 400 && strpos($error_message, 'already a list member') !== false) {
                // User is already subscribed, this is okay
                return true;
            }
            
            return new WP_Error('mailchimp_subscription_failed', $error_message, array('status' => $response_code));
        }
        
        return true;
    }
    
    /**
     * Update user's subscription status
     */
    public function update_subscription_status($email, $status) {
        if (!in_array($status, array('subscribed', 'unsubscribed', 'pending', 'cleaned'))) {
            return new WP_Error('invalid_status', 'Invalid subscription status');
        }
        
        $subscriber_hash = md5(strtolower($email));
        $api_url = "https://{$this->server_prefix}.api.mailchimp.com/3.0/lists/{$this->list_id}/members/{$subscriber_hash}";
        
        $args = array(
            'method' => 'PATCH',
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode('user:' . $this->api_key),
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode(array('status' => $status)),
            'timeout' => 10,
        );
        
        $response = wp_remote_request($api_url, $args);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        return $response_code < 400;
    }
    
    /**
     * Add tags to a subscriber
     */
    public function add_subscriber_tags($email, $tags) {
        if (!is_array($tags) || empty($tags)) {
            return false;
        }
        
        $subscriber_hash = md5(strtolower($email));
        $api_url = "https://{$this->server_prefix}.api.mailchimp.com/3.0/lists/{$this->list_id}/members/{$subscriber_hash}/tags";
        
        $tag_data = array();
        foreach ($tags as $tag) {
            $tag_data[] = array(
                'name' => $tag,
                'status' => 'active',
            );
        }
        
        $args = array(
            'method' => 'POST',
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode('user:' . $this->api_key),
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode(array('tags' => $tag_data)),
            'timeout' => 10,
        );
        
        $response = wp_remote_request($api_url, $args);
        
        return !is_wp_error($response) && wp_remote_retrieve_response_code($response) < 400;
    }
}

// Initialize the Mailchimp integration
new TourismIQ_Mailchimp_Integration();

/**
 * Helper function to subscribe a user to Mailchimp
 */
function tourismiq_subscribe_to_mailchimp($email, $first_name = '', $last_name = '', $tags = array()) {
    $mailchimp = new TourismIQ_Mailchimp_Integration();
    return $mailchimp->subscribe_to_list($email, $first_name, $last_name, $tags);
}

/**
 * Helper function to update subscription status
 */
function tourismiq_update_mailchimp_subscription($email, $status) {
    $mailchimp = new TourismIQ_Mailchimp_Integration();
    return $mailchimp->update_subscription_status($email, $status);
}