<?php
/**
 * CORS Handler for TourismIQ REST API
 * 
 * Handles Cross-Origin Resource Sharing (CORS) headers for all API endpoints
 * This is critical for the headless WordPress setup with separate frontend deployment
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add CORS headers to all REST API responses
 */
function tourismiq_handle_rest_api_cors() {
    // Get the request origin
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    
    // Define allowed origins for production
    $allowed_origins = [
        'http://localhost:3000',
        'https://localhost:3000',
        'https://mytourismiq.com', // Production frontend domain
        'https://www.mytourismiq.com', // Production frontend with www
        'https://hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com', // WPEngine frontend domain
        'https://mytourismiq.wpenginepowered.com', // WordPress backend domain
        'https://tourismqwp-production.up.railway.app', // Railway deployment
    ];
    
    // Check if the origin is allowed
    if (in_array($origin, $allowed_origins)) {
        // Remove any existing headers to prevent duplicates
        remove_filter('rest_pre_serve_request', 'rest_send_cors_headers');
        
        // Set CORS headers
        header("Access-Control-Allow-Origin: {$origin}");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, Cookie, X-WP-Nonce, Cache-Control, Pragma, Expires');
        header('Access-Control-Expose-Headers: X-WP-Total, X-WP-TotalPages, Link, Set-Cookie');
        header('Access-Control-Max-Age: 3600');
    }
}

/**
 * Handle preflight OPTIONS requests for REST API
 */
function tourismiq_handle_preflight_request() {
    // Check if this is a REST API request
    $rest_prefix = trailingslashit(rest_get_url_prefix());
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    
    if (strpos($request_uri, $rest_prefix) !== false || strpos($request_uri, 'wp-json') !== false) {
        // Add CORS headers for all REST API requests
        tourismiq_handle_rest_api_cors();
        
        // If it's an OPTIONS request, send 200 OK and exit
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            status_header(200);
            exit();
        }
    }
}

/**
 * Add CORS headers to REST API responses
 */
function tourismiq_rest_pre_serve_request($served, $result, $request, $server) {
    // Add CORS headers to all REST responses
    tourismiq_handle_rest_api_cors();
    
    return $served;
}

/**
 * Initialize CORS handling
 */
function tourismiq_init_cors() {
    // Handle preflight requests as early as possible
    add_action('init', 'tourismiq_handle_preflight_request', 1);
    
    // Add CORS headers to REST API responses
    add_filter('rest_pre_serve_request', 'tourismiq_rest_pre_serve_request', 15, 4);
    
    // Also handle CORS for direct REST API init
    add_action('rest_api_init', 'tourismiq_handle_rest_api_cors', 1);
    
    // WP Engine specific: Add headers on send_headers action
    add_action('send_headers', 'tourismiq_send_cors_headers', 1);
}

// Initialize CORS handling
tourismiq_init_cors();

/**
 * Send CORS headers (WP Engine compatible)
 */
function tourismiq_send_cors_headers() {
    // Check if this is a REST API request
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($request_uri, 'wp-json') !== false) {
        tourismiq_handle_rest_api_cors();
    }
}

/**
 * Debug function to log CORS issues
 */
function tourismiq_log_cors_debug($message) {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('[TourismIQ CORS]: ' . $message);
    }
}

// Log CORS requests in debug mode
if (defined('WP_DEBUG') && WP_DEBUG) {
    add_action('init', function() {
        $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : 'no-origin';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'unknown';
        $uri = $_SERVER['REQUEST_URI'] ?? 'unknown';
        
        if (strpos($uri, 'wp-json') !== false || strpos($uri, rest_get_url_prefix()) !== false) {
            tourismiq_log_cors_debug("Request: {$method} {$uri} from origin: {$origin}");
        }
    });
}