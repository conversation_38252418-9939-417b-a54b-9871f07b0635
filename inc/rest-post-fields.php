<?php
/**
 * Add ACF fields to the WordPress REST API post response
 */

// Add ACF fields to the REST API response for posts
add_filter('rest_prepare_post', 'tourismiq_add_acf_fields_to_post_rest', 10, 3);

function tourismiq_add_acf_fields_to_post_rest($response, $post, $request) {
    $data = $response->get_data();
    
    // Fix broken featured_media references
    if (!empty($data['featured_media']) && $data['featured_media'] > 0) {
        $media_post = get_post($data['featured_media']);
        if (!$media_post || $media_post->post_type !== 'attachment') {
            // Featured media doesn't exist, clear the reference
            $data['featured_media'] = 0;
            update_post_meta($post->ID, '_thumbnail_id', 0);
            error_log("Cleared broken featured_media reference {$data['featured_media']} for post {$post->ID}");
        }
    }
    
    // Add ACF fields if available
    if (function_exists('get_fields')) {
        $acf_fields = get_fields($post->ID);
        if ($acf_fields) {
            // Process video fields to create video_embed_url
            $acf_fields = tourismiq_process_video_embed_fields($acf_fields);
            $data['acf'] = $acf_fields;
        }
    }
    
    // Set the modified data back to the response
    $response->set_data($data);
    
    return $response;
}

/**
 * Process video ACF fields to create embeddable video_embed_url
 */
function tourismiq_process_video_embed_fields($acf_fields) {
    // Process video post type fields
    if (!empty($acf_fields['video_source'])) {
        $video_source = $acf_fields['video_source'];
        $embed_url = null;
        
        switch ($video_source) {
            case 'youtube':
                if (!empty($acf_fields['youtube_id'])) {
                    $embed_url = tourismiq_convert_youtube_to_embed($acf_fields['youtube_id']);
                }
                break;
                
            case 'vimeo':
                if (!empty($acf_fields['vimeo_id'])) {
                    $embed_url = tourismiq_convert_vimeo_to_embed($acf_fields['vimeo_id']);
                }
                break;
                
            case 'twitter':
                if (!empty($acf_fields['x'])) {
                    $embed_url = $acf_fields['x'];
                }
                break;
                
            case 'linkedin':
                if (!empty($acf_fields['linkedin_iframe'])) {
                    $embed_url = $acf_fields['linkedin_iframe'];
                }
                break;
        }
        
        if ($embed_url) {
            $acf_fields['video_embed_url'] = $embed_url;
        }
    }
    
    // Process webinar post type fields
    if (!empty($acf_fields['youtube_url']) || !empty($acf_fields['vimeo_url'])) {
        $webinar_embed_url = null;
        
        // Prioritize YouTube over Vimeo
        if (!empty($acf_fields['youtube_url'])) {
            $webinar_embed_url = tourismiq_convert_youtube_to_embed($acf_fields['youtube_url']);
        } elseif (!empty($acf_fields['vimeo_url'])) {
            $webinar_embed_url = tourismiq_convert_vimeo_to_embed($acf_fields['vimeo_url']);
        }
        
        if ($webinar_embed_url) {
            $acf_fields['webinar_embed_url'] = $webinar_embed_url;
        }
    }
    
    return $acf_fields;
}

/**
 * Convert YouTube URL to embeddable iframe URL
 */
function tourismiq_convert_youtube_to_embed($youtube_url) {
    if (empty($youtube_url)) return null;
    
    // Extract video ID from various YouTube URL formats
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $youtube_url, $matches);
    
    if (!empty($matches[1])) {
        return "https://www.youtube.com/embed/" . $matches[1];
    }
    
    return null;
}

/**
 * Convert Vimeo URL to embeddable iframe URL
 */
function tourismiq_convert_vimeo_to_embed($vimeo_url) {
    if (empty($vimeo_url)) return null;
    
    // Handle various Vimeo URL formats
    $patterns = [
        // Standard video URLs: vimeo.com/123456789
        '/(?:vimeo\.com\/)([0-9]+)/',
        // User channel with video: vimeo.com/user123/videoname or vimeo.com/channels/channel/123456
        '/(?:vimeo\.com\/(?:user[0-9]+\/|channels\/[^\/]+\/))([^\/\?]+)/',
        // Private videos: vimeo.com/123456789/hash
        '/(?:vimeo\.com\/)([0-9]+)\/[a-zA-Z0-9]+/',
        // Player URLs: player.vimeo.com/video/123456789
        '/(?:player\.vimeo\.com\/video\/)([0-9]+)/',
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $vimeo_url, $matches)) {
            if (!empty($matches[1])) {
                // If it's already a numeric ID, use it
                if (is_numeric($matches[1])) {
                    return "https://player.vimeo.com/video/" . $matches[1];
                }
                // If it's a video name/slug, we need to convert it
                // For now, try to extract any numeric ID from the URL
                if (preg_match('/([0-9]+)/', $vimeo_url, $id_matches)) {
                    return "https://player.vimeo.com/video/" . $id_matches[1];
                }
            }
        }
    }
    
    // Last resort: try to find any numeric ID in the URL
    if (preg_match('/([0-9]{6,})/', $vimeo_url, $matches)) {
        return "https://player.vimeo.com/video/" . $matches[1];
    }
    
    // If we can't parse it, return the original URL for manual handling
    return $vimeo_url;
}

// Register ACF field in the REST API schema for posts
add_action('rest_api_init', function() {
    register_rest_field('post', 'acf', array(
        'get_callback' => function($post_object) {
            if (!function_exists('get_fields')) {
                return null;
            }
            return get_fields($post_object['id']);
        },
        'update_callback' => null,
        'schema' => array(
            'description' => 'ACF fields for the post',
            'type' => 'object',
            'context' => array('view', 'edit'),
        ),
    ));
});