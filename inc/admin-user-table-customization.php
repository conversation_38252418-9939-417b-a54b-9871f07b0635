<?php
/**
 * WordPress Admin User Table Customization
 * 
 * Adds newsletter opt-in column and makes registration date sortable
 */

if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Admin_User_Table {
    
    public function __construct() {
        // Add custom columns to user table
        add_filter('manage_users_columns', array($this, 'add_user_columns'));
        add_filter('manage_users_custom_column', array($this, 'show_user_column_content'), 10, 3);
        
        // Make columns sortable
        add_filter('manage_users_sortable_columns', array($this, 'make_columns_sortable'));
        add_action('pre_get_users', array($this, 'handle_user_sorting'));
        
        // Add some CSS for better column styling
        add_action('admin_head-users.php', array($this, 'add_user_table_styles'));
    }
    
    /**
     * Add custom columns to user table
     */
    public function add_user_columns($columns) {
        // Add newsletter opt-in column after email and ensure registered column is present
        $new_columns = array();
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;
            if ($key === 'email') {
                $new_columns['newsletter_optin'] = 'Opt-In';
            }
        }
        
        // Ensure the registered column is always present
        if (!isset($new_columns['registered'])) {
            $new_columns['registered'] = 'Registered';
        }
        
        return $new_columns;
    }
    
    /**
     * Show content for custom columns
     */
    public function show_user_column_content($value, $column_name, $user_id) {
        switch ($column_name) {
            case 'newsletter_optin':
                // Check ACF field first, fallback to user meta
                if (function_exists('get_field')) {
                    $newsletter_optin = get_field('newsletter_optin', 'user_' . $user_id);
                } else {
                    $newsletter_optin = get_user_meta($user_id, 'newsletter_optin', true);
                }
                
                if ($newsletter_optin) {
                    return '<span style="color: #28a745; font-weight: bold;">✓ Yes</span>';
                } else {
                    return '<span style="color: #6c757d;">— No</span>';
                }
                break;
                
            case 'registered':
                $user = get_user_by('id', $user_id);
                if ($user && !empty($user->user_registered)) {
                    // Format the date as "Jul 29, 2025"
                    $registered_date = mysql2date('M j, Y', $user->user_registered);
                    return $registered_date;
                }
                return '—';
                break;
        }
        
        return $value;
    }
    
    /**
     * Make columns sortable
     */
    public function make_columns_sortable($columns) {
        $columns['registered'] = 'registered'; // Make registration date sortable
        return $columns;
    }
    
    /**
     * Handle sorting for custom columns
     */
    public function handle_user_sorting($query) {
        if (!is_admin() || !$query->is_main_query()) {
            return;
        }
        
        $orderby = $query->get('orderby');
        
        switch ($orderby) {
            case 'registered':
                $query->set('orderby', 'registered');
                break;
        }
    }
    
    /**
     * Add CSS styles for better column appearance
     */
    public function add_user_table_styles() {
        ?>
        <style>
        .wp-list-table .column-newsletter_optin {
            width: 80px;
            text-align: center;
        }
        .wp-list-table .column-registered {
            width: 140px;
        }
        .wp-list-table .column-newsletter_optin .dashicons {
            width: 16px;
            height: 16px;
            font-size: 16px;
        }
        </style>
        <?php
    }
}

// Initialize the customization
new TourismIQ_Admin_User_Table();

/**
 * Add bulk action to export newsletter subscribers
 */
add_filter('bulk_actions-users', 'add_newsletter_bulk_actions');
function add_newsletter_bulk_actions($bulk_actions) {
    $bulk_actions['export_newsletter_subscribers'] = 'Export Newsletter Subscribers';
    return $bulk_actions;
}

/**
 * Handle the bulk export action
 */
add_filter('handle_bulk_actions-users', 'handle_newsletter_export_bulk_action', 10, 3);
function handle_newsletter_export_bulk_action($redirect_to, $doaction, $user_ids) {
    if ($doaction !== 'export_newsletter_subscribers') {
        return $redirect_to;
    }
    
    if (empty($user_ids)) {
        return $redirect_to;
    }
    
    // Get newsletter subscribers from selected users
    $subscribers = array();
    foreach ($user_ids as $user_id) {
        $user = get_user_by('id', $user_id);
        if (!$user) continue;
        
        // Check if user opted in to newsletter
        if (function_exists('get_field')) {
            $newsletter_optin = get_field('newsletter_optin', 'user_' . $user_id);
        } else {
            $newsletter_optin = get_user_meta($user_id, 'newsletter_optin', true);
        }
        
        if ($newsletter_optin) {
            $subscribers[] = array(
                'ID' => $user->ID,
                'Username' => $user->user_login,
                'Email' => $user->user_email,
                'First Name' => $user->first_name,
                'Last Name' => $user->last_name,
                'Registered' => $user->user_registered,
                'Newsletter Optin' => 'Yes'
            );
        }
    }
    
    if (empty($subscribers)) {
        $redirect_to = add_query_arg('newsletter_export_result', 'no_subscribers', $redirect_to);
        return $redirect_to;
    }
    
    // Generate CSV
    $filename = 'newsletter-subscribers-' . date('Y-m-d-H-i-s') . '.csv';
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // CSV headers
    fputcsv($output, array('ID', 'Username', 'Email', 'First Name', 'Last Name', 'Registered', 'Newsletter Optin'));
    
    // CSV data
    foreach ($subscribers as $subscriber) {
        fputcsv($output, $subscriber);
    }
    
    fclose($output);
    exit;
}

/**
 * Show admin notice for export results
 */
add_action('admin_notices', 'newsletter_export_admin_notices');
function newsletter_export_admin_notices() {
    if (!isset($_REQUEST['newsletter_export_result'])) {
        return;
    }
    
    $result = $_REQUEST['newsletter_export_result'];
    
    if ($result === 'no_subscribers') {
        ?>
        <div class="notice notice-warning is-dismissible">
            <p><strong>No newsletter subscribers found</strong> in the selected users.</p>
        </div>
        <?php
    }
}

/**
 * Add quick filter links for newsletter subscribers
 */
add_action('restrict_manage_users', 'add_newsletter_filter');
function add_newsletter_filter() {
    $current_filter = isset($_GET['newsletter_filter']) ? $_GET['newsletter_filter'] : '';
    ?>
    <select name="newsletter_filter" id="newsletter_filter">
        <option value="">All Newsletter Status</option>
        <option value="subscribed" <?php selected($current_filter, 'subscribed'); ?>>Newsletter Subscribers</option>
        <option value="not_subscribed" <?php selected($current_filter, 'not_subscribed'); ?>>Not Subscribed</option>
    </select>
    <?php
}

/**
 * Handle newsletter filter
 */
add_action('pre_get_users', 'handle_newsletter_filter');
function handle_newsletter_filter($query) {
    global $pagenow;
    
    if ($pagenow !== 'users.php' || !is_admin()) {
        return;
    }
    
    $newsletter_filter = isset($_GET['newsletter_filter']) ? $_GET['newsletter_filter'] : '';
    
    if (empty($newsletter_filter)) {
        return;
    }
    
    $meta_query = $query->get('meta_query') ?: array();
    
    switch ($newsletter_filter) {
        case 'subscribed':
            $meta_query[] = array(
                'key' => 'newsletter_optin',
                'value' => '1',
                'compare' => '='
            );
            break;
            
        case 'not_subscribed':
            $meta_query[] = array(
                'relation' => 'OR',
                array(
                    'key' => 'newsletter_optin',
                    'value' => '1',
                    'compare' => '!='
                ),
                array(
                    'key' => 'newsletter_optin',
                    'compare' => 'NOT EXISTS'
                )
            );
            break;
    }
    
    if (!empty($meta_query)) {
        $query->set('meta_query', $meta_query);
    }
}