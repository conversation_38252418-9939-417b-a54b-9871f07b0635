<?php
/**
 * REST API Endpoints
 *
 * Handles custom REST API endpoints for the theme.
 */

// Create custom roles if they don't exist
add_action('init', function() {
    // Add founder role if it doesn't exist
    if (!get_role('founder')) {
        add_role('founder', 'Founder', array(
            'read' => true,
            'edit_posts' => true,
            'edit_others_posts' => true,
            'publish_posts' => true,
            'edit_published_posts' => true,
            'delete_posts' => true,
            'delete_others_posts' => true,
            'delete_published_posts' => true,
            'edit_pages' => true,
            'edit_others_pages' => true,
            'publish_pages' => true,
            'edit_published_pages' => true,
            'delete_pages' => true,
            'delete_others_pages' => true,
            'delete_published_pages' => true,
            'upload_files' => true,
        ));
    }
    
    // Add member role if it doesn't exist
    if (!get_role('member')) {
        add_role('member', 'Member', array(
            'read' => true,
            'edit_posts' => true,
            'publish_posts' => true,
            'edit_published_posts' => true,
            'delete_posts' => true,
            'delete_published_posts' => true,
            'upload_files' => true,
        ));
    }
});

// Register REST API routes
add_action('rest_api_init', function() {
    // Members endpoint
    register_rest_route('tourismiq/v1', '/members', [
        'methods' => 'GET',
        'callback' => 'tourismiq_get_members',
        'permission_callback' => '__return_true',
    ]);

    // Individual member by username/slug endpoint
    register_rest_route('tourismiq/v1', '/members/(?P<username>[a-zA-Z0-9\-_]+)', [
        'methods' => 'GET',
        'callback' => 'tourismiq_get_member_by_username',
        'permission_callback' => '__return_true',
        'args' => [
            'username' => [
                'validate_callback' => function($param, $request, $key) {
                    return is_string($param);
                }
            ],
        ],
    ]);

    // Current user endpoint
    register_rest_route('wp/v2', '/users/me', [
        'methods' => 'GET',
        'callback' => 'tourismiq_get_current_user',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
    ]);

    // Custom endpoint for updating ACF fields for current user
    register_rest_route('tourismiq/v1', '/users/me/acf', [
        'methods' => 'POST',
        'callback' => 'tourismiq_update_user_acf',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
    ]);

    // Custom endpoint for uploading user cover image
    register_rest_route('tourismiq/v1', '/users/me/cover-image', [
        'methods' => 'POST',
        'callback' => 'tourismiq_upload_user_cover_image',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
    ]);

    // Custom endpoint for updating ACF fields for posts
    register_rest_route('tourismiq/v1', '/posts/(?P<id>\d+)/acf', [
        'methods' => 'POST',
        'callback' => 'tourismiq_update_post_acf',
        'permission_callback' => function($request) {
            $post_id = $request->get_param('id');
            return current_user_can('edit_post', $post_id);
        },
    ]);

    // Post editing endpoint
    register_rest_route('tourismiq/v1', '/posts/(?P<id>\d+)/edit', [
        'methods' => 'POST',
        'callback' => 'tourismiq_edit_post',
        'permission_callback' => function($request) {
            // Must be logged in
            if (!is_user_logged_in()) {
                return false;
            }
            
            $post_id = $request->get_param('id');
            $post = get_post($post_id);
            if (!$post) {
                return false;
            }
            
            $current_user_id = get_current_user_id();
            
            // Only allow post author or admin to edit
            // Regular members can only edit their own posts
            return ($current_user_id == $post->post_author) || current_user_can('edit_others_posts');
        },
    ]);

    // Post deletion endpoint
    register_rest_route('tourismiq/v1', '/posts/(?P<id>\d+)/delete', [
        'methods' => 'DELETE',
        'callback' => 'tourismiq_delete_post',
        'permission_callback' => function($request) {
            // Must be logged in
            if (!is_user_logged_in()) {
                return false;
            }
            
            $post_id = $request->get_param('id');
            $post = get_post($post_id);
            if (!$post) {
                return false;
            }
            
            $current_user_id = get_current_user_id();
            
            // Only allow post author or admin to delete
            // Regular members can only delete their own posts
            return ($current_user_id == $post->post_author) || current_user_can('delete_others_posts');
        },
    ]);


});

/**
 * Register authentication endpoints
 */
function tourismiq_register_auth_endpoints() {
    // Auth status endpoint
    register_rest_route('tourismiq/v1', '/auth/status', array(
        'methods' => 'GET',
        'callback' => 'tourismiq_auth_status',
        'permission_callback' => '__return_true',
    ));

    // Password verification endpoint
    register_rest_route('tourismiq/v1', '/auth/verify-password', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_verify_password',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
    ));
}
add_action('rest_api_init', 'tourismiq_register_auth_endpoints');

/**
 * Get current user data
 */
function tourismiq_get_current_user($request) {
    $current_user = wp_get_current_user();

    if (!$current_user->exists()) {
        return new WP_Error('not_logged_in', 'User is not logged in', array('status' => 401));
    }

    // Get user meta
    $user_meta = get_user_meta($current_user->ID);

    // Get ACF fields if available
    $acf_fields = function_exists('get_fields') ? get_fields('user_' . $current_user->ID) : [];

    // Prepare user data
    $user_data = [
        'id' => $current_user->ID,
        'username' => $current_user->user_login,
        'slug' => $current_user->user_nicename,
        'name' => $current_user->display_name,
        'first_name' => $current_user->first_name,
        'last_name' => $current_user->last_name,
        'email' => $current_user->user_email,
        'url' => $current_user->user_url,
        'description' => $current_user->description,
        'registered' => $current_user->user_registered,
        'roles' => $current_user->roles,
        'capabilities' => $current_user->allcaps,
        // Let frontend handle avatar fallback
        'avatar_urls' => [
            '24' => null,
            '48' => null,
            '96' => null,
            'thumbnail' => null,
        ],
        'meta' => array_map(function($a) { return $a[0]; }, $user_meta),
        'acf' => $acf_fields,
    ];

    return new WP_REST_Response($user_data, 200);
}

/**
 * Get members for the REST API
 */
function tourismiq_get_members($request) {
    // Get pagination parameters
    $page = $request->get_param('page') ? intval($request->get_param('page')) : 1;
    $per_page = $request->get_param('per_page') ? intval($request->get_param('per_page')) : 20;
    $offset = ($page - 1) * $per_page;
    
    // Get search parameter
    $search = $request->get_param('search') ? sanitize_text_field($request->get_param('search')) : '';

    // Build query arguments
    $query_args = [
        'number' => $per_page,
        'offset' => $offset,
        'count_total' => true,
        'orderby' => 'registered',
        'order' => 'DESC', // Show newest members first
    ];

    // Add search functionality if search term provided
    if (!empty($search)) {
        // Use search parameter for basic user fields
        $query_args['search'] = '*' . $search . '*';
        $query_args['search_columns'] = ['user_login', 'user_email', 'user_nicename', 'display_name'];
        
        // Also search in custom meta fields and ACF fields
        $search_meta_query = [
            'relation' => 'OR',
            [
                'key' => 'first_name',
                'value' => $search,
                'compare' => 'LIKE'
            ],
            [
                'key' => 'last_name', 
                'value' => $search,
                'compare' => 'LIKE'
            ],
            [
                'key' => 'description',
                'value' => $search,
                'compare' => 'LIKE'
            ],
            [
                'key' => 'bio', // ACF bio field
                'value' => $search,
                'compare' => 'LIKE'
            ],
            [
                'key' => 'job_title',
                'value' => $search,
                'compare' => 'LIKE'
            ],
            [
                'key' => 'company',
                'value' => $search,
                'compare' => 'LIKE'
            ],
            [
                'key' => 'location',
                'value' => $search,
                'compare' => 'LIKE'
            ]
        ];
        
        // Combine existing meta_query with search meta_query
        $query_args['meta_query'] = [
            'relation' => 'AND',
            $search_meta_query
        ];
    }

    // Get all users with pagination and search
    $user_query = new WP_User_Query($query_args);

    $total_users = $user_query->get_total();
    $total_pages = ceil($total_users / $per_page);
    $users = $user_query->get_results();

    $founders = [];
    $members = [];

    foreach ($users as $user) {
        // Get user role from WordPress roles (primary source)
        $user_roles = $user->roles;
        $user_role = 'member'; // Default fallback
        
        // Check if user has founder role
        if (in_array('founder', $user_roles)) {
            $user_role = 'Founder';
        } elseif (in_array('administrator', $user_roles)) {
            $user_role = 'Admin';
        } elseif (in_array('member', $user_roles)) {
            $user_role = 'Member';
        } elseif (in_array('subscriber', $user_roles)) {
            $user_role = 'Member';
        } elseif (in_array('author', $user_roles)) {
            $user_role = 'Member';
        } elseif (in_array('contributor', $user_roles)) {
            $user_role = 'Member';
        } elseif (in_array('editor', $user_roles)) {
            $user_role = 'Member';
        }

        // Get user categories
        $user_categories = get_user_meta($user->ID, 'categories', true) ?: [];
        if (!is_array($user_categories)) {
            $user_categories = [$user_categories];
        }

        // Get location
        $user_location = get_user_meta($user->ID, 'location', true) ?: '';

        // Get cover image using ACF function
        $cover_image = get_field('cover_image', 'user_' . $user->ID);
        $user_cover = '';
        
        if ($cover_image) {
            if (is_array($cover_image) && isset($cover_image['url'])) {
                // ACF image field returns array with URL
                $user_cover = $cover_image['url'];
            } elseif (is_numeric($cover_image)) {
                // If it's just an attachment ID
                $cover_image_data = wp_get_attachment_image_src($cover_image, 'full');
                $user_cover = $cover_image_data ? $cover_image_data[0] : '';
            } elseif (is_string($cover_image)) {
                // If it's already a URL string
                $user_cover = $cover_image;
            }
        }

        // Get avatar - check for ACF profile_picture first, then let frontend handle fallback
        $profile_picture = get_field('profile_picture', 'user_' . $user->ID);
        $avatar_url = null; // Let frontend handle the fallback to placeholder
        
        if ($profile_picture && is_array($profile_picture) && isset($profile_picture['url'])) {
            $avatar_url = $profile_picture['url'];
        }

        // Get bio - prefer ACF bio field over WordPress description
        $bio = get_field('bio', 'user_' . $user->ID) ?: $user->description;

        // Get social links - check ACF fields first, then user meta
        $social_links = [
            'website' => get_field('social_website', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_website', true),
            'twitter' => get_field('twitter', 'user_' . $user->ID) ?: get_field('social_twitter', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_twitter', true),
            'facebook' => get_field('facebook', 'user_' . $user->ID) ?: get_field('social_facebook', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_facebook', true),
            'linkedin' => get_field('linkedin', 'user_' . $user->ID) ?: get_field('social_linkedin', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_linkedin', true),
            'instagram' => get_field('instagram', 'user_' . $user->ID) ?: get_field('social_instagram', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_instagram', true),
        ];

        // Get contact info - prioritize core WordPress website field, then ACF, then user meta
        $contact_info = [
            'email' => $user->user_email,
            'phone' => get_field('phone', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'phone', true),
            'website' => $user->user_url ?: get_field('website', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'website', true),
        ];

        // Create user data object
        $user_data = [
            'id' => $user->ID,
            'name' => $user->display_name,
            'firstName' => $user->first_name,
            'lastName' => $user->last_name,
            'username' => $user->user_login,
            'slug' => $user->user_nicename,
            'role' => $user_role,
            'bio' => $bio,
            'avatar' => $avatar_url,
            'avatarUrl' => $avatar_url, // Add avatarUrl for compatibility
            'profile_picture' => $profile_picture, // Include raw ACF profile_picture data
            'location' => $user_location,
            'featured' => strtolower($user_role) === 'founder' || strtolower($user_role) === 'admin',
            'categories' => $user_categories,
            'coverImage' => $user_cover,
            'job_title' => get_field('job_title', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'job_title', true) ?: '',
            'company' => get_field('company', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'company', true) ?: '',
            'socialLinks' => $social_links,
            'contactInfo' => $contact_info,
            'user_registered' => $user->user_registered,
            'roles' => [strtolower($user_role)], // Add roles array for frontend compatibility
        ];

        // Add to appropriate array based on role (case-insensitive comparison)
        if (strcasecmp($user_role, 'founder') === 0) {
            $founders[] = $user_data;
        } elseif (strcasecmp($user_role, 'admin') === 0) {
            $founders[] = $user_data; // Admins go in founders section for prominence
        } else {
            $members[] = $user_data;
        }
    }

    // Prepare the structured response with separate arrays
    $response_data = [
        'founders' => $founders,
        'members' => $members
    ];

    // Set the response headers
    $response = new WP_REST_Response($response_data);
    $response->header('X-WP-Total', $total_users);
    $response->header('X-WP-TotalPages', $total_pages);

    return $response;
}

/**
 * Get a single member by username/slug
 */
function tourismiq_get_member_by_username($request) {
    $username = $request->get_param('username');
    
    if (empty($username)) {
        return new WP_Error('invalid_username', 'Username parameter is required', array('status' => 400));
    }

    // Try to find user by username (user_login) or nicename (slug)
    $user = get_user_by('login', $username);
    if (!$user) {
        $user = get_user_by('slug', $username);
    }
    
    if ($user) {
        // Force fresh user data - clear user cache and reload
        clean_user_cache($user->ID);
        wp_cache_delete($user->ID, 'users');
        wp_cache_delete($user->ID, 'user_meta');
        
        // Get fresh user data
        $user = get_userdata($user->ID);
    }
    
    if (!$user) {
        return new WP_Error('member_not_found', 'Member not found', array('status' => 404));
    }
    
    // Allow administrators to be viewed as profiles
    // No exclusion needed - all users should have viewable profiles

    // Get user role from WordPress roles (primary source)
    $user_roles = $user->roles;
    $user_role = 'member'; // Default fallback
    
    // Check if user has founder role
    if (in_array('founder', $user_roles)) {
        $user_role = 'Founder';
    } elseif (in_array('administrator', $user_roles)) {
        $user_role = 'Admin';
    } elseif (in_array('member', $user_roles)) {
        $user_role = 'Member';
    } elseif (in_array('subscriber', $user_roles)) {
        $user_role = 'Member';
    } elseif (in_array('author', $user_roles)) {
        $user_role = 'Member';
    } elseif (in_array('contributor', $user_roles)) {
        $user_role = 'Member';
    } elseif (in_array('editor', $user_roles)) {
        $user_role = 'Member';
    }

    // Get user categories
    $user_categories = get_user_meta($user->ID, 'categories', true) ?: [];
    if (!is_array($user_categories)) {
        $user_categories = [$user_categories];
    }

    // Get location
    $user_location = get_user_meta($user->ID, 'location', true) ?: '';

    // Get cover image using ACF function
    $cover_image = get_field('cover_image', 'user_' . $user->ID);
    $user_cover = '';
    
    if ($cover_image) {
        if (is_array($cover_image) && isset($cover_image['url'])) {
            // ACF image field returns array with URL
            $user_cover = $cover_image['url'];
        } elseif (is_numeric($cover_image)) {
            // If it's just an attachment ID
            $cover_image_data = wp_get_attachment_image_src($cover_image, 'full');
            $user_cover = $cover_image_data ? $cover_image_data[0] : '';
        } elseif (is_string($cover_image)) {
            // If it's already a URL string
            $user_cover = $cover_image;
        }
    }

    // Get avatar - check for ACF profile_picture first, then let frontend handle fallback
    $profile_picture = get_field('profile_picture', 'user_' . $user->ID);
    $avatar_url = null; // Let frontend handle the fallback to placeholder
    
    if ($profile_picture && is_array($profile_picture) && isset($profile_picture['url'])) {
        $avatar_url = $profile_picture['url'];
    }

    // Get bio - prefer ACF bio field over WordPress description
    $bio = get_field('bio', 'user_' . $user->ID) ?: $user->description;

    // Get social links - check ACF fields first, then user meta
    $social_links = [
        'website' => get_field('social_website', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_website', true),
        'twitter' => get_field('twitter', 'user_' . $user->ID) ?: get_field('social_twitter', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_twitter', true),
        'facebook' => get_field('facebook', 'user_' . $user->ID) ?: get_field('social_facebook', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_facebook', true),
        'linkedin' => get_field('linkedin', 'user_' . $user->ID) ?: get_field('social_linkedin', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_linkedin', true),
        'instagram' => get_field('instagram', 'user_' . $user->ID) ?: get_field('social_instagram', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'social_instagram', true),
    ];

    // Get contact info - prioritize core WordPress website field, then ACF, then user meta
    $contact_info = [
        'email' => $user->user_email,
        'phone' => get_field('phone', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'phone', true),
        'website' => $user->user_url ?: get_field('website', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'website', true),
    ];

    // Create user data object
    $user_data = [
        'id' => $user->ID,
        'name' => $user->display_name,
        'firstName' => $user->first_name,
        'lastName' => $user->last_name,
        'username' => $user->user_login,
        'slug' => $user->user_nicename,
        'role' => $user_role,
        'bio' => $bio,
        'avatar' => $avatar_url,
        'avatarUrl' => $avatar_url, // Add avatarUrl for compatibility
        'profile_picture' => $profile_picture, // Include raw ACF profile_picture data
        'location' => $user_location,
        'featured' => strtolower($user_role) === 'founder',
        'categories' => $user_categories,
        'coverImage' => $user_cover,
        'job_title' => get_field('job_title', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'job_title', true) ?: '',
        'company' => get_field('company', 'user_' . $user->ID) ?: get_user_meta($user->ID, 'company', true) ?: '',
        'socialLinks' => $social_links,
        'contactInfo' => $contact_info,
        'user_registered' => $user->user_registered,
        'roles' => $user_roles, // Return actual WordPress roles array
    ];

    return new WP_REST_Response($user_data, 200);
}

/**
 * Update ACF fields for the current user
 */
function tourismiq_update_user_acf($request) {
    $current_user = wp_get_current_user();

    if (!$current_user->exists()) {
        return new WP_Error('not_logged_in', 'User is not logged in', array('status' => 401));
    }

    // Check permissions
    if (!tourismiq_check_permission('update_profile', $current_user->ID)) {
        return new WP_Error('insufficient_permissions', 'You do not have permission to update profile', array('status' => 403));
    }

    // Get the fields from the request
    $fields = $request->get_param('fields');

    if (empty($fields) || !is_array($fields)) {
        return new WP_Error('invalid_fields', 'Invalid fields parameter', array('status' => 400));
    }

    // Define allowed ACF fields for security
    $allowed_fields = [
        'bio', 'location', 'job_title', 'company', 'phone', 'website',
        'twitter', 'facebook', 'linkedin', 'instagram',
        'profile_picture', 'cover_image'
    ];

    // Sanitize and validate fields
    $sanitized_fields = [];
    foreach ($fields as $field_name => $field_value) {
        $field_name = tourismiq_validate_input($field_name, 'text', 50);
        
        // Only allow whitelisted fields
        if (!in_array($field_name, $allowed_fields)) {
            continue;
        }

        // Sanitize based on field type
        switch ($field_name) {
            case 'bio':
                $sanitized_fields[$field_name] = tourismiq_validate_input($field_value, 'textarea', 1000);
                break;
            case 'website':
            case 'twitter':
            case 'facebook':
            case 'linkedin':
            case 'instagram':
                $sanitized_fields[$field_name] = tourismiq_validate_input($field_value, 'url');
                break;
            case 'profile_picture':
            case 'cover_image':
                // Handle file uploads separately - for now just validate as text
                $sanitized_fields[$field_name] = tourismiq_validate_input($field_value, 'text', 500);
                break;
            default:
                $sanitized_fields[$field_name] = tourismiq_validate_input($field_value, 'text', 255);
                break;
        }
    }


    $success = true;
    $updated_fields = [];

    // Check if ACF is available
    if (!function_exists('update_field')) {
        return new WP_Error('acf_not_available', 'ACF plugin is not available', array('status' => 500));
    }

    // Update each ACF field
    foreach ($sanitized_fields as $field_name => $field_value) {
        error_log("ACF Update Debug - Field: $field_name, Value: $field_value, User ID: " . $current_user->ID);
        
        // Map field names to their ACF field keys
        $field_key_map = [
            'cover_image' => 'field_cover_image',
            'profile_picture' => 'field_profile_picture',
            'bio' => 'field_bio',
            'job_title' => 'field_job_title',
            'company' => 'field_company',
            'location' => 'field_location',
            'phone' => 'field_phone',
            'website' => 'field_website',
            'linkedin' => 'field_linkedin',
            'twitter' => 'field_twitter',
            'facebook' => 'field_facebook',
            'instagram' => 'field_instagram'
        ];
        
        // Use field key if available, otherwise use field name
        $field_identifier = isset($field_key_map[$field_name]) ? $field_key_map[$field_name] : $field_name;
        
        // Update using the field key
        $update_result = update_field($field_identifier, $field_value, 'user_' . $current_user->ID);
        error_log("ACF Update Debug - Result with identifier '$field_identifier': " . ($update_result ? 'SUCCESS' : 'FAILED'));
        
        // Verify the update by reading the field back
        $verification = get_field($field_name, 'user_' . $current_user->ID);
        error_log("ACF Update Debug - Verification read back: " . ($verification ? 'FOUND DATA' : 'NO DATA'));
        if ($verification) {
            error_log("ACF Update Debug - Verification value: " . print_r($verification, true));
        }
        
        if ($update_result) {
            $updated_fields[$field_name] = $field_value;
        } else {
            $success = false;
            tourismiq_log_security('acf_update_failed', [
                'user_id' => $current_user->ID,
                'field_name' => $field_name
            ]);
        }
    }

    // Get all ACF fields after update
    $acf_fields = function_exists('get_fields') ? get_fields('user_' . $current_user->ID) : [];

    // Return response
    if ($success) {
        return new WP_REST_Response([
            'success' => true,
            'message' => 'ACF fields updated successfully',
            'updated_fields' => $updated_fields,
            'acf' => $acf_fields
        ], 200);
    } else {
        return new WP_REST_Response([
            'success' => false,
            'message' => 'Some fields could not be updated',
            'updated_fields' => $updated_fields,
            'acf' => $acf_fields
        ], 207); // 207 Multi-Status
    }
}

/**
 * Upload cover image for the current user
 */
function tourismiq_upload_user_cover_image($request) {
    $current_user = wp_get_current_user();

    if (!$current_user->exists()) {
        return new WP_Error('not_logged_in', 'User is not logged in', array('status' => 401));
    }

    // Check permissions
    if (!tourismiq_check_permission('update_profile', $current_user->ID)) {
        return new WP_Error('insufficient_permissions', 'You do not have permission to update profile', array('status' => 403));
    }

    // Get uploaded file
    $files = $request->get_file_params();
    
    if (empty($files) || !isset($files['cover_image'])) {
        return new WP_Error('no_file', 'No file uploaded', array('status' => 400));
    }

    $file = $files['cover_image'];

    // Validate file type
    $allowed_types = array('image/jpeg', 'image/jpg', 'image/png', 'image/gif');
    if (!in_array($file['type'], $allowed_types)) {
        return new WP_Error('invalid_file_type', 'Invalid file type. Only JPG, PNG, and GIF images are allowed.', array('status' => 400));
    }

    // Validate file size (3MB max as per ACF settings)
    $max_size = 3 * 1024 * 1024; // 3MB in bytes
    if ($file['size'] > $max_size) {
        return new WP_Error('file_too_large', 'File size exceeds 3MB limit.', array('status' => 400));
    }

    // Include WordPress file handling functions
    if (!function_exists('wp_handle_upload')) {
        require_once(ABSPATH . 'wp-admin/includes/file.php');
    }

    // Handle the file upload
    $upload_overrides = array('test_form' => false);
    $movefile = wp_handle_upload($file, $upload_overrides);

    if ($movefile && !isset($movefile['error'])) {
        // Create attachment
        $attachment = array(
            'post_mime_type' => $movefile['type'],
            'post_title' => 'Cover Image - ' . $current_user->display_name,
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attach_id = wp_insert_attachment($attachment, $movefile['file']);

        if (!is_wp_error($attach_id)) {
            // Generate attachment metadata
            if (!function_exists('wp_generate_attachment_metadata')) {
                require_once(ABSPATH . 'wp-admin/includes/image.php');
            }
            
            $attach_data = wp_generate_attachment_metadata($attach_id, $movefile['file']);
            wp_update_attachment_metadata($attach_id, $attach_data);

            // Update the ACF field with the attachment ID
            $update_result = update_field('cover_image', $attach_id, 'user_' . $current_user->ID);

            if ($update_result) {
                // Get the updated cover image data
                $cover_image_data = wp_get_attachment_image_src($attach_id, 'full');
                
                return new WP_REST_Response([
                    'success' => true,
                    'message' => 'Cover image uploaded successfully',
                    'cover_image' => [
                        'id' => $attach_id,
                        'url' => $cover_image_data ? $cover_image_data[0] : $movefile['url'],
                        'width' => $cover_image_data ? $cover_image_data[1] : null,
                        'height' => $cover_image_data ? $cover_image_data[2] : null,
                    ]
                ], 200);
            } else {
                // Delete the attachment if ACF update failed
                wp_delete_attachment($attach_id, true);
                return new WP_Error('update_failed', 'Failed to update cover image field', array('status' => 500));
            }
        } else {
            return new WP_Error('attachment_failed', 'Failed to create attachment', array('status' => 500));
        }
    } else {
        return new WP_Error('upload_failed', isset($movefile['error']) ? $movefile['error'] : 'File upload failed', array('status' => 500));
    }
}

/**
 * Update ACF fields for a specific post
 */
function tourismiq_update_post_acf($request) {
    $post_id = $request->get_param('id');
    $current_user = wp_get_current_user();

    if (!$current_user->exists()) {
        return new WP_Error('not_logged_in', 'User is not logged in', array('status' => 401));
    }

    // Verify post exists
    $post = get_post($post_id);
    if (!$post) {
        return new WP_Error('post_not_found', 'Post not found', array('status' => 404));
    }

    // Check permissions
    if (!current_user_can('edit_post', $post_id)) {
        return new WP_Error('insufficient_permissions', 'You do not have permission to edit this post', array('status' => 403));
    }

    // Get the fields from the request
    $fields = $request->get_param('fields');

    if (empty($fields) || !is_array($fields)) {
        return new WP_Error('invalid_fields', 'Invalid fields parameter', array('status' => 400));
    }

    // Get post categories to determine allowed fields
    $post_categories = wp_get_post_categories($post_id, array('fields' => 'slugs'));
    $allowed_fields = [];

    // Define allowed ACF fields based on post category
    if (in_array('news', $post_categories)) {
        $allowed_fields = ['url', 'image_caption', 'featured_image'];
    } elseif (in_array('blog-post', $post_categories)) {
        $allowed_fields = ['title', 'author', 'website_logo', 'post_publish_date', 'url', 'post_thoughts', 'image_caption', 'featured_image'];
    } elseif (in_array('event', $post_categories)) {
        $allowed_fields = ['event_title', 'event_date', 'event_time', 'time_zone', 'event_end_date', 'event_location', 
                          'host_company_organization', 'host_company_organization_logo', 'registration_link_url', 
                          'additional_details_link_url', 'event_description', 'image_caption', 'featured_image'];
    } elseif (in_array('job', $post_categories)) {
        $allowed_fields = ['position_title', 'company', 'company_logo', 'location', 'job_type', 
                          'experience_level', 'compensation', 'posting_link_url', 'description', 'featured_image'];
    } elseif (in_array('webinar', $post_categories)) {
        $allowed_fields = ['webinar_type', 'date', 'time', 'time_zone', 'presenters', 
                          'webinar_host_company_organization', 'webinar_host_company_organization_logo', 
                          'register_url', 'youtube_url', 'vimeo_url', 'image_caption', 'featured_image'];
    } elseif (in_array('video', $post_categories)) {
        $allowed_fields = ['creators', 'creator_logo', 'video_source', 'youtube_id', 'vimeo_id', 'x', 'linkedin_iframe', 'image_caption', 'featured_image'];
    } elseif (in_array('course', $post_categories)) {
        $allowed_fields = ['course_name', 'course_url', 'sign_up_url', 'thoughts', 'company', 'company_logo', 'image_caption', 'featured_image'];
    } elseif (in_array('presentation', $post_categories)) {
        $allowed_fields = ['host_company_organization', 'host_company_organization_logo', 'pdf_upload', 'image_caption', 'featured_image'];
    } elseif (in_array('whitepaper', $post_categories)) {
        $allowed_fields = ['author', 'companyorganization_logo', 'upload_pdf', 'whitepaper_description', 'image_caption', 'featured_image'];
    } elseif (in_array('case-study', $post_categories)) {
        $allowed_fields = ['author', 'companyorganization_logo', 'upload_pdf', 'description', 'image_caption', 'featured_image'];
    } elseif (in_array('book', $post_categories)) {
        $allowed_fields = ['book_title', 'author_names', 'purchase_url', 'image_caption', 'featured_image'];
    } elseif (in_array('podcast', $post_categories)) {
        $allowed_fields = ['podcast_episode_title', 'podcast_logo', 'podcast_name', 'hosts', 
                          'episode_description', 'episode_url', 'audio_file', 'image_caption', 'featured_image'];
    } elseif (in_array('press-release', $post_categories)) {
        $allowed_fields = ['press_release_url', 'upload_pdf', 'image_caption', 'featured_image'];
    } elseif (in_array('template', $post_categories)) {
        $allowed_fields = ['creator_name', 'logo', 'file_upload', 'url', 'image_caption', 'featured_image'];
    } elseif (in_array('thought-leadership', $post_categories)) {
        $allowed_fields = ['media_type', 'featured_image', 'video_type', 'youtube_url', 'vimeo_url', 'twitter', 'linkedin_iframe', 'image_caption'];
    }

    // Sanitize and validate fields
    $sanitized_fields = [];
    foreach ($fields as $field_name => $field_value) {
        $field_name = sanitize_text_field($field_name);
        
        // Only allow whitelisted fields for this post category
        if (!in_array($field_name, $allowed_fields)) {
            continue;
        }

        // Sanitize based on field type
        if (in_array($field_name, ['url', 'website_logo', 'registration_link_url', 'additional_details_link_url', 
                                   'posting_link_url', 'register_url', 'youtube_url', 'vimeo_url', 'course_url', 
                                   'sign_up_url', 'purchase_url', 'episode_url', 'press_release_url', 'video_embed_url',
                                   'youtube_id', 'vimeo_id'])) {
            // URL fields
            $sanitized_fields[$field_name] = esc_url_raw($field_value);
        } elseif (in_array($field_name, ['post_thoughts', 'thoughts', 'event_description', 'description', 'episode_description', 'whitepaper_description'])) {
            // Textarea/WYSIWYG fields
            $sanitized_fields[$field_name] = sanitize_textarea_field($field_value);
        } elseif (in_array($field_name, ['x', 'linkedin_iframe'])) {
            // Embed code fields - allow more content but still sanitize
            $sanitized_fields[$field_name] = sanitize_textarea_field($field_value);
        } elseif (in_array($field_name, ['presenters', 'hosts', 'creators'])) {
            // Repeater fields - handle ACF repeater structure
            if (is_array($field_value)) {
                $sanitized_repeater = [];
                foreach ($field_value as $row) {
                    if (is_array($row)) {
                        // Handle proper ACF repeater structure
                        $sanitized_row = [];
                        foreach ($row as $sub_field_key => $sub_field_value) {
                            $sanitized_row[sanitize_text_field($sub_field_key)] = sanitize_text_field($sub_field_value);
                        }
                        $sanitized_repeater[] = $sanitized_row;
                    } else {
                        // Handle legacy format - convert to proper structure
                        if ($field_name === 'hosts') {
                            $sanitized_repeater[] = ['host_name' => sanitize_text_field($row)];
                        } elseif ($field_name === 'presenters') {
                            $sanitized_repeater[] = ['presenter_name' => sanitize_text_field($row)];
                        } elseif ($field_name === 'creators') {
                            $sanitized_repeater[] = ['creator_name' => sanitize_text_field($row)];
                        }
                    }
                }
                $sanitized_fields[$field_name] = $sanitized_repeater;
            } else {
                $sanitized_fields[$field_name] = [];
            }
        } elseif (in_array($field_name, ['event_date', 'event_end_date', 'post_publish_date', 'date'])) {
            // Date fields
            $sanitized_fields[$field_name] = sanitize_text_field($field_value);
        } elseif (in_array($field_name, ['event_time', 'time'])) {
            // Time fields
            $sanitized_fields[$field_name] = sanitize_text_field($field_value);
        } elseif (in_array($field_name, ['company_logo', 'host_company_organization_logo', 
                                          'webinar_host_company_organization_logo', 'creator_logo', 
                                          'companyorganization_logo', 'podcast_logo', 'logo', 'featured_image'])) {
            // Image/file ID fields - validate as integer
            $sanitized_fields[$field_name] = intval($field_value);
        } elseif (in_array($field_name, ['select_files', 'upload_pdf', 'file_upload', 'audio_file', 'pdf_upload'])) {
            // File upload fields - validate as integer (attachment ID)
            $sanitized_fields[$field_name] = intval($field_value);
        } else {
            // Default text sanitization for all other fields
            $sanitized_fields[$field_name] = sanitize_text_field($field_value);
        }
    }

    $success = true;
    $updated_fields = [];

    // Debug: Log the fields being processed
    error_log("Processing ACF fields for post {$post_id}: " . print_r($sanitized_fields, true));

    // Update each ACF field
    foreach ($sanitized_fields as $field_name => $field_value) {
        $update_result = update_field($field_name, $field_value, $post_id);
        if ($update_result) {
            $updated_fields[$field_name] = $field_value;
            error_log("Successfully updated ACF field {$field_name} for post {$post_id}");
        } else {
            $success = false;
            error_log("Failed to update ACF field {$field_name} for post {$post_id}");
        }
    }

    // Get all ACF fields after update
    $acf_fields = function_exists('get_fields') ? get_fields($post_id) : [];

    // Return response
    if ($success) {
        return new WP_REST_Response([
            'success' => true,
            'message' => 'ACF fields updated successfully',
            'updated_fields' => $updated_fields,
            'acf' => $acf_fields
        ], 200);
    } else {
        return new WP_REST_Response([
            'success' => false,
            'message' => 'Some fields could not be updated',
            'updated_fields' => $updated_fields,
            'acf' => $acf_fields
        ], 207); // 207 Multi-Status
    }
}

/**
 * Edit a post (update title, content, excerpt, categories, and ACF fields)
 */
function tourismiq_edit_post($request) {
    // Force a debug log to confirm function is called
    error_log("TOURISMIQ DEBUG: tourismiq_edit_post function called!");
    
    $post_id = $request->get_param('id');
    $current_user = wp_get_current_user();

    if (!$current_user->exists()) {
        return new WP_Error('not_logged_in', 'User is not logged in', array('status' => 401));
    }

    // Verify post exists
    $post = get_post($post_id);
    if (!$post) {
        return new WP_Error('post_not_found', 'Post not found', array('status' => 404));
    }

    // Double-check permissions - only post author or admin can edit
    // This is a secondary check after the permission_callback
    if ($current_user->ID != $post->post_author && !current_user_can('edit_others_posts')) {
        return new WP_Error('insufficient_permissions', 'You do not have permission to edit this post', array('status' => 403));
    }
    
    // Log the edit attempt for security monitoring
    error_log("Post edit attempt: User {$current_user->ID} ({$current_user->user_login}) editing post {$post_id} by author {$post->post_author}");

    // Get the data from the request (handles both JSON and FormData)
    $title = $request->get_param('title');
    $content = $request->get_param('content');
    $acf_fields_raw = $request->get_param('acf_fields');
    $files = $request->get_file_params();
    $keep_existing = $request->get_param('keep_existing');
    
    // Handle nested file structure from FormData
    if (isset($files['files']) && is_array($files['files']['name'])) {
        // Convert from files[name][field] to field[name] format
        $restructured_files = array();
        foreach ($files['files']['name'] as $field_name => $filename) {
            $restructured_files[$field_name] = array(
                'name' => $files['files']['name'][$field_name],
                'type' => $files['files']['type'][$field_name],
                'tmp_name' => $files['files']['tmp_name'][$field_name],
                'error' => $files['files']['error'][$field_name],
                'size' => $files['files']['size'][$field_name]
            );
        }
        $files = $restructured_files;
        error_log("DEBUG: Restructured files: " . print_r($files, true));
    }
    
    // Debug: Log what we received
    error_log("DEBUG tourismiq_edit_post: Post ID = {$post_id}");
    error_log("DEBUG tourismiq_edit_post: Title = " . ($title ? $title : 'null'));
    error_log("DEBUG tourismiq_edit_post: Files received: " . print_r($files, true));
    error_log("DEBUG tourismiq_edit_post: Keep existing: " . print_r($keep_existing, true));
    error_log("DEBUG tourismiq_edit_post: All params: " . print_r($request->get_params(), true));
    error_log("DEBUG tourismiq_edit_post: Body params: " . print_r($request->get_body_params(), true));
    error_log("DEBUG tourismiq_edit_post: $_FILES: " . print_r($_FILES, true));

    // Prepare post data for update
    $post_data = array(
        'ID' => $post_id,
        'post_modified' => current_time('mysql'),
        'post_modified_gmt' => current_time('mysql', 1),
    );

    // Only update fields that were provided
    if ($title !== null) {
        $post_data['post_title'] = sanitize_text_field($title);
    }
    if ($content !== null) {
        $post_data['post_content'] = wp_kses_post($content);
    }

    // Update the post
    $update_result = wp_update_post($post_data, true);
    
    if (is_wp_error($update_result)) {
        return new WP_Error('update_failed', 'Failed to update post: ' . $update_result->get_error_message(), array('status' => 500));
    }

    // Categories are not editable in this interface - they remain unchanged

    // Process ACF fields from FormData format
    $acf_fields = array();
    if ($acf_fields_raw && is_array($acf_fields_raw)) {
        foreach ($acf_fields_raw as $key => $value) {
            // Decode JSON-encoded values from FormData
            $decoded_value = json_decode($value, true);
            $acf_fields[$key] = $decoded_value !== null ? $decoded_value : $value;
        }
    }

    // Update ACF fields if provided
    $acf_update_success = true;
    $updated_acf_fields = array();
    
    if (!empty($acf_fields)) {
        // Get post categories to determine allowed fields
        $post_categories = wp_get_post_categories($post_id, array('fields' => 'slugs'));
        $allowed_fields = array();

        // Use the same field validation logic as tourismiq_update_post_acf
        if (in_array('news', $post_categories)) {
            $allowed_fields = ['url', 'image_caption', 'featured_image'];
        } elseif (in_array('blog-post', $post_categories)) {
            $allowed_fields = ['title', 'author', 'website_logo', 'post_publish_date', 'url', 'post_thoughts', 'image_caption', 'featured_image'];
        } elseif (in_array('event', $post_categories)) {
            $allowed_fields = ['event_title', 'event_date', 'event_time', 'time_zone', 'event_end_date', 'event_location', 
                              'host_company_organization', 'host_company_organization_logo', 'registration_link_url', 
                              'additional_details_link_url', 'event_description', 'image_caption', 'featured_image'];
        } elseif (in_array('job', $post_categories)) {
            $allowed_fields = ['position_title', 'company', 'company_logo', 'location', 'job_type', 
                              'experience_level', 'compensation', 'posting_link_url', 'description', 'featured_image'];
        } elseif (in_array('webinar', $post_categories)) {
            $allowed_fields = ['webinar_type', 'date', 'time', 'time_zone', 'presenters', 
                              'webinar_host_company_organization', 'webinar_host_company_organization_logo', 
                              'register_url', 'youtube_url', 'vimeo_url', 'image_caption', 'featured_image'];
        } elseif (in_array('video', $post_categories)) {
            $allowed_fields = ['creators', 'creator_logo', 'video_source', 'youtube_id', 'vimeo_id', 'x', 'linkedin_iframe', 'image_caption', 'featured_image'];
        } elseif (in_array('course', $post_categories)) {
            $allowed_fields = ['course_name', 'course_url', 'sign_up_url', 'thoughts', 'company', 'company_logo', 'image_caption', 'featured_image'];
        } elseif (in_array('presentation', $post_categories)) {
            $allowed_fields = ['host_company_organization', 'host_company_organization_logo', 'pdf_upload', 'image_caption', 'featured_image'];
        } elseif (in_array('whitepaper', $post_categories)) {
            $allowed_fields = ['author', 'companyorganization_logo', 'upload_pdf', 'whitepaper_description', 'image_caption', 'featured_image'];
        } elseif (in_array('case-study', $post_categories)) {
            $allowed_fields = ['author', 'companyorganization_logo', 'upload_pdf', 'description', 'image_caption', 'featured_image'];
        } elseif (in_array('book', $post_categories)) {
            $allowed_fields = ['book_title', 'author_names', 'purchase_url', 'image_caption', 'featured_image'];
        } elseif (in_array('podcast', $post_categories)) {
            $allowed_fields = ['podcast_episode_title', 'podcast_logo', 'podcast_name', 'hosts', 
                              'episode_description', 'episode_url', 'audio_file', 'image_caption', 'featured_image'];
        } elseif (in_array('press-release', $post_categories)) {
            $allowed_fields = ['press_release_url', 'upload_pdf', 'image_caption', 'featured_image'];
        } elseif (in_array('template', $post_categories)) {
            $allowed_fields = ['creator_name', 'logo', 'file_upload', 'url', 'image_caption', 'featured_image'];
        } elseif (in_array('thought-leadership', $post_categories)) {
            $allowed_fields = ['media_type', 'featured_image', 'video_type', 'youtube_url', 'vimeo_url', 'twitter', 'linkedin_iframe', 'image_caption'];
        }

        // Handle file uploads first
        $uploaded_file_ids = array();
        $featured_image_id = null;
        
        if (!empty($files)) {
            // Include WordPress file handling functions
            if (!function_exists('wp_handle_upload')) {
                require_once(ABSPATH . 'wp-admin/includes/file.php');
            }
            
            error_log("DEBUG: Files received: " . print_r($files, true));
            
            foreach ($files as $field_name => $file) {
                error_log("DEBUG: Processing file field: {$field_name}");
                // Handle featured image separately
                if ($field_name === 'featured_image') {
                    error_log("DEBUG: Processing featured image upload");
                    // Handle featured image upload
                    $upload_overrides = array('test_form' => false);
                    $movefile = wp_handle_upload($file, $upload_overrides);
                    error_log("DEBUG: wp_handle_upload result: " . print_r($movefile, true));
                    
                    if ($movefile && !isset($movefile['error'])) {
                        // Create attachment for featured image
                        $attachment = array(
                            'post_mime_type' => $movefile['type'],
                            'post_title' => preg_replace('/\.[^.]+$/', '', basename($movefile['file'])),
                            'post_content' => '',
                            'post_status' => 'inherit'
                        );
                        
                        $attach_id = wp_insert_attachment($attachment, $movefile['file'], $post_id);
                        
                        if (!is_wp_error($attach_id)) {
                            // Generate attachment metadata
                            if (!function_exists('wp_generate_attachment_metadata')) {
                                require_once(ABSPATH . 'wp-admin/includes/image.php');
                            }
                            $attach_data = wp_generate_attachment_metadata($attach_id, $movefile['file']);
                            wp_update_attachment_metadata($attach_id, $attach_data);
                            
                            $featured_image_id = $attach_id;
                            error_log("DEBUG: Featured image attachment created with ID: {$attach_id}");
                        } else {
                            error_log("DEBUG: Failed to create attachment: " . print_r($attach_id, true));
                        }
                    } else {
                        error_log("Featured image upload error: " . (isset($movefile['error']) ? $movefile['error'] : 'Unknown error'));
                    }
                    continue;
                }
                
                if (!in_array($field_name, $allowed_fields)) {
                    continue; // Skip files for fields not allowed for this post type
                }
                
                // Handle the file upload
                $upload_overrides = array('test_form' => false);
                $movefile = wp_handle_upload($file, $upload_overrides);
                
                if ($movefile && !isset($movefile['error'])) {
                    // Create attachment
                    $attachment = array(
                        'post_mime_type' => $movefile['type'],
                        'post_title' => preg_replace('/\.[^.]+$/', '', basename($movefile['file'])),
                        'post_content' => '',
                        'post_status' => 'inherit'
                    );
                    
                    $attach_id = wp_insert_attachment($attachment, $movefile['file'], $post_id);
                    
                    if (!is_wp_error($attach_id)) {
                        // Generate attachment metadata
                        if (!function_exists('wp_generate_attachment_metadata')) {
                            require_once(ABSPATH . 'wp-admin/includes/image.php');
                        }
                        $attach_data = wp_generate_attachment_metadata($attach_id, $movefile['file']);
                        wp_update_attachment_metadata($attach_id, $attach_data);
                        
                        // Store the attachment ID to update the ACF field
                        $uploaded_file_ids[$field_name] = $attach_id;
                    }
                } else {
                    // Log upload error
                    error_log("File upload error for field {$field_name}: " . $movefile['error']);
                }
            }
        }

        // Handle keep existing files
        $keep_existing_files = array();
        if ($keep_existing && is_array($keep_existing)) {
            foreach ($keep_existing as $field_name => $keep) {
                if ($keep === 'true' || $keep === true) {
                    $keep_existing_files[$field_name] = true;
                }
            }
        }

        // Update featured image if a new one was uploaded
        if ($featured_image_id) {
            $thumbnail_result = set_post_thumbnail($post_id, $featured_image_id);
            error_log("DEBUG: Set post thumbnail result: " . ($thumbnail_result ? 'success' : 'failed'));
        } elseif (!isset($keep_existing_files['featured_image']) || !$keep_existing_files['featured_image']) {
            error_log("DEBUG: No new featured image uploaded and not keeping existing");
            // If not keeping existing and no new image, don't change featured image
            // (WordPress handles this automatically)
        }

        // Sanitize and update ACF fields
        foreach ($acf_fields as $field_name => $field_value) {
            $field_name = sanitize_text_field($field_name);
            
            // Only allow whitelisted fields for this post category
            if (!in_array($field_name, $allowed_fields)) {
                continue;
            }

            // Use the same sanitization logic as tourismiq_update_post_acf
            $sanitized_value = $field_value;
            
            if (in_array($field_name, ['url', 'website_logo', 'registration_link_url', 'additional_details_link_url', 
                                       'posting_link_url', 'register_url', 'youtube_url', 'vimeo_url', 'course_url', 
                                       'sign_up_url', 'purchase_url', 'episode_url', 'press_release_url', 'video_embed_url',
                                       'youtube_id', 'vimeo_id'])) {
                $sanitized_value = esc_url_raw($field_value);
            } elseif (in_array($field_name, ['post_thoughts', 'thoughts', 'event_description', 'description', 'episode_description', 'whitepaper_description'])) {
                $sanitized_value = sanitize_textarea_field($field_value);
            } elseif (in_array($field_name, ['x', 'linkedin_iframe'])) {
                $sanitized_value = sanitize_textarea_field($field_value);
            } elseif (in_array($field_name, ['company_logo', 'host_company_organization_logo', 
                                              'webinar_host_company_organization_logo', 'creator_logo', 
                                              'companyorganization_logo', 'podcast_logo', 'logo', 'featured_image'])) {
                // Handle file fields
                if (isset($uploaded_file_ids[$field_name])) {
                    // New file was uploaded
                    $sanitized_value = $uploaded_file_ids[$field_name];
                } elseif (isset($keep_existing_files[$field_name]) && $keep_existing_files[$field_name]) {
                    // Keep existing file - skip updating this field
                    continue;
                } else {
                    // No file provided and not keeping existing - treat as regular field
                    $sanitized_value = intval($field_value);
                }
            } elseif (in_array($field_name, ['select_files', 'upload_pdf', 'file_upload', 'audio_file', 'pdf_upload'])) {
                // Handle file fields
                if (isset($uploaded_file_ids[$field_name])) {
                    // New file was uploaded
                    $sanitized_value = $uploaded_file_ids[$field_name];
                } elseif (isset($keep_existing_files[$field_name]) && $keep_existing_files[$field_name]) {
                    // Keep existing file - skip updating this field
                    continue;
                } else {
                    // No file provided and not keeping existing - treat as regular field
                    $sanitized_value = intval($field_value);
                }
            } else {
                $sanitized_value = sanitize_text_field($field_value);
            }

            $update_result = update_field($field_name, $sanitized_value, $post_id);
            if ($update_result) {
                $updated_acf_fields[$field_name] = $sanitized_value;
            } else {
                $acf_update_success = false;
            }
        }
    }

    // Get updated post data
    $updated_post = get_post($post_id);
    $updated_categories = wp_get_post_categories($post_id, array('fields' => 'slugs'));
    $updated_acf = function_exists('get_fields') ? get_fields($post_id) : array();

    // Debug: Check if featured image was set
    $featured_image_check = get_post_thumbnail_id($post_id);
    error_log("DEBUG: Final featured image check - ID: {$featured_image_check}");
    
    return new WP_REST_Response([
        'success' => true,
        'message' => 'Post updated successfully',
        'post' => array(
            'id' => $updated_post->ID,
            'title' => $updated_post->post_title,
            'content' => $updated_post->post_content,
            'categories' => $updated_categories,
            'acf' => $updated_acf,
            'modified' => $updated_post->post_modified,
            'featured_image_id' => $featured_image_check,
        ),
        'acf_update_success' => $acf_update_success,
        'updated_acf_fields' => $updated_acf_fields,
        'featured_image_updated' => !empty($featured_image_id),
    ], 200);
}

/**
 * Delete a post
 */
function tourismiq_delete_post($request) {
    $post_id = $request->get_param('id');
    $current_user = wp_get_current_user();

    if (!$current_user->exists()) {
        return new WP_Error('not_logged_in', 'User is not logged in', array('status' => 401));
    }

    // Verify post exists
    $post = get_post($post_id);
    if (!$post) {
        return new WP_Error('post_not_found', 'Post not found', array('status' => 404));
    }

    // Double-check permissions - only post author or admin can delete
    // This is a secondary check after the permission_callback
    if ($current_user->ID != $post->post_author && !current_user_can('delete_others_posts')) {
        return new WP_Error('insufficient_permissions', 'You do not have permission to delete this post', array('status' => 403));
    }
    
    // Log the deletion attempt for security monitoring
    error_log("Post deletion attempt: User {$current_user->ID} ({$current_user->user_login}) deleting post {$post_id} by author {$post->post_author}");

    // Store post data before deletion for response
    $post_data = array(
        'id' => $post->ID,
        'title' => $post->post_title,
        'author' => $post->post_author,
    );

    // Delete the post (move to trash by default)
    $delete_result = wp_delete_post($post_id, false); // false = move to trash, true = permanently delete
    
    if (!$delete_result) {
        return new WP_Error('delete_failed', 'Failed to delete post', array('status' => 500));
    }

    return new WP_REST_Response([
        'success' => true,
        'message' => 'Post deleted successfully',
        'deleted_post' => $post_data,
    ], 200);
}


/**
 * Get authentication status for the current user
 */
function tourismiq_auth_status() {
    $response = array(
        'loggedIn' => is_user_logged_in(),
        'nonce' => wp_create_nonce('wp_rest'),
    );

    if (is_user_logged_in()) {
        $current_user = wp_get_current_user();
        
        // Force fresh user data - clear ALL user caches
        clean_user_cache($current_user->ID);
        wp_cache_delete($current_user->ID, 'users');
        wp_cache_delete($current_user->ID, 'user_meta');
        
        // Clear TourismIQ performance cache
        if (function_exists('tourismiq_clear_user_cache')) {
            tourismiq_clear_user_cache($current_user->ID);
        }
        
        // Clear any additional WordPress caches
        wp_cache_flush();
        
        // Get fresh user data
        $current_user = get_userdata($current_user->ID);

        // Get ACF fields if available
        $acf_fields = function_exists('get_fields') ? get_fields('user_' . $current_user->ID) : [];

        // Get profile picture from ACF field with proper handling
        $profile_picture = get_field('profile_picture', 'user_' . $current_user->ID);
        $avatar_url = null; // Let frontend handle the fallback to placeholder
        
        if ($profile_picture) {
            if (is_array($profile_picture) && isset($profile_picture['url'])) {
                $avatar_url = $profile_picture['url'];
            } elseif (is_string($profile_picture) && !empty($profile_picture)) {
                $avatar_url = $profile_picture;
            } elseif (is_numeric($profile_picture)) {
                // If profile_picture is an attachment ID
                $attachment_url = wp_get_attachment_url($profile_picture);
                if ($attachment_url) {
                    $avatar_url = $attachment_url;
                }
            }
        }

        // Simple user object for backward compatibility
        $response['user'] = array(
            'id' => $current_user->ID,
            'username' => $current_user->user_login,
            'name' => $current_user->display_name,
            'email' => $current_user->user_email,
            'avatarUrl' => $avatar_url,
        );

        // Add the complete WordPress user data with ACF fields
        $response['wpUser'] = array(
            'id' => $current_user->ID,
            'username' => $current_user->user_login,
            'slug' => $current_user->user_nicename,
            'name' => $current_user->display_name,
            'first_name' => $current_user->first_name,
            'last_name' => $current_user->last_name,
            'email' => $current_user->user_email,
            'url' => $current_user->user_url,
            'description' => $current_user->description,
            'registered' => $current_user->user_registered,
            'roles' => $current_user->roles,
            'avatar_urls' => [
                '24' => $avatar_url,
                '48' => $avatar_url,
                '96' => $avatar_url,
                'thumbnail' => $avatar_url,
            ],
            'acf' => $acf_fields,
        );

        // Add a token for socket authentication
        $response['token'] = wp_hash($current_user->ID . time() . wp_salt(), 'auth');
    }

    return $response;
}

/**
 * Verify user password
 */
function tourismiq_verify_password($request) {
    $current_user = wp_get_current_user();

    if (!$current_user->exists()) {
        return new WP_Error('not_logged_in', 'User is not logged in', array('status' => 401));
    }

    // Get the password from the request
    $password = $request->get_param('password');

    if (empty($password)) {
        return new WP_Error('missing_password', 'Password is required', array('status' => 400));
    }

    // Verify the password
    if (!wp_check_password($password, $current_user->user_pass, $current_user->ID)) {
        return new WP_Error('invalid_password', 'Password is incorrect', array('status' => 401));
    }

    return new WP_REST_Response([
        'success' => true,
        'message' => 'Password verified successfully'
    ], 200);
}
