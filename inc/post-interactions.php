<?php
/**
 * Post Interactions
 *
 * Handles interactions like upvotes, views, and bookmarks.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register post interactions API endpoints
 */
function tourismiq_register_post_interactions_endpoints() {
    // Upvote endpoint
    register_rest_route('tourismiq/v1', '/posts/(?P<id>\d+)/upvote', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_toggle_post_upvote',
        'permission_callback' => function() {
            return is_user_logged_in();
        },
        'args' => array(
            'id' => array(
                'validate_callback' => function($param) {
                    return is_numeric($param);
                }
            ),
        ),
    ));

    // Get post upvotes endpoint
    register_rest_route('tourismiq/v1', '/posts/(?P<id>\d+)/upvotes', array(
        'methods' => 'GET',
        'callback' => 'tourismiq_get_post_upvotes',
        'permission_callback' => '__return_true',
        'args' => array(
            'id' => array(
                'validate_callback' => function($param) {
                    return is_numeric($param);
                }
            ),
        ),
    ));


}
add_action('rest_api_init', 'tourismiq_register_post_interactions_endpoints');

/**
 * Toggle upvote for a post
 */
function tourismiq_toggle_post_upvote($request) {
    $post_id = $request['id'];
    $user_id = get_current_user_id();

    // Check if post exists
    $post = get_post($post_id);
    if (!$post) {
        return new WP_Error('post_not_found', 'Post not found', array('status' => 404));
    }

    // Check if user has already upvoted this post
    $has_upvoted = tourismiq_user_has_upvoted($user_id, $post_id);

    if ($has_upvoted) {
        // User has already upvoted, so remove the upvote
        $result = tourismiq_remove_post_upvote($user_id, $post_id);
        $action = 'removed';
    } else {
        // User hasn't upvoted, so add the upvote
        $result = tourismiq_add_post_upvote($user_id, $post_id);
        $action = 'added';
    }

    if (is_wp_error($result)) {
        return $result;
    }

    // Get the new upvote count
    $upvote_count = tourismiq_get_post_upvote_count($post_id);

    // Update the post meta for faster retrieval
    update_post_meta($post_id, '_upvotes', $upvote_count);

    return array(
        'success' => true,
        'action' => $action,
        'upvoted' => !$has_upvoted,
        'count' => $upvote_count,
    );
}

/**
 * Get upvotes for a post
 */
function tourismiq_get_post_upvotes($request) {
    $post_id = $request['id'];
    $user_id = get_current_user_id();

    // Check if post exists
    $post = get_post($post_id);
    if (!$post) {
        return new WP_Error('post_not_found', 'Post not found', array('status' => 404));
    }

    // Get upvote count
    $upvote_count = tourismiq_get_post_upvote_count($post_id);

    // Check if current user has upvoted
    $has_upvoted = is_user_logged_in() ? tourismiq_user_has_upvoted($user_id, $post_id) : false;

    return array(
        'count' => $upvote_count,
        'upvoted' => $has_upvoted,
    );
}

/**
 * Check if a user has upvoted a post
 */
function tourismiq_user_has_upvoted($user_id, $post_id) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'postmeta';

    $result = $wpdb->get_var($wpdb->prepare(
        "SELECT meta_id FROM $table_name
         WHERE post_id = %d
         AND meta_key = '_upvoted_by'
         AND meta_value = %d",
        $post_id,
        $user_id
    ));

    return !empty($result);
}

/**
 * Add an upvote to a post
 */
function tourismiq_add_post_upvote($user_id, $post_id) {
    // Check if user has already upvoted
    if (tourismiq_user_has_upvoted($user_id, $post_id)) {
        return new WP_Error('already_upvoted', 'User has already upvoted this post', array('status' => 400));
    }

    // Add upvote to the database
    $result = add_post_meta($post_id, '_upvoted_by', $user_id, false);

    if ($result === false) {
        return new WP_Error('upvote_failed', 'Failed to add upvote', array('status' => 500));
    }

    // Trigger a notification to the post author
    if (function_exists('tourismiq_handle_upvote_notification')) {
        tourismiq_handle_upvote_notification($post_id, $user_id);
    }

    // Trigger IQ points for the user who upvoted
    do_action('tourismiq_post_upvoted', $user_id, $post_id);

    return true;
}

/**
 * Remove an upvote from a post
 */
function tourismiq_remove_post_upvote($user_id, $post_id) {
    // Check if user has upvoted
    if (!tourismiq_user_has_upvoted($user_id, $post_id)) {
        return new WP_Error('not_upvoted', 'User has not upvoted this post', array('status' => 400));
    }

    // Remove upvote from the database
    $result = delete_post_meta($post_id, '_upvoted_by', $user_id);

    if ($result === false) {
        return new WP_Error('upvote_removal_failed', 'Failed to remove upvote', array('status' => 500));
    }

    return true;
}

/**
 * Get the total upvote count for a post
 */
function tourismiq_get_post_upvote_count($post_id) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'postmeta';

    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name
         WHERE post_id = %d
         AND meta_key = '_upvoted_by'",
        $post_id
    ));

    return (int) $count;
}

/**
 * Add upvote count to REST API post response
 */
function tourismiq_add_upvote_data_to_api() {
    register_rest_field('post', 'upvotes', array(
        'get_callback' => function($post_object) {
            $post_id = $post_object['id'];
            $user_id = get_current_user_id();

            // Get upvote count
            $count = tourismiq_get_post_upvote_count($post_id);

            // Check if current user has upvoted
            $has_upvoted = is_user_logged_in() ? tourismiq_user_has_upvoted($user_id, $post_id) : false;

            return array(
                'count' => $count,
                'upvoted' => $has_upvoted,
            );
        },
        'schema' => array(
            'description' => 'Post upvote data',
            'type' => 'object',
            'properties' => array(
                'count' => array(
                    'description' => 'Total upvote count',
                    'type' => 'integer',
                ),
                'upvoted' => array(
                    'description' => 'Whether the current user has upvoted this post',
                    'type' => 'boolean',
                ),
            ),
        ),
    ));


}
add_action('rest_api_init', 'tourismiq_add_upvote_data_to_api');

/**
 * Update UpvoteButton component to use our new API
 */
function tourismiq_update_post_meta_for_existing_upvotes() {
    $posts = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => -1,
    ));

    foreach ($posts as $post) {
        $upvote_count = tourismiq_get_post_upvote_count($post->ID);
        update_post_meta($post->ID, '_upvotes', $upvote_count);
    }
}

// Run once to update existing posts (comment out after running)
// add_action('init', 'tourismiq_update_post_meta_for_existing_upvotes');




