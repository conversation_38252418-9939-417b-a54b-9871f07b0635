<?php
/**
 * Add custom fields to the WordPress REST API user response
 */

// Add user fields to the REST API response
add_filter('rest_prepare_user', 'tourismiq_add_user_fields_to_rest', 10, 3);

function tourismiq_add_user_fields_to_rest($response, $user, $request) {
    $data = $response->get_data();
    $context = !empty($request['context']) ? $request['context'] : 'view';
    
    // Always include roles for our frontend, regardless of authentication
    if (!isset($data['roles'])) {
        $user_data = get_userdata($user->ID);
        if ($user_data) {
            $data['roles'] = $user_data->roles;
            $data['capabilities'] = $user_data->allcaps;
        } else {
            $data['roles'] = [];
            $data['capabilities'] = [];
        }
    }
    
    // Add ACF fields if available
    if (function_exists('get_fields')) {
        $acf_fields = get_fields('user_' . $user->ID);
        if ($acf_fields) {
            $data['acf'] = $acf_fields;
        }
    }
    
    // Ensure we have all the basic user fields
    if (!isset($data['username'])) {
        $data['username'] = $user->user_login;
    }
    
    if (!isset($data['user_nicename'])) {
        $data['user_nicename'] = $user->user_nicename;
    }
    
    if (!isset($data['email'])) {
        $data['email'] = $user->user_email;
    }
    
    // Set the modified data back to the response
    $response->set_data($data);
    
    return $response;
}

// Register the user fields in the REST API
add_action('rest_api_init', function() {
    register_rest_field('user', 'roles', array(
        'get_callback' => function($user, $field_name, $request) {
            $user_data = get_userdata($user['id']);
            return $user_data ? $user_data->roles : [];
        },
        'update_callback' => null,
        'schema' => array(
            'description' => 'User roles',
            'type' => 'array',
            'context' => array('view', 'edit'),
        ),
    ));
    
    register_rest_field('user', 'capabilities', array(
        'get_callback' => function($user, $field_name, $request) {
            $user_data = get_userdata($user['id']);
            return $user_data ? $user_data->allcaps : [];
        },
        'update_callback' => null,
        'schema' => array(
            'description' => 'User capabilities',
            'type' => 'object',
            'context' => array('view', 'edit'),
        ),
    ));
});
