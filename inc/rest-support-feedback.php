<?php
/**
 * REST API endpoints for Support and Feedback forms
 */

// Register REST routes
add_action( 'rest_api_init', function() {
    
    // Support request endpoint
    register_rest_route( 'tourismiq/v1', '/support', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_handle_support_request',
        'permission_callback' => '__return_true', // Public endpoint
        'args' => array(
            'name' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'email' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_email',
                'validate_callback' => 'is_email',
            ),
            'subject' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'priority' => array(
                'required' => true,
                'type' => 'string',
                'enum' => array( 'low', 'medium', 'high', 'urgent' ),
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'description' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'wp_kses_post',
            ),
        ),
    ) );
    
    // Feedback submission endpoint
    register_rest_route( 'tourismiq/v1', '/feedback', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_handle_feedback_submission',
        'permission_callback' => '__return_true', // Public endpoint
        'args' => array(
            'name' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'email' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_email',
                'validate_callback' => 'is_email',
            ),
            'category' => array(
                'required' => true,
                'type' => 'string',
                'enum' => array( 'general', 'feature', 'bug', 'ui', 'performance', 'content', 'other' ),
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'rating' => array(
                'required' => true,
                'type' => 'string',
                'enum' => array( '1', '2', '3', '4', '5' ),
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'feedback' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'wp_kses_post',
            ),
        ),
    ) );
    
    // Vendor suggestion endpoint (moved here for testing)
    register_rest_route('tourismiq/v1', '/vendor-suggestion', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_handle_vendor_suggestion',
        'permission_callback' => '__return_true', // Public endpoint
        'args' => array(
            'firstName' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'lastName' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'email' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_email',
                'validate_callback' => 'is_email',
            ),
            'organization' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'website' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'esc_url',
            ),
            'message' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'wp_kses_post',
            ),
        ),
    ));

    // Sponsorship request endpoint
    register_rest_route('tourismiq/v1', '/sponsorship', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_handle_sponsorship_request',
        'permission_callback' => '__return_true', // Public endpoint
        'args' => array(
            'companyName' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'contactName' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'email' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_email',
                'validate_callback' => 'is_email',
            ),
            'phone' => array(
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'budget' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'message' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'wp_kses_post',
            ),
        ),
    ));
} );

/**
 * Handle support request submission
 */
function tourismiq_handle_support_request( $request ) {
    $params = $request->get_params();
    
    // Handle file attachments if present
    $attachments = array();
    $files = $request->get_file_params();
    
    if ( ! empty( $files ) ) {
        foreach ( $files as $key => $file ) {
            if ( strpos( $key, 'attachment_' ) === 0 ) {
                $attachments[] = $file['name'];
            }
        }
    }
    
    // Prepare data for email
    $data = array(
        'name' => $params['name'],
        'email' => $params['email'],
        'subject' => $params['subject'],
        'priority' => $params['priority'],
        'description' => $params['description'],
        'attachments' => $attachments,
    );
    
    // Log the incoming request for debugging
    error_log( 'TourismIQ Support Request received: ' . print_r( $data, true ) );
    
    // Send email
    tourismiq_send_support_email( $data );
    
    // Log support request (optional - could save to database)
    error_log( sprintf(
        'Support request from %s (%s): %s [Priority: %s]',
        $data['name'],
        $data['email'],
        $data['subject'],
        $data['priority']
    ) );
    
    return new WP_REST_Response( array(
        'success' => true,
        'message' => 'Support request submitted successfully.',
    ), 200 );
}

/**
 * Handle feedback submission
 */
function tourismiq_handle_feedback_submission( $request ) {
    $params = $request->get_params();
    
    // Prepare data for email
    $data = array(
        'name' => $params['name'],
        'email' => $params['email'],
        'category' => $params['category'],
        'rating' => $params['rating'],
        'feedback' => $params['feedback'],
    );
    
    // Send email
    tourismiq_send_feedback_email( $data );
    
    // Log feedback (optional - could save to database)
    error_log( sprintf(
        'Feedback from %s (%s): Category: %s, Rating: %s/5',
        $data['name'],
        $data['email'],
        $data['category'],
        $data['rating']
    ) );
    
    return new WP_REST_Response( array(
        'success' => true,
        'message' => 'Feedback submitted successfully.',
    ), 200 );
}

/**
 * Handle sponsorship request submission
 */
function tourismiq_handle_sponsorship_request( $request ) {
    $params = $request->get_params();

    error_log('TourismIQ REST API: Sponsorship request received with params: ' . print_r($params, true));

    // Validate required fields
    if ( empty( $params['companyName'] ) || empty( $params['contactName'] ) ||
         empty( $params['email'] ) || empty( $params['budget'] ) || empty( $params['message'] ) ) {
        return new WP_Error( 'missing_fields', 'Missing required fields', array( 'status' => 400 ) );
    }

    // Prepare data for email
    $sponsorship_data = array(
        'companyName' => $params['companyName'],
        'contactName' => $params['contactName'],
        'email' => $params['email'],
        'phone' => isset( $params['phone'] ) ? $params['phone'] : '',
        'budget' => $params['budget'],
        'message' => $params['message'],
    );

    // Send email using the email handler
    tourismiq_send_sponsorship_email( $sponsorship_data );

    return array(
        'success' => true,
        'message' => 'Sponsorship inquiry submitted successfully'
    );
}

/**
 * Handle vendor suggestion submission
 */
function tourismiq_handle_vendor_suggestion($request) {
    $params = $request->get_params();
    
    // Get current user info (if logged in)
    $current_user = wp_get_current_user();
    
    // Prepare email data
    $email_data = array(
        'firstName' => $params['firstName'],
        'lastName' => $params['lastName'],
        'email' => $params['email'],
        'organization' => $params['organization'],
        'website' => !empty($params['website']) ? $params['website'] : '',
        'message' => !empty($params['message']) ? $params['message'] : '',
    );
    
    // Add submitter info if user is logged in
    if ($current_user && $current_user->ID > 0) {
        $email_data['submitted_by'] = $current_user->display_name;
        $email_data['submitted_by_email'] = $current_user->user_email;
        $email_data['submitted_by_id'] = $current_user->ID;
    } else {
        $email_data['submitted_by'] = 'Anonymous';
        $email_data['submitted_by_email'] = '';
        $email_data['submitted_by_id'] = 0;
    }
    
    // Log the incoming request for debugging
    error_log('TourismIQ Vendor Suggestion received: ' . print_r($email_data, true));
    
    // Send email notification
    error_log('TourismIQ: About to call tourismiq_send_vendor_suggestion_email()');
    tourismiq_send_vendor_suggestion_email($email_data);
    error_log('TourismIQ: Finished calling tourismiq_send_vendor_suggestion_email()');
    
    // Log the submission
    if ($current_user && $current_user->ID > 0) {
        error_log("Vendor suggestion submitted by user {$current_user->ID} ({$current_user->user_email}) for organization: {$email_data['organization']}");
    } else {
        error_log("Vendor suggestion submitted anonymously for organization: {$email_data['organization']}");
    }
    
    return new WP_REST_Response(array(
        'success' => true,
        'message' => 'Vendor suggestion submitted successfully.',
    ), 200);
}

/**
 * Add CORS headers for these endpoints
 */
add_filter( 'rest_pre_serve_request', function( $value, $response, $request, $server ) {
    $route = $request->get_route();
    
    if ( strpos( $route, '/tourismiq/v1/support' ) === 0 || 
         strpos( $route, '/tourismiq/v1/feedback' ) === 0 ||
         strpos( $route, '/tourismiq/v1/vendor-suggestion' ) === 0 ) {
        
        $origin = get_http_origin();
        if ( $origin ) {
            header( 'Access-Control-Allow-Origin: ' . $origin );
            header( 'Access-Control-Allow-Credentials: true' );
            header( 'Access-Control-Allow-Methods: POST, OPTIONS' );
            header( 'Access-Control-Allow-Headers: Content-Type, Authorization' );
        }
    }
    
    return $value;
}, 10, 4 );