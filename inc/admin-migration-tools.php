<?php
/**
 * Admin Migration Tools
 * Tools for migrating old data to new field structures
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add admin menu item for migration tools
 */
add_action('admin_menu', function() {
    add_management_page(
        'TourismIQ Migration Tools',
        'Migration Tools',
        'manage_options',
        'tourismiq-migration-tools',
        'tourismiq_migration_tools_page'
    );
});

/**
 * Migration tools admin page
 */
function tourismiq_migration_tools_page() {
    ?>
    <div class="wrap">
        <h1>TourismIQ Migration Tools</h1>
        
        <div class="card">
            <h2>Newsletter Opt-in Migration</h2>
            <p>Migrate old 'updates-promotions' field data to the new 'newsletter_optin' ACF field.</p>
            
            <form method="post" action="" id="newsletter-migration-form">
                <?php wp_nonce_field('migrate_newsletter_optin', 'newsletter_migration_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Migration Status</th>
                        <td id="migration-status">
                            <?php
                            $migration_status = get_option('newsletter_optin_migration_status', 'not_started');
                            $migrated_count = get_option('newsletter_optin_migrated_count', 0);
                            
                            switch ($migration_status) {
                                case 'completed':
                                    echo '<span style="color: green;">✓ Migration completed. ' . $migrated_count . ' users migrated.</span>';
                                    break;
                                case 'in_progress':
                                    echo '<span style="color: orange;">⚠ Migration in progress...</span>';
                                    break;
                                default:
                                    echo '<span style="color: #666;">Not started</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Preview Migration</th>
                        <td>
                            <button type="button" class="button" onclick="previewMigration()">Preview Users to Migrate</button>
                            <div id="preview-results" style="margin-top: 10px;"></div>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="migrate_newsletter_optin" class="button-primary" value="Run Migration" 
                           <?php echo ($migration_status === 'completed') ? 'disabled' : ''; ?>
                           onclick="return confirm('Are you sure you want to run the newsletter opt-in migration? This will update user data.');" />
                    
                    <?php if ($migration_status === 'completed'): ?>
                        <input type="submit" name="reset_migration" class="button-secondary" value="Reset Migration Status" 
                               onclick="return confirm('Are you sure you want to reset the migration status? This will allow you to run the migration again.');" style="margin-left: 10px;" />
                    <?php endif; ?>
                </p>
            </form>
        </div>
        
        <div class="card" style="margin-top: 20px;">
            <h3>Migration Details</h3>
            <p><strong>Source Field:</strong> <code>updates-promotions</code> (old checkbox field)</p>
            <p><strong>Target Field:</strong> <code>newsletter_optin</code> (new ACF true/false field)</p>
            <p><strong>Logic:</strong> Users who had checked the old opt-in checkbox will have the new field set to <code>true</code>.</p>
            <p><strong>Data Format:</strong> Old field stored as serialized array, new field stores as boolean.</p>
        </div>
    </div>

    <script>
    function previewMigration() {
        document.getElementById('preview-results').innerHTML = 'Loading...';
        
        fetch(ajaxurl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'preview_newsletter_migration',
                nonce: '<?php echo wp_create_nonce('preview_newsletter_migration'); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div style="background: #f9f9f9; padding: 10px; border: 1px solid #ddd; margin-top: 10px;">';
                html += '<strong>Users to be migrated: ' + data.data.count + '</strong><br>';
                if (data.data.users.length > 0) {
                    html += '<p>Sample users (showing first 10):</p><ul>';
                    data.data.users.slice(0, 10).forEach(user => {
                        html += '<li>ID: ' + user.id + ' - ' + user.display_name + ' (' + user.user_email + ')</li>';
                    });
                    html += '</ul>';
                    if (data.data.users.length > 10) {
                        html += '<p><em>... and ' + (data.data.users.length - 10) + ' more users</em></p>';
                    }
                } else {
                    html += '<p>No users found with the old opt-in field.</p>';
                }
                html += '</div>';
                document.getElementById('preview-results').innerHTML = html;
            } else {
                document.getElementById('preview-results').innerHTML = '<div style="color: red;">Error: ' + data.data + '</div>';
            }
        })
        .catch(error => {
            document.getElementById('preview-results').innerHTML = '<div style="color: red;">Error: ' + error.message + '</div>';
        });
    }
    </script>
    <?php
}

/**
 * Handle migration form submission
 */
add_action('admin_init', function() {
    if (isset($_POST['migrate_newsletter_optin']) && wp_verify_nonce($_POST['newsletter_migration_nonce'], 'migrate_newsletter_optin')) {
        $result = migrate_newsletter_optin_data();
        
        if ($result['success']) {
            add_action('admin_notices', function() use ($result) {
                echo '<div class="notice notice-success is-dismissible"><p>Migration completed successfully! ' . $result['migrated'] . ' users migrated.</p></div>';
            });
        } else {
            add_action('admin_notices', function() use ($result) {
                echo '<div class="notice notice-error is-dismissible"><p>Migration failed: ' . $result['error'] . '</p></div>';
            });
        }
    }
    
    if (isset($_POST['reset_migration']) && wp_verify_nonce($_POST['newsletter_migration_nonce'], 'migrate_newsletter_optin')) {
        delete_option('newsletter_optin_migration_status');
        delete_option('newsletter_optin_migrated_count');
        
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>Migration status reset successfully!</p></div>';
        });
    }
});

/**
 * AJAX handler for migration preview
 */
add_action('wp_ajax_preview_newsletter_migration', function() {
    if (!wp_verify_nonce($_POST['nonce'], 'preview_newsletter_migration')) {
        wp_die('Security check failed');
    }
    
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    $users_to_migrate = get_users_with_old_optin();
    
    wp_send_json_success([
        'count' => count($users_to_migrate),
        'users' => $users_to_migrate
    ]);
});

/**
 * Get users who have the old opt-in field set
 */
function get_users_with_old_optin() {
    global $wpdb;
    
    $users = $wpdb->get_results($wpdb->prepare("
        SELECT u.ID, u.display_name, u.user_email, um.meta_value
        FROM {$wpdb->users} u
        INNER JOIN {$wpdb->usermeta} um ON u.ID = um.user_id
        WHERE um.meta_key = %s
        AND um.meta_value LIKE %s
        ORDER BY u.ID ASC
    ", 'updates-promotions', '%Yes, I would like to receive updates, promotions%'));
    
    return array_map(function($user) {
        return [
            'id' => $user->ID,
            'display_name' => $user->display_name,
            'user_email' => $user->user_email,
            'old_value' => $user->meta_value
        ];
    }, $users);
}

/**
 * Perform the actual migration
 */
function migrate_newsletter_optin_data() {
    // Check if migration already completed
    $migration_status = get_option('newsletter_optin_migration_status', 'not_started');
    if ($migration_status === 'completed') {
        return ['success' => false, 'error' => 'Migration has already been completed'];
    }
    
    // Set migration status to in progress
    update_option('newsletter_optin_migration_status', 'in_progress');
    
    try {
        $users_to_migrate = get_users_with_old_optin();
        $migrated_count = 0;
        $errors = [];
        
        foreach ($users_to_migrate as $user_data) {
            $user_id = $user_data['id'];
            
            // Check if user already has the new field set
            $existing_value = get_field('newsletter_optin', 'user_' . $user_id);
            if ($existing_value !== null && $existing_value !== false) {
                // Skip if already set to avoid overwriting
                continue;
            }
            
            // Set the new ACF field to true
            $result = update_field('newsletter_optin', true, 'user_' . $user_id);
            
            if ($result) {
                $migrated_count++;
                
                // Log the migration
                error_log("Newsletter opt-in migrated for user ID: {$user_id}");
            } else {
                $errors[] = "Failed to migrate user ID: {$user_id}";
            }
        }
        
        // Update migration status
        update_option('newsletter_optin_migration_status', 'completed');
        update_option('newsletter_optin_migrated_count', $migrated_count);
        
        if (!empty($errors)) {
            error_log('Newsletter migration errors: ' . implode(', ', $errors));
        }
        
        return [
            'success' => true, 
            'migrated' => $migrated_count,
            'total_found' => count($users_to_migrate),
            'errors' => $errors
        ];
        
    } catch (Exception $e) {
        // Reset migration status on error
        update_option('newsletter_optin_migration_status', 'error');
        
        return [
            'success' => false, 
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Add quick stats to admin dashboard
 */
add_action('wp_dashboard_setup', function() {
    if (current_user_can('manage_options')) {
        wp_add_dashboard_widget(
            'newsletter_migration_stats',
            'Newsletter Migration Status',
            'newsletter_migration_dashboard_widget'
        );
    }
});

function newsletter_migration_dashboard_widget() {
    $migration_status = get_option('newsletter_optin_migration_status', 'not_started');
    $migrated_count = get_option('newsletter_optin_migrated_count', 0);
    
    echo '<p><strong>Newsletter Opt-in Migration:</strong> ';
    
    switch ($migration_status) {
        case 'completed':
            echo '<span style="color: green;">Completed</span> (' . $migrated_count . ' users migrated)';
            break;
        case 'in_progress':
            echo '<span style="color: orange;">In Progress</span>';
            break;
        case 'error':
            echo '<span style="color: red;">Error occurred</span>';
            break;
        default:
            echo '<span style="color: #666;">Not started</span>';
    }
    
    echo '</p>';
    echo '<p><a href="' . admin_url('tools.php?page=tourismiq-migration-tools') . '" class="button">Go to Migration Tools</a></p>';
}