<?php
/**
 * Roles and Capabilities
 * 
 * Handles custom roles and capabilities for the theme.
 */

// Restrict admin access for specific user roles and vendor members
add_action('admin_init', function() {
    if (defined('DOING_AJAX') && DOING_AJAX) {
        return;
    }

    if (!is_user_logged_in() || wp_doing_cron()) {
        return;
    }

    $current_user = wp_get_current_user();
    
    // Allow administrators unrestricted access
    if (in_array('administrator', $current_user->roles)) {
        return;
    }
    
    // Block wp-admin access for member and founder roles
    $blocked_roles = ['member', 'founder'];
    $user_roles = $current_user->roles;
    
    $should_block = false;
    
    // Check role-based blocking
    foreach ($blocked_roles as $blocked_role) {
        if (in_array($blocked_role, $user_roles)) {
            $should_block = true;
            break;
        }
    }
    
    // Also block if user is a vendor team member (regardless of role)
    if (!$should_block && is_user_vendor_member($current_user->ID)) {
        $should_block = true;
    }
    
    if ($should_block) {
        wp_redirect(home_url());
        exit;
    }
});

// Remove admin bar for specific user roles and vendor members
add_action('after_setup_theme', function() {
    if (!is_user_logged_in()) {
        return;
    }
    
    $current_user = wp_get_current_user();
    
    // Always show admin bar for administrators
    if (in_array('administrator', $current_user->roles)) {
        return;
    }
    
    // Hide admin bar for member and founder roles
    $blocked_roles = ['member', 'founder'];
    $user_roles = $current_user->roles;
    
    $should_hide = false;
    
    // Check role-based hiding
    foreach ($blocked_roles as $blocked_role) {
        if (in_array($blocked_role, $user_roles)) {
            $should_hide = true;
            break;
        }
    }
    
    // Also hide if user is a vendor team member (regardless of role)
    if (!$should_hide && is_user_vendor_member($current_user->ID)) {
        $should_hide = true;
    }
    
    if ($should_hide) {
        show_admin_bar(false);
    }
});
