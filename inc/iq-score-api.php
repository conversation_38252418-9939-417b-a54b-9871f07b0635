<?php
/**
 * TourismIQ Score System REST API Endpoints
 * Provides API access to IQ scores, ranks, and leaderboard data
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register REST API endpoints for IQ Score System
 */
function tourismiq_register_iq_score_endpoints() {
    // Leaderboard endpoint
    register_rest_route('tourismiq/v1', '/leaderboard', array(
        'methods' => 'GET',
        'callback' => 'tourismiq_get_leaderboard_endpoint',
        'permission_callback' => '__return_true',
        'args' => array(
            'limit' => array(
                'default' => 20,
                'validate_callback' => function($param) {
                    return is_numeric($param) && $param > 0 && $param <= 100;
                }
            )
        )
    ));

    // User's IQ score endpoint
    register_rest_route('tourismiq/v1', '/iq-score/(?P<user_id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'tourismiq_get_user_iq_score_endpoint',
        'permission_callback' => '__return_true',
        'args' => array(
            'user_id' => array(
                'validate_callback' => function($param) {
                    return is_numeric($param);
                },
                'sanitize_callback' => 'absint',
            ),
        ),
    ));

    // Current user's IQ score endpoint
    register_rest_route('tourismiq/v1', '/iq-score/me', array(
        'methods' => 'GET',
        'callback' => 'tourismiq_get_current_user_iq_score_endpoint',
        'permission_callback' => 'is_user_logged_in',
    ));

    // Manually award points endpoint (admin only)
    register_rest_route('tourismiq/v1', '/iq-score/award', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_award_points_endpoint',
        'permission_callback' => function() {
            return current_user_can('manage_options');
        },
        'args' => array(
            'user_id' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return is_numeric($param);
                },
                'sanitize_callback' => 'absint',
            ),
            'points' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return is_numeric($param) && $param > 0;
                },
                'sanitize_callback' => 'absint',
            ),
            'activity_type' => array(
                'default' => 'manual',
                'sanitize_callback' => 'sanitize_text_field',
            ),
        ),
    ));

    // Get all ranks and their requirements
    register_rest_route('tourismiq/v1', '/iq-score/ranks', array(
        'methods' => 'GET',
        'callback' => 'tourismiq_get_ranks_endpoint',
        'permission_callback' => '__return_true',
    ));

    // Test endpoint for simulating point-awarding activities (logged in users only)
    register_rest_route('tourismiq/v1', '/iq-score/test-award', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_test_award_points_endpoint',
        'permission_callback' => 'is_user_logged_in',
        'args' => array(
            'user_id' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return is_numeric($param);
                },
                'sanitize_callback' => 'absint',
            ),
            'activity_type' => array(
                'required' => true,
                'sanitize_callback' => 'sanitize_text_field',
            ),
        ),
    ));

    // Bulk IQ scores endpoint
    register_rest_route('tourismiq/v1', '/iq-score/bulk', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_get_bulk_iq_scores_endpoint',
        'permission_callback' => '__return_true',
        'args' => array(
            'userIds' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return is_array($param) && !empty($param) && array_filter($param, 'is_numeric') === $param;
                },
            ),
        ),
    ));
    
    // Debug CORS endpoint
    register_rest_route('tourismiq/v1', '/debug/cors', array(
        'methods' => WP_REST_Server::ALLMETHODS,
        'callback' => 'tourismiq_debug_cors_endpoint',
        'permission_callback' => '__return_true',
    ));

    // Clear leaderboard cache endpoint (admin only)
    register_rest_route('tourismiq/v1', '/iq-score/clear-cache', array(
        'methods' => 'POST',
        'callback' => 'tourismiq_clear_leaderboard_cache_endpoint',
        'permission_callback' => function() {
            return current_user_can('manage_options');
        },
    ));
}
add_action('rest_api_init', 'tourismiq_register_iq_score_endpoints');

/**
 * Get leaderboard data
 */
function tourismiq_get_leaderboard_endpoint(WP_REST_Request $request) {
    $limit = $request->get_param('limit');
    
    // Check if this is a cache-busting request
    $force_refresh = $request->get_param('force_refresh') || $request->get_param('_');
    
    // If force refresh is requested, clear the cache first
    if ($force_refresh) {
        if (class_exists('TourismIQ_Performance')) {
            wp_cache_flush_group('tourismiq_scores');
            wp_cache_delete("leaderboard_{$limit}", 'tourismiq_scores');
        }
    }
    
    // Get the global IQ score system instance
    if (class_exists('TourismIQ_Score_System_Instance')) {
        $tourismiq_score_system = TourismIQ_Score_System_Instance::get_instance();
    } else {
        global $tourismiq_score_system;
        if (!$tourismiq_score_system) {
            $tourismiq_score_system = new TourismIQ_Score_System();
        }
    }
    
    $leaderboard = $tourismiq_score_system->get_leaderboard($limit);
    
    // Add position/rank to each entry
    foreach ($leaderboard as $index => &$entry) {
        $entry['position'] = $index + 1;
    }
    
    $response = rest_ensure_response(array(
        'success' => true,
        'data' => $leaderboard,
        'total' => count($leaderboard),
        'cache_busted' => (bool) $force_refresh,
        'timestamp' => current_time('mysql')
    ));
    
    // Set cache control headers to prevent aggressive caching
    $response->header('Cache-Control', 'no-cache, no-store, must-revalidate');
    $response->header('Pragma', 'no-cache');
    $response->header('Expires', '0');
    
    return $response;
}

/**
 * Get specific user's IQ score and rank
 */
function tourismiq_get_user_iq_score_endpoint(WP_REST_Request $request) {
    try {
        $user_id = $request->get_param('user_id');
        
        // Validate user_id
        if (!$user_id || !is_numeric($user_id) || $user_id <= 0) {
            return new WP_Error('invalid_user_id', 'Invalid user ID provided', array('status' => 400));
        }
        
        // Check if user exists
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return new WP_Error('user_not_found', 'User not found', array('status' => 404));
        }
        
        // Get the global IQ score system instance with error handling
        $tourismiq_score_system = null;
        
        try {
            if (class_exists('TourismIQ_Score_System_Instance')) {
                $tourismiq_score_system = TourismIQ_Score_System_Instance::get_instance();
            } else {
                global $tourismiq_score_system;
                if (!$tourismiq_score_system) {
                    // Load the class file if not already loaded
                    if (!class_exists('TourismIQ_Score_System')) {
                        $class_file = get_template_directory() . '/inc/iq-score-system.php';
                        if (file_exists($class_file)) {
                            require_once $class_file;
                        }
                    }
                    $tourismiq_score_system = new TourismIQ_Score_System();
                }
            }
        } catch (Exception $e) {
            error_log('Failed to initialize IQ Score System: ' . $e->getMessage());
            return new WP_Error('system_error', 'Failed to initialize scoring system', array('status' => 500));
        }
        
        if (!$tourismiq_score_system) {
            error_log('IQ Score System is null after initialization attempt');
            return new WP_Error('system_error', 'Scoring system not available', array('status' => 500));
        }
        
        // Get score data with error handling
        $score = 0;
        $rank = null;
        $badge = '';
        $position = 0;
        
        try {
            $score = $tourismiq_score_system->get_user_score($user_id);
            $rank = $tourismiq_score_system->get_user_rank($user_id);
            $badge = $tourismiq_score_system->get_user_badge($user_id);
            $position = tourismiq_get_user_leaderboard_position($user_id);
        } catch (Exception $e) {
            error_log('Error fetching IQ score data for user ' . $user_id . ': ' . $e->getMessage());
            // Continue with default values rather than failing completely
        }
        
        // Ensure we have valid data
        if ($rank === null) {
            $rank = array(
                'name' => 'Novice',
                'min_points' => 0,
                'max_points' => 100,
                'member_badge' => '/images/icons/novice.svg',
                'founder_badge' => '/images/icons/novice-fc.svg'
            );
        }
        
        $response_data = array(
            'success' => true,
            'data' => array(
                'user_id' => intval($user_id),
                'username' => $user->user_login,
                'display_name' => $user->display_name,
                'iq_score' => intval($score),
                'rank' => $rank,
                'badge' => $badge ?: $rank['member_badge'],
                'position' => intval($position)
            )
        );
        
        $response = rest_ensure_response($response_data);
        
        // Add cache headers to reduce server load
        $response->header('Cache-Control', 'private, max-age=60');
        $response->header('X-Content-Type-Options', 'nosniff');
        
        return $response;
        
    } catch (Exception $e) {
        error_log('Unexpected error in tourismiq_get_user_iq_score_endpoint: ' . $e->getMessage());
        return new WP_Error('server_error', 'An unexpected error occurred', array('status' => 500));
    }
}

/**
 * Get current user's IQ score and rank
 */
function tourismiq_get_current_user_iq_score_endpoint(WP_REST_Request $request) {
    $user_id = get_current_user_id();
    
    // Reuse the user score endpoint logic
    $fake_request = new WP_REST_Request();
    $fake_request->set_param('user_id', $user_id);
    
    return tourismiq_get_user_iq_score_endpoint($fake_request);
}

/**
 * Manually award points (admin only)
 */
function tourismiq_award_points_endpoint(WP_REST_Request $request) {
    $user_id = $request->get_param('user_id');
    $points = $request->get_param('points');
    $activity_type = $request->get_param('activity_type');
    
    // Check if user exists
    $user = get_user_by('id', $user_id);
    if (!$user) {
        return new WP_Error('user_not_found', 'User not found', array('status' => 404));
    }
    
    // Get the global IQ score system instance
    if (class_exists('TourismIQ_Score_System_Instance')) {
        $tourismiq_score_system = TourismIQ_Score_System_Instance::get_instance();
    } else {
        global $tourismiq_score_system;
        if (!$tourismiq_score_system) {
            $tourismiq_score_system = new TourismIQ_Score_System();
        }
    }
    
    $success = $tourismiq_score_system->award_points($user_id, $points, $activity_type);
    
    if ($success) {
        $new_score = $tourismiq_score_system->get_user_score($user_id);
        $rank = $tourismiq_score_system->get_user_rank($user_id);
        
        return rest_ensure_response(array(
            'success' => true,
            'message' => "Awarded {$points} points to {$user->display_name}",
            'data' => array(
                'user_id' => $user_id,
                'points_awarded' => $points,
                'new_total' => $new_score,
                'rank' => $rank
            )
        ));
    } else {
        return new WP_Error('award_failed', 'Failed to award points', array('status' => 500));
    }
}

/**
 * Test endpoint for simulating point-awarding activities (logged in users only)
 */
function tourismiq_test_award_points_endpoint(WP_REST_Request $request) {
    $user_id = $request->get_param('user_id');
    $activity_type = $request->get_param('activity_type');
    
    // Security check: only allow users to award points to themselves for testing
    $current_user_id = get_current_user_id();
    if ($user_id != $current_user_id) {
        return new WP_Error('permission_denied', 'You can only award test points to yourself', array('status' => 403));
    }
    
    // Check if user exists
    $user = get_user_by('id', $user_id);
    if (!$user) {
        return new WP_Error('user_not_found', 'User not found', array('status' => 404));
    }
    
    // Get the global IQ score system instance
    if (class_exists('TourismIQ_Score_System_Instance')) {
        $tourismiq_score_system = TourismIQ_Score_System_Instance::get_instance();
    } else {
        global $tourismiq_score_system;
        if (!$tourismiq_score_system) {
            $tourismiq_score_system = new TourismIQ_Score_System();
        }
    }
    
    // Define test activity points mapping
    $activity_points = array(
        'thought_leadership_post' => 8,
        'resource_post' => 5,
        'comment' => 3,
        'upvote' => 3,
        'news_post' => 2,
        'add_connection' => 1,
        'daily_visit' => 1
    );
    
    // Get points for this activity type
    $points = isset($activity_points[$activity_type]) ? $activity_points[$activity_type] : 1;
    
    // Award the points
    $success = $tourismiq_score_system->award_points($user_id, $points, $activity_type . '_test');
    
    if ($success) {
        $new_score = $tourismiq_score_system->get_user_score($user_id);
        $rank = $tourismiq_score_system->get_user_rank($user_id);
        
        return rest_ensure_response(array(
            'success' => true,
            'message' => "Test: Awarded {$points} points for {$activity_type}",
            'data' => array(
                'user_id' => $user_id,
                'points_awarded' => $points,
                'new_score' => $new_score,
                'activity_type' => $activity_type,
                'rank' => $rank
            )
        ));
    } else {
        return new WP_Error('award_failed', 'Failed to award test points', array('status' => 500));
    }
}

/**
 * Get all available ranks and their requirements
 */
function tourismiq_get_ranks_endpoint(WP_REST_Request $request) {
    return rest_ensure_response(array(
        'success' => true,
        'data' => TourismIQ_Score_System::RANKS
    ));
}

/**
 * Helper function to get user's position on leaderboard
 */
function tourismiq_get_user_leaderboard_position($user_id) {
    try {
        global $wpdb;
        
        // Validate user_id
        if (!$user_id || !is_numeric($user_id) || $user_id <= 0) {
            return 0;
        }
        
        $user_score = (int) get_user_meta($user_id, 'iq_score', true);
        
        // If user has no score, they're not on the leaderboard
        if ($user_score <= 0) {
            return 0;
        }
        
        // Count how many users have higher scores with error handling
        $higher_scores = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*)
            FROM {$wpdb->usermeta}
            WHERE meta_key = 'iq_score'
            AND CAST(meta_value AS UNSIGNED) > %d
        ", $user_score));
        
        // Handle database errors
        if ($higher_scores === null) {
            error_log('Database error in tourismiq_get_user_leaderboard_position for user ' . $user_id);
            return 0;
        }
        
        return (int) $higher_scores + 1;
    } catch (Exception $e) {
        error_log('Error in tourismiq_get_user_leaderboard_position: ' . $e->getMessage());
        return 0;
    }
}

/**
 * Initialize global score system instance
 */
function tourismiq_init_score_system() {
    if (class_exists('TourismIQ_Score_System_Instance')) {
        TourismIQ_Score_System_Instance::get_instance();
    } else {
        global $tourismiq_score_system;
        if (!$tourismiq_score_system) {
            $tourismiq_score_system = new TourismIQ_Score_System();
        }
    }
}
add_action('init', 'tourismiq_init_score_system');

/**
 * Clear leaderboard cache endpoint (admin only)
 */
function tourismiq_clear_leaderboard_cache_endpoint(WP_REST_Request $request) {
    // Clear all WordPress caches
    wp_cache_flush();
    
    // Clear specific IQ score caches
    if (class_exists('TourismIQ_Performance')) {
        wp_cache_flush_group('tourismiq_scores');
        // Clear all possible leaderboard cache variants
        for ($limit = 1; $limit <= 100; $limit++) {
            wp_cache_delete("leaderboard_{$limit}", 'tourismiq_scores');
        }
    }
    
    // Clear any transients related to scores
    global $wpdb;
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_iq_score_%' OR option_name LIKE '_transient_timeout_iq_score_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_leaderboard_%' OR option_name LIKE '_transient_timeout_leaderboard_%'");
    
    return rest_ensure_response(array(
        'success' => true,
        'message' => 'Leaderboard cache cleared successfully'
    ));
}

/**
 * Get bulk IQ scores for multiple users
 */
function tourismiq_get_bulk_iq_scores_endpoint(WP_REST_Request $request) {
    $user_ids = $request->get_param('userIds');
    
    if (!is_array($user_ids) || empty($user_ids)) {
        return new WP_Error(
            'invalid_user_ids',
            'Valid user IDs array is required',
            array('status' => 400)
        );
    }

    // Limit to prevent abuse
    if (count($user_ids) > 100) {
        return new WP_Error(
            'too_many_users',
            'Maximum 100 users allowed per request',
            array('status' => 400)
        );
    }

    $start_time = microtime(true);
    $scores = array();
    
    // Sanitize user IDs
    $sanitized_user_ids = array_map('intval', $user_ids);
    $sanitized_user_ids = array_filter($sanitized_user_ids, function($id) { return $id > 0; });

    if (empty($sanitized_user_ids)) {
        return rest_ensure_response(array('scores' => array()));
    }

    // Get the global IQ score system instance
    $tourismiq_score_system = null;
    
    try {
        if (class_exists('TourismIQ_Score_System_Instance')) {
            $tourismiq_score_system = TourismIQ_Score_System_Instance::get_instance();
        } else {
            global $tourismiq_score_system;
            if (!$tourismiq_score_system) {
                // Load the class file if not already loaded
                if (!class_exists('TourismIQ_Score_System')) {
                    $class_file = get_template_directory() . '/inc/iq-score-system.php';
                    if (file_exists($class_file)) {
                        require_once $class_file;
                    }
                }
                $tourismiq_score_system = new TourismIQ_Score_System();
            }
        }
    } catch (Exception $e) {
        error_log('Failed to initialize IQ Score System: ' . $e->getMessage());
        return new WP_Error('system_error', 'Failed to initialize scoring system', array('status' => 500));
    }

    // Get scores for all users in a single operation
    foreach ($sanitized_user_ids as $user_id) {
        // Check if user exists
        if (!get_user_by('id', $user_id)) {
            $scores[$user_id] = array(
                'score' => 0,
                'rank' => 'Novice',
                'rank_data' => array(
                    'name' => 'Novice',
                    'min_score' => 0,
                    'max_score' => 100,
                    'color' => '#gray-500',
                    'icon' => '/images/icons/novice.svg'
                ),
                'total_activities' => 0,
                'activities' => array(),
            );
            continue;
        }

        // Get user's IQ score using the same logic as individual endpoint
        $score = 0;
        $rank = null;
        
        try {
            if ($tourismiq_score_system) {
                $score = $tourismiq_score_system->get_user_score($user_id);
                $rank = $tourismiq_score_system->get_user_rank($user_id);
            }
        } catch (Exception $e) {
            error_log('Error fetching IQ score data for user ' . $user_id . ': ' . $e->getMessage());
            // Continue with default values
        }
        
        // Ensure we have valid rank data
        if ($rank === null) {
            $rank = array(
                'name' => 'Novice',
                'min_points' => 0,
                'max_points' => 100,
                'member_badge' => '/images/icons/novice.svg',
                'founder_badge' => '/images/icons/novice-fc.svg'
            );
        }

        $scores[$user_id] = array(
            'score' => intval($score),
            'rank' => $rank['name'],
            'rank_data' => array(
                'name' => $rank['name'],
                'min_score' => $rank['min_points'],
                'max_score' => $rank['max_points'] ?? null,
                'color' => '#3b82f6', // Default blue color
                'icon' => $rank['member_badge']
            ),
            'total_activities' => 0, // We'll skip this for performance
            'activities' => array(), // We'll skip this for performance
        );
    }

    $duration = round((microtime(true) - $start_time) * 1000, 2);
    error_log("[Bulk IQ Score] Query completed in {$duration}ms for " . count($sanitized_user_ids) . " users");

    // Add cache headers
    if (!headers_sent()) {
        header('Cache-Control: public, max-age=300'); // 5 minutes cache
        header('Pragma: cache');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 300) . ' GMT');
    }

    return rest_ensure_response(array('scores' => $scores));
}

/**
 * Debug CORS endpoint
 */
function tourismiq_debug_cors_endpoint(WP_REST_Request $request) {
    $headers = getallheaders();
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : 'no-origin';
    
    return rest_ensure_response(array(
        'success' => true,
        'message' => 'CORS test successful',
        'request_headers' => $headers,
        'origin' => $origin,
        'method' => $_SERVER['REQUEST_METHOD'],
        'cors_headers_sent' => headers_list(),
    ));
} 