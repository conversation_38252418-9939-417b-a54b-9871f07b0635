<?php
/**
 * TourismIQ Messaging API
 *
 * Expected database table schema:
 * CREATE TABLE wp_messages (
 *   id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
 *   conversation_id varchar(255) NOT NULL,
 *   sender_id bigint(20) UNSIGNED NOT NULL,
 *   recipient_id bigint(20) UNSIGNED NOT NULL,
 *   content text NOT NULL,
 *   created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *   updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
 *   is_read tinyint(1) NOT NULL DEFAULT 0,
 *   PRIMARY KEY (id),
 *   KEY conversation_id (conversation_id),
 *   KEY sender_id (sender_id),
 *   KEY recipient_id (recipient_id),
 *   KEY is_read (is_read),
 *   <PERSON>EY created_at (created_at),
 *   FOREIGN KEY (sender_id) REFERENCES wp_users(ID) ON DELETE CASCADE,
 *   FOREIGN KEY (recipient_id) REFERENCES wp_users(ID) ON DELETE CASCADE
 * );
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Messaging {
    private static $instance = null;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        add_action('init', array($this, 'maybe_create_messages_table'));
    }

    /**
     * Register REST API endpoints for messaging
     */
    public function register_rest_routes() {
        // Send a message
        register_rest_route('tourismiq/v1', '/messages/send', array(
            'methods' => 'POST',
            'callback' => array($this, 'send_message'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'recipient_id' => array(
                    'required' => true,
                    'type' => 'integer',
                    'validate_callback' => function($param) {
                        return is_numeric($param) && $param > 0;
                    }
                ),
                'content' => array(
                    'required' => true,
                    'type' => 'string',
                    'validate_callback' => function($param) {
                        return !empty(trim($param));
                    }
                ),
            ),
        ));

        // Get conversations for current user
        register_rest_route('tourismiq/v1', '/messages/conversations', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_conversations'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
        ));

        // Get messages for a specific conversation
        register_rest_route('tourismiq/v1', '/messages/conversation/(?P<user_id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_conversation_messages'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'page' => array(
                    'default' => 1,
                    'type' => 'integer',
                    'minimum' => 1,
                ),
                'per_page' => array(
                    'default' => 50,
                    'type' => 'integer',
                    'minimum' => 1,
                    'maximum' => 100,
                ),
            ),
        ));

        // Mark messages as read
        register_rest_route('tourismiq/v1', '/messages/mark-read', array(
            'methods' => 'POST',
            'callback' => array($this, 'mark_messages_as_read'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'conversation_with' => array(
                    'required' => true,
                    'type' => 'integer',
                    'validate_callback' => function($param) {
                        return is_numeric($param) && $param > 0;
                    }
                ),
            ),
        ));

        // Get unread message count
        register_rest_route('tourismiq/v1', '/messages/unread-count', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_unread_count'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
        ));

        // Start a new conversation (check if users can message each other)
        register_rest_route('tourismiq/v1', '/messages/can-message/(?P<user_id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'can_message_user'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
        ));
    }

    /**
     * Send a message
     */
    public function send_message($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $recipient_id = tourismiq_validate_input($request->get_param('recipient_id'), 'int');
        $content = tourismiq_validate_input($request->get_param('content'), 'textarea', 1000);

        // Check permissions
        if (!tourismiq_check_permission('send_message', $current_user_id)) {
            return new WP_Error(
                'insufficient_permissions',
                'You do not have permission to send messages',
                array('status' => 403)
            );
        }

        // Validate input
        if (empty($content)) {
            return new WP_Error(
                'empty_content',
                'Message content cannot be empty',
                array('status' => 400)
            );
        }

        if (!$recipient_id || $recipient_id <= 0) {
            return new WP_Error(
                'invalid_recipient_id',
                'Invalid recipient ID',
                array('status' => 400)
            );
        }

        // Validate recipient exists
        if (!get_user_by('id', $recipient_id)) {
            return new WP_Error(
                'invalid_recipient',
                'Recipient does not exist',
                array('status' => 404)
            );
        }

        // Don't allow messaging yourself
        if ($current_user_id == $recipient_id) {
            return new WP_Error(
                'self_message',
                'Cannot send message to yourself',
                array('status' => 400)
            );
        }

        // Check if users are connected
        if (!$this->are_users_connected($current_user_id, $recipient_id)) {
            return new WP_Error(
                'not_connected',
                'You must be connected to send messages to this user',
                array('status' => 403)
            );
        }

        // Generate conversation ID (consistent regardless of who initiated)
        $conversation_id = $this->generate_conversation_id($current_user_id, $recipient_id);

        // Sanitize content for storage
        $sanitized_content = wp_kses_post($content);

        // Insert message into database with prepared statement
        $table_name = $wpdb->prefix . 'messages';
        $result = $wpdb->insert(
            $table_name,
            array(
                'conversation_id' => $conversation_id,
                'sender_id' => $current_user_id,
                'recipient_id' => $recipient_id,
                'content' => $sanitized_content,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql'),
                'is_read' => 0
            ),
            array('%s', '%d', '%d', '%s', '%s', '%s', '%d')
        );

        if ($result === false) {
            tourismiq_log_security('message_send_failed', [
                'user_id' => $current_user_id,
                'recipient_id' => $recipient_id,
                'error' => $wpdb->last_error
            ]);
            
            return new WP_Error(
                'message_failed',
                'Failed to send message',
                array('status' => 500)
            );
        }

        $message_id = $wpdb->insert_id;

        // Get the inserted message with user data
        $message = $this->get_message_with_user_data($message_id);

        // Send real-time notification via Socket.IO
        $this->send_message_notification($recipient_id, $current_user_id, $sanitized_content, $message_id);

        return rest_ensure_response(array(
            'success' => true,
            'message_id' => $message_id,
            'message' => $message,
            'conversation_id' => $conversation_id
        ));
    }

    /**
     * Get conversations for current user
     */
    public function get_conversations($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $table_name = $wpdb->prefix . 'messages';

        // Get latest message for each conversation
        $conversations = $wpdb->get_results($wpdb->prepare("
            SELECT 
                m1.*,
                CASE 
                    WHEN m1.sender_id = %d THEN m1.recipient_id 
                    ELSE m1.sender_id 
                END as other_user_id,
                COUNT(m2.id) as unread_count
            FROM {$table_name} m1
            LEFT JOIN {$table_name} m2 ON (
                m1.conversation_id = m2.conversation_id 
                AND m2.recipient_id = %d 
                AND m2.is_read = 0
            )
            WHERE m1.id IN (
                SELECT MAX(id) 
                FROM {$table_name} 
                WHERE sender_id = %d OR recipient_id = %d
                GROUP BY conversation_id
            )
            GROUP BY m1.conversation_id
            ORDER BY m1.created_at DESC
        ", $current_user_id, $current_user_id, $current_user_id, $current_user_id));

        $formatted_conversations = array();
        foreach ($conversations as $conversation) {
            $other_user = get_user_by('id', $conversation->other_user_id);
            if (!$other_user) continue;

            $user_meta = get_user_meta($conversation->other_user_id);
            
            // Get profile picture from ACF
            $profile_picture = get_field('profile_picture', 'user_' . $conversation->other_user_id);
            $avatar_url = null;
            
            if ($profile_picture) {
                if (is_array($profile_picture) && isset($profile_picture['url'])) {
                    $avatar_url = $profile_picture['url'];
                } elseif (is_string($profile_picture)) {
                    $avatar_url = $profile_picture;
                }
            }
            
            // Let frontend handle the fallback to placeholder if no ACF profile picture
            if (!$avatar_url) {
                $avatar_url = null; // Let frontend handle the fallback to placeholder
            }

            $formatted_conversations[] = array(
                'id' => $conversation->conversation_id,
                'participants' => array(
                    array(
                        'id' => (string)$conversation->other_user_id,
                        'username' => $other_user->user_login,
                        'firstName' => $user_meta['first_name'][0] ?? null,
                        'lastName' => $user_meta['last_name'][0] ?? null,
                        'avatar' => $avatar_url ? array('url' => $avatar_url) : null,
                        'isOnline' => false // This would need real-time status tracking
                    )
                ),
                'lastMessage' => array(
                    'id' => (string)$conversation->id,
                    'conversationId' => $conversation->conversation_id,
                    'senderId' => (string)$conversation->sender_id,
                    'content' => $conversation->content,
                    'timestamp' => $conversation->created_at,
                    'isRead' => (bool)$conversation->is_read
                ),
                'unreadCount' => (int)$conversation->unread_count
            );
        }

        return rest_ensure_response($formatted_conversations);
    }

    /**
     * Get messages for a specific conversation
     */
    public function get_conversation_messages($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $other_user_id = $request->get_param('user_id');
        $page = $request->get_param('page');
        $per_page = $request->get_param('per_page');
        $offset = ($page - 1) * $per_page;

        // Validate other user exists
        if (!get_user_by('id', $other_user_id)) {
            return new WP_Error(
                'invalid_user',
                'User does not exist',
                array('status' => 404)
            );
        }

        $conversation_id = $this->generate_conversation_id($current_user_id, $other_user_id);
        $table_name = $wpdb->prefix . 'messages';

        // Get messages for this conversation
        $messages = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$table_name}
            WHERE conversation_id = %s
            ORDER BY created_at DESC
            LIMIT %d OFFSET %d
        ", $conversation_id, $per_page, $offset));

        $formatted_messages = array();
        foreach (array_reverse($messages) as $message) { // Reverse to show oldest first
            $formatted_messages[] = array(
                'id' => (string)$message->id,
                'conversationId' => $message->conversation_id,
                'senderId' => (string)$message->sender_id,
                'content' => $message->content,
                'timestamp' => $message->created_at,
                'isRead' => (bool)$message->is_read
            );
        }

        return rest_ensure_response($formatted_messages);
    }

    /**
     * Mark messages as read
     */
    public function mark_messages_as_read($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $other_user_id = $request->get_param('conversation_with');

        $conversation_id = $this->generate_conversation_id($current_user_id, $other_user_id);
        $table_name = $wpdb->prefix . 'messages';

        // Mark all unread messages in this conversation as read for the current user
        $wpdb->update(
            $table_name,
            array('is_read' => 1, 'updated_at' => current_time('mysql')),
            array(
                'conversation_id' => $conversation_id,
                'recipient_id' => $current_user_id,
                'is_read' => 0
            ),
            array('%d', '%s'),
            array('%s', '%d', '%d')
        );

        return rest_ensure_response(array(
            'success' => true,
            'message' => 'Messages marked as read'
        ));
    }

    /**
     * Get unread message count for current user
     */
    public function get_unread_count($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $table_name = $wpdb->prefix . 'messages';

        $unread_count = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*) FROM {$table_name}
            WHERE recipient_id = %d AND is_read = 0
        ", $current_user_id));

        return rest_ensure_response(array(
            'unreadCount' => (int)$unread_count
        ));
    }

    /**
     * Check if current user can message another user
     */
    public function can_message_user($request) {
        $current_user_id = get_current_user_id();
        $other_user_id = $request->get_param('user_id');

        // Validate other user exists
        if (!get_user_by('id', $other_user_id)) {
            return new WP_Error(
                'invalid_user',
                'User does not exist',
                array('status' => 404)
            );
        }

        // Check if users are connected
        $can_message = $this->are_users_connected($current_user_id, $other_user_id);

        return rest_ensure_response(array(
            'canMessage' => $can_message,
            'reason' => $can_message ? 'connected' : 'not_connected'
        ));
    }

    /**
     * Generate a consistent conversation ID for two users
     */
    private function generate_conversation_id($user1_id, $user2_id) {
        $ids = array($user1_id, $user2_id);
        sort($ids);
        return 'conv_' . implode('_', $ids);
    }

    /**
     * Check if two users are connected
     */
    private function are_users_connected($user1_id, $user2_id) {
        global $wpdb;

        $connections_table = $wpdb->prefix . 'user_connections';
        
        // Check if the connections table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$connections_table'") != $connections_table) {
            return false;
        }

        $connection = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$connections_table}
            WHERE ((requester_id = %d AND requested_id = %d) 
                OR (requester_id = %d AND requested_id = %d))
            AND status = 'accepted'
        ", $user1_id, $user2_id, $user2_id, $user1_id));

        return !empty($connection);
    }

    /**
     * Get message with user data
     */
    private function get_message_with_user_data($message_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'messages';
        $message = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$table_name} WHERE id = %d
        ", $message_id));

        if (!$message) {
            return null;
        }

        return array(
            'id' => (string)$message->id,
            'conversationId' => $message->conversation_id,
            'senderId' => (string)$message->sender_id,
            'content' => $message->content,
            'timestamp' => $message->created_at,
            'isRead' => (bool)$message->is_read
        );
    }

    /**
     * Send message notification via Socket.IO
     */
    private function send_message_notification($recipient_id, $sender_id, $content, $message_id) {
        $sender = get_user_by('id', $sender_id);
        if (!$sender) {
            return;
        }

        $sender_meta = get_user_meta($sender_id);
        $sender_name = trim(($sender_meta['first_name'][0] ?? '') . ' ' . ($sender_meta['last_name'][0] ?? ''));
        if (empty($sender_name)) {
            $sender_name = $sender->user_login;
        }

        // Get sender avatar from ACF
        $profile_picture = get_field('profile_picture', 'user_' . $sender_id);
        $sender_avatar = null;
        
        if ($profile_picture) {
            if (is_array($profile_picture) && isset($profile_picture['url'])) {
                $sender_avatar = $profile_picture['url'];
            } elseif (is_string($profile_picture)) {
                $sender_avatar = $profile_picture;
            }
        }
        
        // Let frontend handle the fallback to placeholder if no ACF profile picture
        if (!$sender_avatar) {
            $sender_avatar = null; // Let frontend handle the fallback to placeholder
        }

        // Truncate content for notification
        $notification_content = strlen($content) > 100 ? substr($content, 0, 97) . '...' : $content;

        $notification_data = array(
            'recipientId' => $recipient_id,
            'type' => 'message',
            'content' => $sender_name . ': ' . $notification_content,
            'referenceId' => $message_id,
            'referenceType' => 'message',
            'senderId' => $sender_id,
            'senderName' => $sender_name,
            'senderAvatar' => $sender_avatar,
            'messageContent' => $content
        );

        // Send to Socket.IO server
        $socket_server_url = defined('SOCKET_SERVER_URL') ? SOCKET_SERVER_URL : 'http://localhost:3000';
        $socket_url = $socket_server_url . '/api/internal/send-notification';
        
        $response = wp_remote_post($socket_url, array(
            'body' => json_encode($notification_data),
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'timeout' => 5,
        ));

        if (is_wp_error($response)) {
            error_log("Message notification failed: " . $response->get_error_message());
        } else {
            $response_code = wp_remote_retrieve_response_code($response);
            error_log("Message notification sent to {$socket_url}: HTTP {$response_code}");
        }
    }

    /**
     * Create messages table if it doesn't exist
     */
    public function maybe_create_messages_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'messages';
        
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $table_name (
                id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                conversation_id varchar(255) NOT NULL,
                sender_id bigint(20) UNSIGNED NOT NULL,
                recipient_id bigint(20) UNSIGNED NOT NULL,
                content text NOT NULL,
                created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                is_read tinyint(1) NOT NULL DEFAULT 0,
                PRIMARY KEY (id),
                KEY conversation_id (conversation_id),
                KEY sender_id (sender_id),
                KEY recipient_id (recipient_id),
                KEY is_read (is_read),
                KEY created_at (created_at)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
}

// Initialize the messaging system
TourismIQ_Messaging::get_instance(); 