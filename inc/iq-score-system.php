<?php
/**
 * TourismIQ Score System
 * Handles point tracking, ranks, and badges for user gamification
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Score_System {
    
    /**
     * Point values for different activities
     */
    const POINTS = [
        'thought_leadership_post' => 8,
        'resource_post' => 5,
        'news_post' => 2,
        'event_post' => 1,
        'create_account' => 10,
        'social_media_link' => 2,
        'profile_image' => 5,
        'add_connection' => 1,
        'comment' => 3,
        'upvote' => 3,
        'newsletter_subscribe' => 6,
        'daily_visit' => 1,
        'forum_question' => 3,
        'forum_response' => 1,
    ];

    /**
     * Rank definitions with point ranges and badge paths
     */
    const RANKS = [
        [
            'name' => 'Novice',
            'min_points' => 0,
            'max_points' => 100,
            'member_badge' => '/images/icons/novice.svg',
            'founder_badge' => '/images/icons/novice-fc.svg'
        ],
        [
            'name' => 'Contributor',
            'min_points' => 101,
            'max_points' => 300,
            'member_badge' => '/images/icons/contributor.svg',
            'founder_badge' => '/images/icons/contributor-fc.svg'
        ],
        [
            'name' => 'Engager',
            'min_points' => 301,
            'max_points' => 500,
            'member_badge' => '/images/icons/engager.svg',
            'founder_badge' => '/images/icons/engager-fc.svg'
        ],
        [
            'name' => 'Influencer',
            'min_points' => 501,
            'max_points' => 700,
            'member_badge' => '/images/icons/influencer.svg',
            'founder_badge' => '/images/icons/influencer-fc.svg'
        ],
        [
            'name' => 'Expert',
            'min_points' => 701,
            'max_points' => 900,
            'member_badge' => '/images/icons/expert.svg',
            'founder_badge' => '/images/icons/expert-fc.svg'
        ],
        [
            'name' => 'Master',
            'min_points' => 901,
            'max_points' => ********,
            'member_badge' => '/images/icons/master.svg',
            'founder_badge' => '/images/icons/master-fc.svg'
        ]
    ];

    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // User registration
        add_action('user_register', [$this, 'award_account_creation_points']);
        
        // Post publishing - use a later hook to ensure meta fields are saved
        // Priority 30 ensures this runs after ACF field sync (priority 20)
        add_action('transition_post_status', [$this, 'award_post_points'], 30, 3);
        
        // Also hook into rest_after_insert_post to catch REST API posts after meta is saved
        // Priority 30 ensures this runs after ACF field sync (priority 20)
        add_action('rest_after_insert_post', [$this, 'award_post_points_rest'], 30, 3);
        
        // Comments
        add_action('wp_insert_comment', [$this, 'award_comment_points'], 10, 2);
        
        // Profile updates
        add_action('personal_options_update', [$this, 'check_profile_updates']);
        add_action('edit_user_profile_update', [$this, 'check_profile_updates']);
        
        // Daily visit tracking
        add_action('wp_login', [$this, 'track_daily_visit'], 10, 2);
        
        // Connection points (we'll hook into existing connection system)
        add_action('tourismiq_connection_accepted', [$this, 'award_connection_points'], 10, 2);
        
        // Post upvotes (we'll hook into existing upvote system)
        add_action('tourismiq_post_upvoted', [$this, 'award_upvote_points'], 10, 2);
    }

    /**
     * Award points to a user and send notification
     */
    public function award_points($user_id, $points, $activity_type = '') {
        // Validate inputs
        $user_id = tourismiq_validate_input($user_id, 'int');
        $points = tourismiq_validate_input($points, 'int');
        $activity_type = tourismiq_validate_input($activity_type, 'text', 50);

        if (!$user_id || $points <= 0) {
            return false;
        }

        // Verify user exists
        if (!get_user_by('id', $user_id)) {
            tourismiq_log_security('invalid_user_points_award', [
                'user_id' => $user_id,
                'points' => $points,
                'activity_type' => $activity_type
            ]);
            return false;
        }

        // Prevent point manipulation - limit max points per action
        $max_points_per_action = 50;
        if ($points > $max_points_per_action) {
            tourismiq_log_security('excessive_points_attempt', [
                'user_id' => $user_id,
                'attempted_points' => $points,
                'activity_type' => $activity_type
            ]);
            $points = $max_points_per_action;
        }

        // For post-related activities, we need to be more specific to allow multiple posts
        // but still prevent rapid-fire duplicate API calls
        if (in_array($activity_type, ['thought_leadership_post', 'resource_post', 'news_post', 'event_post', 'post_publish'])) {
            // For posts, use a very short window (2 seconds) to prevent only rapid duplicates
            $duplicate_window = 2;
            $recent_award_key = "iq_recent_{$user_id}_{$activity_type}_" . floor(time() / 2); // Group by 2-second windows
        } else {
            // For other activities like upvotes/comments, use the shorter window
            $duplicate_window = in_array($activity_type, ['upvote', 'comment']) ? 5 : 10;
            $recent_award_key = "iq_recent_{$user_id}_{$activity_type}";
        }
        
        // Check if we've already awarded points for this exact activity recently
        if (get_transient($recent_award_key)) {
            return false;
        }
        
        // Set a transient to prevent duplicates
        set_transient($recent_award_key, true, $duplicate_window);

        $current_score = (int) get_user_meta($user_id, 'iq_score', true);
        $old_rank = $this->calculate_rank_from_score($current_score);
        $new_score = $current_score + $points;
        $new_rank = $this->calculate_rank_from_score($new_score);
        
        // Update user meta
        update_user_meta($user_id, 'iq_score', $new_score);
        
        // Clear leaderboard cache when scores change
        if (class_exists('TourismIQ_Performance')) {
            // Clear all leaderboard caches since rankings may have changed
            wp_cache_flush_group('tourismiq_scores');
        }
        
        // Log the points award for audit trail
        tourismiq_log_security('points_awarded', [
            'user_id' => $user_id,
            'points' => $points,
            'activity_type' => $activity_type,
            'new_score' => $new_score
        ]);
        
        // Send socket notification for points earned
        $this->send_points_notification($user_id, $points, $activity_type, $new_score, $old_rank, $new_rank);
        
        return true;
    }

    /**
     * Send socket notification for points earned
     */
    private function send_points_notification($user_id, $points, $activity_type, $new_score, $old_rank, $new_rank) {
        // Create notification content based on activity type
        $content = $this->get_notification_content($points, $activity_type);
        
        // Check if user ranked up
        $ranked_up = $new_rank['name'] !== $old_rank['name'];
        
        if ($ranked_up) {
            $content .= " 🎉 You've been promoted to {$new_rank['name']} rank!";
        }

        // Prepare notification data
        $notification_data = array(
            'recipient_id' => $user_id,
            'sender_id' => 0, // System notification
            'type' => 'iq_points_earned',
            'content' => $content,
            'reference_id' => $new_score,
            'reference_type' => 'iq_score',
            'sender_name' => 'TourismIQ System',
            'sender_avatar' => '/images/icons/logo.png', // System avatar
            'activity_type' => $activity_type,
            'points_earned' => $points,
            'new_total' => $new_score,
            'ranked_up' => $ranked_up,
            'new_rank' => $new_rank['name'],
            'timestamp' => time(),
        );

        
        if (function_exists('tourismiq_store_and_send_notification')) {
            tourismiq_store_and_send_notification($notification_data);
        } else {
            // Fallback - trigger socket notification directly
            do_action('tourismiq_notification_sent', $notification_data);
        }
    }

    /**
     * Get human-readable notification content for points earned
     */
    private function get_notification_content($points, $activity_type) {
        // Clean up activity type by removing "_test" suffix
        $clean_activity_type = str_replace('_test', '', $activity_type);
        
        $messages = array(
            'create_account' => "Welcome to TourismIQ! You earned {$points} points for creating your account.",
            'post_publish' => "Great content! You earned {$points} points for publishing a post.",
            'thought_leadership_post' => "Excellent insight! You earned {$points} points for your thought leadership post.",
            'resource_post' => "Great resource! You earned {$points} points for sharing valuable content.",
            'news_post' => "Great content! You earned {$points} points for your post.",
            'event_post' => "Event shared! You earned {$points} points for posting an event.",
            'comment' => "Thanks for engaging! You earned {$points} points for commenting.",
            'profile_image' => "Looking good! You earned {$points} points for uploading your profile image.",
            'social_media_link' => "Stay connected! You earned {$points} points for adding a social media link.",
            'daily_visit' => "Welcome back! You earned {$points} points for your daily visit.",
            'add_connection' => "Building connections! You earned {$points} points for connecting with another member.",
            'connection' => "Building connections! You earned {$points} points for connecting with another member.",
            'upvote' => "Great participation! You earned {$points} points for upvoting content.",
            'newsletter_subscribe' => "Stay informed! You earned {$points} points for subscribing to our newsletter.",
            'forum_question' => "Great question! You earned {$points} points for asking the community.",
            'forum_response' => "Thanks for helping! You earned {$points} points for responding to a question.",
            'manual' => "You've been awarded {$points} IQ points!"
        );

        // Use cleaned activity type for lookup
        return isset($messages[$clean_activity_type]) 
            ? $messages[$clean_activity_type] 
            : "You earned {$points} IQ points for {$this->format_activity_name($clean_activity_type)}!";
    }

    /**
     * Format activity type name for display
     */
    private function format_activity_name($activity_type) {
        // Convert snake_case to Title Case
        $formatted = str_replace('_', ' ', $activity_type);
        return ucwords($formatted);
    }

    /**
     * Get user's current IQ score
     */
    public function get_user_score($user_id) {
        return (int) get_user_meta($user_id, 'iq_score', true);
    }

    /**
     * Get user's rank based on score
     */
    public function get_user_rank($user_id) {
        $score = $this->get_user_score($user_id);
        return $this->calculate_rank_from_score($score);
    }

    /**
     * Calculate rank from score
     */
    public function calculate_rank_from_score($score) {
        foreach (self::RANKS as $rank) {
            if ($score >= $rank['min_points'] && $score <= $rank['max_points']) {
                return $rank;
            }
        }
        
        // Default to Novice if something goes wrong
        return self::RANKS[0];
    }

    /**
     * Get badge path for user
     */
    public function get_user_badge($user_id) {
        $rank = $this->get_user_rank($user_id);
        $user = get_user_by('id', $user_id);
        
        // Check if user is founder
        $is_founder = in_array('founder', $user->roles);
        
        return $is_founder ? $rank['founder_badge'] : $rank['member_badge'];
    }

    /**
     * Award points for account creation
     */
    public function award_account_creation_points($user_id) {
        $this->award_points($user_id, self::POINTS['create_account'], 'create_account');
    }

    /**
     * Award points for REST API post creation (runs after meta fields are saved)
     */
    public function award_post_points_rest($post, $request, $creating) {
        // Only process if this is a new post being created
        if (!$creating || $post->post_status !== 'publish') {
            return;
        }
        
        // Use the same logic as the regular post points method
        $this->process_post_points($post, 'rest_api');
    }

    /**
     * Award points for publishing posts
     */
    public function award_post_points($new_status, $old_status, $post) {
        // Only award points when post goes from draft/pending to published
        if ($new_status !== 'publish' || $old_status === 'publish') {
            return;
        }

        // Skip auto-drafts and revisions
        if (in_array($post->post_status, ['auto-draft', 'inherit'])) {
            return;
        }

        // Skip if this is a REST API request - let rest_after_insert_post handle it
        if (defined('REST_REQUEST') && REST_REQUEST) {
            return;
        }

        
        // Use the shared processing function
        $this->process_post_points($post, 'transition_post_status');
    }

    /**
     * Shared function to process post points (used by both hooks)
     */
    private function process_post_points($post, $source = 'unknown') {
        // Skip vendor posts - vendors don't receive IQ points
        // Check both the meta field and ACF field for vendor attribution
        $vendor_id = get_post_meta($post->ID, 'post_vendor_id', true);
        $acf_vendor = get_field('post_vendor', $post->ID);
        
        // Convert to integer for proper comparison and check if it's a valid vendor ID
        $vendor_id_int = intval($vendor_id);
        
        // Check if post is attributed to a vendor via either field
        if (($vendor_id && $vendor_id_int > 0) || (!empty($acf_vendor))) {
            error_log("IQ Score: Skipping points for vendor post {$post->ID} (vendor_id: {$vendor_id}, acf_vendor: " . print_r($acf_vendor, true) . ")");
            return;
        }
        

        // Get post categories with more details for debugging
        $category_ids = wp_get_post_categories($post->ID);
        $categories = wp_get_post_categories($post->ID, ['fields' => 'slugs']);
        
        // Get full category objects for debugging
        $full_categories = [];
        foreach ($category_ids as $cat_id) {
            $cat = get_category($cat_id);
            if ($cat) {
                $full_categories[] = [
                    'id' => $cat->term_id,
                    'name' => $cat->name,
                    'slug' => $cat->slug
                ];
            }
        }
        
        // Determine points and activity type based on category
        $points = 0;
        $activity_type = 'post_publish'; // default fallback
        
        if (in_array('thought-leadership', $categories)) {
            $points = self::POINTS['thought_leadership_post'];
            $activity_type = 'thought_leadership_post';
        } elseif (in_array('news', $categories)) {
            $points = self::POINTS['news_post'];
            $activity_type = 'news_post';
        } elseif (in_array('event', $categories)) {
            $points = self::POINTS['event_post'];
            $activity_type = 'event_post';
        } else {
            // Check if it's a resource category (matches frontend category slugs)
            $resource_categories = [
                'blog-post', 'book', 'course', 'podcast', 'presentation',
                'press-release', 'template', 'video', 'webinar',
                'whitepaper', 'case-study'
            ];
            
            foreach ($resource_categories as $resource_cat) {
                if (in_array($resource_cat, $categories)) {
                    $points = self::POINTS['resource_post'];
                    $activity_type = 'resource_post';
                    break;
                }
            }
        }

        if ($points > 0) {
            $this->award_points($post->post_author, $points, $activity_type);
        }
    }

    /**
     * Award points for comments
     */
    public function award_comment_points($comment_id, $comment) {
        // Only award points for approved comments
        if ($comment->comment_approved == '1') {
            $this->award_points($comment->user_id, self::POINTS['comment'], 'comment');
        }
    }

    /**
     * Check for profile updates that should award points
     */
    public function check_profile_updates($user_id) {
        // Check if profile image was added
        $avatar_url = get_user_meta($user_id, 'profile_picture', true);
        $avatar_awarded = get_user_meta($user_id, 'iq_avatar_awarded', true);
        
        if ($avatar_url && !$avatar_awarded) {
            $this->award_points($user_id, self::POINTS['profile_image'], 'profile_image');
            update_user_meta($user_id, 'iq_avatar_awarded', true);
        }

        // Check for social media links
        $social_fields = ['linkedin_url', 'twitter_url', 'facebook_url', 'instagram_url'];
        
        foreach ($social_fields as $field) {
            $social_url = get_user_meta($user_id, $field, true);
            $awarded_meta = "iq_{$field}_awarded";
            $already_awarded = get_user_meta($user_id, $awarded_meta, true);
            
            if ($social_url && !$already_awarded) {
                $this->award_points($user_id, self::POINTS['social_media_link'], $field);
                update_user_meta($user_id, $awarded_meta, true);
            }
        }
    }

    /**
     * Track daily visits
     */
    public function track_daily_visit($user_login, $user) {
        $last_visit = get_user_meta($user->ID, 'iq_last_daily_visit', true);
        $today = date('Y-m-d');
        
        if ($last_visit !== $today) {
            $this->award_points($user->ID, self::POINTS['daily_visit'], 'daily_visit');
            update_user_meta($user->ID, 'iq_last_daily_visit', $today);
        }
    }

    /**
     * Award points for connections
     */
    public function award_connection_points($user_id, $connected_user_id) {
        // Award points to both users
        $this->award_points($user_id, self::POINTS['add_connection'], 'connection');
        $this->award_points($connected_user_id, self::POINTS['add_connection'], 'connection');
    }

    /**
     * Award points for upvoting
     */
    public function award_upvote_points($user_id, $post_id) {
        // Check if user has already been awarded points for upvoting this specific post
        $upvote_award_key = "iq_upvote_awarded_{$user_id}_{$post_id}";
        
        // Check if points have already been awarded for this user/post combination
        if (get_user_meta($user_id, $upvote_award_key, true)) {
            // Log spam protection trigger
            tourismiq_log_security('upvote_spam_blocked', [
                'user_id' => $user_id,
                'post_id' => $post_id,
                'reason' => 'points_already_awarded'
            ]);
            return false; // Points already awarded for this post
        }
        
        // Award points to the user who upvoted
        $points_awarded = $this->award_points($user_id, self::POINTS['upvote'], 'upvote');
        
        // If points were successfully awarded, mark this post as awarded to prevent future awards
        if ($points_awarded) {
            update_user_meta($user_id, $upvote_award_key, time());
        }
        
        return $points_awarded;
    }

    /**
     * Reset upvote awards for a user (admin function)
     * This allows a user to earn upvote points again
     */
    public function reset_user_upvote_awards($user_id, $post_id = null) {
        if (!current_user_can('manage_options')) {
            return false;
        }
        
        global $wpdb;
        
        if ($post_id) {
            // Reset for specific post
            $upvote_award_key = "iq_upvote_awarded_{$user_id}_{$post_id}";
            delete_user_meta($user_id, $upvote_award_key);
        } else {
            // Reset all upvote awards for user
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->usermeta} 
                 WHERE user_id = %d 
                 AND meta_key LIKE %s",
                $user_id,
                'iq_upvote_awarded_%'
            ));
        }
        
        return true;
    }

    /**
     * Award points for forum activities
     */
    public function award_forum_points($user_id, $activity_type, $reference_id = null) {
        if (!isset(self::POINTS[$activity_type])) {
            return false;
        }
        
        $points = self::POINTS[$activity_type];
        return $this->award_points($user_id, $points, $activity_type);
    }

    /**
     * Get leaderboard data
     */
    public function get_leaderboard($limit = 20) {
        global $wpdb;
        
        // Validate and sanitize limit
        $limit = tourismiq_validate_input($limit, 'int');
        $limit = max(1, min($limit, 100)); // Ensure limit is between 1 and 100
        
        // Check cache first
        if (class_exists('TourismIQ_Performance')) {
            $cached_leaderboard = TourismIQ_Performance::get_cached_leaderboard($limit);
            if ($cached_leaderboard !== false) {
                return $cached_leaderboard;
            }
        }
        
        $results = $wpdb->get_results(
            TourismIQ_Security::prepare_query("
                SELECT u.ID, u.user_login, u.display_name, um.meta_value as iq_score
                FROM {$wpdb->users} u
                LEFT JOIN {$wpdb->usermeta} um ON u.ID = um.user_id AND um.meta_key = 'iq_score'
                LEFT JOIN {$wpdb->usermeta} capabilities ON u.ID = capabilities.user_id AND capabilities.meta_key = '{$wpdb->prefix}capabilities'
                WHERE um.meta_value IS NOT NULL AND um.meta_value > 0
                AND (capabilities.meta_value IS NULL OR capabilities.meta_value NOT LIKE '%administrator%')
                ORDER BY CAST(um.meta_value AS UNSIGNED) DESC
                LIMIT %d
            ", [$limit])
        );

        $leaderboard = [];
        foreach ($results as $result) {
            $user_data = get_userdata($result->ID);
            $rank = $this->calculate_rank_from_score((int)$result->iq_score);
            
            // Get profile picture from ACF field
            $profile_picture = get_field('profile_picture', 'user_' . $result->ID);
            $avatar_url = null;
            
            if ($profile_picture) {
                if (is_array($profile_picture) && isset($profile_picture['url'])) {
                    $avatar_url = $profile_picture['url'];
                } elseif (is_string($profile_picture)) {
                    $avatar_url = $profile_picture;
                }
            }
            
            $leaderboard[] = [
                'id' => $result->ID,
                'username' => $result->user_login,
                'display_name' => $result->display_name,
                'iq_score' => (int)$result->iq_score,
                'rank' => $rank,
                'profile_picture' => $avatar_url,
                'is_founder' => in_array('founder', $user_data->roles),
                'badge' => $this->get_user_badge($result->ID)
            ];
        }

        // Cache the result
        if (class_exists('TourismIQ_Performance')) {
            TourismIQ_Performance::cache_leaderboard($leaderboard, $limit);
        }

        return $leaderboard;
    }
}

// Initialize the system only once
if (!class_exists('TourismIQ_Score_System_Instance')) {
    class TourismIQ_Score_System_Instance {
        private static $instance = null;
        
        public static function get_instance() {
            if (self::$instance === null) {
                self::$instance = new TourismIQ_Score_System();
            }
            return self::$instance;
        }
    }
    
    // Initialize the system
    TourismIQ_Score_System_Instance::get_instance();
}

// Global helper function for awarding IQ points
if (!function_exists('award_iq_points')) {
    function award_iq_points($user_id, $points, $activity_type, $reference_id = null) {
        $system = TourismIQ_Score_System_Instance::get_instance();
        
        if ($activity_type === 'forum_question' || $activity_type === 'forum_response') {
            return $system->award_forum_points($user_id, $activity_type, $reference_id);
        }
        
        return $system->award_points($user_id, $points, $activity_type);
    }
} 