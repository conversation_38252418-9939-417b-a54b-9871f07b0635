<?php
/**
 * People Custom Post Type for "People on the Move" section
 */

// Register People Custom Post Type
function register_people_post_type() {
    $labels = array(
        'name'                  => _x('People', 'Post type general name', 'tourismiq'),
        'singular_name'         => _x('Person', 'Post type singular name', 'tourismiq'),
        'menu_name'             => _x('People on the Move', 'Admin Menu text', 'tourismiq'),
        'name_admin_bar'        => _x('Person', 'Add New on Toolbar', 'tourismiq'),
        'add_new'               => __('Add New', 'tourismiq'),
        'add_new_item'          => __('Add New Person', 'tourismiq'),
        'new_item'              => __('New Person', 'tourismiq'),
        'edit_item'             => __('Edit Person', 'tourismiq'),
        'view_item'             => __('View Person', 'tourismiq'),
        'all_items'             => __('All People', 'tourismiq'),
        'search_items'          => __('Search People', 'tourismiq'),
        'parent_item_colon'     => __('Parent People:', 'tourismiq'),
        'not_found'             => __('No people found.', 'tourismiq'),
        'not_found_in_trash'    => __('No people found in Trash.', 'tourismiq'),
        'featured_image'        => _x('Person Photo', 'Overrides the "Featured Image" phrase', 'tourismiq'),
        'set_featured_image'    => _x('Set person photo', 'Overrides the "Set featured image" phrase', 'tourismiq'),
        'remove_featured_image' => _x('Remove person photo', 'Overrides the "Remove featured image" phrase', 'tourismiq'),
        'use_featured_image'    => _x('Use as person photo', 'Overrides the "Use as featured image" phrase', 'tourismiq'),
        'archives'              => _x('People archives', 'The post type archive label', 'tourismiq'),
        'insert_into_item'      => _x('Insert into person', 'Overrides the "Insert into post" phrase', 'tourismiq'),
        'uploaded_to_this_item' => _x('Uploaded to this person', 'Overrides the "Uploaded to this post" phrase', 'tourismiq'),
        'filter_items_list'     => _x('Filter people list', 'Screen reader text for the filter links', 'tourismiq'),
        'items_list_navigation' => _x('People list navigation', 'Screen reader text for the pagination', 'tourismiq'),
        'items_list'            => _x('People list', 'Screen reader text for the items list', 'tourismiq'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'people'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 21,
        'menu_icon'          => 'dashicons-groups',
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'show_in_rest'       => true,
        'rest_base'          => 'people',
        'rest_controller_class' => 'WP_REST_Posts_Controller',
    );

    register_post_type('people', $args);
}
add_action('init', 'register_people_post_type');

// Force people post type editor to default to Code view
function force_people_editor_code_view() {
    global $typenow;
    
    if ($typenow === 'people') {
        add_filter('wp_default_editor', function() {
            return 'html'; // Force HTML/Code view
        });
        
        // Also add some custom CSS to make the Code tab more prominent
        add_action('admin_head', function() {
            echo '<style>
                .wp-editor-tabs .switch-html {
                    background: #0073aa;
                    color: white;
                }
                .wp-editor-tabs .switch-tmce {
                    background: #f1f1f1;
                }
            </style>';
        });
    }
}
add_action('admin_init', 'force_people_editor_code_view');

// Add custom REST API fields for people
function add_people_rest_fields() {
    // Add people meta fields to REST API
    register_rest_field('people', 'people_meta', array(
        'get_callback' => 'get_people_meta_for_api',
        'schema' => array(
            'description' => 'People metadata',
            'type' => 'object',
        )
    ));

    // Add people slug for easy frontend routing
    register_rest_field('people', 'slug', array(
        'get_callback' => function($object) {
            return get_post_field('post_name', $object['id']);
        },
        'schema' => array(
            'description' => 'Person slug',
            'type' => 'string',
        )
    ));

    // Add featured media URL
    register_rest_field('people', 'featured_media_url', array(
        'get_callback' => function($object) {
            if ($object['featured_media']) {
                return wp_get_attachment_url($object['featured_media']);
            }
            return null;
        },
        'schema' => array(
            'description' => 'Featured media URL',
            'type' => 'string',
        )
    ));
}
add_action('rest_api_init', 'add_people_rest_fields');

// Get people meta for REST API
function get_people_meta_for_api($object) {
    $people_id = $object['id'];
    
    // Get ACF fields
    $acf_fields = get_fields($people_id) ?: array();
    
    // If there's a people_gallery, enhance it with full image data
    if (!empty($acf_fields['people_gallery']) && is_array($acf_fields['people_gallery'])) {
        foreach ($acf_fields['people_gallery'] as &$gallery_image) {
            if (is_array($gallery_image) && isset($gallery_image['ID'])) {
                // Add different image sizes
                $gallery_image['sizes'] = array(
                    'full' => wp_get_attachment_image_url($gallery_image['ID'], 'full'),
                    'large' => wp_get_attachment_image_url($gallery_image['ID'], 'large'),
                    'medium' => wp_get_attachment_image_url($gallery_image['ID'], 'medium'),
                    'thumbnail' => wp_get_attachment_image_url($gallery_image['ID'], 'thumbnail'),
                );
            }
        }
    }
    
    return $acf_fields;
}

// Register custom REST endpoints for people
function register_people_rest_endpoints() {
    // Get people with search functionality
    register_rest_route('tourismiq/v1', '/people/search', array(
        'methods' => 'GET',
        'callback' => 'search_people',
        'permission_callback' => '__return_true',
        'args' => array(
            's' => array(
                'type' => 'string',
                'description' => 'Search term',
            ),
        ),
    ));

    // Get person by slug
    register_rest_route('tourismiq/v1', '/people/slug/(?P<slug>[a-zA-Z0-9-]+)', array(
        'methods' => 'GET',
        'callback' => 'get_person_by_slug',
        'permission_callback' => '__return_true',
        'args' => array(
            'slug' => array(
                'required' => true,
                'type' => 'string',
            ),
        ),
    ));

    // Get filtered people with advanced filtering
    register_rest_route('tourismiq/v1', '/people/filtered', array(
        'methods' => 'GET',
        'callback' => 'get_filtered_people',
        'permission_callback' => '__return_true',
        'args' => array(
            'search' => array(
                'type' => 'string',
                'description' => 'Search term',
            ),
            'move_type' => array(
                'type' => 'string',
                'description' => 'Filter by move type',
            ),
            'position_level' => array(
                'type' => 'string',
                'description' => 'Filter by position level',
            ),
            'year' => array(
                'type' => 'string',
                'description' => 'Filter by year',
            ),
            'page' => array(
                'type' => 'integer',
                'description' => 'Page number',
                'default' => 1,
            ),
            'per_page' => array(
                'type' => 'integer',
                'description' => 'Posts per page',
                'default' => 100,
            ),
        ),
    ));
}
add_action('rest_api_init', 'register_people_rest_endpoints');

// Search people function
function search_people($request) {
    $search_term = $request->get_param('s');
    
    $args = array(
        'post_type' => 'people',
        'post_status' => 'publish',
        'numberposts' => -1,
        'orderby' => 'date',
        'order' => 'DESC'
    );
    
    if (!empty($search_term)) {
        // Use a more complex query to search both post content and meta fields
        global $wpdb;
        
        $search_like = '%' . $wpdb->esc_like($search_term) . '%';
        
        // Get post IDs that match either title or content
        $post_ids = $wpdb->get_col($wpdb->prepare("
            SELECT DISTINCT p.ID FROM {$wpdb->posts} p
            WHERE p.post_type = 'people' 
            AND p.post_status = 'publish'
            AND (
                p.post_title LIKE %s
                OR p.post_content LIKE %s
            )
            ORDER BY p.post_date DESC
        ", $search_like, $search_like));
        
        if (!empty($post_ids)) {
            $args['post__in'] = $post_ids;
            $args['orderby'] = 'post__in'; // Maintain the order from the query
            unset($args['s']); // Remove the default search since we're using post__in
        } else {
            // No matches found, return empty
            return rest_ensure_response(array());
        }
    }
    
    $people = get_posts($args);
    $controller = new WP_REST_Posts_Controller('people');
    $data = array();
    
    foreach ($people as $person) {
        $person_data = $controller->prepare_item_for_response($person, $request);
        $data[] = $person_data->get_data();
    }
    
    return rest_ensure_response($data);
}

// Get person by slug
function get_person_by_slug($request) {
    $slug = $request['slug'];
    
    $people = get_posts(array(
        'post_type' => 'people',
        'name' => $slug,
        'post_status' => 'publish',
        'numberposts' => 1
    ));
    
    if (empty($people)) {
        return new WP_Error('person_not_found', 'Person not found', array('status' => 404));
    }
    
    $person = $people[0];
    $controller = new WP_REST_Posts_Controller('people');
    $data = $controller->prepare_item_for_response($person, $request);
    
    return rest_ensure_response($data);
}

// Get filtered people with advanced filtering
function get_filtered_people($request) {
    $search_term = $request->get_param('search');
    $move_type = $request->get_param('move_type');
    $position_level = $request->get_param('position_level');
    $year = $request->get_param('year');
    $page = $request->get_param('page') ?: 1;
    $per_page = $request->get_param('per_page') ?: 100;
    
    $args = array(
        'post_type' => 'people',
        'post_status' => 'publish',
        'posts_per_page' => $per_page,
        'paged' => $page,
        'orderby' => 'date',
        'order' => 'DESC'
    );
    
    // Build meta query for ACF fields
    $meta_query = array('relation' => 'AND');
    
    if (!empty($move_type) && $move_type !== 'all') {
        if ($move_type === 'uncategorized') {
            $meta_query[] = array(
                'relation' => 'OR',
                array(
                    'key' => 'move_type',
                    'value' => '',
                    'compare' => '='
                ),
                array(
                    'key' => 'move_type',
                    'compare' => 'NOT EXISTS'
                )
            );
        } else {
            $meta_query[] = array(
                'key' => 'move_type',
                'value' => $move_type,
                'compare' => '='
            );
        }
    }
    
    if (!empty($position_level) && $position_level !== 'all') {
        if ($position_level === 'uncategorized') {
            $meta_query[] = array(
                'relation' => 'OR',
                array(
                    'key' => 'position_level',
                    'value' => '',
                    'compare' => '='
                ),
                array(
                    'key' => 'position_level',
                    'compare' => 'NOT EXISTS'
                )
            );
        } else {
            $meta_query[] = array(
                'key' => 'position_level',
                'value' => $position_level,
                'compare' => '='
            );
        }
    }
    
    if (count($meta_query) > 1) {
        $args['meta_query'] = $meta_query;
    }
    
    // Add date filtering for year
    if (!empty($year)) {
        $args['date_query'] = array(
            array(
                'year' => intval($year),
            ),
        );
    }
    
    // Add search functionality
    if (!empty($search_term)) {
        $args['s'] = $search_term;
    }
    
    $query = new WP_Query($args);
    $people = $query->posts;
    
    // Prepare response data similar to WordPress REST API
    $controller = new WP_REST_Posts_Controller('people');
    $data = array();
    
    foreach ($people as $person) {
        $person_data = $controller->prepare_item_for_response($person, $request);
        $prepared_data = $person_data->get_data();
        
        // Add our custom fields
        $prepared_data['people_meta'] = get_people_meta_for_api(array('id' => $person->ID));
        $prepared_data['slug'] = $person->post_name;
        
        // Add featured media URL
        if ($person->_thumbnail_id) {
            $prepared_data['featured_media_url'] = wp_get_attachment_url($person->_thumbnail_id);
        } else if (has_post_thumbnail($person->ID)) {
            $prepared_data['featured_media_url'] = get_the_post_thumbnail_url($person->ID, 'full');
        }
        
        $data[] = $prepared_data;
    }
    
    // Build response with pagination info
    $response = array(
        'people' => $data,
        'totalPages' => $query->max_num_pages,
        'currentPage' => $page,
        'totalItems' => $query->found_posts,
    );
    
    return rest_ensure_response($response);
}