<?php
/**
 * TourismIQ Notifications API
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Notifications {
    private static $instance = null;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('init', array($this, 'maybe_create_notifications_table'));

        // Hook for upvotes if using a post meta for likes/upvotes
        add_action('added_post_meta', array($this, 'check_for_upvote'), 10, 4);
    }

    /**
     * Create the notifications table if it doesn't exist
     */
    public function maybe_create_notifications_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'user_notifications';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        if (!$table_exists) {
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                recipient_id bigint(20) UNSIGNED NOT NULL,
                sender_id bigint(20) UNSIGNED NOT NULL,
                type varchar(50) NOT NULL,
                content text NOT NULL,
                reference_id bigint(20) UNSIGNED NULL,
                reference_type varchar(50) NULL,
                sender_name varchar(255) NULL,
                sender_avatar text NULL,
                is_read tinyint(1) NOT NULL DEFAULT 0,
                created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY recipient_id (recipient_id),
                KEY sender_id (sender_id),
                KEY type (type),
                KEY is_read (is_read),
                KEY created_at (created_at)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }

    /**
     * Register REST API endpoints for notifications
     */
    public function register_rest_routes() {
        register_rest_route('tourismiq/v1', '/notifications/send', array(
            'methods' => 'POST',
            'callback' => array($this, 'send_notification'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'recipient_id' => array(
                    'required' => true,
                    'type' => 'integer',
                ),
                'type' => array(
                    'required' => true,
                    'type' => 'string',
                ),
                'content' => array(
                    'required' => true,
                    'type' => 'string',
                ),
                'reference_id' => array(
                    'required' => false,
                    'type' => 'integer',
                ),
                'reference_type' => array(
                    'required' => false,
                    'type' => 'string',
                ),
            ),
        ));

        // Add endpoint to fetch notifications
        register_rest_route('tourismiq/v1', '/notifications', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_notifications'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
            'args' => array(
                'limit' => array(
                    'required' => false,
                    'type' => 'integer',
                    'default' => 50,
                ),
                'offset' => array(
                    'required' => false,
                    'type' => 'integer',
                    'default' => 0,
                ),
                'unread_only' => array(
                    'required' => false,
                    'type' => 'boolean',
                    'default' => false,
                ),
            ),
        ));

        // Add endpoint to mark notifications as read
        register_rest_route('tourismiq/v1', '/notifications/(?P<id>\d+)/read', array(
            'methods' => 'POST',
            'callback' => array($this, 'mark_notification_read'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
        ));

        // Add endpoint to mark all notifications as read
        register_rest_route('tourismiq/v1', '/notifications/read-all', array(
            'methods' => 'POST',
            'callback' => array($this, 'mark_all_notifications_read'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
        ));

        // Add endpoint to delete notification
        register_rest_route('tourismiq/v1', '/notifications/(?P<id>\d+)', array(
            'methods' => 'DELETE',
            'callback' => array($this, 'delete_notification'),
            'permission_callback' => function() {
                return is_user_logged_in();
            },
        ));
    }

    /**
     * Get notifications for current user
     */
    public function get_notifications($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $limit = $request->get_param('limit') ?: 50;
        $offset = $request->get_param('offset') ?: 0;
        $unread_only = $request->get_param('unread_only') === 'true';

        $table_name = $wpdb->prefix . 'user_notifications';

        $where_clause = "WHERE recipient_id = %d";
        $where_values = array($current_user_id);

        if ($unread_only) {
            $where_clause .= " AND is_read = 0";
        }

        $sql = $wpdb->prepare(
            "SELECT * FROM $table_name
             $where_clause
             ORDER BY created_at DESC
             LIMIT %d OFFSET %d",
            array_merge($where_values, array($limit, $offset))
        );

        $notifications = $wpdb->get_results($sql);

        // Format notifications for frontend
        $formatted_notifications = array_map(function($notification) {
            return array(
                'id' => (string)$notification->id,
                'type' => $notification->type,
                'content' => $notification->content,
                'timestamp' => strtotime($notification->created_at) * 1000, // Convert to JS timestamp
                'read' => (bool)$notification->is_read,
                'referenceId' => $notification->reference_id ? (int)$notification->reference_id : null,
                'referenceType' => $notification->reference_type,
                'senderId' => $notification->sender_id ? (int)$notification->sender_id : null,
                'senderName' => $notification->sender_name,
                'senderAvatar' => $notification->sender_avatar,
            );
        }, $notifications);

        return array(
            'success' => true,
            'notifications' => $formatted_notifications,
            'total' => count($formatted_notifications),
        );
    }

    /**
     * Mark a notification as read
     */
    public function mark_notification_read($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $notification_id = $request->get_param('id');

        $table_name = $wpdb->prefix . 'user_notifications';

        // Verify the notification belongs to the current user
        $notification = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND recipient_id = %d",
            $notification_id, $current_user_id
        ));

        if (!$notification) {
            return new WP_Error(
                'notification_not_found',
                'Notification not found',
                array('status' => 404)
            );
        }

        // Update the notification
        $result = $wpdb->update(
            $table_name,
            array('is_read' => 1),
            array('id' => $notification_id, 'recipient_id' => $current_user_id),
            array('%d'),
            array('%d', '%d')
        );

        if ($result === false) {
            return new WP_Error(
                'database_error',
                'Failed to mark notification as read',
                array('status' => 500)
            );
        }

        return array(
            'success' => true,
            'message' => 'Notification marked as read',
        );
    }

    /**
     * Mark all notifications as read
     */
    public function mark_all_notifications_read($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $table_name = $wpdb->prefix . 'user_notifications';

        $result = $wpdb->update(
            $table_name,
            array('is_read' => 1),
            array('recipient_id' => $current_user_id, 'is_read' => 0),
            array('%d'),
            array('%d', '%d')
        );

        if ($result === false) {
            return new WP_Error(
                'database_error',
                'Failed to mark notifications as read',
                array('status' => 500)
            );
        }

        return array(
            'success' => true,
            'message' => 'All notifications marked as read',
            'updated_count' => $result,
        );
    }

    /**
     * Delete a notification
     */
    public function delete_notification($request) {
        global $wpdb;

        $current_user_id = get_current_user_id();
        $notification_id = $request->get_param('id');

        $table_name = $wpdb->prefix . 'user_notifications';

        // Verify the notification belongs to the current user
        $notification = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND recipient_id = %d",
            $notification_id, $current_user_id
        ));

        if (!$notification) {
            return new WP_Error(
                'notification_not_found',
                'Notification not found',
                array('status' => 404)
            );
        }

        // Delete the notification
        $result = $wpdb->delete(
            $table_name,
            array('id' => $notification_id, 'recipient_id' => $current_user_id),
            array('%d', '%d')
        );

        if ($result === false) {
            return new WP_Error(
                'database_error',
                'Failed to delete notification',
                array('status' => 500)
            );
        }

        return array(
            'success' => true,
            'message' => 'Notification deleted',
        );
    }

    /**
     * Store notification in database
     */
    public function store_notification($data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'user_notifications';

        $result = $wpdb->insert(
            $table_name,
            array(
                'recipient_id' => $data['recipient_id'],
                'sender_id' => isset($data['sender_id']) ? $data['sender_id'] : null,
                'type' => $data['type'],
                'content' => $data['content'],
                'reference_id' => isset($data['reference_id']) ? $data['reference_id'] : null,
                'reference_type' => isset($data['reference_type']) ? $data['reference_type'] : null,
                'sender_name' => isset($data['sender_name']) ? $data['sender_name'] : null,
                'sender_avatar' => isset($data['sender_avatar']) ? $data['sender_avatar'] : null,
            ),
            array('%d', '%d', '%s', '%s', '%d', '%s', '%s', '%s')
        );

        if ($result === false) {
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
     * Enqueue scripts for notifications
     */
    public function enqueue_scripts() {
        if (is_user_logged_in()) {
            // This would typically include scripts for handling notifications
            // In our headless setup, this is mainly handled by the Next.js frontend

            // Pass authentication data for the API
            wp_localize_script('jquery', 'tourismiqNotificationData', array(
                'nonce' => wp_create_nonce('wp_rest'),
                'user_id' => get_current_user_id(),
                'rest_url' => rest_url('tourismiq/v1/notifications/'),
            ));
        }
    }

    /**
     * Send notification via REST API
     */
    public function send_notification($request) {
        $current_user_id = get_current_user_id();
        $params = $request->get_params();

        $recipient_id = $params['recipient_id'];
        $type = $params['type'];
        $content = $params['content'];
        $reference_id = isset($params['reference_id']) ? $params['reference_id'] : 0;
        $reference_type = isset($params['reference_type']) ? $params['reference_type'] : '';

        // Don't send notifications to yourself
        if ($recipient_id == $current_user_id) {
            return new WP_Error(
                'self_notification',
                'Cannot send notifications to yourself',
                array('status' => 400)
            );
        }

        // Check if recipient exists
        $recipient = get_user_by('id', $recipient_id);
        if (!$recipient) {
            return new WP_Error(
                'invalid_recipient',
                'Recipient user does not exist',
                array('status' => 404)
            );
        }

        // Prepare notification data
        $notification_data = array(
            'recipient_id' => $recipient_id,
            'sender_id' => $current_user_id,
            'type' => $type,
            'content' => $content,
            'reference_id' => $reference_id ?: null,
            'reference_type' => $reference_type ?: null,
            'timestamp' => time(),
        );

        // Get sender information
        $sender = get_user_by('id', $current_user_id);
        if ($sender) {
            $notification_data['sender_name'] = $sender->display_name;

            // Get sender avatar from ACF
            $profile_picture = get_field('profile_picture', 'user_' . $current_user_id);
            if ($profile_picture && is_array($profile_picture) && isset($profile_picture['url'])) {
                $notification_data['sender_avatar'] = $profile_picture['url'];
            }
        }

        // Store in database first
        $stored_id = $this->store_notification($notification_data);

        if (!$stored_id) {
            return new WP_Error(
                'storage_failed',
                'Failed to store notification',
                array('status' => 500)
            );
        }

        // Add stored ID to data
        $notification_data['id'] = $stored_id;

        // Trigger the notification action for real-time delivery
        do_action('tourismiq_notification_sent', $notification_data);

        return array(
            'success' => true,
            'message' => 'Notification sent',
            'data' => $notification_data,
        );
    }

    /**
     * Check for upvote actions and send notifications
     */
    public function check_for_upvote($meta_id, $post_id, $meta_key, $meta_value) {
        // Disable WordPress-side upvote notifications as they're handled by the frontend
        return;

        // The code below is now disabled

        // Check if this is an upvote meta key
        if ($meta_key === 'post_likes' || $meta_key === '_upvotes') {
            $current_user_id = get_current_user_id();
            $post_author_id = get_post_field('post_author', $post_id);

            // Don't notify on self-upvotes
            if ($post_author_id == $current_user_id) {
                return;
            }

            $post_title = get_the_title($post_id);
            $user_data = get_userdata($current_user_id);
            $username = $user_data->display_name;

            // Create notification content
            $content = sprintf('%s upvoted your post "%s"', $username, $post_title);

            // This would trigger a notification in a full implementation
            do_action('tourismiq_notification_sent', array(
                'recipient_id' => $post_author_id,
                'sender_id' => $current_user_id,
                'type' => 'upvote',
                'content' => $content,
                'reference_id' => $post_id,
                'reference_type' => 'post',
                'timestamp' => time(),
            ));
        }
    }
}

// Initialize
TourismIQ_Notifications::get_instance();

/**
 * Enhanced notification storage and sending
 */
function tourismiq_store_and_send_notification($data) {
    $notifications_instance = TourismIQ_Notifications::get_instance();

    // Skip database storage for temporary notifications
    $skip_storage_types = array('connection_accepted', 'iq_points_earned');

    if (in_array($data['type'], $skip_storage_types)) {
        // Only send real-time notification for temporary types
        do_action('tourismiq_notification_sent', $data);
        return true;
    }

    // Store in database for persistent notifications
    $stored_id = $notifications_instance->store_notification($data);

    if ($stored_id) {
        // Add the database ID to the data
        $data['id'] = $stored_id;

        // Trigger real-time notification
        do_action('tourismiq_notification_sent', $data);

        return $stored_id;
    }

    return false;
}

/**
 * Handle upvote notifications
 */
function tourismiq_handle_upvote_notification($post_id, $user_id) {
    // Disable WordPress-side upvote notifications as they're handled by the frontend
    return;

    // The code below is now disabled

    $post_author_id = get_post_field('post_author', $post_id);

    // Don't notify on self-upvotes
    if ($post_author_id == $user_id) {
        return;
    }

    $post_title = get_the_title($post_id);
    $user_data = get_userdata($user_id);
    $username = $user_data->display_name;

    // Create notification content
    $content = sprintf('%s upvoted your post "%s"', $username, $post_title);

    // Store and send notification
    tourismiq_store_and_send_notification(array(
        'recipient_id' => $post_author_id,
        'sender_id' => $user_id,
        'type' => 'upvote',
        'content' => $content,
        'reference_id' => $post_id,
        'reference_type' => 'post',
        'timestamp' => time(),
    ));

    return true;
}

/**
 * Triggers the sending of a notification to the Socket.IO server.
 * Hooks into 'tourismiq_notification_sent'.
 *
 * @param array $data Notification data.
 */
function tourismiq_trigger_socket_notification($data) {
    // Use environment variable for socket server URL, fallback to localhost:3000 for development
    $socket_server_url = defined('SOCKET_SERVER_URL') ? SOCKET_SERVER_URL : 'http://localhost:3000';
    $api_endpoint = $socket_server_url . '/api/internal/send-notification';


    // Transform snake_case keys from $data to camelCase for the Node.js server
    $camel_case_data = array();
    foreach ($data as $key => $value) {
        $camel_key = lcfirst(str_replace('_', '', ucwords($key, '_')));
        $camel_case_data[$camel_key] = $value;
    }


    // The Node.js server expects 'recipientId', 'senderId', 'type', 'content', 'referenceId', 'referenceType'
    // Ensure all necessary fields are present, especially senderId which was added to the $data for do_action
    if (!isset($camel_case_data['senderId'])) {
        // Attempt to get current user if senderId is missing, though it should be in $data
        $camel_case_data['senderId'] = get_current_user_id();
    }


    $args = array(
        'body'        => json_encode($camel_case_data),
        'headers'     => array(
            'Content-Type' => 'application/json',
        ),
        'timeout'     => 15, // seconds
        'redirection' => 5,
        'blocking'    => true, // Set to false for non-blocking if preferred, but true helps with debugging.
        'httpversion' => '1.0',
        'sslverify'   => false, // Set to true in production with valid SSL
        'data_format' => 'body',
    );

    $response = wp_remote_post($api_endpoint, $args);

    if (is_wp_error($response)) {
        $error_message = $response->get_error_message();
        error_log("Socket notification failed: " . $error_message);
    } else {
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        error_log("Socket notification sent to {$api_endpoint}: HTTP {$response_code} - {$response_body}");
    }
}
add_action('tourismiq_notification_sent', 'tourismiq_trigger_socket_notification', 10, 1);


/**
 * Sends a notification when a new comment is posted.
 * This is the MAIN entry point for comment notifications.
 * Hooks into 'wp_insert_comment'.
 *
 * @param int   $comment_id The comment ID.
 * @param WP_Comment $comment_object The comment object.
 */
function tourismiq_notify_on_new_comment($comment_id, $comment_object) {
    // Skip non-standard comment types (pingbacks, trackbacks, etc.)
    if (!empty($comment_object->comment_type) && $comment_object->comment_type !== 'comment') {
        return;
    }

    // For automatically approved comments (status = 1 or 'approve'), process immediately
    if ($comment_object->comment_approved === '1' || $comment_object->comment_approved === 1 || $comment_object->comment_approved === 'approve') {
        tourismiq_process_comment_notification($comment_id, $comment_object);
    }
}
add_action('wp_insert_comment', 'tourismiq_notify_on_new_comment', 10, 2);

/**
 * Sends notification when a comment status changes.
 * Hooks into 'transition_comment_status'.
 *
 * @param string $new_status New comment status.
 * @param string $old_status Old comment status.
 * @param object $comment Comment object.
 */
function tourismiq_notify_on_comment_approval($new_status, $old_status, $comment) {
    // Only send notification when comment is newly approved
    if (($new_status === 'approved' || $new_status === '1' || $new_status === 1) &&
        $old_status !== 'approved' && $old_status !== '1' && $old_status !== 1) {
        tourismiq_process_comment_notification($comment->comment_ID, $comment);
    }
}
add_action('transition_comment_status', 'tourismiq_notify_on_comment_approval', 10, 3);

/**
 * Process and send a comment notification
 *
 * @param int $comment_id The comment ID
 * @param object $comment_object The comment object
 */
function tourismiq_process_comment_notification($comment_id, $comment_object) {
    // Skip non-standard comment types (extra safety check)
    if (!empty($comment_object->comment_type) && $comment_object->comment_type !== 'comment') {
        return;
    }

    $post_id = $comment_object->comment_post_ID;
    $post_author_id = get_post_field('post_author', $post_id);
    $commenter_id = $comment_object->user_id; // This will be 0 for non-logged-in users

    // Prevent self-notification
    if ($post_author_id == $commenter_id && $commenter_id != 0) {
        return;
    }

    // Ensure post author exists
    $post_author_obj = get_user_by('id', $post_author_id);
    if (!$post_author_obj) {
        return;
    }

    $post_title = get_the_title($post_id);
    $commenter_name = '';

    if ($commenter_id != 0) {
        $commenter_obj = get_user_by('id', $commenter_id);
        $commenter_name = $commenter_obj ? $commenter_obj->display_name : 'Someone';
    } else {
        $commenter_name = !empty($comment_object->comment_author) ? $comment_object->comment_author : 'A guest';
    }

    $content = sprintf(
        '%s commented on your post "%s"',
        $commenter_name,
        $post_title
    );

    // Create the notification data
    $notification_data = array(
        'recipient_id'   => (int)$post_author_id,
        'sender_id'      => (int)$commenter_id,
        'type'           => 'comment', // Match expected frontend type
        'content'        => $content,
        'reference_id'   => (int)$post_id,
        'reference_type' => 'post',
        'timestamp'      => time(),
    );

    // Trigger the notification
    do_action('tourismiq_notification_sent', $notification_data);
}
