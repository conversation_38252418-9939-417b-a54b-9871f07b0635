<?php
/**
 * Custom Login Page Styling
 * Customizes the WordPress login page with MyTourismIQ branding
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Customize login page styles
 */
function tourismiq_custom_login_styles() {
    $logo_url = get_template_directory_uri() . '/images/logo.svg';
    ?>
    <style type="text/css">
        /* Completely replace WordPress logo */
        .login h1 a {
            background-image: url('<?php echo $logo_url; ?>') !important;
            background-size: 262px 66px !important;
            background-position: center !important;
            background-repeat: no-repeat !important;
            width: 262px !important;
            height: 66px !important;
            text-indent: -9999px !important;
            overflow: hidden !important;
            display: block !important;
            margin: 0 auto;
        }
        
        /* Ensure container is properly sized */
        .login h1 {
            padding: 20px 0 !important;
            text-align: center !important;
        }
        
        /* Adjust login form positioning */
        .login {
            background-color: #f0f5f7;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        /* Style the login form */
        .login form {
            background: #ffffff;
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }
        
        /* Style form inputs */
        .login form .input {
            border-radius: 8px;
            border: 2px solid #e1e5e9;
            font-size: 16px;
            padding: 12px;
            margin-bottom: 16px;
        }
        
        .login form .input:focus {
            border-color: #5cc6ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(92, 198, 255, 0.1);
        }
        
        /* Style submit button */
        .login form .button-primary {
            background-color: #5cc6ff;
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: bold;
            letter-spacing: 0.3px;
            text-decoration: none;
            color: #ffffff;
            font-size: 16px;
            width: 100%;
            text-align: center;
        }
        
        .login form .button-primary:hover {
            background-color: #4db8f0;
        }
        
        /* Style links */
        .login #nav a, .login #backtoblog a {
            color: #5cc6ff;
            text-decoration: none;
        }
        
        .login #nav a:hover, .login #backtoblog a:hover {
            color: #4db8f0;
        }
        
        /* Center navigation */
        .login #nav, .login #backtoblog {
            text-align: center;
            margin-top: 20px;
        }
        
        /* Custom message styling */
        .login .message, .login .success {
            background: #e8f5e9;
            border: 1px solid #4caf50;
            border-radius: 8px;
            color: #2e7d32;
            padding: 15px;
        }
        
        .login .error {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 8px;
            color: #c62828;
            padding: 15px;
        }
        
        /* Style form labels */
        .login form label {
            font-weight: 600;
            color: #3d405b;
            margin-bottom: 8px;
            display: block;
        }
        
        /* Responsive adjustments */
        @media (max-width: 480px) {
            .login h1 {
                background-size: 200px 50px;
                width: 200px;
                height: 50px;
            }
            
            .login form {
                padding: 30px 20px;
                margin: 20px;
            }
        }
    </style>
    
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            // Backup method to replace logo with JavaScript
            var logoLink = document.querySelector('.login h1 a');
            if (logoLink) {
                logoLink.style.backgroundImage = 'url("<?php echo $logo_url; ?>")';
                logoLink.style.backgroundSize = '262px 66px';
                logoLink.style.backgroundPosition = 'center';
                logoLink.style.backgroundRepeat = 'no-repeat';
                logoLink.style.width = '262px';
                logoLink.style.height = '66px';
                logoLink.style.textIndent = '-9999px';
                logoLink.style.overflow = 'hidden';
                logoLink.style.display = 'block';
                logoLink.style.margin = '0 auto';
                logoLink.innerHTML = '';
            }
        });
    </script>
    <?php
}
add_action('login_enqueue_scripts', 'tourismiq_custom_login_styles');

/**
 * Change login logo URL to homepage
 */
function tourismiq_login_logo_url() {
    return home_url();
}
add_filter('login_headerurl', 'tourismiq_login_logo_url');

/**
 * Change login logo title
 */
function tourismiq_login_logo_url_title() {
    return 'MyTourismIQ';
}
add_filter('login_headertitle', 'tourismiq_login_logo_url_title');

/**
 * Custom login page messages
 */
function tourismiq_custom_login_message($message) {
    // Customize the password reset message
    if (strpos($_SERVER['REQUEST_URI'], 'action=lostpassword') !== false) {
        $message = '<p class="message">Enter your email address and we\'ll send you a link to reset your password.</p>';
    }
    
    return $message;
}
add_filter('login_message', 'tourismiq_custom_login_message');

// Password reset redirect removed - now handled by frontend flow

/**
 * Custom reset confirmation message
 */
function tourismiq_password_reset_message() {
    if (isset($_GET['reset']) && $_GET['reset'] == 'true') {
        echo '<div class="notice notice-success"><p><strong>Password reset email sent!</strong> Check your email for the reset link.</p></div>';
    }
}
add_action('login_form_lostpassword', 'tourismiq_password_reset_message');

/**
 * Redirect users to frontend after successful WordPress login
 */
function tourismiq_login_redirect($redirect_to, $request, $user) {
    // Only redirect for successful logins (not errors)
    if (isset($user->user_login)) {
        // Check if user is an administrator
        if ($user instanceof WP_User && $user->has_cap('administrator')) {
            // Allow administrators to go to wp-admin
            return $redirect_to;
        }
        
        // Get the correct frontend URL based on environment
        $frontend_url = 'https://mytourismiq.com'; // Production frontend URL
        
        // For local development, use local URL
        if (strpos(get_site_url(), 'tourismiq.local') !== false || strpos(get_site_url(), 'localhost') !== false) {
            $frontend_url = 'http://localhost:3000';
        }
        
        // Check if this is from a password reset in multiple ways
        $is_password_reset = (
            strpos($request, 'action=rp') !== false || 
            strpos($request, 'action=resetpass') !== false ||
            strpos($_SERVER['HTTP_REFERER'] ?? '', 'action=rp') !== false ||
            strpos($_SERVER['HTTP_REFERER'] ?? '', 'action=resetpass') !== false ||
            strpos($_SERVER['REQUEST_URI'] ?? '', 'action=rp') !== false ||
            strpos($_SERVER['REQUEST_URI'] ?? '', 'action=resetpass') !== false ||
            isset($_GET['action']) && ($_GET['action'] === 'rp' || $_GET['action'] === 'resetpass')
        );
        
        if ($is_password_reset) {
            return $frontend_url . '?login=success&message=password_reset';
        }
        
        // For regular logins, redirect to frontend home
        return $frontend_url . '?login=success';
    }
    
    return $redirect_to;
}
add_filter('login_redirect', 'tourismiq_login_redirect', 10, 3);

/**
 * More aggressive redirect after login
 */
function tourismiq_after_login_redirect($user_login, $user) {
    // Don't redirect AJAX requests or API calls
    if (defined('DOING_AJAX') && DOING_AJAX) return;
    if (defined('REST_REQUEST') && REST_REQUEST) return;
    
    // Check if user is an administrator - allow them to access wp-admin
    if ($user instanceof WP_User && $user->has_cap('administrator')) {
        return;
    }
    
    // Check if we're coming from password reset
    $is_password_reset = (
        strpos($_SERVER['HTTP_REFERER'] ?? '', 'action=rp') !== false ||
        strpos($_SERVER['HTTP_REFERER'] ?? '', 'action=resetpass') !== false ||
        strpos($_SERVER['REQUEST_URI'] ?? '', 'action=rp') !== false ||
        strpos($_SERVER['REQUEST_URI'] ?? '', 'action=resetpass') !== false ||
        isset($_GET['action']) && ($_GET['action'] === 'rp' || $_GET['action'] === 'resetpass')
    );
    
    // Get the correct frontend URL based on environment
    $frontend_url = 'https://mytourismiq.com'; // Production frontend URL
    
    // For local development, use local URL
    if (strpos(get_site_url(), 'tourismiq.local') !== false || strpos(get_site_url(), 'localhost') !== false) {
        $frontend_url = 'http://localhost:3000';
    }
    
    if ($is_password_reset) {
        wp_redirect($frontend_url . '?login=success&message=password_reset');
        exit();
    } else {
        wp_redirect($frontend_url . '?login=success');
        exit();
    }
}
add_action('wp_login', 'tourismiq_after_login_redirect', 10, 2);

/**
 * Redirect logout to frontend
 */
function tourismiq_logout_redirect() {
    wp_redirect(get_site_url() . '?logout=success');
    exit();
}
add_action('wp_logout', 'tourismiq_logout_redirect');