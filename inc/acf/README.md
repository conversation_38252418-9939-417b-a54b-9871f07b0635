# ACF (Advanced Custom Fields) Integration Documentation

This document explains how ACF fields are integrated with the frontend post creation system and provides implementation details for all post categories.

## Overview

The TourismIQ theme uses Advanced Custom Fields (ACF) to add category-specific fields to posts. Each post category has its own set of custom fields that are displayed in the admin interface and used in the frontend.

## Architecture

### 1. WordPress ACF Configuration
- **Location**: `/inc/acf/` directory
- **Files**: One PHP file per category (e.g., `acf-news.php`, `acf-event.php`)
- **Structure**: Each file defines an ACF field group with category-specific fields
- **REST API**: All field groups have `'show_in_rest' => 1` to enable REST API access

### 2. Custom REST API Endpoint
- **Endpoint**: `POST /wp-json/tourismiq/v1/posts/{id}/acf`
- **Function**: `tourismiq_update_post_acf()` in `/inc/rest-api.php`
- **Purpose**: Handles ACF field updates using WordPress's native `update_field()` function
- **Security**: Category-based field validation and proper permission checks

### 3. Frontend Integration
- **PostCreator Component**: `/src/components/feed/PostCreator.tsx`
- **API Route**: `/src/app/api/wp-proxy/posts/create/route.ts`
- **Feed Display**: Category-specific feed item components

## Implementation Process

### Step 1: WordPress ACF Configuration
Each ACF configuration file must have:
```php
'show_in_rest' => 1,  // Field group level
// AND
'show_in_rest' => 1,  // Individual field level
```

### Step 2: REST API Endpoint Update
Add allowed fields for each category in `tourismiq_update_post_acf()`:
```php
if (in_array('category-slug', $post_categories)) {
    $allowed_fields = ['field1', 'field2', 'field3'];
}
```

### Step 3: Frontend PostCreator
Add category-specific ACF fields state and UI:
```tsx
const [acfFields, setAcfFields] = useState({
    field1: "",
    field2: "",
    // ...
});
```

### Step 4: Feed Item Display
Update the category's feed item component to display ACF fields.

## Post Categories and ACF Fields

### ✅ **News** (IMPLEMENTED)
**File**: `acf-news.php`
**Fields**:
- `url` (URL) - External article URL *required*
- `image_caption` (Text) - Image caption *required when image present*

**Notes**: Removed `article_image` field as it duplicates WordPress featured image

### 🔄 **Blog Post** (TODO)
**File**: `acf-blog-post.php`
**Fields**:
- `title` (Text) - Custom post title override
- `author` (Text) - Custom author name
- `website_logo` (Image) - Source website logo
- `post_publish_date` (Date) - Original publication date
- `url` (URL) - External blog post URL

### 🔄 **Event** (TODO)
**File**: `acf-event.php`
**Fields**:
- `event_date` (Date) - Event start date
- `event_time` (Time) - Event start time
- `time_zone` (Text) - Event time zone
- `event_end_date` (Date) - Event end date
- `event_location` (Text) - Event location/venue
- `host_company_organization` (Text) - Event host organization
- `host_company_organization_logo` (Image) - Host organization logo
- `registration_link_url` (URL) - Event registration URL
- `additional_details_link_url` (URL) - Additional event information URL
- ~~`event_promo_image` (Image)~~ - *Remove: duplicates featured image*

### 🔄 **Job** (TODO)
**File**: `acf-job.php`
**Fields**:
- `position_title` (Text) - Job position title
- `company` (Text) - Hiring company name
- `company_logo` (Image) - Company logo
- `location` (Text) - Job location
- `job_type` (Select) - Employment type
- `experience_level` (Text) - Required experience level
- `compensation` (Text) - Salary/compensation details
- `posting_link_url` (URL) - External job posting URL

### 🔄 **Webinar** (TODO)
**File**: `acf-webinar.php`
**Fields**:
- `webinar_type` (Select) - "live" or "recorded"
- `date` (Date) - Live webinar date
- `time` (Time) - Live webinar time
- `time_zone` (Text) - Time zone information
- `presenters` (Repeater) - Array of presenter objects
- `webinar_host_company_organization` (Text) - Host company name
- `webinar_host_company_organization_logo` (Image) - Host company logo
- `register_url` (URL) - Registration link for live webinars
- `youtube_url` (URL) - YouTube video link for recorded
- `vimeo_url` (URL) - Vimeo video link for recorded
- ~~`webinar_promo_image` (Image)~~ - *Remove: duplicates featured image*

### 🔄 **Video** (TODO)
**File**: `acf-video.php`
**Fields**:
- `video_title` (Text) - Custom video title
- `creators` (Repeater) - Array of creator objects
- `creator_logo` (Image) - Creator/company logo
- `video_source` (Text) - Video source platform
- `video_embed_url` (URL) - Video playback URL
- ~~`video_thumbnail` (Image)~~ - *Remove: duplicates featured image*

### 🔄 **Course** (TODO)
**File**: `acf-course.php`
**Fields**:
- `course_name` (Text) - Course title
- ~~`course_image` (Image)~~ - *Remove: duplicates featured image*
- `course_url` (URL) - Course details URL
- `sign_up_url` (URL) - Course registration URL
- `thoughts` (Textarea) - Additional course details/review
- `company` (Text) - Course provider name
- `company_logo` (Image) - Provider logo

### 🔄 **Presentation** (TODO)
**File**: `acf-presentation.php`
**Fields**:
- `presentation_title` (Text) - Custom presentation title
- `select_files` (File) - Downloadable presentation file
- `host_company_organization` (Text) - Host organization name
- `host_company_organization_logo` (Image) - Host organization logo

### 🔄 **Whitepaper** (TODO)
**File**: `acf-whitepaper.php`
**Fields**:
- `companyorganization_logo` (Image) - Publishing organization logo
- `upload_pdf` (File) - PDF whitepaper file

### 🔄 **Case Study** (TODO)
**File**: `acf-case-study.php`
**Fields**:
- `companyorganization_logo` (Image) - Featured organization logo
- `upload_pdf` (File) - PDF case study file

### 🔄 **Book** (TODO)
**File**: `acf-book.php`
**Fields**:
- `book_title` (Text) - Book title
- `author_names` (Text) - Book author(s)
- ~~`book_cover_image` (Image)~~ - *Remove: duplicates featured image*
- `purchase_url` (URL) - Book purchase link

### 🔄 **Podcast** (TODO)
**File**: `acf-podcast.php`
**Fields**:
- `podcast_episode_title` (Text) - Episode title *required*
- ~~`podcast_preview_image` (Image)~~ - *Remove: duplicates featured image*
- `podcast_logo` (Image) - Podcast brand logo
- `podcast_name` (Text) - Podcast name *required*
- `hosts` (Repeater) - Array of host objects *required*
- `episode_description` (Textarea) - Episode description *required*
- `episode_url` (URL) - External episode URL
- `audio_file` (File/URL) - Direct audio file

### 🔄 **Press Release** (TODO)
**File**: `acf-press-release.php`
**Fields**:
- `press_release_url` (URL) - External press release link
- `upload_pdf` (File) - PDF document upload

### 🔄 **Template** (TODO)
**File**: `acf-template.php`
**Fields**:
- `creator_name` (Text) - Template creator name
- `logo` (Image) - Creator/company logo
- `file_upload` (File) - Downloadable template file
- `url` (URL) - Online preview URL

### 🔄 **Thought Leadership** (TODO)
**File**: `acf-leadership.php`
**Fields**:
- ~~`featured_image` (Image)~~ - *Remove: duplicates featured image*

## Field Naming Conventions

### Common Patterns:
- **URLs**: End with `_url` or `_link_url`
- **Images**: End with `_image`, `_logo`, or contain `logo`
- **Dates/Times**: Use `_date`, `_time`, `_zone` suffixes
- **Companies**: Often use `company` or `organization` in field names
- **Files**: End with `_file` or `_upload`

### Fields to Remove (Duplicate Featured Image):
These fields duplicate WordPress's built-in featured image functionality and should be removed:
- `article_image` (News) ✅ DONE
- `event_promo_image` (Event)
- `webinar_promo_image` (Webinar)
- `video_thumbnail` (Video)
- `course_image` (Course)
- `book_cover_image` (Book)
- `podcast_preview_image` (Podcast)
- `featured_image` (Thought Leadership)

## Technical Implementation Details

### ACF Field Types Support:
- **Text**: `sanitize_text_field()`
- **URL**: `esc_url_raw()`
- **Textarea**: `sanitize_textarea_field()`
- **Image**: File upload handling
- **File**: File upload handling
- **Repeater**: JSON array handling
- **Select**: Value validation against options

### Error Handling:
- Field validation based on post category
- Proper sanitization for each field type
- Security checks for user permissions
- Comprehensive logging for debugging

### Development Workflow:
1. Update ACF configuration file (enable REST API)
2. Add fields to REST API endpoint validation
3. Implement frontend form fields
4. Update feed item display component
5. Test field saving and display

## Future Enhancements

### Planned Improvements:
1. **File Upload Handling**: Direct file uploads for PDF and document fields
2. **Image Field Migration**: Automatic migration from duplicate image fields to featured images
3. **Repeater Field Support**: Enhanced UI for repeater fields (presenters, hosts, creators)
4. **Field Dependencies**: Conditional field display based on other field values
5. **Bulk Updates**: Mass update ACF fields across multiple posts

### Performance Considerations:
- Fields are cached with posts in the frontend
- REST API responses include embedded ACF data
- Minimal database queries through optimized ACF functions
- Client-side validation reduces server requests

## Troubleshooting

### Common Issues:
1. **Fields not saving**: Check `show_in_rest` settings and field validation
2. **Fields not displaying**: Verify ACF field names match frontend implementation
3. **Permission errors**: Ensure user can edit the specific post
4. **Type mismatches**: Check field sanitization and validation logic

### Debug Steps:
1. Check browser console for API errors
2. Verify WordPress error logs
3. Test ACF field updates directly in WordPress admin
4. Validate field names and types in ACF configuration