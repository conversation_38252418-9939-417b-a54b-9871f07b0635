<?php
/**
 * ACF Post-Vendor Relationship Field
 * 
 * Adds a relationship field to posts for directly selecting which vendor the post belongs to
 */

add_action('acf/include_fields', function() {
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }

    acf_add_local_field_group(array(
        'key' => 'group_post_vendor_relationship',
        'title' => 'Vendor Attribution',
        'fields' => array(
            array(
                'key' => 'field_post_vendor',
                'label' => 'Vendor',
                'name' => 'post_vendor',
                'type' => 'relationship',
                'instructions' => 'Select a vendor to attribute this post to (optional)',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'post_type' => array(
                    0 => 'vendor',
                ),
                'taxonomy' => '',
                'filters' => array(
                    0 => 'search',
                    1 => 'post_type',
                    2 => 'taxonomy',
                ),
                'elements' => '',
                'min' => '',
                'max' => 1, // Only allow one vendor per post
                'return_format' => 'id',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'post',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'side', // Show in sidebar for easy access
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 1,
    ));
});

// Sync ACF field to post meta for API compatibility
add_action('acf/save_post', function($post_id) {
    // Only run for regular posts
    if (get_post_type($post_id) !== 'post') {
        return;
    }
    
    // Get the vendor relationship field value
    $vendor = get_field('post_vendor', $post_id);
    
    if ($vendor && !empty($vendor)) {
        // ACF relationship field returns an array, get the first (and only) vendor ID
        $vendor_id = is_array($vendor) ? $vendor[0] : $vendor;
        
        // Update the post_vendor_id meta field for API compatibility
        update_post_meta($post_id, 'post_vendor_id', $vendor_id);
    } else {
        // Remove the meta field if no vendor is selected
        delete_post_meta($post_id, 'post_vendor_id');
    }
}, 20);

// Add vendor info to REST API
add_action('rest_api_init', function() {
    register_rest_field('post', 'vendor_info', array(
        'get_callback' => function($object) {
            $vendor_field = get_field('post_vendor', $object['id']);
            $vendor_id = null;
            
            // DEBUG LOG - Remove this after fixing
            error_log('[WordPress] vendor_info callback for post ' . $object['id'] . ': vendor_field = ' . print_r($vendor_field, true));
            
            // Handle ACF relationship field return value (can be array or single ID)
            if ($vendor_field) {
                if (is_array($vendor_field) && !empty($vendor_field)) {
                    // ACF relationship field returned an array, get the first ID
                    $vendor_id = $vendor_field[0];
                    error_log('[WordPress] vendor_info: extracted vendor_id from array = ' . $vendor_id);
                } elseif (is_numeric($vendor_field)) {
                    // ACF relationship field returned a single ID
                    $vendor_id = intval($vendor_field);
                    error_log('[WordPress] vendor_info: vendor_id from single value = ' . $vendor_id);
                }
            }
            
            // Fallback to post meta if ACF field is empty
            if (!$vendor_id) {
                $vendor_id = get_post_meta($object['id'], 'post_vendor_id', true);
                if ($vendor_id) {
                    $vendor_id = intval($vendor_id);
                    error_log('[WordPress] vendor_info: vendor_id from post meta fallback = ' . $vendor_id);
                }
            }
            
            if (!$vendor_id) {
                error_log('[WordPress] vendor_info: no vendor_id found for post ' . $object['id']);
                return null;
            }
            
            $vendor = get_post($vendor_id);
            if (!$vendor) {
                return null;
            }
            
            return array(
                'id' => $vendor_id,
                'name' => $vendor->post_title,
                'slug' => $vendor->post_name,
                'logo_url' => wp_get_attachment_url(get_post_thumbnail_id($vendor_id)),
            );
        },
        'schema' => array(
            'description' => 'Vendor information if post is attributed to a vendor',
            'type' => 'object',
            'properties' => array(
                'id' => array('type' => 'integer'),
                'name' => array('type' => 'string'),
                'slug' => array('type' => 'string'),
                'logo_url' => array('type' => 'string'),
            ),
        ),
    ));
    
    // Custom endpoint to set vendor for a post
    register_rest_route('tourismiq/v1', '/posts/(?P<id>\d+)/set-vendor', array(
        'methods' => 'POST',
        'callback' => function($request) {
            $post_id = $request->get_param('id');
            $vendor_id = $request->get_json_params()['vendor_id'] ?? null;
            
            if (!$post_id || !$vendor_id) {
                return new WP_Error('missing_params', 'Post ID and vendor ID are required', array('status' => 400));
            }
            
            // Check if user can edit this post
            if (!current_user_can('edit_post', $post_id)) {
                return new WP_Error('forbidden', 'You do not have permission to edit this post', array('status' => 403));
            }
            
            // Set the ACF field
            $result = update_field('post_vendor', $vendor_id, $post_id);
            
            if ($result) {
                // Also trigger the sync to meta field
                update_post_meta($post_id, 'post_vendor_id', $vendor_id);
                
                return array(
                    'success' => true,
                    'post_id' => $post_id,
                    'vendor_id' => $vendor_id,
                    'message' => 'Vendor field updated successfully'
                );
            } else {
                return new WP_Error('update_failed', 'Failed to update vendor field', array('status' => 500));
            }
        },
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
});