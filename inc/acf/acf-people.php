<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_people_fields',
	'title' => 'People on the Move Fields',
	'fields' => array(
		array(
			'key' => 'field_people_gallery',
			'label' => 'People Gallery',
			'name' => 'people_gallery',
			'aria-label' => '',
			'type' => 'gallery',
			'instructions' => 'Add multiple images for this person. If images are added here, they will replace the featured image and show as a swiper gallery.',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'library' => 'all',
			'min' => '',
			'max' => '',
			'preview_size' => 'medium',
			'insert' => 'append',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'people',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 1,
) );
} );