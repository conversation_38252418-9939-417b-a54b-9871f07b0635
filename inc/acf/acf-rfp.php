<?php
/**
 * ACF Field Groups for RFP Post Type
 */

if( function_exists('acf_add_local_field_group') ):

acf_add_local_field_group(array(
    'key' => 'group_rfp_fields',
    'title' => 'RFP Information',
    'fields' => array(
        array(
            'key' => 'field_rfp_status',
            'label' => 'RFP Status',
            'name' => 'rfp_status',
            'type' => 'select',
            'instructions' => 'Select the current status of this RFP',
            'required' => 1,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '50',
                'class' => '',
                'id' => '',
            ),
            'choices' => array(
                'NEW' => 'New',
                'EXPIRED' => 'Expired',
            ),
            'default_value' => 'NEW',
            'allow_null' => 0,
            'multiple' => 0,
            'ui' => 0,
            'return_format' => 'value',
            'ajax' => 0,
            'placeholder' => '',
        ),
        array(
            'key' => 'field_rfp_organization',
            'label' => 'Organization',
            'name' => 'rfp_organization',
            'type' => 'text',
            'instructions' => 'Enter the organization issuing this RFP',
            'required' => 1,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '50',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'placeholder' => 'e.g., Visit California',
            'prepend' => '',
            'append' => '',
            'maxlength' => '',
        ),
        array(
            'key' => 'field_rfp_source_url',
            'label' => 'Source URL',
            'name' => 'rfp_source_url',
            'type' => 'url',
            'instructions' => 'Enter the URL where this RFP was originally posted',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '50',
                'class' => '',
                'id' => '',
            ),
            'default_value' => '',
            'placeholder' => 'https://example.com/rfp',
        ),
        array(
            'key' => 'field_rfp_deadline',
            'label' => 'Submission Deadline',
            'name' => 'rfp_deadline',
            'type' => 'date_picker',
            'instructions' => 'Select the deadline for RFP submissions',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '50',
                'class' => '',
                'id' => '',
            ),
            'display_format' => 'm/d/Y',
            'return_format' => 'Y-m-d',
            'first_day' => 0,
        ),
    ),
    'location' => array(
        array(
            array(
                'param' => 'post_type',
                'operator' => '==',
                'value' => 'rfp',
            ),
        ),
    ),
    'menu_order' => 0,
    'position' => 'normal',
    'style' => 'default',
    'label_placement' => 'top',
    'instruction_placement' => 'label',
    'hide_on_screen' => '',
    'active' => true,
    'description' => '',
    'show_in_rest' => 1,
));

endif;