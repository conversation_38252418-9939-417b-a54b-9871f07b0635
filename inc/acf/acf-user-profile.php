<?php 
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_user_profile_fields',
	'title' => 'User Profile Fields',
	'fields' => array(
		array(
			'key' => 'field_profile_picture',
			'label' => 'Profile Picture',
			'name' => 'profile_picture',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => 'Upload your profile photo',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'library' => 'all',
			'min_width' => 150,
			'min_height' => 150,
			'min_size' => '',
			'max_width' => 800,
			'max_height' => 800,
			'max_size' => '2MB',
			'mime_types' => 'jpg,jpeg,png,gif',
			'preview_size' => 'medium',
		),
		array(
			'key' => 'field_cover_image',
			'label' => 'Cover Image',
			'name' => 'cover_image',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => 'Upload a cover image for your profile',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'return_format' => 'array',
			'library' => 'all',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '5MB',
			'mime_types' => 'jpg,jpeg,png,gif,webp',
			'preview_size' => 'medium',
		),
		array(
			'key' => 'field_bio',
			'label' => 'Bio',
			'name' => 'bio',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => 'Tell us about yourself',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'maxlength' => 500,
			'placeholder' => 'Enter your bio...',
			'rows' => 4,
		),
		array(
			'key' => 'field_job_title',
			'label' => 'Job Title',
			'name' => 'job_title',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => 'e.g. Marketing Manager',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_company',
			'label' => 'Company',
			'name' => 'company',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => 'Company name',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_location',
			'label' => 'Location',
			'name' => 'location',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => 'City, Country',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_phone',
			'label' => 'Phone',
			'name' => 'phone',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '+****************',
			'prepend' => '',
			'append' => '',
		),
		// Social Media Fields
		array(
			'key' => 'field_website',
			'label' => 'Website',
			'name' => 'website',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => 'https://www.example.com',
		),
		array(
			'key' => 'field_linkedin',
			'label' => 'LinkedIn',
			'name' => 'linkedin',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => 'https://linkedin.com/in/username',
		),
		array(
			'key' => 'field_twitter',
			'label' => 'Twitter/X',
			'name' => 'twitter',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => 'https://twitter.com/username',
		),
		array(
			'key' => 'field_facebook',
			'label' => 'Facebook',
			'name' => 'facebook',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => 'https://facebook.com/username',
		),
		array(
			'key' => 'field_instagram',
			'label' => 'Instagram',
			'name' => 'instagram',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => 'https://instagram.com/username',
		),
		// Newsletter subscription field
		array(
			'key' => 'field_newsletter_optin',
			'label' => 'Newsletter Subscription',
			'name' => 'newsletter_optin',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => 'Stay in the loop with job alerts, industry insights, and new tools',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'message' => 'Yes, I want occasional email updates',
			'default_value' => 0,
			'ui' => 1,
			'ui_on_text' => 'Subscribed',
			'ui_off_text' => 'Not subscribed',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'user_form',
				'operator' => '==',
				'value' => 'all',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => 'User profile fields for member profiles',
	'show_in_rest' => 1,
) );
} );