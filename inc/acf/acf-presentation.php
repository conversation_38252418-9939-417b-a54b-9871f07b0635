<?php 
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}
	
	acf_add_local_field_group( array(
		'key' => 'group_presentation_new_2025',
		'title' => 'Presentation Fields',
		'fields' => array(
			array(
				'key' => 'field_presentation_org_2025',
				'label' => 'Company/Organization',
				'name' => 'host_company_organization',
				'aria-label' => '',
				'type' => 'text',
				'instructions' => '',
				'required' => 1,
				'conditional_logic' => 0,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'relevanssi_exclude' => 0,
				'hide_admin' => 0,
				'default_value' => '',
				'maxlength' => '',
				'placeholder' => '',
				'prepend' => '',
				'append' => '',
			),
			array(
				'key' => 'field_presentation_logo_2025',
				'label' => 'Company/Organization Logo',
				'name' => 'host_company_organization_logo',
				'aria-label' => '',
				'type' => 'image',
				'instructions' => '',
				'required' => 0,
				'conditional_logic' => 0,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'relevanssi_exclude' => 0,
				'hide_admin' => 0,
				'return_format' => 'array',
				'library' => 'all',
				'min_width' => '',
				'min_height' => '',
				'min_size' => '',
				'max_width' => '',
				'max_height' => '',
				'max_size' => 5,
				'mime_types' => '',
				'preview_size' => 'medium',
				'uploader' => '',
				'acfe_thumbnail' => 0,
			),
			array(
				'key' => 'field_presentation_pdf_2025',
				'label' => 'PDF Upload',
				'name' => 'pdf_upload',
				'aria-label' => '',
				'type' => 'file',
				'instructions' => '',
				'required' => 1,
				'conditional_logic' => 0,
				'wrapper' => array(
					'width' => '',
					'class' => '',
					'id' => '',
				),
				'relevanssi_exclude' => 0,
				'hide_admin' => 0,
				'return_format' => 'array',
				'library' => 'all',
				'min_size' => '',
				'max_size' => 15,
				'mime_types' => 'pdf',
				'uploader' => '',
			),
		),
		'location' => array(
			array(
				array(
					'param' => 'post_category',
					'operator' => '==',
					'value' => 'category:presentation',
				),
			),
		),
		'menu_order' => 0,
		'position' => 'normal',
		'style' => 'default',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'hide_on_screen' => '',
		'active' => true,
		'description' => '',
		'show_in_rest' => 0,
	) );
} );

