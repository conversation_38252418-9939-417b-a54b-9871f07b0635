<?php 
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_6585b1f8ac64a',
	'title' => 'Video Fields',
	'fields' => array(
		array(
			'key' => 'field_6585b205f310d',
			'label' => 'Creators',
			'name' => 'creators',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'hide_admin' => 0,
			'acfe_repeater_stylised_button' => 0,
			'layout' => 'table',
			'pagination' => 0,
			'min' => 0,
			'max' => 0,
			'collapsed' => '',
			'button_label' => 'Add Creator',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_6585b20ff310e',
					'label' => 'Creator Name',
					'name' => 'creator_name',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'hide_admin' => 0,
					'default_value' => '',
					'maxlength' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'parent_repeater' => 'field_6585b205f310d',
				),
			),
		),
		array(
			'key' => 'field_6585b21df310f',
			'label' => 'Creator Logo',
			'name' => 'creator_logo',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'return_format' => 'array',
			'library' => 'uploadedTo',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => 5,
			'mime_types' => '',
			'preview_size' => 'medium',
			'uploader' => '',
			'acfe_thumbnail' => 0,
		),
		array(
			'key' => 'field_658625e492177',
			'label' => 'Video Source',
			'name' => 'video_source',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'choices' => array(
				'youtube' => 'YouTube URL',
				'vimeo' => 'Vimeo URL',
				'twitter' => 'X Embed',
				'linkedin' => 'LinkedIn Embed',
			),
			'default_value' => false,
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
			'allow_custom' => 0,
			'search_placeholder' => '',
			'create_options' => 0,
			'save_options' => 0,
		),
		array(
			'key' => 'field_6585b23af3111',
			'label' => 'YouTube URL',
			'name' => 'youtube_id',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '<a class="btn-tutorial" data-source="youtube" href="#" uk-toggle="target: #how-to" uk-tooltip="Watch How!"><svg xmlns="http://www.w3.org/2000/svg" width="21.426" height="21.426" viewBox="0 0 21.426 21.426">
	<g id="Icon_akar-question" data-name="Icon akar-question" transform="translate(-2 -2)">
		<path id="Path_502" data-name="Path 502" d="M22.426,12.713A9.713,9.713,0,1,1,12.713,3a9.713,9.713,0,0,1,9.713,9.713Z" transform="translate(0 0)" fill="none" stroke="#3d405b" stroke-width="2"/>
		<path id="Path_503" data-name="Path 503" d="M15,11.941c.486-.962.971-1.441,1.943-1.441a1.907,1.907,0,0,1,1.943,1.921c0,.961-.486,1.44-1.943,2.4v1.5m0,3.4v.486" transform="translate(-4.23 -2.644)" fill="none" stroke="#3d405b" stroke-linecap="round" stroke-width="2"/>
	</g>
</svg></a>',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_658625e492177',
						'operator' => '==',
						'value' => 'youtube',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'width' => '',
			'height' => '',
		),
		array(
			'key' => 'field_65d6344452233',
			'label' => 'Vimeo URL',
			'name' => 'vimeo_id',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '<a class="btn-tutorial" data-source="vimeo" href="#" uk-toggle="target: #how-to" uk-tooltip="Watch How!"><svg xmlns="http://www.w3.org/2000/svg" width="21.426" height="21.426" viewBox="0 0 21.426 21.426">
	<g id="Icon_akar-question" data-name="Icon akar-question" transform="translate(-2 -2)">
		<path id="Path_502" data-name="Path 502" d="M22.426,12.713A9.713,9.713,0,1,1,12.713,3a9.713,9.713,0,0,1,9.713,9.713Z" transform="translate(0 0)" fill="none" stroke="#3d405b" stroke-width="2"/>
		<path id="Path_503" data-name="Path 503" d="M15,11.941c.486-.962.971-1.441,1.943-1.441a1.907,1.907,0,0,1,1.943,1.921c0,.961-.486,1.44-1.943,2.4v1.5m0,3.4v.486" transform="translate(-4.23 -2.644)" fill="none" stroke="#3d405b" stroke-linecap="round" stroke-width="2"/>
	</g>
</svg></a>',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_658625e492177',
						'operator' => '==',
						'value' => 'vimeo',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'width' => '',
			'height' => '',
		),
		array(
			'key' => 'field_6620469e2d4b6',
			'label' => 'X Embed',
			'name' => 'x',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '<a class="btn-tutorial" data-source="twitter" href="#" uk-toggle="target: #how-to" uk-tooltip="Watch How!"><svg xmlns="http://www.w3.org/2000/svg" width="21.426" height="21.426" viewBox="0 0 21.426 21.426">
	<g id="Icon_akar-question" data-name="Icon akar-question" transform="translate(-2 -2)">
		<path id="Path_502" data-name="Path 502" d="M22.426,12.713A9.713,9.713,0,1,1,12.713,3a9.713,9.713,0,0,1,9.713,9.713Z" transform="translate(0 0)" fill="none" stroke="#3d405b" stroke-width="2"/>
		<path id="Path_503" data-name="Path 503" d="M15,11.941c.486-.962.971-1.441,1.943-1.441a1.907,1.907,0,0,1,1.943,1.921c0,.961-.486,1.44-1.943,2.4v1.5m0,3.4v.486" transform="translate(-4.23 -2.644)" fill="none" stroke="#3d405b" stroke-linecap="round" stroke-width="2"/>
	</g>
</svg></a>',
			'required' => 1,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_658625e492177',
						'operator' => '==',
						'value' => 'twitter',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => 'Paste in the X Embed link only...',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_662046ad2d4b7',
			'label' => 'LinkedIn Embed',
			'name' => 'linkedin_iframe',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '<a class="btn-tutorial" data-source="linkedin" href="#" uk-toggle="target: #how-to" uk-tooltip="Watch How!"><svg xmlns="http://www.w3.org/2000/svg" width="21.426" height="21.426" viewBox="0 0 21.426 21.426">
	<g id="Icon_akar-question" data-name="Icon akar-question" transform="translate(-2 -2)">
		<path id="Path_502" data-name="Path 502" d="M22.426,12.713A9.713,9.713,0,1,1,12.713,3a9.713,9.713,0,0,1,9.713,9.713Z" transform="translate(0 0)" fill="none" stroke="#3d405b" stroke-width="2"/>
		<path id="Path_503" data-name="Path 503" d="M15,11.941c.486-.962.971-1.441,1.943-1.441a1.907,1.907,0,0,1,1.943,1.921c0,.961-.486,1.44-1.943,2.4v1.5m0,3.4v.486" transform="translate(-4.23 -2.644)" fill="none" stroke="#3d405b" stroke-linecap="round" stroke-width="2"/>
	</g>
</svg></a>',
			'required' => 1,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_658625e492177',
						'operator' => '==',
						'value' => 'linkedin',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => 'Paste in the LinkedIn Embed link only...',
			'prepend' => '',
			'append' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_category',
				'operator' => '==',
				'value' => 'category:video',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );
} );

