<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_64b7510517579',
	'title' => 'Event Fields',
	'fields' => array(
		array(
			'key' => 'field_64b752c1c2770',
			'label' => 'Event Start Date',
			'name' => 'event_date',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'display_format' => 'F j, Y',
			'return_format' => 'F j, Y',
			'first_day' => 1,
		),
		array(
			'key' => 'field_6695c42586e88',
			'label' => 'Time (Optional)',
			'name' => 'event_time',
			'aria-label' => '',
			'type' => 'time_picker',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_64b752c1c2770',
						'operator' => '!=empty',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'display_format' => 'g:i a',
			'return_format' => 'g:i a',
		),
		array(
			'key' => 'field_6695c44b86e89',
			'label' => 'Time Zone (Optional)',
			'name' => 'time_zone',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_64b752c1c2770',
						'operator' => '!=empty',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'choices' => array(
				'EST' => 'EST',
				'CST' => 'CST',
				'MST' => 'MST',
				'PST' => 'PST',
				'AKST' => 'AKST',
				'HST' => 'HST',
			),
			'default_value' => false,
			'return_format' => 'value',
			'multiple' => 0,
			'allow_custom' => 0,
			'placeholder' => '',
			'search_placeholder' => '',
			'allow_null' => 0,
			'ui' => 0,
			'ajax' => 0,
			'create_options' => 0,
			'save_options' => 0,
		),
		array(
			'key' => 'field_65e0f4cb24cea',
			'label' => 'Event End Date',
			'name' => 'event_end_date',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'display_format' => 'F j, Y',
			'return_format' => 'F j, Y',
			'first_day' => 1,
		),
		array(
			'key' => 'field_6585a9ae0bd25',
			'label' => 'Host Company/Organization',
			'name' => 'host_company_organization',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6585a9b90bd26',
			'label' => 'Host Company/Organization Logo',
			'name' => 'host_company_organization_logo',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'return_format' => 'array',
			'library' => 'all',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => 5,
			'mime_types' => '',
			'preview_size' => 'medium',
			'uploader' => '',
			'acfe_thumbnail' => 0,
		),
		array(
			'key' => 'field_64b752cac2771',
			'label' => 'Event Location',
			'name' => 'event_location',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6585a9ed0bd28',
			'label' => 'Additional Details Link (URL)',
			'name' => 'additional_details_link_url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'placeholder' => '',
		),
		array(
			'key' => 'field_6585aa000bd29',
			'label' => 'Registration Link (URL)',
			'name' => 'registration_link_url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'placeholder' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_category',
				'operator' => '==',
				'value' => 'category:event',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );
} );

