<?php 
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_64b74415e8a7d',
	'title' => 'Jobs Fields',
	'fields' => array(
		array(
			'key' => 'field_job_status',
			'label' => 'Status',
			'name' => 'job_status',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'choices' => array(
				'NEW' => 'New',
				'ACTIVE' => 'Active',
				'EXPIRED' => 'Expired',
				'FILLED' => 'Filled',
				'DRAFT' => 'Draft',
			),
			'default_value' => '',
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 1,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => 'Select status (optional)',
		),
		array(
			'key' => 'field_job_organization',
			'label' => 'Organization',
			'name' => 'job_organization',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_job_source_url',
			'label' => 'Source URL',
			'name' => 'job_source_url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => 'Link to the original job posting',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
		),
		array(
			'key' => 'field_job_deadline',
			'label' => 'Deadline',
			'name' => 'job_deadline',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => 'Application deadline',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'display_format' => 'm/d/Y',
			'return_format' => 'Y-m-d',
			'first_day' => 1,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'job',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => array(
		0 => 'the_content',
	),
	'active' => true,
	'description' => '',
	'show_in_rest' => 1,
) );
} );

