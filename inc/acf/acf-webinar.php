<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_65235872a0e08',
	'title' => 'Webinar Form Fields',
	'fields' => array(
		array(
			'key' => 'field_6585a8549f112',
			'label' => 'Webinar Type',
			'name' => 'webinar_type',
			'aria-label' => '',
			'type' => 'button_group',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'hide_admin' => 0,
			'choices' => array(
				'recorded' => 'Recorded',
				'live' => 'Live',
			),
			'default_value' => '',
			'return_format' => 'array',
			'allow_null' => 0,
			'layout' => 'horizontal',
		),
		array(
			'key' => 'field_66e893649a536',
			'label' => 'Image Caption',
			'name' => 'image_caption',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'allow_in_bindings' => 0,
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6583938744021',
			'label' => 'Presenters',
			'name' => 'presenters',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'acfe_repeater_stylised_button' => 0,
			'layout' => 'block',
			'pagination' => 0,
			'min' => 0,
			'max' => 0,
			'collapsed' => '',
			'button_label' => 'Add Presenter',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_6583945d44022',
					'label' => 'Presenter Name',
					'name' => 'presenter_name',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'default_value' => '',
					'maxlength' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'parent_repeater' => 'field_6583938744021',
				),
			),
		),
		array(
			'key' => 'field_6585a8d09f113',
			'label' => 'Date',
			'name' => 'date',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_6585a8549f112',
						'operator' => '==',
						'value' => 'live',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'display_format' => 'F j, Y',
			'return_format' => 'F j, Y',
			'first_day' => 1,
		),
		array(
			'key' => 'field_6695c1c334053',
			'label' => 'Time',
			'name' => 'time',
			'aria-label' => '',
			'type' => 'time_picker',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_6585a8549f112',
						'operator' => '==',
						'value' => 'live',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'display_format' => 'g:i a',
			'return_format' => 'g:i a',
		),
		array(
			'key' => 'field_6695c1eb34054',
			'label' => 'Time Zone',
			'name' => 'time_zone',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_6585a8549f112',
						'operator' => '==',
						'value' => 'live',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'choices' => array(
				'EST' => 'EST',
				'CST' => 'CST',
				'MST' => 'MST',
				'PST' => 'PST',
				'AKST' => 'AKST',
				'HST' => 'HST',
			),
			'default_value' => false,
			'return_format' => 'value',
			'multiple' => 0,
			'placeholder' => '',
			'allow_null' => 1,
			'ui' => 0,
			'ajax' => 0,
			'allow_custom' => 0,
			'search_placeholder' => '',
			'create_options' => 0,
			'save_options' => 0,
		),
		array(
			'key' => 'field_6583947144023',
			'label' => 'Webinar Host Company/Organization',
			'name' => 'webinar_host_company_organization',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6583948c44024',
			'label' => 'Webinar Host Company/Organization Logo',
			'name' => 'webinar_host_company_organization_logo',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'uploader' => '',
			'return_format' => 'array',
			'acfe_thumbnail' => 0,
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => 1000,
			'max_height' => 1000,
			'max_size' => '',
			'mime_types' => '',
			'preview_size' => 'medium',
			'library' => 'all',
		),
		array(
			'key' => 'field_658394b344025',
			'label' => 'Video Type',
			'name' => 'video_type',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_6585a8549f112',
						'operator' => '==',
						'value' => 'recorded',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'choices' => array(
				'youtube' => 'YouTube URL',
				'vimeo' => 'Vimeo URL',
				'twitter' => 'X Embed',
				'linkedin' => 'LinkedIn Embed',
			),
			'default_value' => false,
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
			'allow_custom' => 0,
			'search_placeholder' => '',
			'create_options' => 0,
			'save_options' => 0,
		),
		array(
			'key' => 'field_66204cb2d2d96',
			'label' => 'YouTube URL',
			'name' => 'youtube_url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '<a class="btn-tutorial" data-source="youtube" href="#" uk-toggle="target: #how-to" uk-tooltip="Watch How!">
<svg xmlns="http://www.w3.org/2000/svg" width="21.426" height="21.426" viewBox="0 0 21.426 21.426">
	<g id="Icon_akar-question" data-name="Icon akar-question" transform="translate(-2 -2)">
		<path id="Path_502" data-name="Path 502" d="M22.426,12.713A9.713,9.713,0,1,1,12.713,3a9.713,9.713,0,0,1,9.713,9.713Z" transform="translate(0 0)" fill="none" stroke="#3d405b" stroke-width="2"/>
		<path id="Path_503" data-name="Path 503" d="M15,11.941c.486-.962.971-1.441,1.943-1.441a1.907,1.907,0,0,1,1.943,1.921c0,.961-.486,1.44-1.943,2.4v1.5m0,3.4v.486" transform="translate(-4.23 -2.644)" fill="none" stroke="#3d405b" stroke-linecap="round" stroke-width="2"/>
	</g>
</svg>
</a>',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_658394b344025',
						'operator' => '==',
						'value' => 'youtube',
					),
					array(
						'field' => 'field_6585a8549f112',
						'operator' => '==',
						'value' => 'recorded',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'width' => '',
			'height' => '',
		),
		array(
			'key' => 'field_66204cc7d2d97',
			'label' => 'Vimeo URL',
			'name' => 'vimeo_url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '<a class="btn-tutorial" data-source="vimeo" href="#" uk-toggle="target: #how-to" uk-tooltip="Watch How!">
<svg xmlns="http://www.w3.org/2000/svg" width="21.426" height="21.426" viewBox="0 0 21.426 21.426">
	<g id="Icon_akar-question" data-name="Icon akar-question" transform="translate(-2 -2)">
		<path id="Path_502" data-name="Path 502" d="M22.426,12.713A9.713,9.713,0,1,1,12.713,3a9.713,9.713,0,0,1,9.713,9.713Z" transform="translate(0 0)" fill="none" stroke="#3d405b" stroke-width="2"/>
		<path id="Path_503" data-name="Path 503" d="M15,11.941c.486-.962.971-1.441,1.943-1.441a1.907,1.907,0,0,1,1.943,1.921c0,.961-.486,1.44-1.943,2.4v1.5m0,3.4v.486" transform="translate(-4.23 -2.644)" fill="none" stroke="#3d405b" stroke-linecap="round" stroke-width="2"/>
	</g>
</svg>
</a>',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_658394b344025',
						'operator' => '==',
						'value' => 'vimeo',
					),
					array(
						'field' => 'field_6585a8549f112',
						'operator' => '==',
						'value' => 'recorded',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'width' => '',
			'height' => '',
		),
		array(
			'key' => 'field_66204cd2d2d98',
			'label' => 'X Embed',
			'name' => 'twitter',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '<a class="btn-tutorial" data-source="twitter" href="#" uk-toggle="target: #how-to" uk-tooltip="Watch How!">
<svg xmlns="http://www.w3.org/2000/svg" width="21.426" height="21.426" viewBox="0 0 21.426 21.426">
	<g id="Icon_akar-question" data-name="Icon akar-question" transform="translate(-2 -2)">
		<path id="Path_502" data-name="Path 502" d="M22.426,12.713A9.713,9.713,0,1,1,12.713,3a9.713,9.713,0,0,1,9.713,9.713Z" transform="translate(0 0)" fill="none" stroke="#3d405b" stroke-width="2"/>
		<path id="Path_503" data-name="Path 503" d="M15,11.941c.486-.962.971-1.441,1.943-1.441a1.907,1.907,0,0,1,1.943,1.921c0,.961-.486,1.44-1.943,2.4v1.5m0,3.4v.486" transform="translate(-4.23 -2.644)" fill="none" stroke="#3d405b" stroke-linecap="round" stroke-width="2"/>
	</g>
</svg>
</a>',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_658394b344025',
						'operator' => '==',
						'value' => 'twitter',
					),
					array(
						'field' => 'field_6585a8549f112',
						'operator' => '==',
						'value' => 'recorded',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => 'Paste in the X Embed link only...',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_66204cdfd2d99',
			'label' => 'LinkedIn Embed',
			'name' => 'linkedin_iframe',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '<a class="btn-tutorial" data-source="linkedin" href="#" uk-toggle="target: #how-to" uk-tooltip="Watch How!">
<svg xmlns="http://www.w3.org/2000/svg" width="21.426" height="21.426" viewBox="0 0 21.426 21.426">
	<g id="Icon_akar-question" data-name="Icon akar-question" transform="translate(-2 -2)">
		<path id="Path_502" data-name="Path 502" d="M22.426,12.713A9.713,9.713,0,1,1,12.713,3a9.713,9.713,0,0,1,9.713,9.713Z" transform="translate(0 0)" fill="none" stroke="#3d405b" stroke-width="2"/>
		<path id="Path_503" data-name="Path 503" d="M15,11.941c.486-.962.971-1.441,1.943-1.441a1.907,1.907,0,0,1,1.943,1.921c0,.961-.486,1.44-1.943,2.4v1.5m0,3.4v.486" transform="translate(-4.23 -2.644)" fill="none" stroke="#3d405b" stroke-linecap="round" stroke-width="2"/>
	</g>
</svg>
</a>',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_658394b344025',
						'operator' => '==',
						'value' => 'linkedin',
					),
					array(
						'field' => 'field_6585a8549f112',
						'operator' => '==',
						'value' => 'recorded',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => 'Paste in the LinkedIn Embed link only...',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6585a9119f114',
			'label' => 'Register URL',
			'name' => 'register_url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_6585a8549f112',
						'operator' => '==',
						'value' => 'live',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'placeholder' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_category',
				'operator' => '==',
				'value' => 'category:webinar',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );
} );

