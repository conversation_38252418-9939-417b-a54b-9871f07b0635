<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
		'key' => 'group_vendor_fields',
		'title' => 'Vendor Information',
		'fields' => array(
			// Basic Information Tab
			array(
				'key' => 'field_vendor_basic_tab',
				'label' => 'Basic Information',
				'name' => '',
				'type' => 'tab',
				'placement' => 'top',
			),
			array(
				'key' => 'field_vendor_website',
				'label' => 'Website',
				'name' => 'vendor_website',
				'type' => 'url',
				'instructions' => 'Enter the vendor\'s website URL',
				'required' => 0,
				'wrapper' => array(
					'width' => '50',
				),
			),
			array(
				'key' => 'field_vendor_is_paid',
				'label' => 'Paid Vendor',
				'name' => 'vendor_is_paid',
				'type' => 'true_false',
				'instructions' => 'Is this a paid vendor account?',
				'default_value' => 0,
				'ui' => 1,
				'wrapper' => array(
					'width' => '25',
				),
			),
			array(
				'key' => 'field_vendor_cover_photo',
				'label' => 'Cover Photo',
				'name' => 'vendor_cover_photo',
				'type' => 'image',
				'instructions' => 'Upload a cover photo for the vendor profile (recommended size: 1920x480)',
				'return_format' => 'array',
				'preview_size' => 'medium',
				'library' => 'all',
				// Removed conditional logic so all vendors can have cover photos
			),
			
			// Social Media Tab
			array(
				'key' => 'field_vendor_social_tab',
				'label' => 'Social Media',
				'name' => '',
				'type' => 'tab',
				'placement' => 'top',
				'conditional_logic' => array(
					array(
						array(
							'field' => 'field_vendor_is_paid',
							'operator' => '==',
							'value' => '1',
						),
					),
				),
			),
			array(
				'key' => 'field_vendor_social_links',
				'label' => 'Social Media Links',
				'name' => 'vendor_social_links',
				'type' => 'group',
				'layout' => 'block',
				'sub_fields' => array(
					array(
						'key' => 'field_vendor_facebook',
						'label' => 'Facebook',
						'name' => 'facebook',
						'type' => 'url',
						'wrapper' => array(
							'width' => '50',
						),
					),
					array(
						'key' => 'field_vendor_instagram',
						'label' => 'Instagram',
						'name' => 'instagram',
						'type' => 'url',
						'wrapper' => array(
							'width' => '50',
						),
					),
					array(
						'key' => 'field_vendor_twitter',
						'label' => 'Twitter',
						'name' => 'twitter',
						'type' => 'url',
						'wrapper' => array(
							'width' => '50',
						),
					),
					array(
						'key' => 'field_vendor_linkedin',
						'label' => 'LinkedIn',
						'name' => 'linkedin',
						'type' => 'url',
						'wrapper' => array(
							'width' => '50',
						),
					),
					array(
						'key' => 'field_vendor_youtube',
						'label' => 'YouTube',
						'name' => 'youtube',
						'type' => 'url',
						'wrapper' => array(
							'width' => '50',
						),
					),
				),
			),
			
			// Team Members Tab
			array(
				'key' => 'field_vendor_team_tab',
				'label' => 'Team Members',
				'name' => '',
				'type' => 'tab',
				'placement' => 'top',
			),
			array(
				'key' => 'field_vendor_assigned_members',
				'label' => 'Team Members',
				'name' => 'vendor_assigned_members',
				'type' => 'user',
				'instructions' => 'Team members are automatically assigned when they upgrade the vendor to paid status. Users with edit access to this vendor will appear here.',
				'role' => array(
					'administrator',
					'founder',
					'member',
				),
				'allow_null' => 1,
				'multiple' => 1,
				'return_format' => 'id',
			),
			
			// Resources Tab
			array(
				'key' => 'field_vendor_resources_tab',
				'label' => 'Resources',
				'name' => '',
				'type' => 'tab',
				'placement' => 'top',
				'conditional_logic' => array(
					array(
						array(
							'field' => 'field_vendor_is_paid',
							'operator' => '==',
							'value' => '1',
						),
					),
				),
			),
			array(
				'key' => 'field_vendor_resources',
				'label' => 'Resources & Downloads',
				'name' => 'vendor_resources',
				'type' => 'repeater',
				'instructions' => 'Add downloadable resources for vendor profile',
				'layout' => 'block',
				'button_label' => 'Add Resource',
				'sub_fields' => array(
					array(
						'key' => 'field_resource_title',
						'label' => 'Title',
						'name' => 'title',
						'type' => 'text',
						'required' => 1,
						'wrapper' => array(
							'width' => '50',
						),
					),
					array(
						'key' => 'field_resource_description',
						'label' => 'Description',
						'name' => 'description',
						'type' => 'text',
						'wrapper' => array(
							'width' => '50',
						),
					),
					array(
						'key' => 'field_resource_file',
						'label' => 'File',
						'name' => 'file',
						'type' => 'file',
						'required' => 1,
						'return_format' => 'array',
						'library' => 'all',
						'mime_types' => 'pdf,doc,docx,xls,xlsx,ppt,pptx,jpg,jpeg,png,gif,zip',
					),
				),
			),
			
			// Settings Tab
			array(
				'key' => 'field_vendor_settings_tab',
				'label' => 'Settings',
				'name' => '',
				'type' => 'tab',
				'placement' => 'top',
				'conditional_logic' => array(
					array(
						array(
							'field' => 'field_vendor_is_paid',
							'operator' => '==',
							'value' => '1',
						),
					),
				),
			),
			array(
				'key' => 'field_vendor_lead_form_enabled',
				'label' => 'Enable Contact Form',
				'name' => 'vendor_lead_form_enabled',
				'type' => 'true_false',
				'instructions' => 'Enable contact form on vendor profile',
				'default_value' => 0,
				'ui' => 1,
			),
			array(
				'key' => 'field_vendor_subscription_status',
				'label' => 'Subscription Status',
				'name' => 'vendor_subscription_status',
				'type' => 'select',
				'instructions' => 'Current subscription status',
				'choices' => array(
					'active' => 'Active',
					'pending' => 'Pending',
					'cancelled' => 'Cancelled',
					'expired' => 'Expired',
				),
				'default_value' => 'active',
			),
			
			// Stripe Integration Tab
			array(
				'key' => 'field_vendor_stripe_tab',
				'label' => 'Stripe Integration',
				'name' => '',
				'type' => 'tab',
				'placement' => 'top',
				'conditional_logic' => array(
					array(
						array(
							'field' => 'field_vendor_is_paid',
							'operator' => '==',
							'value' => '1',
						),
					),
				),
			),
			array(
				'key' => 'field_vendor_stripe_customer_id',
				'label' => 'Stripe Customer ID',
				'name' => 'vendor_stripe_customer_id',
				'type' => 'text',
				'instructions' => 'Stripe customer ID for this vendor',
				'wrapper' => array(
					'width' => '50',
				),
			),
			array(
				'key' => 'field_vendor_stripe_subscription_id',
				'label' => 'Stripe Subscription ID',
				'name' => 'vendor_stripe_subscription_id',
				'type' => 'text',
				'instructions' => 'Stripe subscription ID for this vendor',
				'wrapper' => array(
					'width' => '50',
				),
			),
			array(
				'key' => 'field_vendor_stripe_session_id',
				'label' => 'Last Stripe Session ID',
				'name' => 'vendor_stripe_session_id',
				'type' => 'text',
				'instructions' => 'Last successful Stripe checkout session ID',
				'wrapper' => array(
					'width' => '50',
				),
			),
			array(
				'key' => 'field_vendor_subscription_start',
				'label' => 'Subscription Start Date',
				'name' => 'vendor_subscription_start',
				'type' => 'date_time_picker',
				'instructions' => 'When the subscription started',
				'display_format' => 'F j, Y g:i a',
				'return_format' => 'Y-m-d H:i:s',
				'wrapper' => array(
					'width' => '50',
				),
			),
			array(
				'key' => 'field_vendor_last_payment_amount',
				'label' => 'Last Payment Amount',
				'name' => 'vendor_last_payment_amount',
				'type' => 'number',
				'instructions' => 'Last payment amount in cents',
				'wrapper' => array(
					'width' => '33',
				),
			),
			array(
				'key' => 'field_vendor_currency',
				'label' => 'Currency',
				'name' => 'vendor_currency',
				'type' => 'text',
				'instructions' => 'Currency code (e.g., USD)',
				'wrapper' => array(
					'width' => '33',
				),
			),
			array(
				'key' => 'field_vendor_upgraded_at',
				'label' => 'Upgraded At',
				'name' => 'vendor_upgraded_at',
				'type' => 'date_time_picker',
				'instructions' => 'When the vendor was upgraded to paid status',
				'display_format' => 'F j, Y g:i a',
				'return_format' => 'Y-m-d H:i:s',
				'wrapper' => array(
					'width' => '34',
				),
			),
		),
		'location' => array(
			array(
				array(
					'param' => 'post_type',
					'operator' => '==',
					'value' => 'vendor',
				),
			),
		),
		'menu_order' => 0,
		'position' => 'normal',
		'style' => 'default',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'active' => true,
		'show_in_rest' => 1,
	));
});

// Hook to sync ACF fields with post meta for backwards compatibility
add_action('acf/save_post', function($post_id) {
	// Only run for vendor post type
	if (get_post_type($post_id) !== 'vendor') {
		return;
	}
	
	// Sync ACF fields to post meta
	$fields_to_sync = array(
		'vendor_website',
		'vendor_is_paid',
		'vendor_cover_photo',
		'vendor_social_links',
		'vendor_assigned_members',
		'vendor_resources',
		'vendor_lead_form_enabled',
		'vendor_subscription_status',
		'vendor_stripe_customer_id',
		'vendor_stripe_subscription_id',
		'vendor_stripe_session_id',
		'vendor_subscription_start',
		'vendor_last_payment_amount',
		'vendor_currency',
		'vendor_upgraded_at',
	);
	
	foreach ($fields_to_sync as $field_name) {
		$value = get_field($field_name, $post_id);
		
		// Skip cover photo - let ACF handle it natively
		if ($field_name === 'vendor_cover_photo') {
			continue;
		}
		
		// Sync other fields to post meta for backwards compatibility
		update_post_meta($post_id, $field_name, $value);
	}
}, 20);