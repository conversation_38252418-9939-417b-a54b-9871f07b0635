<?php 
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_6585a760b3aca',
	'title' => 'Blog Post Fields',
	'fields' => array(
		array(
			'key' => 'field_661558f10c747',
			'label' => 'Post Publish Date',
			'name' => 'post_publish_date',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'display_format' => 'F j, Y',
			'return_format' => 'F j, Y',
			'first_day' => 1,
			'show_in_rest' => 1,
		),
		array(
			'key' => 'field_66e88bc240454',
			'label' => 'Image Caption',
			'name' => 'image_caption',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'allow_in_bindings' => 0,
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'show_in_rest' => 1,
		),
		array(
			'key' => 'field_661498d0cb044',
			'label' => 'Website Logo',
			'name' => 'website_logo',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'return_format' => 'array',
			'library' => 'all',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
			'preview_size' => 'medium',
			'uploader' => '',
			'acfe_thumbnail' => 0,
			'show_in_rest' => 1,
		),
		array(
			'key' => 'field_661498dbcb045',
			'label' => 'Author',
			'name' => 'author',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'show_in_rest' => 1,
		),
		array(
			'key' => 'field_6585a77376fac',
			'label' => 'URL',
			'name' => 'url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'hide_admin' => 0,
			'default_value' => '',
			'allow_in_bindings' => 1,
			'placeholder' => '',
			'show_in_rest' => 1,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_category',
				'operator' => '==',
				'value' => 'category:blog-post',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 1,
) );
} );

