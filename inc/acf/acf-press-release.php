<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_6585b2a3dbdf0',
	'title' => 'Press Release Fields',
	'fields' => array(
		array(
			'key' => 'field_6585b2b3b682f',
			'label' => 'Upload PDF',
			'name' => 'upload_pdf',
			'aria-label' => '',
			'type' => 'file',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'hide_admin' => 0,
			'return_format' => 'array',
			'library' => 'all',
			'min_size' => '',
			'max_size' => 15,
			'mime_types' => 'pdf',
			'uploader' => '',
		),
		array(
			'key' => 'field_6585b2cfb6831',
			'label' => 'Press Release URL',
			'name' => 'press_release_url',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'relevanssi_exclude' => 0,
			'hide_admin' => 0,
			'default_value' => '',
			'placeholder' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_category',
				'operator' => '==',
				'value' => 'category:press-release',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
) );
} );

