<?php
/**
 * Admin Email Tester
 * 
 * Provides an admin page to test email templates directly
 */

// Add admin menu
add_action('admin_menu', function() {
    add_management_page(
        'Email Template Tester',
        'Email Tester',
        'manage_options',
        'tourismiq-email-tester',
        'tourismiq_email_tester_page'
    );
});

// Handle form submission
add_action('admin_init', function() {
    if (!isset($_POST['tourismiq_test_email_nonce']) || 
        !wp_verify_nonce($_POST['tourismiq_test_email_nonce'], 'tourismiq_test_email')) {
        return;
    }
    
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $email_type = sanitize_text_field($_POST['email_type'] ?? '');
    $test_email = sanitize_email($_POST['test_email'] ?? '<EMAIL>');
    
    if (!$email_type || !$test_email) {
        return;
    }
    
    $result = tourismiq_send_test_email($email_type, $test_email);
    
    if ($result['success']) {
        add_settings_error('tourismiq_email_test', 'email_sent', 
            'Test email sent successfully to ' . $test_email, 'success');
    } else {
        add_settings_error('tourismiq_email_test', 'email_failed', 
            'Failed to send email: ' . $result['error'], 'error');
    }
});

// Email tester page
function tourismiq_email_tester_page() {
    ?>
    <div class="wrap">
        <h1>Email Template Tester</h1>
        
        <?php settings_errors('tourismiq_email_test'); ?>
        
        <form method="post" action="">
            <?php wp_nonce_field('tourismiq_test_email', 'tourismiq_test_email_nonce'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="test_email">Send Test Email To</label>
                    </th>
                    <td>
                        <input type="email" name="test_email" id="test_email" 
                               value="<EMAIL>" class="regular-text" required />
                        <p class="description">All test emails will be sent to this address</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="email_type">Email Template</label>
                    </th>
                    <td>
                        <select name="email_type" id="email_type" required>
                            <option value="">Select an email template</option>
                            <option value="welcome">Welcome Email</option>
                            <option value="password-reset">Password Reset</option>
                            <option value="account-deleted">Account Deleted</option>
                            <option value="vendor-approval">Vendor Account Upgraded</option>
                            <option value="support-request">Support Request (Admin)</option>
                            <option value="feedback-submission">Feedback Submission (Admin)</option>
                            <option value="vendor-suggestion">Vendor Suggestion (Admin)</option>
                        </select>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <button type="submit" class="button button-primary">Send Test Email</button>
            </p>
        </form>
        
        <div style="margin-top: 40px; padding: 20px; background: #f0f0f1; border-left: 4px solid #2271b1;">
            <h3>Email Templates Location</h3>
            <p>Email templates are located in: <code><?php echo get_template_directory(); ?>/emails/</code></p>
            
            <h3>Available Templates</h3>
            <ul>
                <?php
                $email_dir = get_template_directory() . '/emails/';
                if (is_dir($email_dir)) {
                    $files = scandir($email_dir);
                    foreach ($files as $file) {
                        if ($file !== '.' && $file !== '..' && str_ends_with($file, '.html')) {
                            echo '<li><code>' . esc_html($file) . '</code></li>';
                        }
                    }
                } else {
                    echo '<li>Email directory not found!</li>';
                }
                ?>
            </ul>
        </div>
    </div>
    <?php
}

// Function to send test emails
function tourismiq_send_test_email($type, $to) {
    $template_file = '';
    $subject = '';
    $data = array(
        'site_name' => get_bloginfo('name'),
        'site_url' => 'https://mytourismiq.com',
        'admin_email' => get_option('admin_email'),
        'username' => 'testuser',
        'email' => $to,
        'year' => date('Y'),
    );
    
    switch ($type) {
        case 'welcome':
            $template_file = 'welcome.html';
            $subject = 'Welcome to ' . get_bloginfo('name') . ' (TEST)';
            // Use explicit frontend URL for testing
            $data['action_url'] = 'https://mytourismiq.com';
            $data['action_title'] = 'Visit Site';
            $data['site_url'] = 'https://mytourismiq.com';
            break;
            
        case 'password-reset':
            $template_file = 'password-reset.html';
            $subject = 'Password Reset Request (TEST)';
            $data['password_reset_link'] = home_url('/wp-login.php?action=rp&key=TESTKEY&login=testuser');
            $data['reset_link'] = $data['password_reset_link'];
            $data['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $data['browser'] = 'Test Browser';
            $data['date_time'] = current_time('F j, Y \a\t g:i a');
            break;
            
        case 'account-deleted':
            $template_file = 'account-deleted.html';
            $subject = 'Your account has been deleted (TEST)';
            $data['deletion_date'] = current_time('F j, Y');
            break;
            
        case 'vendor-approval':
            $template_file = 'vendor-approval.html';
            $subject = 'Your vendor account has been upgraded! (TEST)';
            $data['vendor_name'] = 'Test Tourism Company';
            $data['vendor_url'] = home_url('/vendors/test-vendor');
            $data['vendor_edit_url'] = home_url('/vendors/test-vendor');
            $data['vendor_status'] = 'Premium Vendor';
            break;
            
        case 'support-request':
            $template_file = 'support-request.html';
            $subject = '[Support] Test Support Request (TEST)';
            $data['name'] = 'Test User';
            $data['email'] = '<EMAIL>';
            $data['subject'] = 'Test Support Request';
            $data['priority'] = 'HIGH';
            $data['priority_style'] = 'background-color: #f59e0b; color: white;';
            $data['description'] = "This is a test support request.\n\nIt includes multiple lines\nto test the formatting.";
            $data['attachments_section'] = '<div style="padding: 20px; background: #f8f9fa; border-radius: 10px; text-align: left; font-size: 14px; margin-bottom: 20px;"><div style="font-weight: bold; margin-bottom: 10px;">Attachments (2):</div><ul style="margin: 0; padding-left: 20px;"><li style="margin-bottom: 5px;">screenshot.png</li><li style="margin-bottom: 5px;">error-log.txt</li></ul></div>';
            $data['date_time'] = current_time('F j, Y \a\t g:i a');
            $data['admin_url'] = admin_url('admin.php?page=tourismiq-support');
            break;
            
        case 'feedback-submission':
            $template_file = 'feedback-submission.html';
            $subject = '[Feedback] Feature Request - Rating: 4/5 (TEST)';
            $data['name'] = 'Test User';
            $data['email'] = '<EMAIL>';
            $data['category'] = 'Feature Request';
            $data['rating'] = '4';
            $data['rating_stars'] = '⭐⭐⭐⭐';
            $data['feedback'] = "This is test feedback.\n\nI really like the platform but would love to see:\n- More social features\n- Better mobile experience\n- Advanced search filters\n\nKeep up the great work!";
            $data['date_time'] = current_time('F j, Y \a\t g:i a');
            $data['admin_url'] = admin_url('admin.php?page=tourismiq-feedback');
            break;
            
        case 'vendor-suggestion':
            $template_file = 'vendor-suggestion.html';
            $subject = '[Vendor Suggestion] Test Tourism Company (TEST)';
            $data['first_name'] = 'John';
            $data['last_name'] = 'Doe';
            $data['email'] = '<EMAIL>';
            $data['organization'] = 'Test Tourism Company';
            $data['website'] = 'https://example.com';
            $data['message'] = 'This is a test vendor suggestion. The company provides excellent tourism services and would be a great addition to the directory.';
            $data['date_time'] = current_time('F j, Y \\a\\t g:i a');
            $data['admin_url'] = admin_url('edit.php?post_type=vendor');
            $data['year'] = date('Y');
            break;
            
        default:
            return array('success' => false, 'error' => 'Invalid email type');
    }
    
    $template_path = get_template_directory() . '/emails/' . $template_file;
    
    if (!file_exists($template_path)) {
        return array('success' => false, 'error' => 'Template file not found: ' . $template_file);
    }
    
    $template = file_get_contents($template_path);
    
    // Replace placeholders
    foreach ($data as $key => $value) {
        $template = str_replace('{' . $key . '}', $value, $template);
    }
    
    // Send email with proper headers
    $headers = array('Content-Type: text/html; charset=UTF-8');
    $sent = wp_mail($to, $subject, $template, $headers);
    
    return array(
        'success' => $sent,
        'error' => $sent ? '' : 'wp_mail() failed',
        'subject' => $subject,
        'to' => $to,
        'template' => $template_file
    );
}