<?php
/**
 * Cache Invalidation for TourismIQ
 * Handles cache invalidation when posts are updated in WordPress admin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class TourismIQ_Cache_Invalidation {
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Hook into post save/update events
        add_action('save_post', [$this, 'invalidate_post_cache'], 10, 3);
        add_action('transition_post_status', [$this, 'invalidate_post_cache_on_status_change'], 10, 3);
        add_action('deleted_post', [$this, 'invalidate_post_cache_on_delete']);
        add_action('wp_trash_post', [$this, 'invalidate_post_cache_on_trash']);
        
        // Hook into term updates (categories)
        add_action('edited_term', [$this, 'invalidate_category_cache'], 10, 3);
        add_action('delete_term', [$this, 'invalidate_category_cache'], 10, 3);
        
        // Add REST endpoint for cache invalidation
        add_action('rest_api_init', [$this, 'register_cache_endpoints']);
    }
    
    /**
     * Invalidate cache when a post is saved/updated
     */
    public function invalidate_post_cache($post_id, $post, $update) {
        // Skip auto-saves and revisions
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if ($post->post_type === 'revision') {
            return;
        }
        
        // Only invalidate for published posts or posts being published
        if (!in_array($post->post_status, ['publish', 'future', 'private'])) {
            return;
        }
        
        // Clear WordPress object cache
        $this->clear_wordpress_cache($post_id, $post);
        
        // Notify frontend to invalidate cache
        $this->notify_frontend_cache_invalidation('post', $post_id);
        
        // Log the invalidation
        error_log("TourismIQ: Cache invalidated for post ID: {$post_id}");
    }
    
    /**
     * Invalidate cache when post status changes
     */
    public function invalidate_post_cache_on_status_change($new_status, $old_status, $post) {
        // If status changed to/from published
        if ($old_status !== $new_status && 
            ($new_status === 'publish' || $old_status === 'publish')) {
            $this->invalidate_post_cache($post->ID, $post, true);
        }
    }
    
    /**
     * Invalidate cache when post is deleted
     */
    public function invalidate_post_cache_on_delete($post_id) {
        $post = get_post($post_id);
        if ($post) {
            $this->clear_wordpress_cache($post_id, $post);
            $this->notify_frontend_cache_invalidation('post', $post_id);
        }
    }
    
    /**
     * Invalidate cache when post is trashed
     */
    public function invalidate_post_cache_on_trash($post_id) {
        $this->invalidate_post_cache_on_delete($post_id);
    }
    
    /**
     * Invalidate cache when categories are updated
     */
    public function invalidate_category_cache($term_id, $tt_id, $taxonomy) {
        if ($taxonomy === 'category') {
            // Clear category-related caches
            wp_cache_delete('categories', 'tourismiq');
            
            // Notify frontend
            $this->notify_frontend_cache_invalidation('category', $term_id);
        }
    }
    
    /**
     * Clear WordPress object cache
     */
    private function clear_wordpress_cache($post_id, $post) {
        // Clear post cache
        clean_post_cache($post_id);
        
        // Clear taxonomy cache
        $taxonomies = get_object_taxonomies($post->post_type);
        foreach ($taxonomies as $taxonomy) {
            clean_object_term_cache($post_id, $taxonomy);
        }
        
        // Clear custom caches
        wp_cache_delete('posts_' . md5('recent'), 'tourismiq_posts');
        wp_cache_delete('posts_featured', 'tourismiq_posts');
        
        // Clear category-specific caches
        $categories = wp_get_post_categories($post_id);
        foreach ($categories as $cat_id) {
            $cat = get_category($cat_id);
            if ($cat) {
                wp_cache_delete('posts_category_' . $cat->slug, 'tourismiq_posts');
            }
        }
        
        // Use performance cache helper if available
        if (function_exists('tourismiq_clear_all_cache')) {
            tourismiq_clear_all_cache();
        }
    }
    
    /**
     * Notify frontend about cache invalidation
     */
    private function notify_frontend_cache_invalidation($type, $id) {
        // Store invalidation request in transient
        $invalidations = get_transient('tourismiq_cache_invalidations') ?: [];
        $invalidations[] = [
            'type' => $type,
            'id' => $id,
            'timestamp' => time()
        ];
        
        // Keep only last 100 invalidations
        if (count($invalidations) > 100) {
            $invalidations = array_slice($invalidations, -100);
        }
        
        set_transient('tourismiq_cache_invalidations', $invalidations, HOUR_IN_SECONDS);
        
        // Also trigger server-side cache revalidation
        $this->trigger_server_revalidation($type, $id);
    }
    
    /**
     * Get the frontend URL with fallbacks
     */
    private function get_frontend_url() {
        // Try various configuration methods
        if (defined('FRONTEND_URL')) {
            return FRONTEND_URL;
        }
        
        if (defined('NEXT_PUBLIC_FRONTEND_URL')) {
            return NEXT_PUBLIC_FRONTEND_URL;
        }
        
        // Try to get from WordPress options
        $frontend_url = get_option('tourismiq_frontend_url');
        if ($frontend_url) {
            return $frontend_url;
        }
        
        // Try to determine from current site
        $site_url = get_site_url();
        if (strpos($site_url, 'wpenginepowered.com') !== false) {
            // Production WP Engine - assume Railway deployment
            return 'https://tourismqwp-production.up.railway.app';
        }
        
        if (strpos($site_url, 'tourismiq.local') !== false) {
            // Local development
            return 'http://localhost:3000';
        }
        
        // Default fallback
        return 'http://localhost:3000';
    }

    /**
     * Trigger server-side cache revalidation in Next.js
     */
    private function trigger_server_revalidation($type, $id) {
        // Get the frontend URL
        $frontend_url = $this->get_frontend_url();
        
        // Remove trailing slash
        $frontend_url = rtrim($frontend_url, '/');
        
        // Prepare revalidation data
        $revalidation_data = [
            'type' => $type,
            'id' => $id
        ];
        
        // Add slug for posts
        if ($type === 'post' && $id) {
            $post = get_post($id);
            if ($post) {
                $revalidation_data['slug'] = $post->post_name;
            }
        }
        
        // Add slug for categories
        if ($type === 'category' && $id) {
            $term = get_term($id);
            if ($term) {
                $revalidation_data['slug'] = $term->slug;
            }
        }
        
        // Make async HTTP request to Next.js revalidation endpoint
        wp_remote_post($frontend_url . '/api/revalidate', [
            'body' => json_encode($revalidation_data),
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'timeout' => 5,
            'blocking' => false, // Don't wait for response
        ]);
        
        error_log("TourismIQ: Triggered server-side revalidation for {$type} ID: {$id}");
    }
    
    /**
     * Register REST endpoints for cache management
     */
    public function register_cache_endpoints() {
        register_rest_route('tourismiq/v1', '/cache/invalidations', [
            'methods' => 'GET',
            'callback' => [$this, 'get_cache_invalidations'],
            'permission_callback' => '__return_true', // Public endpoint
        ]);
        
        register_rest_route('tourismiq/v1', '/cache/clear', [
            'methods' => 'POST',
            'callback' => [$this, 'clear_all_caches'],
            'permission_callback' => function() {
                return current_user_can('manage_options');
            }
        ]);
    }
    
    /**
     * Get recent cache invalidations
     */
    public function get_cache_invalidations($request) {
        $since = $request->get_param('since') ?: 0;
        $invalidations = get_transient('tourismiq_cache_invalidations') ?: [];
        
        // Filter invalidations since timestamp
        if ($since > 0) {
            $invalidations = array_filter($invalidations, function($inv) use ($since) {
                return $inv['timestamp'] > $since;
            });
        }
        
        return new WP_REST_Response([
            'invalidations' => array_values($invalidations),
            'timestamp' => time()
        ]);
    }
    
    /**
     * Clear all caches manually
     */
    public function clear_all_caches($request) {
        // Clear WordPress caches
        wp_cache_flush();
        
        // Clear custom caches
        if (function_exists('tourismiq_clear_all_cache')) {
            tourismiq_clear_all_cache();
        }
        
        // Add global invalidation
        $this->notify_frontend_cache_invalidation('all', 0);
        
        return new WP_REST_Response([
            'success' => true,
            'message' => 'All caches cleared successfully'
        ]);
    }
}

// Initialize cache invalidation
TourismIQ_Cache_Invalidation::get_instance();