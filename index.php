<?php
/**
 * Main template file
 *
 * This is a minimal template file for our headless WordPress theme.
 * Since we're using WordPress as a headless CMS, this file will rarely be used.
 *
 * @package TourismIQ_Headless
 */

// Redirect to the frontend application
// This is a fallback in case the template_redirect in functions.php doesn't work
// Use environment variable or constant, fallback to production URL
if (defined('FRONTEND_URL')) {
    $frontend_url = FRONTEND_URL;
} elseif (getenv('FRONTEND_URL')) {
    $frontend_url = getenv('FRONTEND_URL');
} else {
    // Default to your production frontend URL
    $frontend_url = 'https://mytourismiq.com';
}

// Only redirect homepage requests to the frontend
// This allows wp-admin, wp-login.php, and other WordPress URLs to work normally
if (!is_admin() && !defined('REST_REQUEST') && $_SERVER['REQUEST_URI'] === '/') {
    wp_redirect($frontend_url);
    exit;
}

// If we're still here, show a simple message
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php bloginfo('name'); ?> - Headless WordPress</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            background-color: #f1f1f1;
            color: #333;
            max-width: 700px;
            margin: 100px auto;
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2271b1;
        }
        a {
            color: #2271b1;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1><?php bloginfo('name'); ?></h1>
    <p>This is a headless WordPress site. The frontend is powered by Next.js.</p>
    <p><a href="<?php echo esc_url(admin_url()); ?>">Login to WordPress Admin</a></p>
    <p><a href="<?php echo esc_url(get_rest_url()); ?>">Browse the REST API</a></p>
</body>
</html>

/* We're redirecting all frontend requests to the Next.js app */
// Role activation code removed to prevent header conflicts
// Roles should be managed through WordPress admin or activation hooks
