# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a headless WordPress theme with a Next.js 15 frontend. The architecture consists of:
- **Backend**: WordPress with custom REST API endpoints and database extensions
- **Frontend**: Next.js 15 (App Router) with TypeScript, deployed separately
- **Real-time**: Socket.io server integrated with Next.js for messaging and notifications
- **Authentication**: WordPress cookie-based auth with secure session handling

## Common Development Commands

### Frontend Development (from `frontend/` directory)
```bash
# Install dependencies
pnpm install
pnpm setup:socket    # Install socket server dependencies

# Development
pnpm dev             # Next.js only (port 3000)
pnpm dev:socket      # Socket server only
pnpm dev:all         # Both Next.js and Socket server (recommended)

# Production build
pnpm build
pnpm start:all       # Start both servers

# Code quality
pnpm lint            # Run ESLint
pnpm type-check      # Run TypeScript checks
pnpm format          # Auto-fix code formatting with Prettier
pnpm format:check    # Check code formatting without fixing
```

### WordPress Development
- WordPress runs on Local by Flywheel at `http://tourismiq.local`
- No build process required for PHP files
- Changes to PHP files take effect immediately

## High-Level Architecture

### API Structure
The frontend communicates with WordPress through proxy routes in `/api/wp-proxy/` that:
1. Handle authentication via WordPress cookies
2. Add caching layer with configurable TTL
3. Provide security through rate limiting
4. Transform data for frontend consumption

### Key WordPress REST API Endpoints
All custom endpoints are under `/wp-json/tourismiq/v1/`:
- **Auth**: `/auth/login`, `/auth/logout`, `/auth/status`
- **Members**: `/members`, `/members/{username}`, `/connections/*`
- **IQ Score**: `/iq-score/{userId}`, `/leaderboard`, `/iq-score/award`
- **Messaging**: `/messages/send`, `/messages/conversations`, `/messages/mark-read`
- **Forum**: `/forum/*` (questions, comments)
- **Vendors**: `/vendors/*` (multi-author system)

### Real-time Features
Socket.io server handles:
- Direct messaging between connected users
- Real-time notifications (IQ points, connections, mentions)
- Typing indicators and online status
- User-specific notification storage (`tourismiq_notifications_{userId}`)

### Database Extensions
Custom WordPress tables:
- `wp_user_connections`: Member connection relationships
- `wp_messages`: Direct messaging system
- `wp_user_notifications`: Notification storage
- User meta for IQ scores and extended profiles

### Caching Strategy
Server-side in-memory cache with TTL:
- Posts: 5 minutes
- Users: 10 minutes
- Categories: 30 minutes
- Forum: 3 minutes
- IQ Scores: 5 minutes

### Security Implementation
- CORS configuration for API access
- Rate limiting on all API endpoints
- XSS/CSRF protection headers
- Input validation and sanitization
- WordPress Application Passwords for API auth
- User-specific localStorage keys for notifications

## Environment Variables

### Frontend (.env.local)
```bash
NEXT_PUBLIC_WORDPRESS_API_URL=http://tourismiq.local
WORDPRESS_USERNAME=your_username
WORDPRESS_APPLICATION_PASSWORD=your_app_password
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
```

### Production (Railway)
```bash
PORT=8080
NEXT_PUBLIC_SOCKET_URL=https://tourismqwp-production.up.railway.app
SOCKET_SERVER_URL=https://tourismqwp-production.up.railway.app
```

## Key Features to Understand

1. **IQ Score System**: Gamification with points for actions (posts, comments, upvotes)
2. **Connection System**: Friend requests with accept/decline/remove functionality
3. **Messaging**: One-to-one chat with connection-based permissions
4. **Forum**: Q&A system with comments and voting
5. **Vendor System**: Multi-author profiles with assigned members
6. **Post Interactions**: Upvotes, bookmarks, comments with real-time updates

## Development Workflow

1. Always run `pnpm dev:all` for full functionality (frontend + socket server)
2. Check WordPress error logs at `wp-content/debug.log` for backend issues
3. Use browser DevTools Network tab to debug API calls
4. Socket connections visible in Network > WS tab
5. Test notifications with `/test-iq-notifications` page in development

## Performance Considerations

- Messaging queries optimized with database indexes (70-85% improvement)
- Image optimization through Next.js Image component
- Bundle splitting and tree shaking enabled
- Tailwind CSS purging for minimal CSS

## Critical Security Notes

- Never expose WordPress credentials to client-side code
- Always use WordPress Application Passwords, not regular passwords
- User-specific notification storage prevents cross-user data leakage
- All sensitive operations happen server-side through API routes

## CORS Configuration

CORS headers are managed in `inc/cors-handler.php`. If you encounter CORS issues:

1. Check that the frontend domain is in the `$allowed_origins` array
2. For WP Engine deployments, you may need to add custom headers via their portal:
   - Go to WP Engine User Portal > Site > Web Rules
   - Add custom headers for your REST API endpoints
3. Test CORS with: `curl -I https://mytourismiq.wpenginepowered.com/wp-json/tourismiq/v1/debug/cors`
4. Common issues:
   - Preflight OPTIONS requests not handled properly
   - Missing credentials flag for cookie-based auth
   - Frontend and backend domains mismatch