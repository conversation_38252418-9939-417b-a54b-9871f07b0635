<?php
/**
 * WP Engine CORS Configuration
 * 
 * Add this code to your wp-config.php file on WP Engine
 * Place it BEFORE the line that says "That's all, stop editing!"
 */

// Force CORS headers for REST API requests on WP Engine
if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], 'wp-json') !== false) {
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    
    // List of allowed origins
    $allowed_origins = [
        'https://hpmaxdrwtcitbzj2bh2u79bv5.js.wpenginepowered.com',
        'https://tourismqwp-production.up.railway.app',
        'http://localhost:3000',
        'https://localhost:3000'
    ];
    
    if (in_array($origin, $allowed_origins)) {
        header("Access-Control-Allow-Origin: {$origin}");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization, Cookie, X-WP-Nonce');
        header('Access-Control-Expose-Headers: X-WP-Total, X-WP-TotalPages, Link, Set-Cookie');
        header('Access-Control-Max-Age: 3600');
    }
    
    // Handle preflight
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        header('HTTP/1.1 200 OK');
        exit();
    }
}