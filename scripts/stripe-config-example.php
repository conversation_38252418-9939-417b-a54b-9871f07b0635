<?php
/**
 * Stripe Configuration for TourismIQ
 * 
 * Add these constants to your wp-config.php file or create a separate config file
 * that is loaded before WordPress initializes.
 * 
 * IMPORTANT: Never commit your actual API keys to version control!
 */

// Stripe API Keys
// Get these from your Stripe Dashboard at https://dashboard.stripe.com/apikeys
define('STRIPE_SECRET_KEY', 'sk_test_your_actual_secret_key_here');
define('STRIPE_PUBLISHABLE_KEY', 'pk_test_your_actual_publishable_key_here');

// Stripe Webhook Secret
// Get this from your webhook endpoint settings in Stripe Dashboard
define('STRIPE_WEBHOOK_SECRET', 'whsec_your_actual_webhook_secret_here');

// Stripe Payment Link
// Create this in your Stripe Dashboard under Payment Links
define('STRIPE_PAYMENT_LINK_URL', 'https://buy.stripe.com/your_actual_payment_link_here');

/**
 * Instructions for setup:
 * 
 * 1. Create a Stripe account at https://stripe.com
 * 
 * 2. Get your API keys:
 *    - Go to https://dashboard.stripe.com/apikeys
 *    - Copy your Secret key (starts with sk_test_ or sk_live_)
 *    - Copy your Publishable key (starts with pk_test_ or pk_live_)
 * 
 * 3. Create a Payment Link:
 *    - Go to https://dashboard.stripe.com/payment-links
 *    - Click "Create payment link"
 *    - Set up a $99/month recurring subscription
 *    - Under "After payment", set success URL to your domain + /dashboard/vendor-settings
 *    - Under "Collect customer information", enable "Email addresses"
 *    - In "Additional options", add metadata field: vendor_id (this will be populated dynamically)
 * 
 * 4. Set up webhook:
 *    - Go to https://dashboard.stripe.com/webhooks
 *    - Click "Add endpoint"
 *    - URL: https://yourdomain.com/wp-json/tourismiq/v1/stripe/webhook
 *    - Select events: customer.subscription.created, customer.subscription.updated, 
 *      customer.subscription.deleted, invoice.payment_succeeded, invoice.payment_failed
 *    - Copy the webhook secret (starts with whsec_)
 * 
 * 5. Install Stripe PHP library (if not already installed):
 *    - Via Composer: composer require stripe/stripe-php
 *    - Or download and include manually
 * 
 * 6. Replace the placeholder values above with your actual keys
 * 
 * 7. Add these constants to your wp-config.php file
 */