# TourismIQ Real-Time Notification System

This document explains the real-time notification system implementation for TourismIQ using Socket.io.

## Overview

The notification system consists of:

1. A Socket.io server that runs alongside Next.js
2. A React context for managing notifications in the frontend
3. WordPress REST API endpoints for notification management
4. LocalStorage persistence for offline notification storage

## Architecture

```
┌─────────────┐     ┌────────────┐     ┌───────────────────┐
│ WordPress   │     │ Socket.io  │     │ Next.js Frontend  │
│ Backend     │────▶│ Server     │────▶│ NotificationContext│
└─────────────┘     └────────────┘     └───────────────────┘
      │                                         │
      │                                         │
      └─────────────────────────────────────────┘
              REST API fallback path
```

## Components

### WordPress Backend (inc/notifications.php)

- Provides REST API endpoints for sending notifications
- Hooks into WordPress events (e.g., upvotes) to trigger notifications
- Passes authentication tokens to the frontend

### Socket.io Server (src/socket-server/server.js)

- Runs alongside Next.js using a custom server
- Manages real-time connections
- Handles authentication with WordPress
- Routes notifications to connected users

### Next.js Components

- **NotificationContext.tsx**: React context for notification state management
- **NotificationDropdown.tsx**: UI component for displaying notifications
- **UpvoteButton.tsx**: Example component that triggers notifications

## Setup Instructions

1. Install dependencies:
   ```
   pnpm add socket.io socket.io-client date-fns
   ```

2. Start the development server:
   ```
   pnpm dev
   ```

This will run the custom server.js file which includes the Socket.io server.

## How Notifications Work

1. **Sending**:
   - User actions (e.g., upvoting) can trigger notifications
   - Notifications are sent via Socket.io to online users
   - For offline users, they'll see notifications when they reconnect

2. **Receiving**:
   - The NotificationContext listens for new notifications
   - Notifications are stored in localStorage for persistence
   - The UI updates in real-time when new notifications arrive

3. **Displaying**:
   - The NotificationDropdown shows a badge with unread count
   - Clicking opens a dropdown with notification details
   - Notifications are marked as read when viewed

## Current Notification Types

- **Upvote**: When someone upvotes a user's post
- **Comment**: When someone comments on a user's post (not yet implemented)
- **Message**: Direct messages between users (not yet implemented)
- **System**: System-generated notifications

## Future Enhancements

- Database persistence for notifications
- Push notifications for browser/mobile devices
- More notification types (follows, mentions, etc.)
- Notification preferences and filtering

## Troubleshooting

- If notifications aren't working, check browser console for connection errors
- Verify WordPress user is authenticated
- Check that the Socket.io server is running on the correct port
- Ensure CORS settings are correctly configured
