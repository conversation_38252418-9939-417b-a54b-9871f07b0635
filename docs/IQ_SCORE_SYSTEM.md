# TourismIQ Score System

## Overview
The TourismIQ Score System is a comprehensive gamification feature that rewards user engagement and community participation through points, ranks, and badges.

## Point System

### Point Values
- **Thought Leadership post**: 8 points
- **Resource category post**: 5 points (Blog Posts, Books, Courses, Podcasts, Presentations, Press Releases, Templates, Videos, Webinars, Whitepapers, Case Studies)
- **News post**: 2 points
- **Event post**: 1 point
- **Create account**: 10 points
- **Add social media link**: 2 points each
- **Upload profile image**: 5 points
- **Add connection**: 1 point (both users receive points)
- **Comment on post**: 3 points
- **Upvote post**: 3 points
- **Subscribe to newsletter**: 6 points
- **Daily visit**: 1 point

## Rank System

### Rank Progression
1. **Novice**: 0-100 points
2. **Contributor**: 101-300 points
3. **Engager**: 301-500 points
4. **Influencer**: 501-700 points
5. **Expert**: 701-900 points
6. **Master**: 901+ points

### Badge System
Each rank has two badge variants:
- **Member Badge**: For regular users
- **Founder Badge**: For users with "founder" role (distinguished with gold accents)

Badge files located in: `/frontend/public/images/icons/`
- `novice.svg` / `novice-fc.svg`
- `contributor.svg` / `contributor-fc.svg`
- `engager.svg` / `engager-fc.svg`
- `influencer.svg` / `influencer-fc.svg`
- `expert.svg` / `expert-fc.svg`
- `master.svg` / `master-fc.svg`

## Implementation

### WordPress Backend

#### Core Files
- **`inc/iq-score-system.php`**: Main system class with point tracking logic
- **`inc/iq-score-api.php`**: REST API endpoints for frontend integration

#### Database Storage
- **User Meta Key**: `iq_score` (integer)
- **Tracking Fields**: Various meta keys for one-time awards (e.g., `iq_avatar_awarded`)

#### Automatic Point Tracking
The system automatically hooks into WordPress actions:
- `user_register` - Account creation
- `transition_post_status` - Post publishing
- `wp_insert_comment` - Comments
- `wp_login` - Daily visits
- Custom actions for connections and upvotes

### REST API Endpoints

#### Leaderboard
```
GET /wp-json/tourismiq/v1/leaderboard?limit=20
```
Returns top users with scores, ranks, and positions.

#### User IQ Score
```
GET /wp-json/tourismiq/v1/iq-score/{user_id}
GET /wp-json/tourismiq/v1/iq-score/me
```
Returns specific user's score, rank, badge, and leaderboard position.

#### Ranks Information
```
GET /wp-json/tourismiq/v1/iq-score/ranks
```
Returns all available ranks and their requirements.

#### Manual Point Award (Admin Only)
```
POST /wp-json/tourismiq/v1/iq-score/award
{
  "user_id": 123,
  "points": 10,
  "activity_type": "manual"
}
```

### Frontend Components

#### API Client
- **`frontend/src/lib/api/iq-score.ts`**: TypeScript API client with helper functions

#### UI Components
- **`frontend/src/components/leaderboard/Leaderboard.tsx`**: Full leaderboard sheet component
- **`frontend/src/components/ui/IQScoreDisplay.tsx`**: Reusable score display component

#### Display Variants
1. **Full**: Complete score display with progress bars and next rank info
2. **Compact**: Score, rank, and position
3. **Minimal**: Just score and rank badge

#### Header Integration
- Leaderboard icon (⚡) in header navigation
- Clickable to open leaderboard sheet

## Usage Examples

### Display User IQ Score
```tsx
import { IQScoreDisplay } from "@/components/ui/IQScoreDisplay";

// Current user (full display)
<IQScoreDisplay variant="full" />

// Specific user (compact)
<IQScoreDisplay userId={123} variant="compact" />

// Minimal display
<IQScoreDisplay userId={123} variant="minimal" showBadge={false} />
```

### Open Leaderboard
```tsx
import { Leaderboard } from "@/components/leaderboard";

const [leaderboardOpen, setLeaderboardOpen] = useState(false);

<Leaderboard 
  isOpen={leaderboardOpen} 
  onClose={() => setLeaderboardOpen(false)} 
/>
```

### Manual Point Award (Admin)
```tsx
import { iqScoreAPI } from "@/lib/api/iq-score";

await iqScoreAPI.awardPoints(userId, 50, "admin_bonus");
```

## Color Scheme

### Rank Colors
- **Novice**: `#94a3b8` (slate-400)
- **Contributor**: `#06b6d4` (cyan-500)
- **Engager**: `#10b981` (emerald-500)
- **Influencer**: `#f59e0b` (amber-500)
- **Expert**: `#f97316` (orange-500)
- **Master**: `#dc2626` (red-600)

## Future Enhancements

### Planned Features
1. **Achievement System**: Special badges for milestones
2. **Point History**: Track point sources and timeline
3. **Leaderboard Filters**: By category, timeframe, etc.
4. **Point Decay**: Optional system to encourage continued engagement
5. **Custom Point Values**: Admin configurable point system
6. **Streak Bonuses**: Extra points for consecutive activities

### Analytics Integration
- Track engagement rates by point value
- Monitor rank progression patterns
- Identify most effective gamification elements

## Security Considerations

### Point Integrity
- All point awards validated server-side
- One-time awards tracked to prevent duplicates
- Admin-only manual point adjustment

### Rate Limiting
- Daily visit points limited to once per day
- Social media link points awarded only once per platform
- Profile image points awarded only once

## Troubleshooting

### Common Issues

#### Points Not Awarded
1. Check WordPress action hooks are firing
2. Verify user permissions and status
3. Check error logs for system issues

#### Leaderboard Not Loading
1. Verify REST API endpoints are accessible
2. Check authentication for `/me` endpoint
3. Ensure database has users with scores

#### Badges Not Displaying
1. Verify SVG files exist in `/frontend/public/images/icons/`
2. Check user role detection for founder badges
3. Ensure proper rank calculation

### Debug Tools

#### Enable Debug Logging
The system logs point awards to WordPress error log when `WP_DEBUG` is enabled.

#### Test Endpoints
Use browser dev tools or Postman to test API endpoints directly.

#### Database Queries
Check `wp_usermeta` table for `iq_score` entries to verify data storage.

## Performance Notes

### Database Optimization
- Indexes added for leaderboard queries
- Efficient user meta queries
- Cached rank calculations where possible

### Frontend Optimization
- Lazy loading of leaderboard data
- Optimized re-renders with proper state management
- SVG badges for fast loading

## Conclusion

The TourismIQ Score System provides a robust foundation for community engagement through gamification. The modular design allows for easy customization and extension while maintaining performance and security standards. 