# IQ Points Notification System

## Overview

The TourismIQ platform now features a comprehensive real-time notification system for all gamification activities. Users receive instant socket.io notifications whenever they earn IQ points, with special celebrations for rank promotions.

## Features

### Point-Awarding Activities
- **Account Creation**: 10 points
- **Post Publishing**: 1-8 points (varies by category)
  - Thought Leadership: 8 points
  - Resource Posts: 5 points
  - News Posts: 2 points
  - Event Posts: 1 point
- **Comments**: 3 points
- **Upvotes**: 3 points
- **Profile Updates**: 2-5 points
  - Profile Image: 5 points
  - Social Media Links: 2 points each
- **Connections**: 1 point (both users)
- **Daily Visits**: 1 point
- **Newsletter Subscription**: 6 points

### Rank System
- **Novice**: 0-100 points
- **Contributor**: 101-300 points
- **Engager**: 301-500 points
- **Influencer**: 501-700 points
- **Expert**: 701-900 points
- **Master**: 901+ points

## Technical Implementation

### Backend (WordPress)

#### IQ Score System (`inc/iq-score-system.php`)
- `award_points()` method tracks rank changes and sends notifications
- `send_points_notification()` creates notification data and triggers socket events
- `get_notification_content()` provides human-readable messages for each activity
- Integrates with existing `tourismiq_store_and_send_notification()` function

#### API Endpoints (`inc/iq-score-api.php`)
- `POST /wp-json/tourismiq/v1/iq-score/award` - Manual point awarding (admin only)
- `GET /wp-json/tourismiq/v1/iq-score/me` - Current user's score
- `GET /wp-json/tourismiq/v1/leaderboard` - Top users

### Frontend (Next.js)

#### Notification Types
```typescript
interface Notification {
  id: string;
  type: "iq_points_earned";
  content: string;
  timestamp: number;
  read: boolean;
  // IQ-specific fields
  activityType?: string;
  pointsEarned?: number;
  newTotal?: number;
  rankedUp?: boolean;
  newRank?: string;
}
```

#### Components
- **NotificationContext**: Handles socket connections and notification state
- **NotificationMenu**: Renders IQ points notifications with special styling
- **Test Page**: `/test-iq-notifications` for testing the system

#### Visual Design
- **Regular Points**: Zap icon with amber color scheme
- **Rank Promotions**: Crown icon with gradient backgrounds
- **Special Messages**: Celebration text for rank ups

## Testing

### Test Page
Visit `/test-iq-notifications` to:
1. Trigger different point-awarding activities
2. See real-time notifications appear
3. Test rank promotion celebrations
4. View notification history

### Manual Testing
1. Log in to the platform
2. Perform activities (post, comment, upvote, etc.)
3. Watch for notification bell badge
4. Click bell to see detailed notifications

## Socket.io Integration

### Notification Flow
1. User performs point-worthy activity
2. WordPress `award_points()` method called
3. Points awarded and rank calculated
4. Notification data created with activity details
5. Socket.io event triggered via `tourismiq_store_and_send_notification()`
6. Frontend receives notification and updates UI
7. User sees real-time feedback

### Data Structure
```php
$notification_data = array(
    'recipient_id' => $user_id,
    'sender_id' => 0, // System notification
    'type' => 'iq_points_earned',
    'content' => $human_readable_message,
    'activity_type' => $activity_type,
    'points_earned' => $points,
    'new_total' => $new_score,
    'ranked_up' => $ranked_up,
    'new_rank' => $new_rank['name'],
    'timestamp' => time(),
);
```

## Configuration

### Environment Variables
- `WORDPRESS_API_URL`: WordPress backend URL
- `NEXT_PUBLIC_SOCKET_URL`: Socket.io server URL

### WordPress Hooks
The system automatically hooks into existing WordPress actions:
- `user_register` - Account creation
- `transition_post_status` - Post publishing
- `wp_insert_comment` - Comments
- `personal_options_update` - Profile updates
- `wp_login` - Daily visits
- `tourismiq_connection_accepted` - Connections
- `tourismiq_post_upvoted` - Upvotes

## Future Enhancements

### Potential Additions
- **Achievement Badges**: Special notifications for milestones
- **Streak Tracking**: Consecutive day bonuses
- **Leaderboard Notifications**: Position changes
- **Weekly/Monthly Summaries**: Progress reports
- **Custom Point Values**: Admin-configurable point system
- **Notification Preferences**: User-controlled notification types

### Performance Considerations
- **Notification Deduplication**: Prevents spam notifications
- **Rate Limiting**: Prevents abuse of point system
- **Batch Processing**: Efficient handling of multiple activities
- **Caching**: Rank calculations and leaderboard data

## Troubleshooting

### Common Issues
1. **No Notifications**: Check socket.io connection status
2. **Wrong Point Values**: Verify activity type mapping
3. **Missing Rank Promotions**: Check rank calculation logic
4. **API Errors**: Verify WordPress authentication

### Debug Tools
- Browser console for socket events
- WordPress error logs for backend issues
- Test page for manual verification
- Network tab for API request inspection

## Security

### Authentication
- Admin-only manual point awarding
- User authentication for socket connections
- CSRF protection on API endpoints
- Input validation and sanitization

### Data Integrity
- Point values are server-controlled
- Rank calculations are immutable
- Notification history is preserved
- Audit trail for point awards 