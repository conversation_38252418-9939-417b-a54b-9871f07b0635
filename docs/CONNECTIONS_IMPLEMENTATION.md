# Member Connections Implementation

## Overview
A complete member connection system (friend request functionality) has been implemented for the TourismIQ platform with real-time notifications via Socket.io.

## Features Implemented

### Backend (WordPress)
- **REST API Endpoints** (`inc/connections-api.php`):
  - `POST /tourismiq/v1/connections/request` - Send connection request
  - `POST /tourismiq/v1/connections/accept` - Accept connection request
  - `POST /tourismiq/v1/connections/decline` - Decline connection request
  - `POST /tourismiq/v1/connections/remove` - Remove existing connection
  - `GET /tourismiq/v1/connections/status/{user_id}` - Get connection status
  - `GET /tourismiq/v1/connections` - Get user's connections
  - `GET /tourismiq/v1/connections/pending` - Get pending requests

### Frontend (Next.js)
- **API Routes** (Next.js proxy to WordPress):
  - `/api/connections/request`
  - `/api/connections/accept`
  - `/api/connections/decline`
  - `/api/connections/remove`
  - `/api/connections/status/[otherUserId]`
  - `/api/connections/pending`

- **React Hooks**:
  - `useConnections()` - Main connection management hook
  - `useConnectionStatus(userId)` - Track specific connection status

- **UI Components**:
  - Updated `MemberCard` component with connection buttons
  - Dynamic button states (Connect, Request Sent, Accept/Decline, Connected)
  - Loading states and error handling

### Real-time Notifications
- **Socket.io Integration**:
  - `SocketProvider` context for real-time communication
  - Browser notifications for connection events
  - Automatic status updates when actions occur

## Database Schema
Required table: `wp_user_connections`
```sql
CREATE TABLE wp_user_connections (
  id int(11) NOT NULL AUTO_INCREMENT,
  requester_id bigint(20) UNSIGNED NOT NULL,
  requested_id bigint(20) UNSIGNED NOT NULL,
  status varchar(20) NOT NULL DEFAULT 'pending',
  created_at datetime NOT NULL,
  updated_at datetime NOT NULL,
  PRIMARY KEY (id),
  KEY requester_id (requester_id),
  KEY requested_id (requested_id),
  KEY status (status),
  UNIQUE KEY unique_connection (requester_id, requested_id)
);
```

## Connection Flow
1. **Send Request**: User clicks "Connect" → sends request → target user gets notification
2. **Receive Request**: User sees "Accept/Decline" buttons for incoming requests
3. **Accept**: Connection becomes "accepted" → requester gets notification
4. **Connected**: Both users see "Connected" status, can remove connection

## Auth Context Consolidation
- Removed duplicate `AuthContext.tsx` file
- Consolidated to use `auth-context.tsx` as the single source of truth
- Updated all imports to use consistent auth context

## Socket.io Setup
- Socket server runs on `localhost:3000` (development) or port 8080 (Railway production)
- Automatic authentication with user ID
- Real-time notification delivery
- Browser notification permissions for connection events

## Next Steps
1. Ensure the socket server is running: `npm run dev:socket`
2. Test the connection functionality in the member directory
3. Verify real-time notifications are working
4. Consider adding a connections page to view all user connections
