# Ultimate Member Connections Migration Guide

This guide will help you migrate all user connections from your old Ultimate Member system to the new TourismIQ headless connection system.

## Overview

The migration transfers approximately **500 connection records** from the Ultimate Member `wp_um_friends` table to the new `wp_user_connections` table, converting:
- UM status `1` (accepted) → `'accepted'`
- UM status `0` (pending) → `'pending'`
- Bidirectional UM relationships → Unidirectional requester-based system

## Prerequisites

1. **Data File**: Ensure `um_friends_data.txt` exists in your theme root with the Ultimate Member export data
2. **Admin Access**: You must be logged in as a WordPress administrator
3. **Backup**: Create a database backup before running the migration

## Migration Steps

### Step 1: Access WordPress Admin

1. Log into your WordPress admin at `http://tourismiq.local/wp-admin`
2. Navigate to **Users** → **All Users**
3. You should see a blue notice about Ultimate Member connections migration

### Step 2: Create Temporary Table

Click **"Step 1: Create Temporary Table"** in the admin notice.

This creates a temporary table `wp_um_friends_temp` to hold the Ultimate Member data during migration.

✅ **Success**: You'll see "Temporary table created successfully!"

### Step 3: Load Ultimate Member Data

Click **"Step 2: Load UM Data"** to populate the temporary table.

This reads from `um_friends_data.txt` and loads all ~500 connection records.

✅ **Success**: You'll see "Ultimate Member data loaded successfully! (XXX records)"

### Step 4: Test Migration (Dry Run)

Click **"Step 3: Test Migration (Dry Run)"** to preview the migration.

This simulates the migration without making any changes, showing you:
- How many connections will be migrated
- Any users that don't exist in the current system
- Potential conflicts or duplicates

✅ **Success**: You'll see "Migration dry run completed! (XXX connections would be migrated)"

### Step 5: Execute Migration

⚠️ **IMPORTANT**: This step modifies your database permanently.

Click **"Step 4: Execute Migration"** and confirm when prompted.

This will:
- Create connection records in the `wp_user_connections` table
- Preserve original timestamps from Ultimate Member
- Set proper requester/requested relationships
- Convert status values to the new format

✅ **Success**: You'll see "Migration executed successfully! (XXX connections migrated)"

## Verification

After successful migration, verify the results:

1. **Check Connection Counts**:
   ```sql
   SELECT COUNT(*) FROM wp_user_connections;
   ```

2. **Sample Connection Data**:
   ```sql
   SELECT * FROM wp_user_connections LIMIT 10;
   ```

3. **Test Frontend**: Verify connections work in your Next.js frontend

## Data Mapping

| Ultimate Member | New System |
|----------------|------------|
| `user_id1` | `requester_id` |
| `user_id2` | `requested_id` |
| `status = 1` | `status = 'accepted'` |
| `status = 0` | `status = 'pending'` |
| `time` | `created_at` and `updated_at` |

## Troubleshooting

### Migration Notice Not Showing

1. Ensure `um_friends_data.txt` exists in theme root
2. Verify you're logged in as an administrator
3. Check WordPress error logs for any PHP errors

### Data File Issues

The data file should contain lines like:
```
(4, '2023-12-26 16:16:08', 6, 1, 1)
(5, '2023-12-26 21:51:29', 6, 3, 1)
```

If the format is incorrect, re-extract from the Ultimate Member export.

### User ID Mismatches

Some connections may be skipped if the user IDs from Ultimate Member don't exist in your current WordPress installation. This is normal if you've added/removed users since the export.

### Duplicate Connections

The migration automatically skips connections that already exist, preventing duplicates.

## Cleanup (Optional)

After successful migration, you can clean up the temporary table:

1. Visit: `http://tourismiq.local/wp-admin/admin.php?um_cleanup_temp=1`
2. This removes the `wp_um_friends_temp` table

## Files Involved

- **Migration Script**: `/inc/um-connections-migration.php`
- **Data File**: `um_friends_data.txt` (theme root)
- **Target Table**: `wp_user_connections`
- **Temporary Table**: `wp_um_friends_temp`

## Support

If you encounter issues:

1. Check WordPress debug logs
2. Verify all prerequisites are met
3. Ensure the connections API is properly initialized
4. Test with a smaller subset of data first

The migration preserves all original data and timestamps, ensuring a seamless transition to the new connection system.