# Stripe Subscription Integration for TourismIQ Vendor Profiles

This integration provides automated subscription management for vendor paid profiles using Stripe Payment Links and Webhooks.

## Overview

The system automatically manages vendor subscription status using:
- **Stripe Payment Links**: Simple $99/month subscription checkout
- **Stripe Webhooks**: Automatic status updates when subscriptions change
- **WordPress ACF Fields**: `vendor_is_paid` field automatically updated
- **React Component**: Easy-to-use subscription management UI

## Features

- ✅ **Ultra Simple Setup**: No custom checkout code needed
- ✅ **Fully Automated**: Webhooks handle all subscription state changes
- ✅ **Secure**: Stripe handles all payment data and PCI compliance
- ✅ **Customer Portal**: Users can manage their own subscriptions
- ✅ **Real-time Updates**: Subscription changes immediately update vendor status

## File Structure

```
inc/
├── stripe-integration.php          # Main webhook and API handler
├── stripe-config-example.php       # Configuration example
└── stripe-test-endpoints.php       # Development testing tools

frontend/src/
├── components/
│   ├── VendorSubscription.tsx      # React subscription component
│   └── VendorSubscription-usage-example.tsx
└── app/api/wp-proxy/vendors/[id]/
    ├── subscription-status/route.ts
    └── customer-portal/route.ts
```

## Setup Instructions

### 1. Stripe Dashboard Setup

#### Create Payment Link
1. Go to [Stripe Payment Links](https://dashboard.stripe.com/payment-links)
2. Click "Create payment link"
3. Configure:
   - **Product**: Create new product "Vendor Premium" - $99/month recurring
   - **Success URL**: `https://yourdomain.com/dashboard/vendor-settings`
   - **Customer Information**: Enable "Email addresses"
   - **Metadata**: Add field `vendor_id` (will be populated dynamically)

#### Setup Webhook
1. Go to [Stripe Webhooks](https://dashboard.stripe.com/webhooks)
2. Click "Add endpoint"
3. Configure:
   - **URL**: `https://yourdomain.com/wp-json/tourismiq/v1/stripe/webhook`
   - **Events**: Select these events:
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`
4. Copy the webhook secret (starts with `whsec_`)

### 2. WordPress Configuration

Add these constants to your `wp-config.php`:

```php
// Stripe API Keys (get from https://dashboard.stripe.com/apikeys)
define('STRIPE_SECRET_KEY', 'sk_test_your_secret_key_here');
define('STRIPE_WEBHOOK_SECRET', 'whsec_your_webhook_secret_here');
```

### 3. Frontend Configuration

Add to your `.env.local`:

```bash
NEXT_PUBLIC_STRIPE_PAYMENT_LINK=https://buy.stripe.com/your_payment_link_here
```

### 4. Install Stripe PHP Library

Via Composer:
```bash
composer require stripe/stripe-php
```

Or download manually and include in your theme.

## Usage

### Basic Component Usage

```tsx
import VendorSubscription from '@/components/VendorSubscription';

function VendorSettings({ vendorId }: { vendorId: number }) {
  const paymentLinkUrl = process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK || '';
  
  return (
    <div>
      <h1>Vendor Settings</h1>
      <VendorSubscription 
        vendorId={vendorId}
        paymentLinkUrl={paymentLinkUrl}
      />
    </div>
  );
}
```

### API Endpoints

#### Get Subscription Status
```
GET /wp-json/tourismiq/v1/vendors/{vendor_id}/subscription-status
```

Response:
```json
{
  "is_paid": true,
  "subscription_status": "active",
  "subscription_id": "sub_1234567890",
  "customer_id": "cus_1234567890"
}
```

#### Create Customer Portal Session
```
POST /wp-json/tourismiq/v1/vendors/{vendor_id}/customer-portal
Content-Type: application/json

{
  "return_url": "https://yourdomain.com/dashboard/vendor-settings"
}
```

Response:
```json
{
  "portal_url": "https://billing.stripe.com/session/xyz"
}
```

## How It Works

### 1. Subscription Flow

1. **User clicks "Subscribe"** → Opens Stripe Payment Link
2. **User completes payment** → Stripe creates subscription
3. **Stripe sends webhook** → WordPress receives `customer.subscription.created`
4. **Webhook handler** → Updates `vendor_is_paid = true` in ACF
5. **Premium features** → Automatically enabled for vendor

### 2. Automatic Status Management

| Stripe Event | Action | ACF Field Updates |
|--------------|--------|-------------------|
| `subscription.created` | Enable premium | `vendor_is_paid = true` |
| `payment.succeeded` | Ensure active | `vendor_is_paid = true` |
| `payment.failed` | Grace period | `subscription_status = past_due` |
| `subscription.deleted` | Disable premium | `vendor_is_paid = false` |

### 3. Database Schema

The integration stores Stripe data in WordPress user meta:

```php
// User meta fields (for vendor author)
$user_id = get_post($vendor_id)->post_author;
update_user_meta($user_id, 'stripe_customer_id', $customer_id);
update_user_meta($user_id, 'stripe_subscription_id', $subscription_id);
update_user_meta($user_id, 'stripe_subscription_status', $status);

// ACF fields (for vendor post)
update_field('vendor_is_paid', true, $vendor_id);
update_field('vendor_subscription_status', 'active', $vendor_id);
```

## Testing

### Development Test Endpoints

When `WP_DEBUG` is enabled, test endpoints are available:

```bash
# Test webhook handling
POST /wp-json/tourismiq/v1/stripe/test-webhook
{
  "event_type": "customer.subscription.created",
  "vendor_id": 123
}

# Test subscription status
GET /wp-json/tourismiq/v1/stripe/test-subscription/123

# Test ACF field updates
POST /wp-json/tourismiq/v1/stripe/test-acf/123
{
  "is_paid": true,
  "status": "active"
}
```

### Manual Testing Checklist

1. ✅ Create test vendor profile
2. ✅ Configure Stripe test keys and payment link
3. ✅ Test subscription creation flow
4. ✅ Verify webhook receives events
5. ✅ Confirm ACF fields update correctly
6. ✅ Test customer portal access
7. ✅ Test subscription cancellation
8. ✅ Verify premium features toggle

## Security Considerations

- ✅ **Webhook Verification**: All webhooks verified with Stripe signature
- ✅ **Permission Checks**: Only vendor owners/admins can access endpoints
- ✅ **No PCI Compliance**: Stripe handles all payment data
- ✅ **Secure Storage**: Only non-sensitive IDs stored in WordPress

## Troubleshooting

### Common Issues

**Webhook not receiving events:**
- Check webhook URL is publicly accessible
- Verify webhook secret matches Stripe dashboard
- Check WordPress error logs for authentication issues

**ACF fields not updating:**
- Confirm ACF plugin is active
- Verify field names match exactly: `vendor_is_paid`, `vendor_subscription_status`
- Check user permissions for field updates

**Payment link not working:**
- Verify `NEXT_PUBLIC_STRIPE_PAYMENT_LINK` environment variable
- Ensure payment link is active in Stripe dashboard
- Check metadata configuration includes `vendor_id` field

### Debug Logs

WordPress logs webhook activity:
```bash
tail -f wp-content/debug.log | grep "TourismIQ Stripe"
```

### Stripe Dashboard Monitoring

Monitor webhook delivery:
1. Go to [Stripe Webhooks](https://dashboard.stripe.com/webhooks)
2. Click your webhook endpoint
3. View "Recent deliveries" for success/failure status

## Production Deployment

### Environment Variables

**WordPress (wp-config.php):**
```php
define('STRIPE_SECRET_KEY', 'sk_live_your_live_secret_key');
define('STRIPE_WEBHOOK_SECRET', 'whsec_your_live_webhook_secret');
```

**Frontend (.env.production):**
```bash
NEXT_PUBLIC_STRIPE_PAYMENT_LINK=https://buy.stripe.com/your_live_payment_link
```

### Security Checklist

- [ ] Replace test keys with live Stripe keys
- [ ] Update webhook URL to production domain  
- [ ] Remove or disable test endpoints (`stripe-test-endpoints.php`)
- [ ] Verify SSL certificate on webhook URL
- [ ] Test end-to-end subscription flow in production

## Support

For issues with this integration:
1. Check WordPress debug logs
2. Verify Stripe webhook delivery status
3. Test with development endpoints
4. Review Stripe dashboard for payment/subscription status

For Stripe-specific issues, consult [Stripe Documentation](https://stripe.com/docs).